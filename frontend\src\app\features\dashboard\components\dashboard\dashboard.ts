import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { takeUntil, finalize } from 'rxjs/operators';

import { DashboardService } from '../../services/dashboard.service';
import {
  ThongKeDashboard,
  HoatDongGanDay,
  ThongBaoHeThong
} from '../../models';

/**
 * Component dashboard chính cho hệ thống BHYT/BHXH
 * Hiển thị thống kê tổng quan, hoạt động gần đây và thông báo
 */
@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dashboard.html',
  styleUrl: './dashboard.scss'
})
export class DashboardComponent implements OnInit, OnDestroy {
  thongKe: ThongKeDashboard | null = null;
  hoatDongGanDay: HoatDongGanDay[] = [];
  thongBaoHeThong: Thong<PERSON>aoHeThong[] = [];
  dangTai = true;

  private readonly destroy$ = new Subject<void>();

  constructor(private dashboardService: DashboardService) {}

  ngOnInit(): void {
    this.taiDuLieuDashboard();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Tải dữ liệu dashboard
   */
  private taiDuLieuDashboard(): void {
    this.dangTai = true;

    // Tải thống kê
    this.dashboardService.layThongKeDashboard()
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.dangTai = false)
      )
      .subscribe(thongKe => {
        this.thongKe = thongKe;
      });

    // Tải hoạt động gần đây
    this.dashboardService.layHoatDongGanDay(8)
      .pipe(takeUntil(this.destroy$))
      .subscribe(hoatDong => {
        this.hoatDongGanDay = hoatDong;
      });

    // Tải thông báo hệ thống
    this.dashboardService.layThongBaoHeThong(5)
      .pipe(takeUntil(this.destroy$))
      .subscribe(thongBao => {
        this.thongBaoHeThong = thongBao;
      });
  }

  /**
   * Định dạng số tiền
   */
  dinhDangTien(soTien: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(soTien);
  }

  /**
   * Định dạng thời gian tương đối
   */
  dinhDangThoiGian(thoiGian: Date): string {
    const now = new Date();
    const diff = now.getTime() - thoiGian.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 60) {
      return `${minutes} phút trước`;
    } else if (hours < 24) {
      return `${hours} giờ trước`;
    } else {
      return `${days} ngày trước`;
    }
  }

  /**
   * Lấy icon cho loại hoạt động
   */
  layIconHoatDong(loai: string): string {
    const icons: { [key: string]: string } = {
      'tao_moi': 'M12 6v6m0 0v6m0-6h6m-6 0H6',
      'duyet': 'M5 13l4 4L19 7',
      'tu_choi': 'M6 18L18 6M6 6l12 12',
      'cap_nhat': 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
    };
    return icons[loai] || 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
  }

  /**
   * Lấy màu cho loại hoạt động
   */
  layMauHoatDong(loai: string): string {
    const colors: { [key: string]: string } = {
      'tao_moi': 'text-blue-500',
      'duyet': 'text-green-500',
      'tu_choi': 'text-red-500',
      'cap_nhat': 'text-yellow-500'
    };
    return colors[loai] || 'text-gray-500';
  }

  /**
   * Lấy màu cho loại thông báo
   */
  layMauThongBao(loai: string): string {
    const colors: { [key: string]: string } = {
      'thong_tin': 'border-blue-200 bg-blue-50',
      'canh_bao': 'border-yellow-200 bg-yellow-50',
      'loi': 'border-red-200 bg-red-50'
    };
    return colors[loai] || 'border-gray-200 bg-gray-50';
  }
}
