using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HuyPhuc.Application.Features.BHXH.DTOs;
using HuyPhuc.Application.Features.BHXH.Interfaces;

namespace HuyPhuc.API.Controllers;

/// <summary>
/// Controller để xử lý authentication với VNPost
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class VnPostAuthController : ControllerBase
{
    private readonly IVnPostAuthService _authService;
    private readonly ILogger<VnPostAuthController> _logger;

    public VnPostAuthController(
        IVnPostAuthService authService,
        ILogger<VnPostAuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    /// <summary>
    /// Lấy captcha từ VNPost để hiển thị cho người dùng
    /// </summary>
    /// <returns>Captcha image và code</returns>
    [HttpGet("captcha")]
    public async Task<ActionResult<VnPostCaptchaResponseDto>> GetCaptcha(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Getting captcha from VNPost");
            
            var captcha = await _authService.GetCaptchaAsync(cancellationToken);
            
            if (!captcha.Success)
            {
                _logger.LogWarning("Failed to get captcha from VNPost: {Message}", captcha.Message);
                return BadRequest(new { message = "Không thể lấy captcha từ VNPost", error = captcha.Message });
            }

            return Ok(captcha);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting captcha from VNPost");
            return StatusCode(500, new { message = "Lỗi server khi lấy captcha", error = ex.Message });
        }
    }

    /// <summary>
    /// Refresh access token VNPost
    /// </summary>
    /// <param name="request">Thông tin đăng nhập và captcha</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Kết quả refresh token</returns>
    [HttpPost("refresh-token")]
    public async Task<ActionResult> RefreshToken(
        [FromBody] RefreshTokenRequestDto request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Refreshing VNPost access token for user: {UserName}", request.UserName);

            if (string.IsNullOrWhiteSpace(request.UserName) || 
                string.IsNullOrWhiteSpace(request.Password) ||
                string.IsNullOrWhiteSpace(request.CaptchaText) ||
                string.IsNullOrWhiteSpace(request.CaptchaCode))
            {
                return BadRequest(new { message = "Vui lòng điền đầy đủ thông tin đăng nhập và captcha" });
            }

            var success = await _authService.RefreshTokenAsync(request, cancellationToken);
            
            if (!success)
            {
                _logger.LogWarning("Failed to refresh VNPost token for user: {UserName}", request.UserName);
                return BadRequest(new { message = "Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin." });
            }

            _logger.LogInformation("Successfully refreshed VNPost token for user: {UserName}", request.UserName);
            return Ok(new { message = "Đăng nhập thành công", success = true });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing VNPost token for user: {UserName}", request.UserName);
            return StatusCode(500, new { message = "Lỗi server khi đăng nhập", error = ex.Message });
        }
    }

    /// <summary>
    /// Kiểm tra trạng thái token hiện tại
    /// </summary>
    /// <returns>Thông tin về token</returns>
    [HttpGet("token-status")]
    public ActionResult GetTokenStatus()
    {
        try
        {
            var isExpired = _authService.IsTokenExpired();
            var currentToken = _authService.GetCurrentAccessToken();
            
            return Ok(new 
            { 
                isExpired = isExpired,
                hasToken = !string.IsNullOrEmpty(currentToken),
                message = isExpired ? "Token đã hết hạn" : "Token còn hiệu lực"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking token status");
            return StatusCode(500, new { message = "Lỗi server khi kiểm tra token", error = ex.Message });
        }
    }

    /// <summary>
    /// Force expire token for testing purposes
    /// </summary>
    [HttpPost("force-expire-token")]
    public IActionResult ForceExpireToken()
    {
        try
        {
            _authService.ForceTokenExpired();
            _logger.LogInformation("🔴 VNPost token force expired for testing");
            return Ok(new { message = "Token force expired successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error force expiring VNPost token");
            return StatusCode(500, new { message = "Error force expiring token" });
        }
    }
}
