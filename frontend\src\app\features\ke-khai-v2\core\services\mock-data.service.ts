import { Injectable } from '@angular/core';
import { Observable, of, delay } from 'rxjs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>KhaiBase, LaoDong, TrangThaiKe<PERSON>hai, LoaiKeKhai, GioiTinh, PhuongAnDongBHXH, PhuongThucDong } from '../models';
import { ToKhai602, To<PERSON><PERSON>602<PERSON><PERSON>er, ToKhai602Response } from '../../features/to-khai-602/models';

/**
 * Service cung cấp mock data cho development
 * Sẽ được thay thế bằng real API calls sau
 */
@Injectable({
  providedIn: 'root'
})
export class MockDataService {

  /**
   * Mock data cho đại lý
   */
  private readonly mockDaiLy: DaiLy[] = [
    {
      id: 1,
      ma: 'DL001',
      ten: '<PERSON>ạ<PERSON> lý <PERSON>',
      dia<PERSON>hi: '123 Đường <PERSON><PERSON>, T<PERSON>. <PERSON>, <PERSON> <PERSON>',
      soDienThoai: '0296.3841.234',
      email: '<EMAIL>',
      trangThai: 1
    },
    {
      id: 2,
      ma: 'DL002', 
      ten: '<PERSON><PERSON><PERSON> l<PERSON>',
      dia<PERSON>hi: '456 Đ<PERSON>ờng 3/2, <PERSON>. <PERSON>nh <PERSON>ều, TP. Cần Thơ',
      soDienThoai: '0292.3812.345',
      email: '<EMAIL>',
      trangThai: 1
    },
    {
      id: 3,
      ma: 'DL003',
      ten: 'Đại lý Kiên Giang', 
      dia<PERSON>hi: '789 Đ<PERSON>ờng Nguyễn Trung Trực, TP. Rạch Giá, Kiên Giang',
      soDienThoai: '0297.3877.456',
      email: '<EMAIL>',
      trangThai: 1
    }
  ];

  /**
   * Mock data cho đơn vị
   */
  private readonly mockDonVi: DonVi[] = [
    // Đơn vị thuộc Đại lý An Giang
    {
      id: 1,
      ma: 'DV001',
      ten: 'Phòng Kê khai An Giang',
      daiLyId: 1,
      diaChi: '123 Đường Nguyễn Văn Cừ, TP. Long Xuyên',
      soDienThoai: '0296.3841.111',
      email: '<EMAIL>',
      trangThai: 1
    },
    {
      id: 2,
      ma: 'DV002',
      ten: 'Phòng Thu BHXH An Giang',
      daiLyId: 1,
      diaChi: '124 Đường Nguyễn Văn Cừ, TP. Long Xuyên',
      soDienThoai: '0296.3841.222',
      email: '<EMAIL>',
      trangThai: 1
    },
    // Đơn vị thuộc Đại lý Cần Thơ
    {
      id: 3,
      ma: 'DV003',
      ten: 'Phòng Kê khai Cần Thơ',
      daiLyId: 2,
      diaChi: '456 Đường 3/2, Q. Ninh Kiều',
      soDienThoai: '0292.3812.111',
      email: '<EMAIL>',
      trangThai: 1
    },
    {
      id: 4,
      ma: 'DV004',
      ten: 'Phòng Thu BHXH Cần Thơ',
      daiLyId: 2,
      diaChi: '457 Đường 3/2, Q. Ninh Kiều',
      soDienThoai: '0292.3812.222',
      email: '<EMAIL>',
      trangThai: 1
    }
  ];

  /**
   * Mock data cho tờ khai 602
   */
  private readonly mockToKhai602: ToKhai602[] = [
    {
      id: 1,
      ma: 'TK602-001-2024',
      loaiKeKhai: LoaiKeKhai.ToKhai602,
      daiLyId: 1,
      donViId: 1,
      kyKeKhai: '01/2024',
      trangThai: TrangThaiKeKhai.DangSoan,
      ngayTao: new Date('2024-01-15'),
      nguoiTao: 'admin',
      ngayCapNhat: new Date('2024-01-15'),
      nguoiCapNhat: 'admin',
      thongTinKhac: {
        soSoBHXH: 'BHXH-AG-001',
        ngayTao: new Date('2024-01-15'),
        nguoiTao: 'admin',
        ghiChu: 'Tờ khai tháng 1/2024'
      },
      danhSachLaoDong: [
        {
          id: '1',
          stt: 1,
          maSoBHXH: 'BHXH123456789',
          hoTen: 'Nguyễn Văn A',
          ccns: '079123456789',
          ngaySinh: '01/01/1990',
          gioiTinh: GioiTinh.Nam,
          quocTich: 'Việt Nam',
          danToc: '01', // Mã dân tộc Kinh
          cmnd: '079123456789',
          maTinhKs: '89',
          maHuyenKs: '890',
          maXaKs: '89001',
          dienThoaiLh: '**********',
          maHoGiaDinh: 'HGD001',
          loai: 1, // Tăng lao động
          phuongAn: PhuongAnDongBHXH.TM,
          phuongThuc: PhuongThucDong.Ba_Thang,
          thangBatDau: '01/2024',
          tienLai: 0,
          tienThua: 0,
          tienTuDong: 400000,
          tongTien: 400000,
          tienHoTro: 0,
          mucThuNhap: 5000000,
          ghiChu: 'Lao động mới'
        }
      ],
      tongSoLaoDong: 1,
      tongTienDong: 400000
    },
    {
      id: 2,
      ma: 'TK602-002-2024',
      loaiKeKhai: LoaiKeKhai.ToKhai602,
      daiLyId: 1,
      donViId: 1,
      kyKeKhai: '02/2024',
      trangThai: TrangThaiKeKhai.DaGui,
      ngayTao: new Date('2024-02-15'),
      nguoiTao: 'admin',
      ngayCapNhat: new Date('2024-02-16'),
      nguoiCapNhat: 'admin',
      thongTinKhac: {
        soSoBHXH: 'BHXH-AG-002',
        ngayTao: new Date('2024-02-15'),
        nguoiTao: 'admin',
        ghiChu: 'Tờ khai tháng 2/2024'
      },
      danhSachLaoDong: [
        {
          id: '2',
          stt: 1,
          maSoBHXH: 'BHXH987654321',
          hoTen: 'Trần Thị B',
          ccns: '079987654321',
          ngaySinh: '15/05/1985',
          gioiTinh: GioiTinh.Nu,
          quocTich: 'Việt Nam',
          danToc: '01', // Mã dân tộc Kinh
          cmnd: '079987654321',
          maTinhKs: '89',
          maHuyenKs: '890',
          maXaKs: '89002',
          dienThoaiLh: '**********',
          maHoGiaDinh: 'HGD002',
          loai: 2, // Tăng mức lương
          phuongAn: PhuongAnDongBHXH.DT,
          phuongThuc: PhuongThucDong.Ba_Thang,
          thangBatDau: '02/2024',
          tienLai: 0,
          tienThua: 0,
          tienTuDong: 480000,
          tongTien: 480000,
          tienHoTro: 0,
          mucThuNhap: 6000000,
          ghiChu: 'Điều chỉnh mức lương'
        }
      ],
      tongSoLaoDong: 1,
      tongTienDong: 480000
    }
  ];

  /**
   * Lấy danh sách đại lý
   */
  layDanhSachDaiLy(): Observable<DaiLy[]> {
    return of(this.mockDaiLy).pipe(delay(500));
  }

  /**
   * Lấy danh sách đơn vị theo đại lý
   */
  layDanhSachDonVi(daiLyId?: number): Observable<DonVi[]> {
    let result = this.mockDonVi;
    if (daiLyId) {
      result = this.mockDonVi.filter(dv => dv.daiLyId === daiLyId);
    }
    return of(result).pipe(delay(300));
  }

  /**
   * Lấy danh sách tờ khai 602
   */
  layDanhSachToKhai602(filter?: ToKhai602Filter): Observable<ToKhai602Response> {
    let result = [...this.mockToKhai602];
    
    // Apply filters
    if (filter?.daiLyId) {
      result = result.filter(tk => tk.daiLyId === filter.daiLyId);
    }
    if (filter?.donViId) {
      result = result.filter(tk => tk.donViId === filter.donViId);
    }
    if (filter?.trangThai !== undefined) {
      result = result.filter(tk => tk.trangThai === filter.trangThai);
    }
    if (filter?.kyKeKhai) {
      result = result.filter(tk => tk.kyKeKhai === filter.kyKeKhai);
    }
    if (filter?.tuKhoa) {
      const tuKhoa = filter.tuKhoa.toLowerCase();
      result = result.filter(tk =>
        (tk.ma || '').toLowerCase().includes(tuKhoa) ||
        (tk.kyKeKhai || '').toLowerCase().includes(tuKhoa)
      );
    }

    // Pagination
    const trang = filter?.trang || 1;
    const kichThuoc = filter?.kichThuoc || 10;
    const startIndex = (trang - 1) * kichThuoc;
    const endIndex = startIndex + kichThuoc;
    const paginatedResult = result.slice(startIndex, endIndex);

    const response: ToKhai602Response = {
      data: paginatedResult,
      tongSo: result.length,
      trang,
      kichThuoc,
      tongTrang: Math.ceil(result.length / kichThuoc)
    };

    return of(response).pipe(delay(800));
  }

  /**
   * Lấy chi tiết tờ khai 602
   */
  layChiTietToKhai602(id: number): Observable<ToKhai602> {
    const toKhai = this.mockToKhai602.find(tk => tk.id === id);
    if (!toKhai) {
      throw new Error(`Không tìm thấy tờ khai với ID: ${id}`);
    }
    return of(toKhai).pipe(delay(400));
  }

  /**
   * Tạo mới tờ khai 602
   */
  taoMoiToKhai602(data: Partial<ToKhai602>): Observable<ToKhai602> {
    const newId = Math.max(...this.mockToKhai602.map(tk => tk.id || 0)) + 1;
    const newToKhai: ToKhai602 = {
      id: newId,
      ma: `TK602-${String(newId).padStart(3, '0')}-2024`,
      loaiKeKhai: LoaiKeKhai.ToKhai602,
      daiLyId: data.daiLyId!,
      donViId: data.donViId!,
      kyKeKhai: data.kyKeKhai!,
      trangThai: TrangThaiKeKhai.DangSoan,
      ngayTao: new Date(),
      nguoiTao: 'admin',
      ngayCapNhat: new Date(),
      nguoiCapNhat: 'admin',
      thongTinKhac: data.thongTinKhac || {
        soSoBHXH: `BHXH-${newId}`,
        ngayTao: new Date(),
        nguoiTao: 'admin'
      },
      danhSachLaoDong: data.danhSachLaoDong || [],
      tongSoLaoDong: data.danhSachLaoDong?.length || 0,
      tongTienDong: data.danhSachLaoDong?.reduce((sum, ld) => sum + (ld.tienTuDong || 0), 0) || 0
    };

    this.mockToKhai602.push(newToKhai);
    return of(newToKhai).pipe(delay(600));
  }

  /**
   * Cập nhật tờ khai 602
   */
  capNhatToKhai602(id: number, data: Partial<ToKhai602>): Observable<ToKhai602> {
    const index = this.mockToKhai602.findIndex(tk => tk.id === id);
    if (index === -1) {
      throw new Error(`Không tìm thấy tờ khai với ID: ${id}`);
    }

    const updatedToKhai = {
      ...this.mockToKhai602[index],
      ...data,
      ngayCapNhat: new Date(),
      nguoiCapNhat: 'admin'
    };

    this.mockToKhai602[index] = updatedToKhai;
    return of(updatedToKhai).pipe(delay(600));
  }

  /**
   * Xóa tờ khai 602
   */
  xoaToKhai602(id: number): Observable<boolean> {
    const index = this.mockToKhai602.findIndex(tk => tk.id === id);
    if (index === -1) {
      throw new Error(`Không tìm thấy tờ khai với ID: ${id}`);
    }

    this.mockToKhai602.splice(index, 1);
    return of(true).pipe(delay(400));
  }
}
