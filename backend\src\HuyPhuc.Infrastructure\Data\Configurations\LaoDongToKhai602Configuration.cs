using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class LaoDongToKhai602Configuration : IEntityTypeConfiguration<LaoDongToKhai602>
{
    public void Configure(EntityTypeBuilder<LaoDongToKhai602> builder)
    {
        builder.ToTable("chi_tiet_to_khai_602");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.ToKhai602Id)
            .HasColumnName("to_khai_602_id")
            .IsRequired();

        builder.Property(e => e.MaSoBHXH)
            .HasColumnName("ma_so_bhxh")
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(e => e.Stt)
            .HasColumnName("stt")
            .IsRequired();

        builder.Property(e => e.PhuongAn)
            .HasColumnName("phuong_an")
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(e => e.PhuongThuc)
            .HasColumnName("phuong_thuc")
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(e => e.ThangBatDau)
            .HasColumnName("thang_bat_dau")
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(e => e.MucThuNhap)
            .HasColumnName("muc_thu_nhap")
            .HasColumnType("decimal(15,2)")
            .IsRequired();

        builder.Property(e => e.TienLai)
            .HasColumnName("tien_lai")
            .HasColumnType("decimal(15,2)")
            .HasDefaultValue(0);

        builder.Property(e => e.TienThua)
            .HasColumnName("tien_thua")
            .HasColumnType("decimal(15,2)")
            .HasDefaultValue(0);

        builder.Property(e => e.TienTuDong)
            .HasColumnName("tien_tu_dong")
            .HasColumnType("decimal(15,2)")
            .HasDefaultValue(0);

        builder.Property(e => e.TongTien)
            .HasColumnName("tong_tien")
            .HasColumnType("decimal(15,2)")
            .HasDefaultValue(0);

        builder.Property(e => e.TienHoTro)
            .HasColumnName("tien_ho_tro")
            .HasColumnType("decimal(15,2)")
            .HasDefaultValue(0);

        builder.Property(e => e.Message)
            .HasColumnName("message")
            .HasColumnType("text");

        builder.Property(e => e.IsError)
            .HasColumnName("is_error")
            .HasDefaultValue(false);

        builder.Property(e => e.MaLoi)
            .HasColumnName("ma_loi")
            .HasMaxLength(10);

        builder.Property(e => e.MoTaLoi)
            .HasColumnName("mo_ta_loi")
            .HasColumnType("text");

        builder.Property(e => e.LoaiNsnn)
            .HasColumnName("loai_nsnn")
            .HasMaxLength(50);

        // Audit fields
        builder.Property(e => e.NgayTao)
            .HasColumnName("created")
            .IsRequired();

        builder.Property(e => e.NgayCapNhat)
            .HasColumnName("last_modified");

        builder.Property(e => e.NguoiTao)
            .HasColumnName("created_by")
            .HasMaxLength(255);

        builder.Property(e => e.NguoiCapNhat)
            .HasColumnName("last_modified_by")
            .HasMaxLength(255);

        // Relationships
        builder.HasOne<ToKhai602>()
            .WithMany(e => e.DanhSachLaoDong)
            .HasForeignKey(e => e.ToKhai602Id)
            .HasConstraintName("fk_chi_tiet_to_khai_602")
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(e => e.Tk1Ts)
            .WithMany(e => e.DanhSachChiTietToKhai)
            .HasForeignKey(e => e.MaSoBHXH)
            .HasPrincipalKey(e => e.MaSoBHXH)
            .HasConstraintName("fk_chi_tiet_to_khai_602_ma_so_bhxh")
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(e => e.ToKhai602Id)
            .HasDatabaseName("idx_chi_tiet_to_khai_602_to_khai_id");

        builder.HasIndex(e => e.MaSoBHXH)
            .HasDatabaseName("idx_chi_tiet_to_khai_602_ma_so_bhxh");

        // Unique constraint: một người chỉ có thể có một bản ghi trong một tờ khai
        builder.HasIndex(e => new { e.ToKhai602Id, e.MaSoBHXH })
            .IsUnique()
            .HasDatabaseName("ix_chi_tiet_to_khai_602_unique");
    }
}
