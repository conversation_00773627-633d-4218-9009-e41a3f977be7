using HuyPhuc.Application.DTOs.DaiLy;
using HuyPhuc.Application.Features.DaiLy.Queries.LayDanhSachDaiLyCuaNguoiDung;
using HuyPhuc.Api.Controllers.Base;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// Controller quản lý đại lý
/// </summary>
[ApiController]
[Route("api/dai-ly")]
[Authorize]
public class DaiLyController : BaseController
{
    /// <summary>
    /// Lấy danh sách đại lý của người dùng hiện tại
    /// </summary>
    /// <returns>Danh sách đại lý</returns>
    [HttpGet("cua-nguoi-dung")]
    public async Task<ActionResult<List<DaiLySelectDto>>> LayDanhSachDaiLyCuaNguoiDung()
    {
        try
        {
            var result = await Mediator.Send(new LayDanhSachDaiLyCuaNguoiDungQuery());
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Có lỗi xảy ra khi lấy danh sách đại lý", error = ex.Message });
        }
    }
}
