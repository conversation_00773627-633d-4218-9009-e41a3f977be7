using HuyPhuc.Domain.Common;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity cho danh mục xã/phường
/// </summary>
public class DmXa : BaseAuditableEntity
{
    /// <summary>
    /// Mã xã (5 ký tự)
    /// </summary>
    public string MaXa { get; set; } = string.Empty;

    /// <summary>
    /// Tên xã/phường
    /// </summary>
    public string TenXa { get; set; } = string.Empty;

    /// <summary>
    /// Text hiển thị (mã - tên)
    /// </summary>
    public string TextDisplay { get; set; } = string.Empty;

    /// <summary>
    /// Mã huyện (foreign key)
    /// </summary>
    public string MaHuyen { get; set; } = string.Empty;

    /// <summary>
    /// Mã tỉnh (foreign key)
    /// </summary>
    public string MaTinh { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property - Huyện mà xã này thuộc về
    /// </summary>
    public virtual DmHuyen? Huyen { get; set; }

    /// <summary>
    /// Navigation property - Tỉnh mà xã này thuộc về
    /// </summary>
    public virtual DmTinh? Tinh { get; set; }
}
