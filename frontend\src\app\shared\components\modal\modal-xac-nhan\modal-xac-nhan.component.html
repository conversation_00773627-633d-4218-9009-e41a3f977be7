<!-- Modal Overlay -->
<div *ngIf="isModalOpen" 
     class="fixed inset-0 z-50 overflow-y-auto"
     (click)="onCloseModal()">
  
  <!-- Modal Background -->
  <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
         aria-hidden="true"></div>

    <!-- Modal Content -->
    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
         (click)="$event.stopPropagation()">
      
      <!-- Modal Body -->
      <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <!-- Icon -->
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10"
               [ngClass]="{
                 'bg-red-100': data?.type === 'danger',
                 'bg-yellow-100': data?.type === 'warning',
                 'bg-green-100': data?.type === 'success',
                 'bg-blue-100': data?.type === 'info' || !data?.type
               }">
            <svg class="h-6 w-6" [ngClass]="modalColorClass" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="modalIcon"></path>
            </svg>
          </div>

          <!-- Content -->
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <!-- Title -->
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
              {{ data?.title }}
            </h3>
            
            <!-- Message -->
            <div class="mt-2">
              <p class="text-sm text-gray-500 whitespace-pre-line">
                {{ data?.message }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
        <!-- Confirm Button -->
        <button type="button"
                (click)="onConfirm()"
                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm transition-colors duration-200"
                [ngClass]="confirmButtonClass">
          {{ data?.confirmText }}
        </button>

        <!-- Cancel Button -->
        <button type="button"
                (click)="onCancel()"
                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition-colors duration-200">
          {{ data?.cancelText }}
        </button>
      </div>
    </div>
  </div>
</div>
