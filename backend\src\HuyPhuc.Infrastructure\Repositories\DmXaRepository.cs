using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories;
using HuyPhuc.Infrastructure.Repositories.Base;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Infrastructure.Repositories;

/// <summary>
/// Repository implementation cho DmXa
/// </summary>
public class DmXaRepository : Repository<DmXa>, IDmXaRepository
{
    public DmXaRepository(IApplicationDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Lấy tất cả xã/phường
    /// </summary>
    public async Task<List<DmXa>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmXa>()
            .Include(x => x.Huyen)
            .Include(x => x.Tinh)
            .OrderBy(x => x.MaTinh)
            .ThenBy(x => x.<PERSON>uyen)
            .ThenBy(x => x.MaXa)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// L<PERSON>y danh sách xã theo mã huyện
    /// </summary>
    public async Task<List<DmXa>> GetByMaHuyenAsync(string maHuyen, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmXa>()
            .Include(x => x.Huyen)
            .Include(x => x.Tinh)
            .Where(x => x.MaHuyen == maHuyen)
            .OrderBy(x => x.MaXa)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Lấy danh sách xã theo mã tỉnh
    /// </summary>
    public async Task<List<DmXa>> GetByMaTinhAsync(string maTinh, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmXa>()
            .Include(x => x.Huyen)
            .Include(x => x.Tinh)
            .Where(x => x.MaTinh == maTinh)
            .OrderBy(x => x.MaHuyen)
            .ThenBy(x => x.MaXa)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Lấy xã theo mã xã
    /// </summary>
    public async Task<DmXa?> GetByMaXaAsync(string maXa, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmXa>()
            .Include(x => x.Huyen)
            .Include(x => x.Tinh)
            .FirstOrDefaultAsync(x => x.MaXa == maXa, cancellationToken);
    }

    /// <summary>
    /// Kiểm tra mã xã có tồn tại không
    /// </summary>
    public async Task<bool> ExistsByMaXaAsync(string maXa, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmXa>()
            .AnyAsync(x => x.MaXa == maXa, cancellationToken);
    }
}
