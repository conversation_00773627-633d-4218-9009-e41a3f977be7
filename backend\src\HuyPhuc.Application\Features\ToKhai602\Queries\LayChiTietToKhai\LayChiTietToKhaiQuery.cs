using HuyPhuc.Application.DTOs.ToKhai602;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities.Base;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.ToKhai602.Queries.LayChiTietToKhai;

/// <summary>
/// Query lấy chi tiết tờ khai 602
/// </summary>
public record LayChiTietToKhaiQuery(int Id) : IRequest<ToKhai602ChiTietDto>;

/// <summary>
/// Handler xử lý query lấy chi tiết tờ khai 602
/// </summary>
public class LayChiTietToKhaiQueryHandler : IRequestHandler<LayChiTietToKhaiQuery, ToKhai602ChiTietDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public LayChiTietToKhaiQueryHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<ToKhai602ChiTietDto> Handle(LayChiTietToKhaiQuery query, CancellationToken cancellationToken)
    {
        var userIdString = _currentUserService.UserId;
        
        if (userIdString == null || !int.TryParse(userIdString, out var userId))
        {
            throw new UnauthorizedAccessException("Không thể xác định người dùng hiện tại");
        }

        var toKhai = await _context.ToKhai602
            .Include(t => t.DaiLy)
            .Include(t => t.DonVi)
            .Include(t => t.DanhSachLaoDong)
                .ThenInclude(ld => ld.Tk1Ts)
            .FirstOrDefaultAsync(t => t.Id == query.Id, cancellationToken);

        if (toKhai == null)
        {
            throw new ArgumentException($"Không tìm thấy tờ khai với ID: {query.Id}");
        }

        return new ToKhai602ChiTietDto
        {
            Id = toKhai.Id,
            MaToKhai = toKhai.MaToKhai,
            DaiLyId = toKhai.DaiLyId,
            TenDaiLy = toKhai.DaiLy.TenDaiLy,
            DonViId = toKhai.DonViId,
            TenDonVi = toKhai.DonVi.TenDonVi,
            SoSoBHXH = toKhai.SoSoBHXH,
            KyKeKhai = null, // TODO: Implement if needed
            TrangThai = toKhai.TrangThai.ToString(),
            GhiChu = toKhai.GhiChu,
            NgayTao = toKhai.NgayTao,
            NgayCapNhat = toKhai.NgayCapNhat,
            NguoiTao = ((IAuditableEntity)toKhai).NguoiTao,
            NguoiCapNhat = ((IAuditableEntity)toKhai).NguoiCapNhat,
            DanhSachLaoDong = toKhai.DanhSachLaoDong.Select(ld => new LaoDongToKhaiDto
            {
                Id = ld.Id,
                Stt = ld.Stt,
                MaSoBHXH = ld.MaSoBHXH,
                // Thông tin từ Tk1Ts
                HoTen = ld.Tk1Ts?.HoTen ?? "",
                Ccns = ld.Tk1Ts?.Ccns ?? "",
                Cmnd = ld.Tk1Ts?.Cmnd ?? "",
                NgaySinh = ld.Tk1Ts?.NgaySinh ?? "",
                GioiTinh = ld.Tk1Ts?.GioiTinh ?? 1,
                QuocTich = ld.Tk1Ts?.QuocTich ?? "VN",
                DanToc = ld.Tk1Ts?.DanToc ?? "01",
                MaTinhKs = ld.Tk1Ts?.MaTinhKs ?? "",
                MaHuyenKs = ld.Tk1Ts?.MaHuyenKs ?? "",
                MaXaKs = ld.Tk1Ts?.MaXaKs ?? "",
                DienThoaiLh = ld.Tk1Ts?.DienThoaiLh ?? "",
                MaHoGiaDinh = ld.Tk1Ts?.MaHoGiaDinh ?? "",
                // Thông tin từ LaoDongToKhai602
                PhuongAn = ld.PhuongAn,
                PhuongThuc = ld.PhuongThuc,
                ThangBatDau = ld.ThangBatDau,
                TienLai = ld.TienLai,
                TienThua = ld.TienThua,
                TienTuDong = ld.TienTuDong,
                TongTien = ld.TongTien,
                TienHoTro = ld.TienHoTro,
                MucThuNhap = ld.MucThuNhap,
                TypeId = ld.Tk1Ts?.TypeId ?? "TM",
                IsThamGiaBb = ld.Tk1Ts?.IsThamGiaBb ?? false,
                IsTamHoanHD = ld.Tk1Ts?.IsTamHoanHd ?? false
            }).ToList(),
            TongSoLaoDong = toKhai.DanhSachLaoDong.Count,
            TongTienDong = toKhai.DanhSachLaoDong.Sum(ld => ld.TongTien)
        };
    }
}
