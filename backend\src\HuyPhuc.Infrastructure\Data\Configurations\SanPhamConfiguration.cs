using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class SanPhamConfiguration : IEntityTypeConfiguration<SanPham>
{
    public void Configure(EntityTypeBuilder<SanPham> builder)
    {
        builder.ToTable("SanPham");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.TenSanPham)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(x => x.MoTa)
            .HasMaxLength(1000);

        builder.Property(x => x.GiaBan)
            .IsRequired()
            .HasColumnType("decimal(18,2)");

        builder.Property(x => x.GiaGoc)
            .HasColumnType("decimal(18,2)");

        builder.Property(x => x.SoLuongTon)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(x => x.LoaiSanPham)
            .IsRequired()
            .HasConversion<int>()
            .HasDefaultValue(LoaiSanPham.KhacHang);

        builder.Property(x => x.HinhAnh)
            .HasMaxLength(500);

        builder.Property(x => x.DangKinhDoanh)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(x => x.NgayTao)
            .IsRequired();

        builder.Property(x => x.NgayCapNhat);

        builder.Property(x => x.NguoiTao)
            .HasMaxLength(50);

        builder.Property(x => x.NguoiCapNhat)
            .HasMaxLength(50);

        // Configure relationships
        builder.HasMany(x => x.DanhSachChiTietDonHang)
            .WithOne(x => x.SanPham)
            .HasForeignKey(x => x.SanPhamId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(x => x.TenSanPham)
            .HasDatabaseName("IX_SanPham_TenSanPham");

        builder.HasIndex(x => x.LoaiSanPham)
            .HasDatabaseName("IX_SanPham_LoaiSanPham");

        builder.HasIndex(x => x.DangKinhDoanh)
            .HasDatabaseName("IX_SanPham_DangKinhDoanh");

        // Ignore domain events
        builder.Ignore(x => x.DomainEvents);
    }
}
