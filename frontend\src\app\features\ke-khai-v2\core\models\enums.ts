/**
 * Enums cho feature kê khai
 */

/**
 * Enum loại kê khai
 */
export enum LoaiKeKhai {
  ToKhai602 = 'to_khai_602',
  ToKhai603 = 'to_khai_603',
  ToKhai604 = 'to_khai_604'
}

/**
 * Enum trạng thái kê khai
 */
export enum TrangThaiKeKhai {
  DangSoan = 'dang_soan',
  DaGui = 'da_gui',
  DaDuyet = 'da_duyet',
  BiTuChoi = 'bi_tu_choi'
}

/**
 * Enum giới tính
 */
export enum GioiTinh {
  Nam = 1,
  Nu = 2
}

/**
 * Enum phương án đóng BHXH
 */
export enum PhuongAnDongBHXH {
  TM = 'TM', // Tăng mới
  DT = 'DT', // Đóng tiếp
  DL = 'DL', // Dừng lại
  GH = 'GH'  // Dừng đóng
}

/**
 * Enum phương thức đóng
 */
export enum PhuongThucDong {
  Mot_Thang = '1',
  Ba_Thang = '3',
  Sau_Thang = '6',
  Mu<PERSON>_Hai_Thang = '12',
  Nam_Sau = 'nam-sau',
  Nam_Thieu = 'nam-thieu'
}
