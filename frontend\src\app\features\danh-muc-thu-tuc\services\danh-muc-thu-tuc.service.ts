import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';

import { 
  DanhMucThuTuc, 
  DanhMucThuTucFilter, 
  DanhMucThuTucResponse 
} from '../models/danh-muc-thu-tuc.model';

/**
 * Service quản lý danh mục thủ tục
 */
@Injectable({
  providedIn: 'root'
})
export class DanhMucThuTucService {
  private readonly apiUrl = 'http://localhost:5129/api/danh-muc-thu-tuc';
  
  // State management
  private readonly _danhSachThuTuc$ = new BehaviorSubject<DanhMucThuTuc[]>([]);
  private readonly _tongSoBanGhi$ = new BehaviorSubject<number>(0);
  private readonly _dangTai$ = new BehaviorSubject<boolean>(false);
  private readonly _boLoc$ = new BehaviorSubject<DanhMucThuTucFilter>({
    trang: 1,
    kichThuocTrang: 10
  });

  // Public observables
  readonly danhSachThuTuc$ = this._danhSachThuTuc$.asObservable();
  readonly tongSoBanGhi$ = this._tongSoBanGhi$.asObservable();
  readonly dangTai$ = this._dangTai$.asObservable();
  readonly boLoc$ = this._boLoc$.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Lấy danh sách thủ tục với phân trang và lọc
   */
  layDanhSachThuTuc(filter?: DanhMucThuTucFilter): Observable<DanhMucThuTucResponse> {
    this._dangTai$.next(true);

    console.log('API URL:', this.apiUrl);
    let params = new HttpParams();
    
    if (filter) {
      if (filter.tuKhoa) {
        params = params.set('tuKhoa', filter.tuKhoa);
      }
      if (filter.linhVuc !== undefined) {
        params = params.set('linhVuc', filter.linhVuc.toString());
      }
      if (filter.trangThai !== undefined) {
        params = params.set('trangThai', filter.trangThai.toString());
      }
      if (filter.trang) {
        params = params.set('page', filter.trang.toString());
      }
      if (filter.kichThuocTrang) {
        params = params.set('pageSize', filter.kichThuocTrang.toString());
      }
    }

    return this.http.get<DanhMucThuTucResponse>(this.apiUrl, { params })
      .pipe(
        tap(response => {
          this._danhSachThuTuc$.next(response.items);
          this._tongSoBanGhi$.next(response.totalCount);
          this._dangTai$.next(false);
        })
      );
  }

  /**
   * Lấy thủ tục theo ID
   */
  layThuTucTheoId(id: number): Observable<DanhMucThuTuc> {
    return this.http.get<DanhMucThuTuc>(`${this.apiUrl}/${id}`);
  }

  /**
   * Tạo thủ tục mới
   */
  taoThuTuc(thuTuc: Partial<DanhMucThuTuc>): Observable<DanhMucThuTuc> {
    return this.http.post<DanhMucThuTuc>(this.apiUrl, thuTuc);
  }

  /**
   * Cập nhật thủ tục
   */
  capNhatThuTuc(id: number, thuTuc: Partial<DanhMucThuTuc>): Observable<DanhMucThuTuc> {
    return this.http.put<DanhMucThuTuc>(`${this.apiUrl}/${id}`, thuTuc);
  }

  /**
   * Xóa thủ tục
   */
  xoaThuTuc(id: number): Observable<boolean> {
    return this.http.delete(`${this.apiUrl}/${id}`)
      .pipe(
        map(() => true)
      );
  }

  /**
   * Cập nhật bộ lọc
   */
  capNhatBoLoc(filter: DanhMucThuTucFilter): void {
    this._boLoc$.next({ ...this._boLoc$.value, ...filter });
  }

  /**
   * Reset bộ lọc về mặc định
   */
  resetBoLoc(): void {
    this._boLoc$.next({
      trang: 1,
      kichThuocTrang: 10
    });
  }


}
