import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Custom validators cho kê khai
 */
export class KeKhaiValidators {
  
  /**
   * Validator cho số sổ BHXH
   */
  static soSoBHXH(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const value = control.value.toString().trim();
      
      // Số sổ BHXH có thể có nhiều format khác nhau
      // Ở đây chỉ check cơ bản: không rỗng và có ít nhất 1 ký tự
      if (value.length === 0) {
        return { soSoBHXH: { value: control.value, message: 'Số sổ BHXH không được để trống' } };
      }

      return null;
    };
  }

  /**
   * Validator cho danh sách lao động
   */
  static danhSachLaoDong(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value || !Array.isArray(control.value)) {
        return { danhSachLaoDong: { message: 'Danh sách lao động không hợp lệ' } };
      }

      if (control.value.length === 0) {
        return { danhSachLaoDong: { message: 'Phải có ít nhất 1 lao động' } };
      }

      return null;
    };
  }

  /**
   * Validator cho tổng tiền đóng
   */
  static tongTienDong(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value && control.value !== 0) {
        return null;
      }

      const value = Number(control.value);
      
      if (isNaN(value)) {
        return { tongTienDong: { value: control.value, message: 'Tổng tiền đóng phải là số' } };
      }

      if (value < 0) {
        return { tongTienDong: { value: control.value, message: 'Tổng tiền đóng phải lớn hơn hoặc bằng 0' } };
      }

      return null;
    };
  }
}
