using FluentValidation;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Common.Models;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.VaiTro.Commands;

public record GanQuyenChoVaiTroCommand : IRequest<Result<Unit>>
{
    public int VaiTroId { get; init; }
    public List<int> DanhSachQuyenId { get; init; } = new();
}

public class GanQuyenChoVaiTroCommandValidator : AbstractValidator<GanQuyenChoVaiTroCommand>
{
    public GanQuyenChoVaiTroCommandValidator()
    {
        RuleFor(x => x.VaiTroId)
            .GreaterThan(0).WithMessage("ID vai trò không hợp lệ");

        RuleFor(x => x.DanhSachQuyenId)
            .NotEmpty().WithMessage("Danh sách quyền không được để trống")
            .Must(list => list.All(id => id > 0)).WithMessage("ID quyền không hợp lệ");
    }
}

public class GanQuyenChoVaiTroCommandHandler : IRequestHandler<GanQuyenChoVaiTroCommand, Result<Unit>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GanQuyenChoVaiTroCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<Result<Unit>> Handle(GanQuyenChoVaiTroCommand request, CancellationToken cancellationToken)
    {
        // Tìm vai trò
        var vaiTro = await _context.VaiTro
            .Include(vt => vt.DanhSachQuyen)
            .FirstOrDefaultAsync(vt => vt.Id == request.VaiTroId, cancellationToken);

        if (vaiTro == null)
        {
            return Result<Unit>.Failure("Vai trò không tồn tại");
        }

        // Kiểm tra các quyền có tồn tại không
        var existingQuyenIds = await _context.Quyen
            .Where(q => request.DanhSachQuyenId.Contains(q.Id) && q.TrangThaiHoatDong)
            .Select(q => q.Id)
            .ToListAsync(cancellationToken);

        var invalidQuyenIds = request.DanhSachQuyenId.Except(existingQuyenIds).ToList();
        if (invalidQuyenIds.Any())
        {
            return Result<Unit>.Failure($"Các quyền không tồn tại: {string.Join(", ", invalidQuyenIds)}");
        }

        // Xóa tất cả quyền hiện tại
        var currentQuyenIds = vaiTro.DanhSachQuyen.Select(vtq => vtq.QuyenId).ToList();
        foreach (var quyenId in currentQuyenIds)
        {
            vaiTro.XoaQuyen(quyenId);
        }

        // Thêm quyền mới
        foreach (var quyenId in existingQuyenIds)
        {
            vaiTro.ThemQuyen(quyenId);
        }

        // Set audit fields
        vaiTro.NguoiCapNhat = _currentUserService.UserId?.ToString();

        await _context.SaveChangesAsync(cancellationToken);

        return Result<Unit>.Success(Unit.Value);
    }
}
