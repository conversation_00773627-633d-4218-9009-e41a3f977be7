// Sidebar Enterprise Styles
.sidebar-desktop {
  background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
  box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
  min-width: 64px; // Ensure minimum width

  &.collapsed {
    .sidebar-header {
      justify-content: center;
      padding: 1rem 0.5rem;
    }

    .sidebar-nav {
      .menu-item {
        justify-content: center;
        padding: 0.75rem 0.5rem;

        .menu-icon {
          margin-right: 0 !important;
        }

        .menu-label {
          display: none !important;
        }

        .expand-arrow {
          display: none !important;
        }
      }

      .submenu {
        display: none !important;
      }
    }

    .sidebar-footer {
      .collapse-btn {
        justify-content: center;
        padding: 0.75rem 0.5rem;

        .ml-2 {
          display: none !important;
        }
      }
    }
  }

  .sidebar-content {
    .sidebar-header {
      transition: all 0.3s ease;
      
      .logo-container {
        transition: all 0.3s ease;
      }
    }

    .sidebar-nav {
      scrollbar-width: thin;
      scrollbar-color: #374151 transparent;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: #374151;
        border-radius: 3px;
        
        &:hover {
          background: #4b5563;
        }
      }

      .menu-item-wrapper {
        .menu-item {
          color: #d1d5db;
          position: relative;
          overflow: hidden;

          &:hover {
            background: rgba(55, 65, 81, 0.5);
            color: #ffffff;
            transform: translateX(2px);
          }

          &.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              bottom: 0;
              width: 4px;
              background: #60a5fa;
            }
          }

          .menu-icon {
            transition: all 0.2s ease;
          }

          .menu-label {
            white-space: nowrap;
          }

          .menu-badge {
            font-size: 0.75rem;
            font-weight: 600;
            transition: all 0.2s ease;
          }

          &.menu-parent {
            .expand-arrow {
              transition: all 0.2s ease;
            }
          }
        }

        .submenu {
          .submenu-item {
            color: #9ca3af;
            position: relative;

            &:hover {
              background: rgba(55, 65, 81, 0.3);
              color: #e5e7eb;
            }

            &.active {
              background: rgba(59, 130, 246, 0.2);
              color: #93c5fd;

              &::before {
                content: '';
                position: absolute;
                left: -12px;
                top: 50%;
                transform: translateY(-50%);
                width: 6px;
                height: 6px;
                background: #3b82f6;
                border-radius: 50%;
              }
            }
          }
        }
      }

      // Animation cho submenu
      .submenu {
        animation: slideDown 0.3s ease-out;
      }
    }

    .sidebar-footer {
      .collapse-btn {
        &:hover {
          background: rgba(55, 65, 81, 0.5);
        }
      }
    }
  }
}

// Mobile Sidebar Styles
.mobile-overlay {
  .mobile-sidebar {
    background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
    box-shadow: 8px 0 24px rgba(0, 0, 0, 0.3);

    .mobile-header {
      .close-btn {
        &:hover {
          background: rgba(55, 65, 81, 0.5);
        }
      }
    }

    .mobile-nav {
      .menu-item-wrapper {
        .menu-item {
          color: #d1d5db;

          &:hover {
            background: rgba(55, 65, 81, 0.5);
            color: #ffffff;
          }

          &.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
          }
        }

        .submenu {
          .submenu-item {
            color: #9ca3af;

            &:hover {
              background: rgba(55, 65, 81, 0.3);
              color: #e5e7eb;
            }

            &.active {
              background: rgba(59, 130, 246, 0.2);
              color: #93c5fd;
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Responsive Styles
@media (max-width: 768px) {
  .sidebar-desktop {
    display: none;
  }

  .mobile-sidebar {
    animation: slideInLeft 0.3s ease-out;
  }
}

@media (min-width: 769px) {
  .mobile-overlay {
    display: none !important;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .sidebar-desktop {
    .sidebar-content {
      background: #000000;
      border-color: #ffffff;

      .sidebar-nav {
        .menu-item-wrapper {
          .menu-item {
            border: 1px solid transparent;

            &:hover {
              border-color: #ffffff;
              background: #333333;
            }

            &.active {
              background: #0066cc;
              border-color: #ffffff;
            }
          }
        }
      }
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .sidebar-desktop,
  .mobile-sidebar,
  .menu-item,
  .submenu,
  .expand-arrow {
    transition: none !important;
    animation: none !important;
  }
}

// Print styles
@media print {
  .sidebar-desktop,
  .mobile-overlay {
    display: none !important;
  }
}
