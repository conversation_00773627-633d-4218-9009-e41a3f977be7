using HuyPhuc.Application.DTOs.ToKhai602;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.ToKhai602.Commands.TaoDraftToKhai;

/// <summary>
/// Command tạo draft tờ khai 602
/// </summary>
public record TaoDraftToKhaiCommand(TaoDraftToKhaiRequest Request) : IRequest<TaoDraftToKhaiResponse>;

/// <summary>
/// Handler cho TaoDraftToKhaiCommand
/// </summary>
public class TaoDraftToKhaiCommandHandler : IRequestHandler<TaoDraftToKhaiCommand, TaoDraftToKhaiResponse>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public TaoDraftToKhaiCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<TaoDraftToKhaiResponse> Handle(TaoDraftToKhaiCommand command, CancellationToken cancellationToken)
    {
        var request = command.Request;
        var userIdString = _currentUserService.UserId;
        
        if (userIdString == null || !int.TryParse(userIdString, out var userId))
        {
            throw new UnauthorizedAccessException("Không thể xác định người dùng hiện tại");
        }

        // Kiểm tra đại lý và đơn vị có tồn tại không
        var daiLy = await _context.DaiLy
            .FirstOrDefaultAsync(x => x.Id == request.DaiLyId, cancellationToken);
        
        if (daiLy == null)
        {
            throw new ArgumentException($"Không tìm thấy đại lý với ID: {request.DaiLyId}");
        }

        var donVi = await _context.DonVi
            .FirstOrDefaultAsync(x => x.Id == request.DonViId && x.DaiLyId == request.DaiLyId, cancellationToken);
        
        if (donVi == null)
        {
            throw new ArgumentException($"Không tìm thấy đơn vị với ID: {request.DonViId} thuộc đại lý {request.DaiLyId}");
        }

        // Tạo mã tờ khai tự động
        var maToKhai = await GenerateMaToKhai(request.DaiLyId, cancellationToken);

        // Tạo entity ToKhai602
        var toKhai = new Domain.Entities.ToKhai602
        {
            MaToKhai = maToKhai,
            DaiLyId = request.DaiLyId,
            DonViId = request.DonViId,
            SoSoBHXH = request.SoSoBHXH,
            GhiChu = request.GhiChu,
            TrangThai = TrangThaiToKhai.DangSoan,
            NgayTao = DateTime.UtcNow,
            NguoiTaoId = userId,
            DanhSachLaoDong = new List<LaoDongToKhai602>()
        };

        _context.ToKhai602.Add(toKhai);
        await _context.SaveChangesAsync(cancellationToken);

        // Tạo response
        return new TaoDraftToKhaiResponse
        {
            DraftId = toKhai.Id,
            MaToKhai = toKhai.MaToKhai,
            NgayTao = toKhai.NgayTao,
            TrangThai = toKhai.TrangThai.ToString(),
            DaiLy = new DaiLyInfo
            {
                Id = daiLy.Id,
                MaDaiLy = daiLy.MaDaiLy,
                TenDaiLy = daiLy.TenDaiLy,
                DiaChi = daiLy.DiaChi,
                SoDienThoai = daiLy.SoDienThoai
            },
            DonVi = new DonViInfo
            {
                Id = donVi.Id,
                MaDonVi = donVi.MaDonVi,
                TenDonVi = donVi.TenDonVi,
                DiaChi = donVi.DiaChi
            }
        };
    }

    /// <summary>
    /// Tạo mã tờ khai tự động theo format: TK602-{MaDaiLy}-{YYYYMMDD}-{STT}
    /// </summary>
    private async Task<string> GenerateMaToKhai(int daiLyId, CancellationToken cancellationToken)
    {
        var daiLy = await _context.DaiLy
            .FirstOrDefaultAsync(x => x.Id == daiLyId, cancellationToken);

        var today = DateTime.UtcNow.Date;
        var dateString = today.ToString("yyyyMMdd");
        
        // Đếm số tờ khai đã tạo trong ngày cho đại lý này
        var count = await _context.ToKhai602
            .Where(x => x.DaiLyId == daiLyId && x.NgayTao.Date == today)
            .CountAsync(cancellationToken);

        var stt = (count + 1).ToString("D3"); // Format 001, 002, 003...
        
        return $"TK602-{daiLy?.MaDaiLy}-{dateString}-{stt}";
    }
}
