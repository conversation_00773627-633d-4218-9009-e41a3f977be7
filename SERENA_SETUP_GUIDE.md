# Serena MCP Server Setup Guide for HuyPhuc Project

## ✅ Setup Complete!

Serena MCP Server đã được cài đặt thành công cho project HuyPhuc.

## 📁 Files Created

1. **claude_desktop_config.json** - <PERSON><PERSON><PERSON> hình cho <PERSON> (local installation)
2. **claude_desktop_config_uvx.json** - <PERSON><PERSON><PERSON> hình cho <PERSON> (uvx method)
3. **start-serena.bat** - Script để khởi động Serena standalone
4. **.serena/project.yml** - Cấu hình project-specific cho Serena

## 🚀 Cách sử dụng với Claude Des<PERSON>op

### Option 1: Sử dụng uvx (Recommended)

1. Mở Claude Desktop
2. Vào **File → Settings → Developer → MCP Servers → Edit Config**
3. Copy nội dung từ file `claude_desktop_config_uvx.json` vào config
4. Save và restart Claude Desktop

### Option 2: Sử dụng local installation

1. Co<PERSON> nội dung từ file `claude_desktop_config.json` v<PERSON><PERSON> config
2. Save và restart Claude Desktop

## 🛠️ Các <PERSON>ls có sẵn trong Serena

### Code Analysis:
- `find_symbol` - Tìm kiếm symbols trong code
- `get_symbols_overview` - Xem overview symbols
- `find_referencing_symbols` - Tìm nơi sử dụng symbol
- `search_for_pattern` - Tìm kiếm pattern

### Code Editing:
- `read_file` - Đọc file
- `create_text_file` - Tạo file mới
- `replace_symbol_body` - Thay thế symbol
- `insert_after_symbol` - Chèn code sau symbol
- `insert_before_symbol` - Chèn code trước symbol

### Project Management:
- `execute_shell_command` - Chạy lệnh shell
- `list_dir` - Liệt kê files/directories
- `write_memory` - Lưu memory
- `read_memory` - Đọc memory

## 📋 Cách sử dụng

### 1. Kích hoạt Project
```
Activate the project C:\Users\<USER>\Documents\huyphuc\huyphuc-ivan
```

### 2. Onboarding
```
Please perform onboarding for this project to understand the codebase structure
```

### 3. Phân tích Code
```
Show me an overview of the backend API controllers
Find all symbols related to "NguoiDung" in the project
```

### 4. Development Tasks
```
Help me implement a new API endpoint for product management
Review and improve the error handling in the authentication service
```

## 🌐 Web Dashboard

Khi Serena chạy, web dashboard sẽ có sẵn tại:
**http://127.0.0.1:24282/dashboard/index.html**

## 🔧 Troubleshooting

### Language Server Issues
Nếu có lỗi với C# language server, có thể bỏ qua vì Serena vẫn hoạt động với các tools khác.

### Restart Serena
```
Please restart the language server for this project
```

### Clear Memory
```
Clear all memories for this project and perform onboarding again
```

## 📝 Configuration Files

### Global Config: `C:\Users\<USER>\.serena\serena_config.yml`
### Project Config: `C:\Users\<USER>\Documents\huyphuc\huyphuc-ivan\.serena\project.yml`

## 🎯 Next Steps

1. Cấu hình Claude Desktop với một trong hai options trên
2. Restart Claude Desktop
3. Test bằng cách gõ: "Activate the project and show me the project structure"
4. Thực hiện onboarding để Serena hiểu project structure

## 💡 Tips

- Luôn activate project trước khi làm việc
- Sử dụng memories để lưu context quan trọng
- Kết hợp multiple tools trong một request
- Chạy tests sau mỗi thay đổi code
