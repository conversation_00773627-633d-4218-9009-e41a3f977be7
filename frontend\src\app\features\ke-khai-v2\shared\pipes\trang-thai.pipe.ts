import { Pipe, PipeTransform } from '@angular/core';
import { TrangThaiKeKhai } from '../../core/models';

/**
 * <PERSON><PERSON> chuyển đổi trạng thái kê khai thành text hiển thị
 */
@Pipe({
  name: 'trangThai',
  standalone: true
})
export class TrangThaiPipe implements PipeTransform {
  transform(value: TrangThaiKeKhai | string): string {
    switch (value) {
      case TrangThaiKeKhai.DangSoan:
      case 'dang_soan':
        return 'Đang soạn';
      case TrangThaiKeKhai.DaGui:
      case 'da_gui':
        return 'Đã gửi';
      case TrangThaiKeKhai.DaDuyet:
      case 'da_duyet':
        return 'Đã duyệt';
      case TrangThaiKeKhai.BiTuChoi:
      case 'bi_tu_choi':
        return 'Bị từ chối';
      default:
        return 'Không xác định';
    }
  }
}
