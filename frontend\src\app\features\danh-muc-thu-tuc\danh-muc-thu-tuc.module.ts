import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';

import { DanhMucThuTucRoutingModule } from './danh-muc-thu-tuc-routing.module';

// Components
import { DanhSachThuTucComponent } from './components/danh-sach-thu-tuc/danh-sach-thu-tuc.component';

// Services
import { DanhMucThuTucService } from './services/danh-muc-thu-tuc.service';

/**
 * Feature module cho quản lý danh mục thủ tục
 * Bao gồm các chức năng CRUD và hiển thị danh sách thủ tục
 */
@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    HttpClientModule,
    DanhMucThuTucRoutingModule,
    
    // Standalone components
    DanhSachThuTucComponent
  ],
  providers: [
    DanhMucThuTucService
  ]
})
export class DanhMucThuTucModule { }
