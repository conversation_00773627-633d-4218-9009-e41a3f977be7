using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class ChiTietDonHangConfiguration : IEntityTypeConfiguration<ChiTietDonHang>
{
    public void Configure(EntityTypeBuilder<ChiTietDonHang> builder)
    {
        builder.ToTable("ChiTietDonHang");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.DonHangId)
            .IsRequired();

        builder.Property(x => x.SanPhamId)
            .IsRequired();

        builder.Property(x => x.SoLuong)
            .IsRequired()
            .HasDefaultValue(1);

        builder.Property(x => x.GiaBan)
            .IsRequired()
            .HasColumnType("decimal(18,2)");

        builder.Property(x => x.ThanhTien)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(0);

        // Configure relationships
        builder.HasOne(x => x.DonHang)
            .WithMany(x => x.DanhSachChiTiet)
            .HasForeignKey(x => x.DonHangId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(x => x.SanPham)
            .WithMany(x => x.DanhSachChiTietDonHang)
            .HasForeignKey(x => x.SanPhamId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(x => x.DonHangId)
            .HasDatabaseName("IX_ChiTietDonHang_DonHangId");

        builder.HasIndex(x => x.SanPhamId)
            .HasDatabaseName("IX_ChiTietDonHang_SanPhamId");

        // Unique constraint: một sản phẩm chỉ xuất hiện một lần trong một đơn hàng
        builder.HasIndex(x => new { x.DonHangId, x.SanPhamId })
            .IsUnique()
            .HasDatabaseName("IX_ChiTietDonHang_DonHangId_SanPhamId");

        // Ignore domain events
        builder.Ignore(x => x.DomainEvents);
    }
}
