import { patchState, signalStore, withMethods, withState, withComputed } from '@ngrx/signals';
import { computed, inject } from '@angular/core';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, switchMap, tap, catchError, of } from 'rxjs';

import { ToKhai602, ToKhai602FormState, ToKhai602Filter } from '../models';
import { TrangThaiKeKhai, LaoDong, PaginationInfo } from '../../../core/models';
import { ToKhai602Service } from '../services';

/**
 * State interface cho tờ khai 602 store
 */
interface ToKhai602State {
  // Danh sách tờ khai
  danhSachToKhai: ToKhai602[];
  toKhaiDangChon: ToKhai602 | null;
  phanTrang: PaginationInfo;
  filter: ToKhai602Filter;
  
  // Form state
  formState: ToKhai602FormState;
  
  // UI state
  dangTai: boolean;
  dangLuu: boolean;
  loi: string | null;
  
  // Modal states
  showCreateModal: boolean;
  showEditModal: boolean;
  showDeleteModal: boolean;
}

/**
 * Initial state
 */
const initialState: ToKhai602State = {
  danhSachToKhai: [],
  toKhaiDangChon: null,
  phanTrang: {
    trang: 1,
    kichThuoc: 10,
    tongSo: 0,
    tongTrang: 0
  },
  filter: {
    trang: 1,
    kichThuoc: 10
  },
  formState: {
    daiLyId: null,
    donViId: null,
    soSoBHXH: '',
    ghiChu: '',
    danhSachLaoDong: [],
    isValid: false,
    isDirty: false,
    errors: {}
  },
  dangTai: false,
  dangLuu: false,
  loi: null,
  showCreateModal: false,
  showEditModal: false,
  showDeleteModal: false
};

/**
 * Store quản lý state của tờ khai 602
 */
export const ToKhai602Store = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withComputed((store) => ({
    // Computed signals
    coToKhai: computed(() => store.danhSachToKhai().length > 0),
    soToKhai: computed(() => store.danhSachToKhai().length),
    toKhaiDangSoan: computed(() => 
      store.danhSachToKhai().filter(tk => tk.trangThai === TrangThaiKeKhai.DangSoan)
    ),
    toKhaiDaGui: computed(() => 
      store.danhSachToKhai().filter(tk => tk.trangThai === TrangThaiKeKhai.DaGui)
    ),
    
    // Form computed
    formValid: computed(() => store.formState().isValid),
    formDirty: computed(() => store.formState().isDirty),
    soLaoDong: computed(() => store.formState().danhSachLaoDong.length),
    tongTienDong: computed(() => 
      store.formState().danhSachLaoDong.reduce((total, ld) => total + ld.tongTien, 0)
    )
  })),
  withMethods((store, toKhaiService = inject(ToKhai602Service)) => ({
    // Basic actions
    setDangTai: (dangTai: boolean) => patchState(store, { dangTai }),
    setDangLuu: (dangLuu: boolean) => patchState(store, { dangLuu }),
    setLoi: (loi: string | null) => patchState(store, { loi }),
    setToKhaiDangChon: (toKhai: ToKhai602 | null) => patchState(store, { toKhaiDangChon: toKhai }),
    
    // Filter actions
    setFilter: (filter: Partial<ToKhai602Filter>) => {
      patchState(store, { 
        filter: { ...store.filter(), ...filter }
      });
    },
    
    // Modal actions
    showCreateModal: () => patchState(store, { showCreateModal: true }),
    hideCreateModal: () => patchState(store, { showCreateModal: false }),
    showEditModal: () => patchState(store, { showEditModal: true }),
    hideEditModal: () => patchState(store, { showEditModal: false }),
    showDeleteModal: () => patchState(store, { showDeleteModal: true }),
    hideDeleteModal: () => patchState(store, { showDeleteModal: false }),
    
    // Form actions
    updateFormState: (updates: Partial<ToKhai602FormState>) => {
      const currentForm = store.formState();
      patchState(store, {
        formState: { 
          ...currentForm, 
          ...updates,
          isDirty: true
        }
      });
    },
    
    resetFormState: () => {
      patchState(store, {
        formState: {
          daiLyId: null,
          donViId: null,
          soSoBHXH: '',
          ghiChu: '',
          danhSachLaoDong: [],
          isValid: false,
          isDirty: false,
          errors: {}
        }
      });
    },
    
    // Lao dong actions
    themLaoDong: (laoDong: LaoDong) => {
      const currentForm = store.formState();
      const danhSachMoi = [...currentForm.danhSachLaoDong, laoDong];
      patchState(store, {
        formState: {
          ...currentForm,
          danhSachLaoDong: danhSachMoi,
          isDirty: true
        }
      });
    },
    
    capNhatLaoDong: (index: number, laoDong: LaoDong) => {
      const currentForm = store.formState();
      const danhSachMoi = [...currentForm.danhSachLaoDong];
      danhSachMoi[index] = laoDong;
      patchState(store, {
        formState: {
          ...currentForm,
          danhSachLaoDong: danhSachMoi,
          isDirty: true
        }
      });
    },
    
    xoaLaoDong: (index: number) => {
      const currentForm = store.formState();
      const danhSachMoi = currentForm.danhSachLaoDong.filter((_, i) => i !== index);
      patchState(store, {
        formState: {
          ...currentForm,
          danhSachLaoDong: danhSachMoi,
          isDirty: true
        }
      });
    },
    
    // Reset state
    reset: () => patchState(store, initialState),
    
    // API methods
    taiDanhSachToKhai: rxMethod<ToKhai602Filter | void>(
      pipe(
        tap((filter) => {
          const newFilter = filter ? { ...store.filter(), ...filter } : store.filter();
          patchState(store, { 
            dangTai: true, 
            loi: null,
            filter: newFilter
          });
        }),
        switchMap((filter) => {
          const currentFilter = filter ? { ...store.filter(), ...filter } : store.filter();
          return toKhaiService.layDanhSachToKhai(currentFilter).pipe(
            tap((response: any) => {
              patchState(store, {
                danhSachToKhai: response.danhSach || [],
                phanTrang: response.phanTrang || store.phanTrang(),
                dangTai: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                loi: error.message || 'Có lỗi khi tải danh sách tờ khai',
                dangTai: false
              });
              return of({ danhSach: [], phanTrang: store.phanTrang() });
            })
          );
        })
      )
    ),
    
    taiChiTietToKhai: rxMethod<number>(
      pipe(
        tap(() => patchState(store, { dangTai: true, loi: null })),
        switchMap((id) =>
          toKhaiService.layChiTietToKhai(id).pipe(
            tap((toKhai) => {
              patchState(store, {
                toKhaiDangChon: toKhai,
                dangTai: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                loi: error.message || 'Có lỗi khi tải chi tiết tờ khai',
                dangTai: false
              });
              return of(null);
            })
          )
        )
      )
    ),
    
    taoToKhai: rxMethod<Omit<ToKhai602, 'id' | 'ngayTao' | 'ngayCapNhat'>>(
      pipe(
        tap(() => patchState(store, { dangLuu: true, loi: null })),
        switchMap((toKhai) =>
          toKhaiService.taoToKhai(toKhai).pipe(
            tap((toKhaiMoi) => {
              const danhSachHienTai = store.danhSachToKhai();
              patchState(store, {
                danhSachToKhai: [toKhaiMoi, ...danhSachHienTai],
                toKhaiDangChon: toKhaiMoi,
                dangLuu: false,
                showCreateModal: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                loi: error.message || 'Có lỗi khi tạo tờ khai',
                dangLuu: false
              });
              return of(null);
            })
          )
        )
      )
    )
  }))
);
