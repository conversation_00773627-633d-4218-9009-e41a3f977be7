using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class Tk1TsConfiguration : IEntityTypeConfiguration<Tk1Ts>
{
    public void Configure(EntityTypeBuilder<Tk1Ts> builder)
    {
        builder.ToTable("tk1_ts");

        builder.<PERSON><PERSON>ey(e => e.Id);

        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(e => e.MaSoBHXH)
            .HasColumnName("ma_so_bhxh")
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(e => e.HoTen)
            .HasColumnName("ho_ten")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(e => e.Ccns)
            .HasColumnName("ccns")
            .HasMaxLength(20)
            .HasDefaultValue("0");

        builder.Property(e => e.NgaySinh)
            .HasColumnName("ngay_sinh")
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(e => e.GioiTinh)
            .HasColumnName("gioi_tinh")
            .IsRequired();

        builder.Property(e => e.QuocTich)
            .HasColumnName("quoc_tich")
            .HasMaxLength(10)
            .HasDefaultValue("VN");

        builder.Property(e => e.DanToc)
            .HasColumnName("dan_toc")
            .HasMaxLength(10)
            .HasDefaultValue("01");

        builder.Property(e => e.Cmnd)
            .HasColumnName("cmnd")
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(e => e.MaTinhKs)
            .HasColumnName("ma_tinh_ks")
            .HasMaxLength(10);

        builder.Property(e => e.MaHuyenKs)
            .HasColumnName("ma_huyen_ks")
            .HasMaxLength(10);

        builder.Property(e => e.MaXaKs)
            .HasColumnName("ma_xa_ks")
            .HasMaxLength(10);

        builder.Property(e => e.DienThoaiLh)
            .HasColumnName("dien_thoai_lh")
            .HasMaxLength(20);

        builder.Property(e => e.MaHoGiaDinh)
            .HasColumnName("ma_ho_gia_dinh")
            .HasMaxLength(50);

        builder.Property(e => e.TypeId)
            .HasColumnName("type_id")
            .HasMaxLength(10)
            .HasDefaultValue("TM");

        builder.Property(e => e.IsThamGiaBb)
            .HasColumnName("is_tham_gia_bb")
            .HasDefaultValue(false);

        builder.Property(e => e.IsTamHoanHd)
            .HasColumnName("is_tam_hoan_hd")
            .HasDefaultValue(false);

        // IAuditableEntity properties - map to existing database columns
        builder.Property(e => e.NgayTao)
            .HasColumnName("created")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(e => e.NgayCapNhat)
            .HasColumnName("last_updated")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        // Note: The tk1_ts table doesn't have created_by/updated_by columns
        // The NguoiTao and NguoiCapNhat are implemented as explicit interface properties
        // and don't need to be configured here since they're not persisted

        // Relationships
        builder.HasMany(e => e.DanhSachChiTietToKhai)
            .WithOne(e => e.Tk1Ts)
            .HasForeignKey(e => e.MaSoBHXH)
            .HasPrincipalKey(e => e.MaSoBHXH)
            .HasConstraintName("fk_chi_tiet_to_khai_602_ma_so_bhxh")
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(e => e.MaSoBHXH)
            .IsUnique()
            .HasDatabaseName("idx_tk1_ts_ma_so_bhxh");

        builder.HasIndex(e => e.Cmnd)
            .HasDatabaseName("idx_tk1_ts_cmnd");

        builder.HasIndex(e => e.HoTen)
            .HasDatabaseName("idx_tk1_ts_ho_ten");

        // Ignore domain events
        builder.Ignore(e => e.DomainEvents);
    }
}
