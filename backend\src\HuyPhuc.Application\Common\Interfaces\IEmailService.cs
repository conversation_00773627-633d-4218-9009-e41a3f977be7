namespace HuyPhuc.Application.Common.Interfaces;

public interface IEmailService
{
    Task GuiEmailAsync(string toEmail, string subject, string body, CancellationToken cancellationToken = default);
    Task GuiEmailNhieuNguoiAsync(IEnumerable<string> toEmails, string subject, string body, CancellationToken cancellationToken = default);
    Task GuiEmailVoiTemplateAsync(string toEmail, string templateName, object model, CancellationToken cancellationToken = default);
}
