using AutoMapper;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Features.DanhMucThuTuc.Common;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Repositories;
using MediatR;

namespace HuyPhuc.Application.Features.DanhMucThuTuc.Queries.GetDanhMucThuTucList;

/// <summary>
/// Query lấy danh sách danh mục thủ tục có phân trang
/// </summary>
public record GetDanhMucThuTucListQuery : IRequest<DanhMucThuTucPaginatedResponse>
{
    public int Page { get; init; } = 1;
    public int PageSize { get; init; } = 10;
    public string? TuKhoa { get; init; }
    public LinhVucThuTuc? LinhVuc { get; init; }
    public TrangThaiThuTuc? TrangThai { get; init; }
    public bool? ChiLayThuTucHoatDong { get; init; }
    public bool? ChiLayThuTucCoHieuLuc { get; init; }
}

/// <summary>
/// Handler cho GetDanhMucThuTucListQuery
/// </summary>
public class GetDanhMucThuTucListQueryHandler : IRequestHandler<GetDanhMucThuTucListQuery, DanhMucThuTucPaginatedResponse>
{
    private readonly IDanhMucThuTucRepository _repository;
    private readonly IMapper _mapper;

    public GetDanhMucThuTucListQueryHandler(
        IDanhMucThuTucRepository repository,
        IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<DanhMucThuTucPaginatedResponse> Handle(
        GetDanhMucThuTucListQuery request,
        CancellationToken cancellationToken)
    {
        // Validate pagination parameters
        var page = Math.Max(1, request.Page);
        var pageSize = Math.Min(Math.Max(1, request.PageSize), 100); // Max 100 items per page

        // Apply filters based on request
        var trangThai = request.TrangThai;
        if (request.ChiLayThuTucHoatDong == true && !trangThai.HasValue)
        {
            trangThai = TrangThaiThuTuc.HoatDong;
        }

        // Get paginated data from repository
        var (items, totalCount) = await _repository.LayDanhSachPhanTrangAsync(
            page,
            pageSize,
            request.TuKhoa,
            request.LinhVuc,
            trangThai,
            cancellationToken);

        // Filter for valid procedures if requested
        if (request.ChiLayThuTucCoHieuLuc == true)
        {
            items = items.Where(x => x.CoHieuLuc);
            totalCount = items.Count(); // Recalculate total count after filtering
        }

        // Map to DTOs
        var dtoItems = _mapper.Map<IEnumerable<DanhMucThuTucSummaryDto>>(items);

        return new DanhMucThuTucPaginatedResponse
        {
            Items = dtoItems,
            TotalCount = totalCount,
            Page = page,
            PageSize = pageSize
        };
    }
}
