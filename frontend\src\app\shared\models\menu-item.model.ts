/**
 * Model cho menu item trong sidebar
 */
export interface MenuItemModel {
  id: string;
  nhan: string;
  duongDan?: string;
  icon: string;
  moTa?: string;
  badge?: {
    text: string;
    type: 'info' | 'warning' | 'success' | 'error';
  };
  children?: MenuItemModel[];
  quyenTruyCap?: string[];
  isActive?: boolean;
  isExpanded?: boolean;
}

/**
 * Cấu hình sidebar
 */
export interface SidebarConfig {
  isCollapsed: boolean;
  isMobileOpen: boolean;
  width: {
    expanded: number;
    collapsed: number;
  };
  breakpoint: number;
}

/**
 * Trạng thái sidebar
 */
export interface SidebarState {
  isCollapsed: boolean;
  isMobileOpen: boolean;
  isMobile: boolean;
  activeMenuId?: string;
  expandedMenuIds: string[];
}
