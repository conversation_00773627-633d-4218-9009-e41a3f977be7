using HuyPhuc.Api.Controllers.Base;
using HuyPhuc.Application.Features.DiaChi.Queries.GetXaByHuyen;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// API Controller cho quản lý xã/phường
/// </summary>
[ApiController]
[Route("api/[controller]")]
// [Authorize] // Temporarily disabled for testing
public class XaController : BaseController
{
    /// <summary>
    /// Lấy danh sách xã theo mã huyện
    /// </summary>
    /// <param name="maHuyen"><PERSON><PERSON> huyện</param>
    /// <returns>Danh sách xã thuộc huyện</returns>
    [HttpGet]
    public async Task<IActionResult> GetByHuyen([FromQuery] string maHuyen)
    {
        var result = await Mediator.Send(new GetXaByHuyenQuery(maHuyen));
        
        if (result.IsSuccess)
        {
            return Ok(new
            {
                data = result.Data,
                success = true,
                message = (string?)null,
                errors = (object?)null,
                status = 200,
                traceId = HttpContext.TraceIdentifier
            });
        }

        return BadRequest(new
        {
            data = (object?)null,
            success = false,
            message = string.Join(", ", result.Errors),
            errors = result.Errors,
            status = 400,
            traceId = HttpContext.TraceIdentifier
        });
    }
}
