using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories.Base;

namespace HuyPhuc.Domain.Repositories;

/// <summary>
/// Repository interface cho DmXa
/// </summary>
public interface IDmXaRepository : IRepository<DmXa>
{
    /// <summary>
    /// Lấy tất cả xã/phường
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Danh sách xã/phường</returns>
    Task<List<DmXa>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy danh sách xã theo mã huyện
    /// </summary>
    /// <param name="maHuyen">Mã huyện</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Danh sách xã thuộc huyện</returns>
    Task<List<DmXa>> GetByMaHuyenAsync(string maHuyen, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy danh sách xã theo mã tỉnh
    /// </summary>
    /// <param name="maTinh">Mã tỉnh</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Danh sách xã thuộc tỉnh</returns>
    Task<List<DmXa>> GetByMaTinhAsync(string maTinh, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy xã theo mã xã
    /// </summary>
    /// <param name="maXa">Mã xã</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Thông tin xã</returns>
    Task<DmXa?> GetByMaXaAsync(string maXa, CancellationToken cancellationToken = default);

    /// <summary>
    /// Kiểm tra mã xã có tồn tại không
    /// </summary>
    /// <param name="maXa">Mã xã</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True nếu tồn tại</returns>
    Task<bool> ExistsByMaXaAsync(string maXa, CancellationToken cancellationToken = default);
}
