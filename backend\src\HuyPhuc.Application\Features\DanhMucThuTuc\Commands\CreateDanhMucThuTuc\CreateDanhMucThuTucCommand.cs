using AutoMapper;
using FluentValidation.Results;
using HuyPhuc.Application.Common.Exceptions;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Features.DanhMucThuTuc.Common;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Repositories;
using MediatR;

namespace HuyPhuc.Application.Features.DanhMucThuTuc.Commands.CreateDanhMucThuTuc;

/// <summary>
/// Command tạo danh mục thủ tục mới
/// </summary>
public record CreateDanhMucThuTucCommand : IRequest<DanhMucThuTucDto>
{
    public string Ma { get; init; } = string.Empty;
    public string Ten { get; init; } = string.Empty;
    public LinhVucThuTuc LinhVuc { get; init; }
    public string TenLinhVuc { get; init; } = string.Empty;
    public DateTime? NgayApDung { get; init; }
    public string? MoTa { get; init; }
    public int? ThoiGianXuLy { get; init; }
    public decimal? PhiThuc<PERSON>ien { get; init; }
    public string? CoQuanThucHien { get; init; }
    public string? CanCuPhapLy { get; init; }
}

/// <summary>
/// Handler cho CreateDanhMucThuTucCommand
/// </summary>
public class CreateDanhMucThuTucCommandHandler : IRequestHandler<CreateDanhMucThuTucCommand, DanhMucThuTucDto>
{
    private readonly IDanhMucThuTucRepository _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;

    public CreateDanhMucThuTucCommandHandler(
        IDanhMucThuTucRepository repository,
        IUnitOfWork unitOfWork,
        IMapper mapper)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
    }

    public async Task<DanhMucThuTucDto> Handle(
        CreateDanhMucThuTucCommand request,
        CancellationToken cancellationToken)
    {
        // Kiểm tra mã thủ tục đã tồn tại chưa
        var maTonTai = await _repository.KiemTraMaTonTaiAsync(request.Ma, cancellationToken: cancellationToken);
        if (maTonTai)
        {
            var failures = new List<ValidationFailure>
            {
                new ValidationFailure(nameof(request.Ma), $"Mã thủ tục '{request.Ma}' đã tồn tại")
            };
            throw new ValidationException(failures);
        }

        // Tạo entity mới
        var entity = Domain.Entities.DanhMucThuTuc.Tao(
            request.Ma,
            request.Ten,
            request.LinhVuc,
            request.TenLinhVuc,
            request.NgayApDung,
            request.MoTa,
            request.ThoiGianXuLy,
            request.PhiThucHien,
            request.CoQuanThucHien,
            request.CanCuPhapLy);

        // Thêm vào repository
        await _repository.ThemAsync(entity, cancellationToken);

        // Lưu thay đổi
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Trả về DTO
        return _mapper.Map<DanhMucThuTucDto>(entity);
    }
}
