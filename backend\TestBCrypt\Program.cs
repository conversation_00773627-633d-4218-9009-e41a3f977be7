﻿using BCrypt.Net;

// Generate new hashes for all test users
var users = new Dictionary<string, string>
{
    { "<EMAIL>", "admin123" },
    { "<EMAIL>", "nhanvien123" },
    { "<EMAIL>", "khachhang123" },
    { "<EMAIL>", "test123" }
};

Console.WriteLine("=== GENERATING NEW BCRYPT HASHES ===");
foreach (var user in users)
{
    var hash = BCrypt.Net.BCrypt.HashPassword(user.Value, 12);
    Console.WriteLine($"Email: {user.Key}");
    Console.WriteLine($"Password: {user.Value}");
    Console.WriteLine($"Hash: {hash}");

    // Verify the hash works
    var isValid = BCrypt.Net.BCrypt.Verify(user.Value, hash);
    Console.WriteLine($"Verification: {isValid}");
    Console.WriteLine("---");
}
