using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HuyPhuc.Application.KeKhai.Commands.TaoKeKhai;
using HuyPhuc.Application.KeKhai.Commands.ThemLaoDong;
using HuyPhuc.Application.Features.KeKhai.Commands.CapNhatLaoDong;
using HuyPhuc.Application.Features.KeKhai.Commands.XoaLaoDong;
using HuyPhuc.Application.Features.KeKhai.Queries.LayDanhSachLaoDong;
using HuyPhuc.Api.Controllers.Base;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// Controller tổng quát cho tất cả loại kê khai
/// Sử dụng bảng danh_sach_ke_khai
/// </summary>
[ApiController]
[Route("api/ke-khai")]
[Authorize]
public class KeKhaiController : BaseController
{
    /// <summary>
    /// Tạo kê khai mới (sử dụng bảng danh_sach_ke_khai)
    /// </summary>
    /// <param name="request">Thông tin kê khai cần tạo</param>
    /// <returns>Thông tin kê khai đã tạo</returns>
    [HttpPost]
    public async Task<ActionResult<object>> TaoKeKhai([FromBody] TaoKeKhaiRequest request)
    {
        try
        {
            var command = new TaoKeKhaiCommand(request);
            var result = await Mediator.Send(command);
            
            return Ok(new
            {
                success = true,
                data = result,
                message = "Tạo kê khai thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(new { success = false, message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Có lỗi xảy ra khi tạo kê khai", error = ex.Message });
        }
    }

    /// <summary>
    /// Thêm lao động vào kê khai
    /// </summary>
    /// <param name="keKhaiId">ID kê khai</param>
    /// <param name="request">Thông tin lao động cần thêm</param>
    /// <returns>Thông tin lao động đã thêm</returns>
    [HttpPost("{keKhaiId}/lao-dong")]
    public async Task<ActionResult<object>> ThemLaoDong(
        int keKhaiId, 
        [FromBody] ThemLaoDongKeKhaiRequest request)
    {
        try
        {
            request.KeKhaiId = keKhaiId;
            var command = new ThemLaoDongKeKhaiCommand(request);
            var result = await Mediator.Send(command);
            
            return Ok(new
            {
                success = result.Success,
                data = result,
                message = result.Message
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Có lỗi xảy ra khi thêm lao động", error = ex.Message });
        }
    }

    /// <summary>
    /// Cập nhật lao động trong kê khai
    /// </summary>
    /// <param name="keKhaiId">ID kê khai</param>
    /// <param name="laoDongId">ID lao động</param>
    /// <param name="request">Thông tin lao động cần cập nhật</param>
    /// <returns>Thông tin lao động đã cập nhật</returns>
    [HttpPut("{keKhaiId}/lao-dong/{laoDongId}")]
    public async Task<ActionResult<object>> CapNhatLaoDong(
        int keKhaiId,
        int laoDongId,
        [FromBody] CapNhatLaoDongKeKhaiRequest request)
    {
        try
        {
            request.Id = laoDongId;
            request.KeKhaiId = keKhaiId;
            var command = new CapNhatLaoDongKeKhaiCommand(request);
            var result = await Mediator.Send(command);

            return Ok(new
            {
                success = result.Success,
                data = result,
                message = result.Message
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Có lỗi xảy ra khi cập nhật lao động", error = ex.Message });
        }
    }

    /// <summary>
    /// Xóa lao động khỏi kê khai
    /// </summary>
    /// <param name="keKhaiId">ID kê khai</param>
    /// <param name="laoDongId">ID lao động</param>
    /// <returns>Kết quả xóa</returns>
    [HttpDelete("{keKhaiId}/lao-dong/{laoDongId}")]
    public async Task<ActionResult<object>> XoaLaoDong(int keKhaiId, int laoDongId)
    {
        try
        {
            var request = new XoaLaoDongKeKhaiRequest
            {
                Id = laoDongId,
                KeKhaiId = keKhaiId
            };
            var command = new XoaLaoDongKeKhaiCommand(request);
            var result = await Mediator.Send(command);

            return Ok(new
            {
                success = result.Success,
                data = result,
                message = result.Message
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Có lỗi xảy ra khi xóa lao động", error = ex.Message });
        }
    }

    /// <summary>
    /// Gửi kê khai (chuyển trạng thái từ Đang soạn sang Đã gửi)
    /// </summary>
    /// <param name="keKhaiId">ID kê khai</param>
    /// <returns>Kết quả gửi kê khai</returns>
    [HttpPost("{keKhaiId}/gui")]
    public async Task<ActionResult<object>> GuiKeKhai(int keKhaiId)
    {
        try
        {
            // TODO: Implement GuiKeKhaiCommand
            return Ok(new
            {
                success = true,
                message = "Gửi kê khai thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Có lỗi xảy ra khi gửi kê khai", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy chi tiết kê khai
    /// </summary>
    /// <param name="keKhaiId">ID kê khai</param>
    /// <returns>Chi tiết kê khai</returns>
    [HttpGet("{keKhaiId}")]
    public async Task<ActionResult<object>> LayChiTietKeKhai(int keKhaiId)
    {
        try
        {
            // TODO: Implement LayChiTietKeKhaiQuery
            return Ok(new
            {
                success = true,
                data = new { },
                message = "Lấy chi tiết kê khai thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Có lỗi xảy ra khi lấy chi tiết kê khai", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy danh sách lao động trong kê khai
    /// </summary>
    /// <param name="keKhaiId">ID kê khai</param>
    /// <returns>Danh sách lao động</returns>
    [HttpGet("{keKhaiId}/lao-dong")]
    public async Task<ActionResult<object>> LayDanhSachLaoDong(int keKhaiId)
    {
        try
        {
            var query = new LayDanhSachLaoDongKeKhaiQuery(keKhaiId);
            var result = await Mediator.Send(query);

            return Ok(new
            {
                success = true,
                data = result,
                message = "Lấy danh sách lao động thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Có lỗi xảy ra khi lấy danh sách lao động", error = ex.Message });
        }
    }

    /// <summary>
    /// Lấy danh sách kê khai với phân trang và filter
    /// </summary>
    /// <returns>Danh sách kê khai</returns>
    [HttpGet]
    public async Task<ActionResult<object>> LayDanhSachKeKhai()
    {
        try
        {
            // TODO: Implement LayDanhSachKeKhaiQuery
            return Ok(new
            {
                success = true,
                data = new
                {
                    items = new List<object>(),
                    totalCount = 0,
                    page = 1,
                    pageSize = 10,
                    totalPages = 0
                },
                message = "Lấy danh sách kê khai thành công"
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Có lỗi xảy ra khi lấy danh sách kê khai", error = ex.Message });
        }
    }

    /// <summary>
    /// Validate kê khai trước khi gửi
    /// </summary>
    /// <param name="keKhaiId">ID kê khai</param>
    /// <returns>Kết quả validate</returns>
    [HttpPost("{keKhaiId}/validate")]
    public async Task<ActionResult<object>> ValidateKeKhai(int keKhaiId)
    {
        try
        {
            // TODO: Implement ValidateKeKhaiCommand
            return Ok(new
            {
                success = true,
                data = new
                {
                    isValid = true,
                    errors = new List<string>(),
                    warnings = new List<string>()
                },
                message = "Validate kê khai thành công"
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Có lỗi xảy ra khi validate kê khai", error = ex.Message });
        }
    }

    /// <summary>
    /// Cập nhật thông tin header của kê khai
    /// </summary>
    /// <param name="keKhaiId">ID kê khai</param>
    /// <param name="request">Thông tin header mới</param>
    /// <returns>Kết quả cập nhật</returns>
    [HttpPut("{keKhaiId}/header")]
    public async Task<ActionResult<object>> CapNhatHeader(int keKhaiId, [FromBody] object request)
    {
        try
        {
            // TODO: Implement CapNhatHeaderKeKhaiCommand
            return Ok(new
            {
                success = true,
                message = "Cập nhật header thành công"
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { success = false, message = "Có lỗi xảy ra khi cập nhật header", error = ex.Message });
        }
    }
}
