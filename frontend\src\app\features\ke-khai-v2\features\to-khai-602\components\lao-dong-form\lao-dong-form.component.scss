// Component-specific styles for lao-dong-form
.lao-dong-form {
  // Custom styles can be added here if needed
  // Currently using Tailwind CSS classes in template
  
  // Example: Custom focus styles for form inputs
  input:focus,
  select:focus,
  textarea:focus {
    @apply ring-2 ring-blue-500 border-transparent;
  }
  
  // Custom validation error styles
  .error-message {
    @apply text-red-500 text-sm mt-1;
  }
  
  // Custom button hover effects
  .btn-primary {
    @apply px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
  }
  
  // Custom form section styles
  .form-section {
    @apply border-t border-gray-200 pt-6 mb-6;
    
    h4 {
      @apply text-base font-medium text-gray-900 mb-4;
    }
  }
  
  // Custom grid layout for responsive design
  .form-grid {
    @apply grid grid-cols-1 gap-4;

    @media (min-width: 768px) {
      @apply grid-cols-3;
    }

    @media (min-width: 1024px) {
      @apply grid-cols-6;
    }
  }

  // Responsive grid utilities for 6-column layout
  .grid-6-cols {
    @apply grid grid-cols-1 gap-4;

    @media (min-width: 768px) {
      @apply grid-cols-3;
    }

    @media (min-width: 1024px) {
      @apply grid-cols-6;
    }
  }

  // Responsive grid utilities for 3-column layout (for sections with fewer fields)
  .grid-3-cols {
    @apply grid grid-cols-1 gap-4;

    @media (min-width: 768px) {
      @apply grid-cols-2;
    }

    @media (min-width: 1024px) {
      @apply grid-cols-3;
    }
  }

  // Ensure form fields don't get too narrow on large screens
  .form-field {
    min-width: 200px;
  }
  
  // Custom label styles
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
    
    .required {
      @apply text-red-500;
    }
  }
  
  // Custom input styles
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
  
  // Custom close button styles
  .close-button {
    @apply text-gray-400 hover:text-gray-600 transition-colors duration-200;
    
    svg {
      @apply w-5 h-5;
    }
  }
  
  // Custom header styles
  .form-header {
    @apply flex justify-between items-center mb-4;
    
    h3 {
      @apply text-lg font-semibold text-gray-900;
    }
  }
  
  // Custom actions section
  .form-actions {
    @apply flex justify-end space-x-3;
  }
}
