{"name": "frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/router": "^19.0.0", "@ngrx/signals": "^19.2.1", "postcss": "^8.5.6", "rxjs": "~7.8.0", "tailwindcss": "^3.4.17", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^19.0.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@stagewise/toolbar": "^0.6.2", "@types/jasmine": "~5.1.0", "@types/tailwindcss": "^3.0.11", "autoprefixer": "^10.4.21", "jasmine-core": "~5.8.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.6.0"}}