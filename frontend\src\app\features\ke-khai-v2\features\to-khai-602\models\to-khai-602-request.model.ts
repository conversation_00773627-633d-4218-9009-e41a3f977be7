import { ToKhai602 } from './to-khai-602.model';

/**
 * Request models cho tờ khai 602
 */

/**
 * Request tạo tờ khai 602 mới
 */
export interface TaoToKhai602Request extends Omit<ToKhai602, 'id' | 'ngayTao' | 'ngayCapNhat'> {
  // C<PERSON> thể thêm các trường specific cho request
}

/**
 * Request cập nhật tờ khai 602
 */
export interface CapNhatToKhai602Request extends Partial<ToKhai602> {
  id: number;
}

/**
 * Request gửi tờ khai 602
 */
export interface GuiToKhai602Request {
  id: number;
  ghiChu?: string;
}

/**
 * Request duyệt tờ khai 602
 */
export interface DuyetToKhai602Request {
  id: number;
  ghiChu?: string;
}

/**
 * Request từ chối tờ khai 602
 */
export interface TuChoiToKhai602Request {
  id: number;
  lyDo: string;
}
