using System.Text;
using System.Text.Json;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace HuyPhuc.Infrastructure.Services;

public class SupabaseApiClient : ISupabaseApiClient
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<SupabaseApiClient> _logger;
    private readonly string _apiUrl;
    private readonly string _apiKey;

    public SupabaseApiClient(HttpClient httpClient, IConfiguration configuration, ILogger<SupabaseApiClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _apiUrl = configuration["Supabase:ApiUrl"] ?? throw new ArgumentNullException("Supabase:ApiUrl");
        _apiKey = configuration["Supabase:ApiKey"] ?? throw new ArgumentNullException("Supabase:ApiKey");

        // Configure HttpClient
        _httpClient.BaseAddress = new Uri(_apiUrl);
        _httpClient.DefaultRequestHeaders.Add("apikey", _apiKey);
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
        _httpClient.DefaultRequestHeaders.Add("Prefer", "return=representation");
    }

    public async Task<IEnumerable<NguoiDung>> LayDanhSachNguoiDungAsync(int offset = 0, int limit = 10)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/rest/v1/dm_nguoi_dung?offset={offset}&limit={limit}");
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var data = JsonSerializer.Deserialize<List<SupabaseNguoiDungDto>>(json, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return data?.Select(MapToEntity) ?? Enumerable.Empty<NguoiDung>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi lấy danh sách người dùng từ Supabase");
            throw;
        }
    }

    public async Task<NguoiDung?> LayNguoiDungTheoIdAsync(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/rest/v1/dm_nguoi_dung?id=eq.{id}&limit=1");
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var data = JsonSerializer.Deserialize<List<SupabaseNguoiDungDto>>(json, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return data?.FirstOrDefault() != null ? MapToEntity(data.First()) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi lấy người dùng theo ID {Id} từ Supabase", id);
            throw;
        }
    }

    public async Task<NguoiDung> TaoNguoiDungAsync(NguoiDung nguoiDung)
    {
        try
        {
            var dto = MapToDto(nguoiDung);
            var json = JsonSerializer.Serialize(dto, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync("/rest/v1/dm_nguoi_dung", content);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync();
            var createdData = JsonSerializer.Deserialize<List<SupabaseNguoiDungDto>>(responseJson, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return MapToEntity(createdData!.First());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi tạo người dùng mới trong Supabase");
            throw;
        }
    }

    public async Task<NguoiDung> CapNhatNguoiDungAsync(NguoiDung nguoiDung)
    {
        try
        {
            var dto = MapToDto(nguoiDung);
            var json = JsonSerializer.Serialize(dto, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PatchAsync($"/rest/v1/dm_nguoi_dung?id=eq.{nguoiDung.Id}", content);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync();
            var updatedData = JsonSerializer.Deserialize<List<SupabaseNguoiDungDto>>(responseJson, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return MapToEntity(updatedData!.First());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi cập nhật người dùng ID {Id} trong Supabase", nguoiDung.Id);
            throw;
        }
    }

    public async Task<bool> XoaNguoiDungAsync(int id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"/rest/v1/dm_nguoi_dung?id=eq.{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi xóa người dùng ID {Id} trong Supabase", id);
            throw;
        }
    }

    private static NguoiDung MapToEntity(SupabaseNguoiDungDto dto)
    {
        // Validate email format, use default if invalid
        var email = IsValidEmail(dto.Email) ? dto.Email : $"user{dto.Id}@example.com";

        // Create using static factory method with valid phone number
        var validSoDienThoai = IsValidPhoneNumber(dto.SoDienThoai) ? dto.SoDienThoai : null;
        var nguoiDung = NguoiDung.Tao(dto.HoTen ?? "Unknown", email, dto.MatKhau ?? "temp", validSoDienThoai, dto.Username);

        // Set additional properties using reflection
        var type = typeof(NguoiDung);

        type.GetProperty("Id")?.SetValue(nguoiDung, dto.Id);
        type.GetProperty("DiaChi")?.SetValue(nguoiDung, dto.DiaChi);
        type.GetProperty("NgaySinh")?.SetValue(nguoiDung, dto.NgaySinh);
        type.GetProperty("GioiTinh")?.SetValue(nguoiDung, dto.GioiTinh);
        type.GetProperty("AvatarUrl")?.SetValue(nguoiDung, dto.AvatarUrl);
        type.GetProperty("LastLogin")?.SetValue(nguoiDung, dto.LastLogin);
        type.GetProperty("MaNhanVien")?.SetValue(nguoiDung, dto.MaNhanVien);
        type.GetProperty("NgayTao")?.SetValue(nguoiDung, dto.CreatedAt);
        type.GetProperty("NgayCapNhat")?.SetValue(nguoiDung, dto.UpdatedAt);
        type.GetProperty("NguoiTao")?.SetValue(nguoiDung, dto.CreatedBy);
        type.GetProperty("NguoiCapNhat")?.SetValue(nguoiDung, dto.UpdatedBy);

        // Set TrangThai
        var trangThai = TrangThaiNguoiDungExtensions.FromDbString(dto.TrangThai);
        type.GetProperty("TrangThai")?.SetValue(nguoiDung, trangThai);

        return nguoiDung;
    }

    private static bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private static bool IsValidPhoneNumber(string? phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        try
        {
            // Use the same regex pattern as SoDienThoai value object
            var phoneRegex = new System.Text.RegularExpressions.Regex(
                @"^(\+84|84|0)(3[2-9]|5[6|8|9]|7[0|6-9]|8[1-6|8|9]|9[0-4|6-9])[0-9]{7}$");

            var cleaned = phoneNumber.Trim().Replace(" ", "").Replace("-", "");
            return phoneRegex.IsMatch(cleaned);
        }
        catch
        {
            return false;
        }
    }

    private static SupabaseNguoiDungDto MapToDto(NguoiDung entity)
    {
        return new SupabaseNguoiDungDto
        {
            Id = entity.Id,
            Email = entity.Email.Value,
            MatKhau = entity.MatKhau,
            HoTen = entity.HoTen,
            SoDienThoai = entity.SoDienThoai?.Value,
            DiaChi = entity.DiaChi,
            NgaySinh = entity.NgaySinh,
            GioiTinh = entity.GioiTinh,
            AvatarUrl = entity.AvatarUrl,
            LastLogin = entity.LastLogin,
            TrangThai = entity.TrangThai.ToDbString(),
            CreatedAt = entity.NgayTao,
            UpdatedAt = entity.NgayCapNhat,
            CreatedBy = entity.NguoiTao,
            UpdatedBy = entity.NguoiCapNhat,
            MaNhanVien = entity.MaNhanVien,
            Username = entity.Username
        };
    }
}

// DTO for Supabase API
public class SupabaseNguoiDungDto
{
    public int Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string MatKhau { get; set; } = string.Empty;
    public string HoTen { get; set; } = string.Empty;
    public string? SoDienThoai { get; set; }
    public string? DiaChi { get; set; }
    public DateTime? NgaySinh { get; set; }
    public string? GioiTinh { get; set; }
    public string? AvatarUrl { get; set; }
    public DateTime? LastLogin { get; set; }
    public string TrangThai { get; set; } = "active";
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
    public string? MaNhanVien { get; set; }
    public string? Username { get; set; }
}
