using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories.Base;

namespace HuyPhuc.Domain.Repositories;

/// <summary>
/// Repository interface cho RefreshToken
/// </summary>
public interface IRefreshTokenRepository : IRepository<RefreshToken>
{
    /// <summary>
    /// Tìm refresh token theo token string
    /// </summary>
    /// <param name="token">Token string</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>RefreshToken nếu tìm thấy</returns>
    Task<RefreshToken?> LayTheoTokenAsync(string token, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy tất cả refresh tokens của một người dùng
    /// </summary>
    /// <param name="nguoiDungId">ID người dùng</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Danh sách refresh tokens</returns>
    Task<IEnumerable<RefreshToken>> LayTheoNguoiDungIdAsync(int nguoiDungId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Thu hồi tất cả refresh tokens của một người dùng
    /// </summary>
    /// <param name="nguoiDungId">ID người dùng</param>
    /// <param name="lyDo">Lý do thu hồi</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task ThuHoiTatCaTokenCuaNguoiDungAsync(int nguoiDungId, string lyDo, CancellationToken cancellationToken = default);

    /// <summary>
    /// Xóa các refresh tokens đã hết hạn
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task XoaTokenHetHanAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Đếm số lượng refresh tokens hợp lệ của một người dùng
    /// </summary>
    /// <param name="nguoiDungId">ID người dùng</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Số lượng tokens hợp lệ</returns>
    Task<int> DemTokenHopLeCuaNguoiDungAsync(int nguoiDungId, CancellationToken cancellationToken = default);
}
