namespace HuyPhuc.Application.DTOs.DanToc;

/// <summary>
/// DTO cho thông tin dân tộc
/// </summary>
public class DanTocDto
{
    /// <summary>
    /// ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Mã dân tộc
    /// </summary>
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Tên dân tộc
    /// </summary>
    public string Ten { get; set; } = string.Empty;

    /// <summary>
    /// Mã và tên dân tộc (mã - tên)
    /// </summary>
    public string MaVaTen { get; set; } = string.Empty;

    /// <summary>
    /// Căn cứ pháp lý
    /// </summary>
    public string? CanCu { get; set; }

    /// <summary>
    /// Số thứ tự
    /// </summary>
    public decimal? Rownum { get; set; }

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Ng<PERSON>y cập nhật
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}
