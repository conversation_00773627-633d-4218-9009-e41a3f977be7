<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Format Tiền & Loại NSNN</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .demo-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #555;
        }
        select, input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input[readonly] {
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }
        select:focus, input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
            color: #0066cc;
        }
        .result-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Demo Format Tiền & Loại NSNN Mặc Định</h1>
    
    <div class="demo-section">
        <h2 class="demo-title">🎯 Các thay đổi đã thực hiện</h2>
        <ul>
            <li><strong>Các field tiền:</strong> Thêm dấu phân cách (1,000,000) và không cho chỉnh sửa (readonly)</li>
            <li><strong>Loại NSNN:</strong> Mặc định chọn "Khác (20%)"</li>
            <li><strong>Tỷ lệ NSNN hỗ trợ:</strong> Tự động cập nhật theo loại đã chọn</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">💰 Demo Format Tiền (Readonly)</h2>
        
        <div class="form-grid">
            <div class="form-group">
                <label>Tiền lãi </label>
                <input type="text" value="3,188" readonly>
            </div>
            
            <div class="form-group">
                <label>Tiền thừa </label>
                <input type="text" value="0" readonly>
            </div>
            
            <div class="form-group">
                <label>Tiền tự đóng </label>
                <input type="text" value="993,188" readonly>
            </div>
            
            <div class="form-group">
                <label>Tổng tiền </label>
                <input type="text" value="894,188" readonly>
            </div>
            
            <div class="form-group">
                <label>NSNN hỗ trợ </label>
                <input type="text" value="99,000" readonly>
            </div>
        </div>
        
        <div class="highlight">
            <strong>📝 Lưu ý:</strong> Các field tiền này được format tự động từ dữ liệu API và không cho phép chỉnh sửa.
        </div>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">🏷️ Demo Loại NSNN Mặc Định</h2>
        
        <div class="form-grid">
            <div class="form-group">
                <label>Loại NSNN</label>
                <select id="loaiNsnn" onchange="updateTyLe()">
                    <option value="ho-ngheo-xa-dao">Hộ nghèo, người đang sinh sống tại xã đảo, đặc khu (50%)</option>
                    <option value="ho-can-ngheo">Hộ cận nghèo (40%)</option>
                    <option value="dan-toc-thieu-so">Dân tộc thiểu số (30%)</option>
                    <option value="khac" selected>Khác (20%)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>Tỷ lệ NSNN hỗ trợ (%)</label>
                <input type="text" id="tyLeHoTro" value="20" readonly>
                <div class="info-box">
                    Tự động cập nhật theo loại NSNN
                </div>
            </div>
        </div>
        
        <div class="highlight">
            <strong>🎯 Mặc định:</strong> Khi form load, loại NSNN sẽ tự động chọn "Khác (20%)" và tỷ lệ hỗ trợ = 20%.
        </div>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">🔧 Demo Tương Tác</h2>
        
        <div class="form-group">
            <label>Mức thu nhập  - Có thể chỉnh sửa</label>
            <input type="number" id="mucThuNhap" value="1500000" min="1500000" step="50000" onchange="updateHeSo()">
            <div class="info-box">
                Mức tối thiểu: 1,500,000 VNĐ
            </div>
        </div>
        
        <div class="form-group">
            <label>Hệ số đóng</label>
            <input type="text" id="heSoDong" value="0" readonly>
            <div class="info-box">
                Tự động tính: Mỗi 50,000 VNĐ tăng = +1 hệ số
            </div>
        </div>
        
        <div class="result-box">
            <h3>📊 Kết quả tính toán:</h3>
            <div id="ketQua">
                <p><strong>Mức thu nhập:</strong> <span id="displayMucThuNhap">1,500,000 VNĐ</span></p>
                <p><strong>Hệ số đóng:</strong> <span id="displayHeSo">0</span></p>
                <p><strong>Loại NSNN:</strong> <span id="displayLoaiNsnn">Khác (20%)</span></p>
                <p><strong>Tỷ lệ hỗ trợ:</strong> <span id="displayTyLe">20%</span></p>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">💻 Code Implementation</h2>
        <h3>TypeScript Methods:</h3>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;"><code>/**
 * Format số tiền với dấu phân cách
 */
formatCurrency(value: number): string {
  if (!value && value !== 0) return '';
  return value.toLocaleString('vi-VN');
}

/**
 * Lấy giá trị tiền đã format để hiển thị
 */
getFormattedValue(fieldName: string): string {
  const value = this.parentForm.get(fieldName)?.value;
  return this.formatCurrency(value);
}

/**
 * Set loại NSNN mặc định là "khác"
 */
private setDefaultLoaiNsnn() {
  const loaiNsnnControl = this.parentForm.get('loaiNsnn');
  if (loaiNsnnControl && !loaiNsnnControl.value) {
    loaiNsnnControl.setValue('khac');
    console.log('🎯 Set loại NSNN mặc định: khac (20%)');
  }
}</code></pre>
    </div>

    <script>
        // Mapping tỷ lệ NSNN
        const tyLeMapping = {
            'ho-ngheo-xa-dao': 50,
            'ho-can-ngheo': 40,
            'dan-toc-thieu-so': 30,
            'khac': 20
        };

        function updateTyLe() {
            const loaiNsnn = document.getElementById('loaiNsnn').value;
            const tyLeHoTro = document.getElementById('tyLeHoTro');
            const displayLoaiNsnn = document.getElementById('displayLoaiNsnn');
            const displayTyLe = document.getElementById('displayTyLe');
            
            const tyLe = tyLeMapping[loaiNsnn] || 0;
            tyLeHoTro.value = tyLe;
            
            const selectedText = document.getElementById('loaiNsnn').options[document.getElementById('loaiNsnn').selectedIndex].text;
            displayLoaiNsnn.textContent = selectedText;
            displayTyLe.textContent = tyLe + '%';
        }

        function updateHeSo() {
            const mucThuNhap = parseInt(document.getElementById('mucThuNhap').value) || 0;
            const heSoDong = document.getElementById('heSoDong');
            const displayMucThuNhap = document.getElementById('displayMucThuNhap');
            const displayHeSo = document.getElementById('displayHeSo');
            
            let heSo = 0;
            if (mucThuNhap >= 1500000) {
                heSo = Math.floor((mucThuNhap - 1500000) / 50000);
            }
            
            heSoDong.value = heSo;
            displayMucThuNhap.textContent = mucThuNhap.toLocaleString('vi-VN') + ' VNĐ';
            displayHeSo.textContent = heSo;
        }

        // Initialize
        updateTyLe();
        updateHeSo();
    </script>
</body>
</html>
