import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ConfirmationDialogData {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'info' | 'warning' | 'danger' | 'success';
  icon?: string;
}

/**
 * Service quản lý dialog xác nhận
 * Sử dụng custom modal component thay vì browser confirm
 */
@Injectable({
  providedIn: 'root'
})
export class ConfirmationDialogService {
  private _modalData$ = new BehaviorSubject<ConfirmationDialogData | null>(null);
  private _isOpen$ = new BehaviorSubject<boolean>(false);
  private _resolvePromise: ((value: boolean) => void) | null = null;

  /**
   * Observable cho modal data
   */
  get modalData$(): Observable<ConfirmationDialogData | null> {
    return this._modalData$.asObservable();
  }

  /**
   * Observable cho modal state
   */
  get isOpen$(): Observable<boolean> {
    return this._isOpen$.asObservable();
  }

  /**
   * <PERSON><PERSON>n thị dialog xác nhận
   */
  async confirm(data: ConfirmationDialogData): Promise<boolean>;
  async confirm(title: string, message: string, confirmText?: string, cancelText?: string): Promise<boolean>;
  async confirm(
    dataOrTitle: ConfirmationDialogData | string,
    message?: string,
    confirmText?: string,
    cancelText?: string
  ): Promise<boolean> {
    // Handle overloaded signatures
    let data: ConfirmationDialogData;

    if (typeof dataOrTitle === 'string') {
      data = {
        title: dataOrTitle,
        message: message || '',
        confirmText,
        cancelText
      };
    } else {
      data = dataOrTitle;
    }

    // Set default values
    data.confirmText = data.confirmText || 'Xác nhận';
    data.cancelText = data.cancelText || 'Hủy bỏ';
    data.type = data.type || 'info';

    return new Promise<boolean>((resolve) => {
      this._resolvePromise = resolve;
      this._modalData$.next(data);
      this._isOpen$.next(true);
    });
  }

  /**
   * Xử lý khi người dùng xác nhận hoặc hủy bỏ
   */
  handleModalResult(result: boolean): void {
    this._isOpen$.next(false);
    this._modalData$.next(null);

    if (this._resolvePromise) {
      this._resolvePromise(result);
      this._resolvePromise = null;
    }
  }

  /**
   * Đóng modal
   */
  closeModal(): void {
    this.handleModalResult(false);
  }

  /**
   * Hiển thị dialog xác nhận tạo kê khai
   */
  async confirmCreateDraft(daiLyName: string, donViName: string, soSoBHXH: string): Promise<boolean> {
    return this.confirm({
      title: 'Xác nhận tạo kê khai',
      message: `Bạn có chắc chắn muốn tạo kê khai với thông tin sau?\n\n` +
               `• Đại lý: ${daiLyName}\n` +
               `• Đơn vị: ${donViName}\n` +
               `• Số sổ BHXH: ${soSoBHXH}`,
      confirmText: 'Tạo kê khai',
      cancelText: 'Hủy bỏ',
      type: 'info'
    });
  }

  /**
   * Hiển thị dialog xác nhận xóa
   */
  async confirmDelete(itemName: string): Promise<boolean> {
    return this.confirm({
      title: 'Xác nhận xóa',
      message: `Bạn có chắc chắn muốn xóa "${itemName}"?\n\nHành động này không thể hoàn tác.`,
      confirmText: 'Xóa',
      cancelText: 'Hủy bỏ',
      type: 'danger'
    });
  }

  /**
   * Hiển thị dialog cảnh báo
   */
  async showWarning(title: string, message: string): Promise<boolean> {
    return this.confirm({
      title,
      message,
      confirmText: 'Đồng ý',
      cancelText: 'Hủy bỏ',
      type: 'warning'
    });
  }
}
