using HuyPhuc.Application.Common.Models;
using MediatR;
using Microsoft.Extensions.Logging;

namespace HuyPhuc.Application.Features.XacThuc.Commands.DangXuat;

/// <summary>
/// Handler xử lý đăng xuất người dùng
/// </summary>
public class DangXuatCommandHandler : IRequestHandler<DangXuatCommand, Result<bool>>
{
    private readonly ILogger<DangXuatCommandHandler> _logger;

    public DangXuatCommandHandler(ILogger<DangXuatCommandHandler> logger)
    {
        _logger = logger;
    }

    public async Task<Result<bool>> Handle(DangXuatCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Bắt đầu xử lý đăng xuất");

            // TODO: Implement refresh token blacklist/revocation
            // - Xóa refresh token khỏi database/cache
            // - Thêm access token vào blacklist nếu cần
            
            // Hiện tại chỉ return success vì JWT stateless
            // Client sẽ tự xóa token khỏi localStorage
            
            _logger.LogInformation("Đăng xuất thành công");
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi xử lý đăng xuất");
            return Result<bool>.Failure("Có lỗi xảy ra khi đăng xuất");
        }
    }
}
