---

# Quy tắc đặt tên trong dự án

## Nguyên tắc chung khi kết hợp tiếng Anh và tiếng Việt

1. **Tính nhất quán**: Chọ<PERSON> một cách tiếp cận và áp dụng nhất quán trong toàn bộ dự án
2. **Ưu tiên ngôn ngữ nghiệp vụ**: Sử dụng tiếng Việt cho các thuật ngữ nghiệp vụ đặc thù
3. **Ưu tiên tiếng Anh**: Sử dụng tiếng Anh cho các thuật ngữ kỹ thuật phổ biến

### Cách kết hợp

1. **Nghiệp vụ + Kỹ thuật**: Nghiệp vụ bằng tiếng Việt, kỹ thuật bằng tiếng Anh
   - `ToKhai602Service` (không phải `Form602Service`)
   - `DanhSachLaoDongComponent` (không phải `WorkerListComponent`)

2. **<PERSON>ừ ghép**: <PERSON><PERSON> kết hợp hai từ từ hai ngôn ngữ, sử dụng camelCase hoặc PascalCase
   - `laoDongService` (không phải `lao_dong_service`)
   - `ToKhaiValidator` (không phải `to_khai_validator`)

## File và thư mục

1. **Thư mục**: Sử dụng kebab-case (chữ thường, nối bằng dấu gạch ngang)
   - `features/ke-khai/to-khai-602/`
   - `shared/components/`

2. **Files Angular**:
   - Components: `[name].component.ts`, `[name].component.html`, `[name].component.scss`
   - Services: `[name].service.ts`
   - Models: `[name].model.ts`
   - Guards: `[name].guard.ts`
   - Pipes: `[name].pipe.ts`
   - Modules: `[name].module.ts`
   - Directives: `[name].directive.ts`
   - Interceptors: `[name].interceptor.ts`

3. **Tên file phải mô tả chức năng**: 
   - `to-khai-602.service.ts` (không phải `service.ts`)
   - `danh-sach-lao-dong.component.ts` (không phải `list.component.ts`)
   - `nguoi-dung.model.ts` (không phải `user.model.ts`)

## Class và Interface

1. **Classes**: Sử dụng PascalCase (chữ cái đầu viết hoa)
   - Components: `export class QuanLyToKhai602Component`
   - Services: `export class ToKhai602Service`
   - Guards: `export class AuthGuard`
   - Interceptors: `export class ErrorInterceptor`

2. **Interfaces và Types**: Sử dụng PascalCase, thêm hậu tố phù hợp
   - Models: `export interface ToKhai602Dto`
   - Requests: `export interface TaoKeKhaiRequest` 
   - Responses: `export interface DanhSachKeKhaiResponse`
   - Config: `export interface AppConfig`

3. **Enums**: Sử dụng PascalCase
   - `export enum TrangThaiKeKhai`
   - `export enum LoaiDaiLy` (không phải `export enum AgentType`)

## Angular Components và Services

1. **Component Selectors**: Sử dụng tiền tố `app-` và kebab-case
   ```typescript
   @Component({
     selector: 'app-khai-bao-to-khai',
     templateUrl: './khai-bao-to-khai.component.html'
   })
   ```

2. **Input/Output Properties**: Sử dụng camelCase
   ```typescript
   @Input() danhSachLaoDong: LaoDongDto[];
   @Output() luuThayDoi = new EventEmitter<any>();
   ```

3. **Methods**: Sử dụng camelCase, động từ mô tả hành động
   ```typescript
   // Tiếng Việt cho các hàm nghiệp vụ
   getLaoDongById(id: number) { ... }
   themLaoDong(laoDong: LaoDongDto) { ... }
   
   // Tiếng Anh cho các hàm kỹ thuật
   formatDate(date: Date) { ... }
   validateInput(input: string) { ... }
   ```

## Biến và hằng số

1. **Biến**: Sử dụng camelCase, kết hợp tiếng Việt cho nghiệp vụ
   ```typescript
   // Biến nghiệp vụ - tiếng Việt
   const danhSachNguoiLaoDong: LaoDongDto[] = [];
   let trangThai: TrangThaiKeKhai;
   
   // Biến kỹ thuật - tiếng Anh
   const apiConfig = { timeout: 3000 };
   let isLoading = false;
   ```

2. **Hằng số**: Sử dụng UPPER_SNAKE_CASE
   ```typescript
   // Hằng số nghiệp vụ - tiếng Việt
   export const SO_NGAY_TOI_DA = 30;
   
   // Hằng số kỹ thuật - tiếng Anh
   export const API_TIMEOUT = 30000;
   ```

## Cấu trúc thư mục phân cấp

Sử dụng cấu trúc phân cấp theo module cha-con:

```
features/
├── ke-khai/                # Module cha
│   ├── shared/             # Shared resources cho tất cả tờ khai
│   ├── to-khai-602/        # Module con - tờ khai 602
│   ├── to-khai-603/        # Module con - tờ khai 603
│   └── ke-khai.module.ts   # Module cha import các module con
```

Đối với module con, sử dụng cấu trúc sau:

```
to-khai-602/
├── components/             # Các component
├── services/               # Các service
├── models/                 # Các model, interface
└── to-khai-602.module.ts   # Module
```
