/* Styles cho component danh sách lao động */

.danh-sach-lao-dong-container {
  max-width: 100%;
  margin: 0 auto;
}

.header-section {
  h2 {
    color: #111827;
    margin-bottom: 0.25rem;
  }

  p {
    color: #6b7280;
  }
}

.filter-section {
  .form-group {
    label {
      color: #374151;
      font-weight: 500;
    }

    input, select {
      transition: all 0.2s ease-in-out;

      &:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }
}

.loading-section {
  .animate-spin {
    animation: spin 1s linear infinite;
  }
}

.table-section {
  .table-header {
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }

  .empty-state {
    svg {
      color: #9ca3af;
    }

    h3 {
      color: #111827;
    }

    p {
      color: #6b7280;
    }
  }

  .table-content {
    table {
      thead {
        background-color: #f9fafb;

        th {
          color: #6b7280;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          font-size: 0.75rem;
        }
      }

      tbody {
        tr {
          transition: background-color 0.2s ease-in-out;

          &:hover {
            background-color: #f9fafb;
          }

          td {
            color: #111827;

            .text-gray-500 {
              color: #6b7280;
            }

            button {
              transition: color 0.2s ease-in-out;

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }

              &.text-blue-600:hover {
                color: #1d4ed8;
              }

              &.text-green-600:hover {
                color: #059669;
              }

              &.text-red-600:hover {
                color: #dc2626;
              }
            }
          }
        }
      }
    }
  }

  .pagination {
    background-color: white;
    border-top: 1px solid #e5e7eb;

    button {
      transition: all 0.2s ease-in-out;

      &:hover:not(:disabled) {
        background-color: #f9fafb;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &.bg-blue-600 {
        background-color: #2563eb;
        color: white;

        &:hover {
          background-color: #1d4ed8;
        }
      }
    }
  }
}

.btn-primary {
  background-color: #2563eb;
  color: white;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: #1d4ed8;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.btn-secondary {
  border: 1px solid #d1d5db;
  color: #374151;
  background-color: white;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: #f9fafb;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .danh-sach-lao-dong-container {
    padding: 1rem;
  }

  .header-section {
    .flex {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  }

  .filter-section {
    .grid {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }

  .table-content {
    table {
      font-size: 0.875rem;

      th, td {
        padding: 0.75rem 0.5rem;
      }
    }
  }

  .pagination {
    .flex {
      flex-direction: column;
      gap: 0.5rem;
      align-items: center;
    }
  }
}

@media (max-width: 640px) {
  .table-content {
    table {
      font-size: 0.75rem;

      th, td {
        padding: 0.5rem 0.25rem;
      }

      .flex {
        flex-direction: column;
        gap: 0.25rem;
      }
    }
  }
}

/* Animation keyframes */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
