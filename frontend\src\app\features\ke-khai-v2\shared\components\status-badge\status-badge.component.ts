import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TrangThaiKeKhai } from '../../../core/models';

/**
 * Component hiển thị badge trạng thái kê khai
 */
@Component({
  selector: 'app-status-badge',
  standalone: true,
  imports: [CommonModule],
  template: `
    <span [class]="badgeClasses">
      <span [class]="dotClasses"></span>
      {{ statusText }}
    </span>
  `,
  styles: [`
    .status-badge {
      display: inline-flex;
      align-items: center;
      padding: 0.125rem 0.625rem;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 500;
    }

    .status-dot {
      width: 0.5rem;
      height: 0.5rem;
      border-radius: 9999px;
      margin-right: 0.375rem;
    }

    .status-dang-soan {
      background-color: #f3f4f6;
      color: #1f2937;
    }
    .dot-dang-soan {
      background-color: #9ca3af;
    }

    .status-da-gui {
      background-color: #dbeafe;
      color: #1e40af;
    }
    .dot-da-gui {
      background-color: #60a5fa;
    }

    .status-da-duyet {
      background-color: #dcfce7;
      color: #166534;
    }
    .dot-da-duyet {
      background-color: #4ade80;
    }

    .status-bi-tu-choi {
      background-color: #fee2e2;
      color: #991b1b;
    }
    .dot-bi-tu-choi {
      background-color: #f87171;
    }
  `]
})
export class StatusBadgeComponent {
  @Input() status!: TrangThaiKeKhai;
  @Input() size: 'sm' | 'md' | 'lg' = 'md';

  get statusText(): string {
    switch (this.status) {
      case TrangThaiKeKhai.DangSoan:
        return 'Đang soạn';
      case TrangThaiKeKhai.DaGui:
        return 'Đã gửi';
      case TrangThaiKeKhai.DaDuyet:
        return 'Đã duyệt';
      case TrangThaiKeKhai.BiTuChoi:
        return 'Bị từ chối';
      default:
        return 'Không xác định';
    }
  }

  get badgeClasses(): string {
    const baseClasses = 'status-badge inline-flex items-center rounded-full font-medium';
    const sizeClasses = this.getSizeClasses();
    const statusClasses = this.getStatusClasses();
    
    return `${baseClasses} ${sizeClasses} ${statusClasses}`.trim();
  }

  get dotClasses(): string {
    const baseClasses = 'status-dot rounded-full mr-1.5';
    const sizeClasses = this.getDotSizeClasses();
    const statusClasses = this.getDotStatusClasses();
    
    return `${baseClasses} ${sizeClasses} ${statusClasses}`.trim();
  }

  private getSizeClasses(): string {
    switch (this.size) {
      case 'sm':
        return 'px-2 py-0.5 text-xs';
      case 'md':
        return 'px-2.5 py-0.5 text-xs';
      case 'lg':
        return 'px-3 py-1 text-sm';
      default:
        return 'px-2.5 py-0.5 text-xs';
    }
  }

  private getDotSizeClasses(): string {
    switch (this.size) {
      case 'sm':
        return 'w-1.5 h-1.5';
      case 'md':
        return 'w-2 h-2';
      case 'lg':
        return 'w-2.5 h-2.5';
      default:
        return 'w-2 h-2';
    }
  }

  private getStatusClasses(): string {
    switch (this.status) {
      case TrangThaiKeKhai.DangSoan:
        return 'status-dang-soan bg-gray-100 text-gray-800';
      case TrangThaiKeKhai.DaGui:
        return 'status-da-gui bg-blue-100 text-blue-800';
      case TrangThaiKeKhai.DaDuyet:
        return 'status-da-duyet bg-green-100 text-green-800';
      case TrangThaiKeKhai.BiTuChoi:
        return 'status-bi-tu-choi bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  private getDotStatusClasses(): string {
    switch (this.status) {
      case TrangThaiKeKhai.DangSoan:
        return 'dot-dang-soan bg-gray-400';
      case TrangThaiKeKhai.DaGui:
        return 'dot-da-gui bg-blue-400';
      case TrangThaiKeKhai.DaDuyet:
        return 'dot-da-duyet bg-green-400';
      case TrangThaiKeKhai.BiTuChoi:
        return 'dot-bi-tu-choi bg-red-400';
      default:
        return 'bg-gray-400';
    }
  }
}
