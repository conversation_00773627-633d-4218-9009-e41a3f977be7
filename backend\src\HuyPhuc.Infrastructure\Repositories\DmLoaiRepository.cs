using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories;
using HuyPhuc.Infrastructure.Repositories.Base;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Infrastructure.Repositories;

/// <summary>
/// Repository implementation cho DmLoai
/// </summary>
public class DmLoaiRepository : Repository<DmLoai>, IDmLoaiRepository
{
    public DmLoaiRepository(IApplicationDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Lấy tất cả loại
    /// </summary>
    public async Task<List<DmLoai>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmLoai>()
            .OrderBy(l => l.Id)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Lấy loại theo ID
    /// </summary>
    public async Task<DmLoai?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmLoai>()
            .FirstOrDefaultAsync(l => l.Id == id, cancellationToken);
    }
}
