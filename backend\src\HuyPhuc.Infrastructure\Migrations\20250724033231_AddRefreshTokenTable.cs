﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HuyPhuc.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddRefreshTokenTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "dm_refresh_token",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    token = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    nguoi_dung_id = table.Column<int>(type: "integer", nullable: false),
                    thoi_gian_het_han = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    da_thu_hoi = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    ly_do_thu_hoi = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ip_address = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    user_agent = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    updated_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dm_refresh_token", x => x.id);
                    table.ForeignKey(
                        name: "fk_refresh_token_nguoi_dung",
                        column: x => x.nguoi_dung_id,
                        principalTable: "dm_nguoi_dung",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "ix_refresh_token_composite",
                table: "dm_refresh_token",
                columns: new[] { "nguoi_dung_id", "da_thu_hoi", "thoi_gian_het_han" });

            migrationBuilder.CreateIndex(
                name: "ix_refresh_token_da_thu_hoi",
                table: "dm_refresh_token",
                column: "da_thu_hoi");

            migrationBuilder.CreateIndex(
                name: "ix_refresh_token_nguoi_dung_id",
                table: "dm_refresh_token",
                column: "nguoi_dung_id");

            migrationBuilder.CreateIndex(
                name: "ix_refresh_token_thoi_gian_het_han",
                table: "dm_refresh_token",
                column: "thoi_gian_het_han");

            migrationBuilder.CreateIndex(
                name: "ix_refresh_token_token",
                table: "dm_refresh_token",
                column: "token",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "dm_refresh_token");
        }
    }
}
