import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

import { Tinh, TinhResponse, TinhOption } from '../models';
import { environment } from '../../../../../environments/environment';

/**
 * Service để quản lý thông tin tỉnh/thành phố
 */
@Injectable({
  providedIn: 'root'
})
export class TinhService {
  private readonly http = inject(HttpClient);
  private readonly apiUrl = `${environment.apiUrl}/tinh`;

  // Cache danh sách tỉnh
  private tinhListSubject = new BehaviorSubject<Tinh[]>([]);
  public tinhList$ = this.tinhListSubject.asObservable();

  // Cache đã load hay chưa
  private isLoaded = false;

  /**
   * <PERSON><PERSON><PERSON> danh sách tất cả tỉnh/thành phố
   */
  getAllTinh(): Observable<Tinh[]> {
    if (this.isLoaded && this.tinhListSubject.value.length > 0) {
      return of(this.tinhListSubject.value);
    }

    return this.http.get<any>(this.apiUrl).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.map((item: any) => ({
            id: item.id || 0,
            maTinh: item.value || item.maTinh || '',
            tenTinh: item.ten || item.tenTinh || '',
            textDisplay: item.text || item.textDisplay || '',
            createdAt: item.createdAt,
            updatedAt: item.updatedAt
          }));
        }
        return [];
      }),
      tap(tinhList => {
        this.tinhListSubject.next(tinhList);
        this.isLoaded = true;
      }),
      catchError(error => {
        console.error('Lỗi khi lấy danh sách tỉnh:', error);
        // Fallback to mock data if API fails
        return this.getMockTinhData().pipe(
          tap(tinhList => {
            this.tinhListSubject.next(tinhList);
            this.isLoaded = true;
          })
        );
      })
    );
  }

  /**
   * Lấy danh sách tỉnh dưới dạng options cho dropdown
   */
  getTinhOptions(): Observable<TinhOption[]> {
    return this.getAllTinh().pipe(
      map(tinhList => 
        tinhList.map(tinh => ({
          value: tinh.maTinh,
          text: tinh.textDisplay,
          ten: tinh.tenTinh
        }))
      )
    );
  }

  /**
   * Tìm tỉnh theo mã tỉnh
   */
  getTinhByMa(maTinh: string): Observable<Tinh | null> {
    return this.getAllTinh().pipe(
      map(tinhList => {
        const tinh = tinhList.find(t => t.maTinh === maTinh);
        return tinh || null;
      })
    );
  }

  /**
   * Lấy tên tỉnh theo mã tỉnh
   */
  getTenTinhByMa(maTinh: string): Observable<string> {
    return this.getTinhByMa(maTinh).pipe(
      map(tinh => tinh ? tinh.tenTinh : maTinh)
    );
  }

  /**
   * Lấy text display theo mã tỉnh
   */
  getTextDisplayByMa(maTinh: string): Observable<string> {
    return this.getTinhByMa(maTinh).pipe(
      map(tinh => tinh ? tinh.textDisplay : maTinh)
    );
  }

  /**
   * Convert mã tỉnh thành tên tỉnh (sync version cho pipe)
   */
  convertMaToTen(maTinh: string): string {
    const tinhList = this.tinhListSubject.value;
    const tinh = tinhList.find(t => t.maTinh === maTinh);
    return tinh ? tinh.tenTinh : maTinh;
  }

  /**
   * Convert mã tỉnh thành text display (sync version cho pipe)
   */
  convertMaToTextDisplay(maTinh: string): string {
    const tinhList = this.tinhListSubject.value;
    const tinh = tinhList.find(t => t.maTinh === maTinh);
    return tinh ? tinh.textDisplay : maTinh;
  }

  /**
   * Preload danh sách tỉnh
   */
  preloadTinhList(): void {
    if (!this.isLoaded) {
      this.getAllTinh().subscribe();
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.tinhListSubject.next([]);
    this.isLoaded = false;
  }

  /**
   * Mock data cho testing (tạm thời)
   */
  private getMockTinhData(): Observable<Tinh[]> {
    const mockData: Tinh[] = [
      { id: 1, maTinh: '01', tenTinh: 'Thành phố Hà Nội', textDisplay: '01 - Thành phố Hà Nội' },
      { id: 2, maTinh: '02', tenTinh: 'Tỉnh Hà Giang', textDisplay: '02 - Tỉnh Hà Giang' },
      { id: 3, maTinh: '04', tenTinh: 'Tỉnh Cao Bằng', textDisplay: '04 - Tỉnh Cao Bằng' },
      { id: 4, maTinh: '06', tenTinh: 'Tỉnh Bắc Kạn', textDisplay: '06 - Tỉnh Bắc Kạn' },
      { id: 5, maTinh: '08', tenTinh: 'Tỉnh Tuyên Quang', textDisplay: '08 - Tỉnh Tuyên Quang' },
      { id: 6, maTinh: '10', tenTinh: 'Tỉnh Lào Cai', textDisplay: '10 - Tỉnh Lào Cai' },
      { id: 7, maTinh: '11', tenTinh: 'Tỉnh Điện Biên', textDisplay: '11 - Tỉnh Điện Biên' },
      { id: 8, maTinh: '12', tenTinh: 'Tỉnh Lai Châu', textDisplay: '12 - Tỉnh Lai Châu' },
      { id: 9, maTinh: '14', tenTinh: 'Tỉnh Sơn La', textDisplay: '14 - Tỉnh Sơn La' },
      { id: 10, maTinh: '15', tenTinh: 'Tỉnh Yên Bái', textDisplay: '15 - Tỉnh Yên Bái' },
      { id: 11, maTinh: '17', tenTinh: 'Tỉnh Hòa Bình', textDisplay: '17 - Tỉnh Hòa Bình' },
      { id: 12, maTinh: '19', tenTinh: 'Tỉnh Thái Nguyên', textDisplay: '19 - Tỉnh Thái Nguyên' },
      { id: 13, maTinh: '20', tenTinh: 'Tỉnh Lạng Sơn', textDisplay: '20 - Tỉnh Lạng Sơn' },
      { id: 14, maTinh: '22', tenTinh: 'Tỉnh Quảng Ninh', textDisplay: '22 - Tỉnh Quảng Ninh' },
      { id: 15, maTinh: '24', tenTinh: 'Tỉnh Bắc Giang', textDisplay: '24 - Tỉnh Bắc Giang' },
      { id: 16, maTinh: '25', tenTinh: 'Tỉnh Phú Thọ', textDisplay: '25 - Tỉnh Phú Thọ' },
      { id: 17, maTinh: '26', tenTinh: 'Tỉnh Vĩnh Phúc', textDisplay: '26 - Tỉnh Vĩnh Phúc' },
      { id: 18, maTinh: '27', tenTinh: 'Tỉnh Bắc Ninh', textDisplay: '27 - Tỉnh Bắc Ninh' },
      { id: 19, maTinh: '30', tenTinh: 'Tỉnh Hải Dương', textDisplay: '30 - Tỉnh Hải Dương' },
      { id: 20, maTinh: '31', tenTinh: 'Thành phố Hải Phòng', textDisplay: '31 - Thành phố Hải Phòng' },
      { id: 21, maTinh: '79', tenTinh: 'Thành phố Hồ Chí Minh', textDisplay: '79 - Thành phố Hồ Chí Minh' },
      { id: 22, maTinh: '89', tenTinh: 'Tỉnh An Giang', textDisplay: '89 - Tỉnh An Giang' },
      { id: 23, maTinh: '92', tenTinh: 'Thành phố Cần Thơ', textDisplay: '92 - Thành phố Cần Thơ' }
    ];

    return of(mockData);
  }
}
