import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { Router } from '@angular/router';

import {
  Nguoi<PERSON>ung,
  ThongTinDangNhap,
  KetQuaDangNhap,
  VaiTro,
  ApiResponse
} from '../models';
import { environment } from '../../../../environments/environment';
import { MockApiService } from './mock-api.service';

/**
 * Service xử lý xác thực người dùng
 * Bao gồm đăng nhập, đăng xuất, quản lý token và session
 */
@Injectable({
  providedIn: 'root'
})
export class XacThucService {
  private readonly API_URL = environment.apiUrl;
  private readonly TOKEN_KEY = 'access_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_KEY = 'nguoi_dung';

  // Subject để theo dõi trạng thái đăng nhập
  private readonly _nguoiDungHienTai$ = new BehaviorSubject<NguoiDung | null>(null);
  private readonly _daDangNhap$ = new BehaviorSubject<boolean>(false);

  // Public observables
  public readonly nguoiDungHienTai$ = this._nguoiDungHienTai$.asObservable();
  public readonly daDangNhap$ = this._daDangNhap$.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router,
    private mockApiService: MockApiService
  ) {
    this.khoiTaoPhienLamViec();
  }

  /**
   * Khởi tạo phiên làm việc từ localStorage
   */
  private khoiTaoPhienLamViec(): void {
    const token = this.layToken();
    const nguoiDung = this.layThongTinNguoiDung();

    if (token && nguoiDung && this.kiemTraTokenConHan()) {
      this._nguoiDungHienTai$.next(nguoiDung);
      this._daDangNhap$.next(true);
    } else {
      this.xoaPhienLamViec();
    }
  }

  /**
   * Đăng nhập người dùng
   */
  dangNhap(thongTinDangNhap: ThongTinDangNhap): Observable<KetQuaDangNhap> {
    // Sử dụng mock API trong development, real API trong production
    if (!environment.production) {
      return this.mockApiService.dangNhap(thongTinDangNhap)
        .pipe(
          tap(ketQua => {
            if (ketQua.thanhCong && ketQua.duLieu) {
              this.luuPhienLamViec(ketQua.duLieu);
            }
          }),
          catchError(error => {
            console.error('Lỗi đăng nhập:', error);
            return of({
              thanhCong: false,
              thongBao: 'Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.'
            });
          })
        );
    }

    // Real API call cho production
    const requestPayload = {
      username: thongTinDangNhap.username,
      matKhau: thongTinDangNhap.matKhau,
      ghiNhoDangNhap: thongTinDangNhap.ghiNhoDangNhap || false
    };

    return this.http.post<any>(`${this.API_URL}/auth/dang-nhap`, requestPayload)
      .pipe(
        map(response => {
          console.log('Backend response:', response);

          if (response.thanhCong && response.duLieu) {
            // Map backend response to frontend format
            const ketQua: KetQuaDangNhap = {
              thanhCong: true,
              thongBao: response.thongBao,
              duLieu: {
                nguoiDung: {
                  id: response.duLieu.nguoiDung.id,
                  hoTen: response.duLieu.nguoiDung.hoTen,
                  email: response.duLieu.nguoiDung.email,
                  username: response.duLieu.nguoiDung.username,
                  soDienThoai: response.duLieu.nguoiDung.soDienThoai,
                  avatar: response.duLieu.nguoiDung.avatarUrl,
                  vaiTro: VaiTro.Admin, // TODO: Map from backend roles properly
                  trangThaiHoatDong: response.duLieu.nguoiDung.trangThai === 'Active',
                  ngayTao: new Date(response.duLieu.nguoiDung.ngayTao),
                  ngayCapNhat: response.duLieu.nguoiDung.lastLogin ? new Date(response.duLieu.nguoiDung.lastLogin) : undefined
                },
                accessToken: response.duLieu.accessToken,
                refreshToken: response.duLieu.refreshToken,
                thoiGianHetHan: new Date(response.duLieu.thoiGianHetHan).getTime()
              }
            };
            return ketQua;
          } else {
            return {
              thanhCong: false,
              thongBao: response.thongBao || 'Đăng nhập thất bại'
            };
          }
        }),
        tap(ketQua => {
          if (ketQua.thanhCong && 'duLieu' in ketQua && ketQua.duLieu) {
            this.luuPhienLamViec(ketQua.duLieu);
          }
        }),
        catchError(error => {
          console.error('Lỗi đăng nhập:', error);
          let thongBaoLoi = 'Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.';

          if (error.status === 400 && error.error?.thongBao) {
            thongBaoLoi = error.error.thongBao;
          } else if (error.status === 401) {
            thongBaoLoi = 'Username hoặc mật khẩu không đúng.';
          }

          return of({
            thanhCong: false,
            thongBao: thongBaoLoi
          });
        })
      );
  }

  /**
   * Đăng xuất người dùng
   */
  dangXuat(): Observable<boolean> {
    // Sử dụng mock API trong development
    if (!environment.production) {
      return this.mockApiService.dangXuat()
        .pipe(
          tap(() => this.xoaPhienLamViec()),
          map(response => response.thanhCong),
          catchError(() => {
            this.xoaPhienLamViec();
            return of(true);
          })
        );
    }

    // Real API call cho production
    const refreshToken = this.layRefreshToken();
    return this.http.post<ApiResponse>(`${this.API_URL}/auth/dang-xuat`, { refreshToken })
      .pipe(
        tap(() => this.xoaPhienLamViec()),
        map(response => response.thanhCong),
        catchError(() => {
          // Dù API lỗi vẫn xóa session local
          this.xoaPhienLamViec();
          return of(true);
        })
      );
  }

  /**
   * Làm mới token
   */
  lamMoiToken(): Observable<string | null> {
    const refreshToken = this.layRefreshToken();
    
    if (!refreshToken) {
      return of(null);
    }

    return this.http.post<ApiResponse<any>>(`${this.API_URL}/auth/lam-moi-token`, { refreshToken })
      .pipe(
        tap(response => {
          if (response.thanhCong && response.duLieu) {
            this.luuToken(response.duLieu.accessToken);
            if (response.duLieu.refreshToken) {
              this.luuRefreshToken(response.duLieu.refreshToken);
            }
          }
        }),
        map(response => response.thanhCong ? response.duLieu?.accessToken : null),
        catchError(() => {
          this.xoaPhienLamViec();
          return of(null);
        })
      );
  }

  /**
   * Lưu thông tin phiên làm việc
   */
  private luuPhienLamViec(duLieu: any): void {
    this.luuToken(duLieu.accessToken);
    this.luuRefreshToken(duLieu.refreshToken);
    this.luuThongTinNguoiDung(duLieu.nguoiDung);
    
    this._nguoiDungHienTai$.next(duLieu.nguoiDung);
    this._daDangNhap$.next(true);
  }

  /**
   * Xóa phiên làm việc
   */
  private xoaPhienLamViec(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    
    this._nguoiDungHienTai$.next(null);
    this._daDangNhap$.next(false);
    
    this.router.navigate(['/dang-nhap']);
  }

  /**
   * Lấy token từ localStorage
   */
  layToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Lấy refresh token từ localStorage
   */
  layRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  /**
   * Lấy thông tin người dùng từ localStorage
   */
  layThongTinNguoiDung(): NguoiDung | null {
    const userData = localStorage.getItem(this.USER_KEY);
    return userData ? JSON.parse(userData) : null;
  }

  /**
   * Lưu token vào localStorage
   */
  private luuToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  /**
   * Lưu refresh token vào localStorage
   */
  private luuRefreshToken(refreshToken: string): void {
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  /**
   * Lưu thông tin người dùng vào localStorage
   */
  private luuThongTinNguoiDung(nguoiDung: NguoiDung): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(nguoiDung));
  }

  /**
   * Kiểm tra token còn hạn hay không
   */
  kiemTraTokenConHan(): boolean {
    const token = this.layToken();
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const now = Math.floor(Date.now() / 1000);
      const isValid = payload.exp > now;

      // Debug logging để kiểm tra
      console.log('🔍 Token validation debug:', {
        exp: payload.exp,
        now: now,
        expDate: new Date(payload.exp * 1000).toLocaleString(),
        nowDate: new Date(now * 1000).toLocaleString(),
        isValid: isValid,
        timeDiff: payload.exp - now
      });

      return isValid;
    } catch (error) {
      console.error('❌ Error parsing token:', error);
      return false;
    }
  }

  /**
   * Kiểm tra người dùng đã đăng nhập
   */
  get daDangNhap(): boolean {
    return this._daDangNhap$.value;
  }

  /**
   * Lấy thông tin người dùng hiện tại
   */
  get nguoiDungHienTai(): NguoiDung | null {
    return this._nguoiDungHienTai$.value;
  }

  /**
   * Lấy danh sách tài khoản demo (chỉ trong development)
   */
  layDanhSachTaiKhoanDemo(): Array<{username: string, matKhau: string, vaiTro: string}> {
    if (!environment.production) {
      return this.mockApiService.layDanhSachTaiKhoanDemo();
    }
    return [];
  }
}
