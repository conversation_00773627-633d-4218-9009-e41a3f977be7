using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace HuyPhuc.Infrastructure.Repositories;

/// <summary>
/// Repository implementation cho RefreshToken
/// </summary>
public class RefreshTokenRepository : IRefreshTokenRepository
{
    private readonly IApplicationDbContext _context;

    public RefreshTokenRepository(IApplicationDbContext context)
    {
        _context = context;
    }

    #region IRepository<RefreshToken> Implementation

    public async Task<RefreshToken?> LayTheoIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.RefreshToken
            .Include(rt => rt.NguoiDung)
            .FirstOrDefaultAsync(rt => rt.Id == id, cancellationToken);
    }

    public async Task<IEnumerable<RefreshToken>> LayTatCaAsync(CancellationToken cancellationToken = default)
    {
        return await _context.RefreshToken
            .Include(rt => rt.NguoiDung)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RefreshToken>> TimKiemAsync(Expression<Func<RefreshToken, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _context.RefreshToken
            .Include(rt => rt.NguoiDung)
            .Where(predicate)
            .ToListAsync(cancellationToken);
    }

    public async Task<RefreshToken?> TimKiemDauTienAsync(Expression<Func<RefreshToken, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _context.RefreshToken
            .Include(rt => rt.NguoiDung)
            .FirstOrDefaultAsync(predicate, cancellationToken);
    }

    public async Task<bool> TonTaiAsync(Expression<Func<RefreshToken, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _context.RefreshToken.AnyAsync(predicate, cancellationToken);
    }

    public async Task<int> DemAsync(Expression<Func<RefreshToken, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        if (predicate == null)
            return await _context.RefreshToken.CountAsync(cancellationToken);

        return await _context.RefreshToken.CountAsync(predicate, cancellationToken);
    }

    public async Task ThemAsync(RefreshToken entity, CancellationToken cancellationToken = default)
    {
        await _context.RefreshToken.AddAsync(entity, cancellationToken);
    }

    public async Task ThemNhieuAsync(IEnumerable<RefreshToken> entities, CancellationToken cancellationToken = default)
    {
        await _context.RefreshToken.AddRangeAsync(entities, cancellationToken);
    }

    public void CapNhat(RefreshToken entity)
    {
        _context.RefreshToken.Update(entity);
    }

    public void CapNhatNhieu(IEnumerable<RefreshToken> entities)
    {
        _context.RefreshToken.UpdateRange(entities);
    }

    public void Xoa(RefreshToken entity)
    {
        _context.RefreshToken.Remove(entity);
    }

    public void XoaNhieu(IEnumerable<RefreshToken> entities)
    {
        _context.RefreshToken.RemoveRange(entities);
    }

    public async Task<IEnumerable<RefreshToken>> LayPhanTrangAsync(
        int trang,
        int kichThuocTrang,
        Expression<Func<RefreshToken, bool>>? predicate = null,
        Expression<Func<RefreshToken, object>>? orderBy = null,
        bool tangDan = true,
        CancellationToken cancellationToken = default)
    {
        var query = _context.RefreshToken.Include(rt => rt.NguoiDung).AsQueryable();

        if (predicate != null)
            query = query.Where(predicate);

        if (orderBy != null)
        {
            query = tangDan ? query.OrderBy(orderBy) : query.OrderByDescending(orderBy);
        }

        return await query
            .Skip((trang - 1) * kichThuocTrang)
            .Take(kichThuocTrang)
            .ToListAsync(cancellationToken);
    }

    #endregion

    #region IRefreshTokenRepository Specific Methods

    /// <summary>
    /// Tìm refresh token theo token string
    /// </summary>
    public async Task<RefreshToken?> LayTheoTokenAsync(string token, CancellationToken cancellationToken = default)
    {
        return await _context.RefreshToken
            .Include(rt => rt.NguoiDung)
            .FirstOrDefaultAsync(rt => rt.Token == token, cancellationToken);
    }

    /// <summary>
    /// Lấy tất cả refresh tokens của một người dùng
    /// </summary>
    public async Task<IEnumerable<RefreshToken>> LayTheoNguoiDungIdAsync(int nguoiDungId, CancellationToken cancellationToken = default)
    {
        return await _context.RefreshToken
            .Where(rt => rt.NguoiDungId == nguoiDungId)
            .OrderByDescending(rt => rt.NgayTao)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Thu hồi tất cả refresh tokens của một người dùng
    /// </summary>
    public async Task ThuHoiTatCaTokenCuaNguoiDungAsync(int nguoiDungId, string lyDo, CancellationToken cancellationToken = default)
    {
        var tokens = await _context.RefreshToken
            .Where(rt => rt.NguoiDungId == nguoiDungId && !rt.DaThuHoi)
            .ToListAsync(cancellationToken);

        foreach (var token in tokens)
        {
            token.ThuHoi(lyDo);
        }
    }

    /// <summary>
    /// Xóa các refresh tokens đã hết hạn
    /// </summary>
    public async Task XoaTokenHetHanAsync(CancellationToken cancellationToken = default)
    {
        var expiredTokens = await _context.RefreshToken
            .Where(rt => rt.ThoiGianHetHan <= DateTime.UtcNow)
            .ToListAsync(cancellationToken);

        _context.RefreshToken.RemoveRange(expiredTokens);
    }

    /// <summary>
    /// Đếm số lượng refresh tokens hợp lệ của một người dùng
    /// </summary>
    public async Task<int> DemTokenHopLeCuaNguoiDungAsync(int nguoiDungId, CancellationToken cancellationToken = default)
    {
        return await _context.RefreshToken
            .CountAsync(rt => rt.NguoiDungId == nguoiDungId &&
                             !rt.DaThuHoi &&
                             rt.ThoiGianHetHan > DateTime.UtcNow,
                       cancellationToken);
    }

    #endregion
}
