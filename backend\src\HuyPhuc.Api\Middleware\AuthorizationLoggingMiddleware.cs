using System.Security.Claims;

namespace HuyPhuc.Api.Middleware;

/// <summary>
/// Middleware để log các attempt authorization
/// </summary>
public class AuthorizationLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<AuthorizationLoggingMiddleware> _logger;

    public AuthorizationLoggingMiddleware(RequestDelegate next, ILogger<AuthorizationLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Log thông tin request trước khi xử lý
        if (context.User.Identity?.IsAuthenticated == true)
        {
            var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userName = context.User.FindFirst(ClaimTypes.Name)?.Value;
            var roles = context.User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
            var permissions = context.User.FindAll("permission").Select(c => c.Value).ToList();

            _logger.LogInformation(
                "Authorization Request: User {UserId} ({UserName}) accessing {Method} {Path}. Roles: [{Roles}], Permissions: [{Permissions}]",
                userId, userName, context.Request.Method, context.Request.Path,
                string.Join(", ", roles), string.Join(", ", permissions));
        }

        await _next(context);

        // Log kết quả authorization
        if (context.Response.StatusCode == 403)
        {
            var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            _logger.LogWarning(
                "Authorization Failed: User {UserId} denied access to {Method} {Path}",
                userId, context.Request.Method, context.Request.Path);
        }
    }
}
