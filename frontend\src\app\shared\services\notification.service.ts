import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  autoClose?: boolean;
}

/**
 * Service quản lý thông báo toast
 */
@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private readonly _notifications$ = new BehaviorSubject<Notification[]>([]);

  /**
   * Observable danh sách thông báo
   */
  get notifications$(): Observable<Notification[]> {
    return this._notifications$.asObservable();
  }

  /**
   * Hiển thị thông báo thành công
   */
  showSuccess(title: string, message?: string, duration?: number): void;
  showSuccess(message: string): void;
  showSuccess(titleOrMessage: string, message?: string, duration: number = 5000): void {
    // Handle overloaded signatures
    let title: string;
    let msg: string;

    if (message === undefined) {
      // Single parameter - treat as message with default title
      title = 'Thành công';
      msg = titleOrMessage;
    } else {
      // Two parameters - title and message
      title = titleOrMessage;
      msg = message;
    }

    this.addNotification({
      type: 'success',
      title,
      message: msg,
      duration,
      autoClose: true
    });
  }

  /**
   * Hiển thị thông báo lỗi
   */
  showError(title: string, message?: string, duration?: number): void;
  showError(message: string): void;
  showError(titleOrMessage: string, message?: string, duration: number = 8000): void {
    // Handle overloaded signatures
    let title: string;
    let msg: string;

    if (message === undefined) {
      // Single parameter - treat as message with default title
      title = 'Lỗi';
      msg = titleOrMessage;
    } else {
      // Two parameters - title and message
      title = titleOrMessage;
      msg = message;
    }

    this.addNotification({
      type: 'error',
      title,
      message: msg,
      duration,
      autoClose: true
    });
  }

  /**
   * Hiển thị thông báo cảnh báo
   */
  showWarning(title: string, message: string, duration: number = 6000): void {
    this.addNotification({
      type: 'warning',
      title,
      message,
      duration,
      autoClose: true
    });
  }

  /**
   * Hiển thị thông báo thông tin
   */
  showInfo(title: string, message: string, duration: number = 5000): void {
    this.addNotification({
      type: 'info',
      title,
      message,
      duration,
      autoClose: true
    });
  }

  /**
   * Xóa thông báo
   */
  removeNotification(id: string): void {
    const current = this._notifications$.value;
    const updated = current.filter(n => n.id !== id);
    this._notifications$.next(updated);
  }

  /**
   * Xóa tất cả thông báo
   */
  clearAll(): void {
    this._notifications$.next([]);
  }

  /**
   * Thêm thông báo mới
   */
  private addNotification(notification: Omit<Notification, 'id'>): void {
    const id = this.generateId();
    const newNotification: Notification = { ...notification, id };
    
    const current = this._notifications$.value;
    this._notifications$.next([...current, newNotification]);

    // Auto remove nếu có duration
    if (notification.autoClose && notification.duration) {
      setTimeout(() => {
        this.removeNotification(id);
      }, notification.duration);
    }
  }

  /**
   * Tạo ID unique cho thông báo
   */
  private generateId(): string {
    return 'notification_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
  }
}
