using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories.Base;

namespace HuyPhuc.Domain.Repositories;

public interface INguoiDungRepository : IRepository<NguoiDung>
{
    Task<NguoiDung?> LayTheoEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<NguoiDung?> LayTheoEmailVoiVaiTroAsync(string email, CancellationToken cancellationToken = default);
    Task<NguoiDung?> LayTheoUsernameAsync(string username, CancellationToken cancellationToken = default);
    Task<NguoiDung?> LayTheoUsernameVoiVaiTroAsync(string username, CancellationToken cancellationToken = default);
    Task<NguoiDung?> LayTheoIdVoiVaiTroAsync(int id, CancellationToken cancellationToken = default);
    Task<bool> EmailDaTonTaiAsync(string email, int? loaiTruId = null, CancellationToken cancellationToken = default);
    Task<bool> UsernameDaTonTaiAsync(string username, int? loaiTruId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<NguoiDung>> TimKiemTheoTenAsync(string tuKhoa, CancellationToken cancellationToken = default);
    Task<IEnumerable<NguoiDung>> LayTheoTrangThaiAsync(int trangThai, CancellationToken cancellationToken = default);
}
