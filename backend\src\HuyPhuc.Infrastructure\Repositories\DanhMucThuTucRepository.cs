using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Repositories;
using HuyPhuc.Infrastructure.Repositories.Base;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Infrastructure.Repositories;

/// <summary>
/// Repository implementation cho danh mục thủ tục
/// </summary>
public class DanhMucThuTucRepository : Repository<DanhMucThuTuc>, IDanhMucThuTucRepository
{
    public DanhMucThuTucRepository(IApplicationDbContext context) : base(context)
    {
    }

    public async Task<DanhMucThuTuc?> LayTheoMaAsync(string ma, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(x => x.Ma == ma.ToUpper(), cancellationToken);
    }

    public async Task<bool> KiemTraMaTonTaiAsync(string ma, int? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(x => x.Ma == ma.ToUpper());
        
        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    public async Task<IEnumerable<DanhMucThuTuc>> TimKiemAsync(
        string? tuKhoa = null,
        LinhVucThuTuc? linhVuc = null,
        TrangThaiThuTuc? trangThai = null,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        if (!string.IsNullOrWhiteSpace(tuKhoa))
        {
            var tuKhoaLower = tuKhoa.ToLower();
            query = query.Where(x => 
                x.Ma.ToLower().Contains(tuKhoaLower) ||
                x.Ten.ToLower().Contains(tuKhoaLower) ||
                x.TenLinhVuc.ToLower().Contains(tuKhoaLower) ||
                (x.MoTa != null && x.MoTa.ToLower().Contains(tuKhoaLower)));
        }

        if (linhVuc.HasValue)
        {
            query = query.Where(x => x.LinhVuc == linhVuc.Value);
        }

        if (trangThai.HasValue)
        {
            query = query.Where(x => x.TrangThai == trangThai.Value);
        }

        return await query
            .OrderBy(x => x.Ma)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<DanhMucThuTuc> Items, int TotalCount)> LayDanhSachPhanTrangAsync(
        int trang = 1,
        int kichThuocTrang = 10,
        string? tuKhoa = null,
        LinhVucThuTuc? linhVuc = null,
        TrangThaiThuTuc? trangThai = null,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(tuKhoa))
        {
            var tuKhoaLower = tuKhoa.ToLower();
            query = query.Where(x => 
                x.Ma.ToLower().Contains(tuKhoaLower) ||
                x.Ten.ToLower().Contains(tuKhoaLower) ||
                x.TenLinhVuc.ToLower().Contains(tuKhoaLower) ||
                (x.MoTa != null && x.MoTa.ToLower().Contains(tuKhoaLower)));
        }

        if (linhVuc.HasValue)
        {
            query = query.Where(x => x.LinhVuc == linhVuc.Value);
        }

        if (trangThai.HasValue)
        {
            query = query.Where(x => x.TrangThai == trangThai.Value);
        }

        // Get total count
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination
        var items = await query
            .OrderBy(x => x.Ma)
            .Skip((trang - 1) * kichThuocTrang)
            .Take(kichThuocTrang)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<IEnumerable<DanhMucThuTuc>> LayTheoLinhVucAsync(
        LinhVucThuTuc linhVuc,
        TrangThaiThuTuc? trangThai = null,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(x => x.LinhVuc == linhVuc);

        if (trangThai.HasValue)
        {
            query = query.Where(x => x.TrangThai == trangThai.Value);
        }

        return await query
            .OrderBy(x => x.Ma)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DanhMucThuTuc>> LayThuTucHoatDongAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.TrangThai == TrangThaiThuTuc.HoatDong)
            .OrderBy(x => x.Ma)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DanhMucThuTuc>> LayThuTucCoHieuLucAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await _dbSet
            .Where(x => x.TrangThai == TrangThaiThuTuc.HoatDong && 
                       (x.NgayApDung == null || x.NgayApDung <= now))
            .OrderBy(x => x.Ma)
            .ToListAsync(cancellationToken);
    }

    public async Task<Dictionary<TrangThaiThuTuc, int>> DemTheoTrangThaiAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .GroupBy(x => x.TrangThai)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<Dictionary<LinhVucThuTuc, int>> DemTheoLinhVucAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .GroupBy(x => x.LinhVuc)
            .ToDictionaryAsync(g => g.Key, g => g.Count(), cancellationToken);
    }

    public async Task<IEnumerable<DanhMucThuTuc>> LayThuTucMoiNhatAsync(
        int soLuong = 10,
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .OrderByDescending(x => x.NgayTao)
            .Take(soLuong)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DanhMucThuTuc>> LayThuTucCapNhatGanDayAsync(
        int soLuong = 10,
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.NgayCapNhat != null)
            .OrderByDescending(x => x.NgayCapNhat)
            .Take(soLuong)
            .ToListAsync(cancellationToken);
    }
}
