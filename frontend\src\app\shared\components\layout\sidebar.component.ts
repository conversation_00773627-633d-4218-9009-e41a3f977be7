import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { SidebarService } from '../../services/sidebar.service';
import { MenuItemModel, SidebarState, SidebarConfig } from '../../models/menu-item.model';
import { LogoComponent } from '../logo/logo.component';

/**
 * Component sidebar enterprise cho navigation
 */
@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule, LogoComponent],
  template: `
    <!-- Desktop Sidebar -->
    <aside class="sidebar-desktop fixed left-0 top-0 z-40 h-screen transition-all duration-300 ease-in-out"
           [class.collapsed]="state.isCollapsed"
           [style.width.px]="sidebarWidth"
           *ngIf="!state.isMobile">
      
      <!-- Sidebar Content -->
      <div class="sidebar-content flex flex-col h-full bg-gray-900 border-r border-gray-800">
        
        <!-- Header với Logo -->
        <div class="sidebar-header flex items-center border-b border-gray-800"
             [class.px-4]="!state.isCollapsed"
             [class.px-2]="state.isCollapsed"
             [class.py-6]="!state.isCollapsed"
             [class.py-4]="state.isCollapsed">
          <app-logo
            [kichThuoc]="state.isCollapsed ? 'small' : 'medium'"
            theme="dark"
            [showText]="!state.isCollapsed"
            tenCongTy="BHYT Portal"
            moTa="Hệ thống kê khai">
          </app-logo>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav flex-1 px-3 py-4 overflow-y-auto">
          <ul class="space-y-2">
            <li *ngFor="let item of menuItems; trackBy: trackByMenuId">
              <!-- Menu Item -->
              <div class="menu-item-wrapper">
                <!-- Main Menu Item -->
                <a *ngIf="item.duongDan && !item.children"
                   [routerLink]="item.duongDan"
                   class="menu-item flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200"
                   [class.active]="state.activeMenuId === item.id"
                   [title]="state.isCollapsed ? item.nhan : ''">
                  
                  <!-- Icon -->
                  <svg class="menu-icon w-5 h-5 flex-shrink-0" 
                       [class.mr-3]="!state.isCollapsed"
                       fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="item.icon"></path>
                  </svg>
                  
                  <!-- Label -->
                  <span class="menu-label transition-opacity duration-200"
                        [class.opacity-0]="state.isCollapsed"
                        [class.w-0]="state.isCollapsed"
                        [class.overflow-hidden]="state.isCollapsed">
                    {{ item.nhan }}
                  </span>

                  <!-- Badge -->
                  <span *ngIf="item.badge && !state.isCollapsed"
                        class="menu-badge ml-auto px-2 py-0.5 text-xs rounded-full"
                        [ngClass]="getBadgeClass(item.badge.type)">
                    {{ item.badge.text }}
                  </span>
                </a>

                <!-- Menu Item với Children -->
                <button *ngIf="item.children"
                        class="menu-item menu-parent flex items-center w-full px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200"
                        [class.active]="isParentActive(item)"
                        [title]="state.isCollapsed ? item.nhan : ''"
                        (click)="toggleSubmenu(item.id)">
                  
                  <!-- Icon -->
                  <svg class="menu-icon w-5 h-5 flex-shrink-0" 
                       [class.mr-3]="!state.isCollapsed"
                       fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="item.icon"></path>
                  </svg>
                  
                  <!-- Label -->
                  <span class="menu-label flex-1 text-left transition-opacity duration-200"
                        [class.opacity-0]="state.isCollapsed"
                        [class.w-0]="state.isCollapsed"
                        [class.overflow-hidden]="state.isCollapsed">
                    {{ item.nhan }}
                  </span>

                  <!-- Expand Arrow -->
                  <svg class="expand-arrow w-4 h-4 transition-transform duration-200"
                       [class.rotate-90]="isMenuExpanded(item.id)"
                       [class.opacity-0]="state.isCollapsed"
                       fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>

                <!-- Submenu -->
                <ul *ngIf="item.children && isMenuExpanded(item.id) && !state.isCollapsed"
                    class="submenu mt-1 ml-8 space-y-1 overflow-hidden transition-all duration-300">
                  <li *ngFor="let child of item.children">
                    <a [routerLink]="child.duongDan"
                       class="submenu-item flex items-center px-3 py-2 text-sm rounded-lg transition-all duration-200"
                       [class.active]="state.activeMenuId === child.id">
                      
                      <svg class="w-4 h-4 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="child.icon"></path>
                      </svg>
                      
                      <span>{{ child.nhan }}</span>
                    </a>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
        </nav>

        <!-- Collapse Toggle Button -->
        <div class="sidebar-footer border-t border-gray-800 p-4">
          <button class="collapse-btn flex items-center justify-center w-full px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-all duration-200"
                  (click)="toggleCollapsed()"
                  [title]="state.isCollapsed ? 'Mở rộng sidebar' : 'Thu gọn sidebar'">
            
            <svg class="w-5 h-5 transition-transform duration-200"
                 [class.rotate-180]="state.isCollapsed"
                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
            </svg>
            
            <span class="ml-2 transition-opacity duration-200"
                  [class.opacity-0]="state.isCollapsed"
                  [class.w-0]="state.isCollapsed"
                  [class.overflow-hidden]="state.isCollapsed">
              Thu gọn
            </span>
          </button>
        </div>
      </div>
    </aside>

    <!-- Mobile Sidebar Overlay -->
    <div *ngIf="state.isMobile && state.isMobileOpen"
         class="mobile-overlay fixed inset-0 z-50 lg:hidden"
         (click)="closeMobileSidebar()">
      
      <!-- Backdrop -->
      <div class="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity"></div>
      
      <!-- Mobile Sidebar -->
      <aside class="mobile-sidebar fixed left-0 top-0 z-50 h-screen w-80 bg-gray-900 border-r border-gray-800 transform transition-transform duration-300 ease-in-out">
        
        <!-- Mobile Header -->
        <div class="mobile-header flex items-center justify-between px-4 py-6 border-b border-gray-800">
          <app-logo 
            kichThuoc="medium"
            theme="dark"
            [showText]="true"
            tenCongTy="BHYT Portal"
            moTa="Hệ thống kê khai">
          </app-logo>
          
          <button class="close-btn p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors"
                  (click)="closeMobileSidebar()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Mobile Navigation -->
        <nav class="mobile-nav flex-1 px-3 py-4 overflow-y-auto">
          <ul class="space-y-2">
            <li *ngFor="let item of menuItems; trackBy: trackByMenuId">
              <!-- Mobile Menu Item -->
              <div class="menu-item-wrapper">
                <a *ngIf="item.duongDan && !item.children"
                   [routerLink]="item.duongDan"
                   class="menu-item flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200"
                   [class.active]="state.activeMenuId === item.id"
                   (click)="closeMobileSidebar()">
                  
                  <svg class="menu-icon w-5 h-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="item.icon"></path>
                  </svg>
                  
                  <span>{{ item.nhan }}</span>
                </a>

                <!-- Mobile Parent Menu -->
                <button *ngIf="item.children"
                        class="menu-item menu-parent flex items-center w-full px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200"
                        [class.active]="isParentActive(item)"
                        (click)="toggleSubmenu(item.id)">
                  
                  <svg class="menu-icon w-5 h-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="item.icon"></path>
                  </svg>
                  
                  <span class="flex-1 text-left">{{ item.nhan }}</span>

                  <svg class="expand-arrow w-4 h-4 transition-transform duration-200"
                       [class.rotate-90]="isMenuExpanded(item.id)"
                       fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>

                <!-- Mobile Submenu -->
                <ul *ngIf="item.children && isMenuExpanded(item.id)"
                    class="submenu mt-1 ml-8 space-y-1">
                  <li *ngFor="let child of item.children">
                    <a [routerLink]="child.duongDan"
                       class="submenu-item flex items-center px-3 py-2 text-sm rounded-lg transition-all duration-200"
                       [class.active]="state.activeMenuId === child.id"
                       (click)="closeMobileSidebar()">
                      
                      <svg class="w-4 h-4 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="child.icon"></path>
                      </svg>
                      
                      <span>{{ child.nhan }}</span>
                    </a>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
        </nav>
      </aside>
    </div>
  `,
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  state: SidebarState = {
    isCollapsed: false,
    isMobileOpen: false,
    isMobile: false,
    expandedMenuIds: []
  };
  
  config: SidebarConfig;
  menuItems: MenuItemModel[] = [];

  constructor(
    private sidebarService: SidebarService
  ) {
    this.config = this.sidebarService.getConfig();
  }

  ngOnInit(): void {
    // Subscribe to sidebar state
    this.sidebarService.state$
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        this.state = state;
      });

    // Subscribe to menu items
    this.sidebarService.menuItems$
      .pipe(takeUntil(this.destroy$))
      .subscribe(items => {
        this.menuItems = items;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Get sidebar width dựa trên state
   */
  get sidebarWidth(): number {
    return this.state.isCollapsed ? this.config.width.collapsed : this.config.width.expanded;
  }

  /**
   * Toggle collapsed state
   */
  toggleCollapsed(): void {
    this.sidebarService.toggleCollapsed();
  }

  /**
   * Close mobile sidebar
   */
  closeMobileSidebar(): void {
    this.sidebarService.closeMobile();
  }

  /**
   * Toggle submenu
   */
  toggleSubmenu(menuId: string): void {
    this.sidebarService.toggleMenuExpanded(menuId);
  }

  /**
   * Check if menu is expanded
   */
  isMenuExpanded(menuId: string): boolean {
    return this.state.expandedMenuIds.includes(menuId);
  }

  /**
   * Check if parent menu is active
   */
  isParentActive(item: MenuItemModel): boolean {
    if (!item.children) return false;
    return item.children.some(child => child.id === this.state.activeMenuId);
  }

  /**
   * Get badge CSS class
   */
  getBadgeClass(type: string): string {
    const classes = {
      info: 'bg-blue-100 text-blue-800',
      warning: 'bg-yellow-100 text-yellow-800',
      success: 'bg-green-100 text-green-800',
      error: 'bg-red-100 text-red-800'
    };
    return classes[type as keyof typeof classes] || classes.info;
  }

  /**
   * TrackBy function for menu items
   */
  trackByMenuId(_index: number, item: MenuItemModel): string {
    return item.id;
  }

  /**
   * Handle escape key to close mobile sidebar
   */
  @HostListener('document:keydown.escape')
  onEscapeKey(): void {
    if (this.state.isMobile && this.state.isMobileOpen) {
      this.closeMobileSidebar();
    }
  }
}
