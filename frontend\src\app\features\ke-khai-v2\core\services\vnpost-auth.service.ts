import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

export interface VnPostCaptchaData {
  image: string;
  code: string;
}

export interface VnPostCaptchaResponse {
  data: VnPostCaptchaData;
  success: boolean;
  message: string | null;
  errors: any;
  status: number;
  traceId: string;
}

export interface RefreshTokenRequest {
  userName: string;
  password: string;
  captchaText: string;
  captchaCode: string;
}

export interface TokenStatusResponse {
  isExpired: boolean;
  hasToken: boolean;
  message: string;
}

@Injectable({
  providedIn: 'root'
})
export class VnPostAuthService {
  private readonly apiUrl = 'http://localhost:5129/api/vnpostauth';

  // Subject để thông báo khi token expired/refreshed
  private tokenExpiredSubject = new BehaviorSubject<boolean>(false);
  public tokenExpired$ = this.tokenExpiredSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Lấy captcha từ VNPost
   */
  getCaptcha(): Observable<VnPostCaptchaResponse> {
    return this.http.get<VnPostCaptchaResponse>(`${this.apiUrl}/captcha`).pipe(
      catchError(error => {
        console.error('Lỗi khi lấy captcha:', error);
        return throwError(() => new Error('Không thể lấy captcha. Vui lòng thử lại.'));
      })
    );
  }

  /**
   * Refresh access token VNPost
   */
  refreshToken(request: RefreshTokenRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/refresh-token`, request).pipe(
      map(response => {
        console.log('✅ VNPost login successful, triggering token refresh event');

        // Reset token state on backend after successful login
        this.resetBackendTokenState().subscribe({
          next: () => console.log('✅ Backend token state reset successful'),
          error: (error) => console.warn('⚠️ Failed to reset backend token state:', error)
        });

        // Trigger token refresh event for auto retry
        this.tokenExpiredSubject.next(false);

        return response;
      }),
      catchError(error => {
        console.error('Lỗi khi refresh token:', error);

        if (error.status === 400) {
          return throwError(() => new Error(error.error?.message || 'Thông tin đăng nhập không chính xác.'));
        } else if (error.status === 500) {
          return throwError(() => new Error('Lỗi server. Vui lòng thử lại sau.'));
        } else {
          return throwError(() => new Error('Có lỗi xảy ra khi đăng nhập.'));
        }
      })
    );
  }

  /**
   * Reset backend token state
   */
  private resetBackendTokenState(): Observable<any> {
    return this.http.post('http://localhost:5129/api/bhxh/reset-token-state', {});
  }

  /**
   * Kiểm tra trạng thái token
   */
  getTokenStatus(): Observable<TokenStatusResponse> {
    return this.http.get<TokenStatusResponse>(`${this.apiUrl}/token-status`).pipe(
      catchError(error => {
        console.error('Lỗi khi kiểm tra token:', error);
        return throwError(() => new Error('Không thể kiểm tra trạng thái token.'));
      })
    );
  }

  /**
   * Force expire token for testing purposes
   */
  forceExpireToken(): Observable<any> {
    return this.http.post(`${this.apiUrl}/force-expire-token`, {}).pipe(
      catchError(error => {
        console.error('Lỗi khi force expire token:', error);
        return throwError(() => new Error('Không thể force expire token.'));
      })
    );
  }
}
