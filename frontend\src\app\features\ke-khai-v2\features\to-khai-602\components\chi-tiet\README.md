# Component Chi Tiết Tờ Khai 602 - Backend Integration

## Tổng quan

Component `ChiTietComponent` đã được tích hợp với backend API để quản lý tờ khai 602 và danh sách lao động.

## C<PERSON>c tính năng đã tích hợp

### 1. <PERSON><PERSON><PERSON><PERSON> lý Lao động
- **Thêm lao động mới**: Sử dụng API `POST /api/ke-khai/{keKhaiId}/lao-dong`
- **Cập nhật lao động**: Sử dụng API `PUT /api/ke-khai/{keKhaiId}/lao-dong/{laoDongId}`
- **Xóa lao động**: Sử dụng API `DELETE /api/ke-khai/{keKhaiId}/lao-dong/{laoDongId}`
- **Lấy danh sách lao động**: Sử dụng API `GET /api/ke-khai/{keKhaiId}/lao-dong`

### 2. <PERSON><PERSON><PERSON><PERSON> lý Tờ khai
- **Lấy chi tiết tờ khai**: Sử dụng API `GET /api/to-khai-602/{id}`
- **Cập nhật tờ khai**: Sử dụng API `PUT /api/to-khai-602/{id}`

## Services được sử dụng

### LaoDongApiService
Service mới được tạo để quản lý các API calls liên quan đến lao động:

```typescript
// Thêm lao động
themLaoDong(request: ThemLaoDongRequest): Observable<LaoDongApiResponse>

// Cập nhật lao động  
capNhatLaoDong(request: CapNhatLaoDongRequest): Observable<LaoDongApiResponse>

// Xóa lao động
xoaLaoDong(keKhaiId: number, laoDongId: number): Observable<LaoDongApiResponse>

// Lấy danh sách lao động
layDanhSachLaoDong(keKhaiId: number): Observable<LaoDongApiResponse<LaoDong[]>>
```

### ToKhai602Service
Service hiện có được sử dụng để quản lý tờ khai:

```typescript
// Lấy chi tiết tờ khai
layToKhaiTheoId(id: number): Observable<ToKhai602>

// Cập nhật tờ khai
capNhatToKhai(id: number, toKhai: Partial<ToKhai602>): Observable<ToKhai602>
```

## Luồng xử lý

### 1. Tải dữ liệu ban đầu
1. Component nhận ID tờ khai từ route parameter
2. Gọi `layToKhaiTheoId()` để lấy thông tin tờ khai
3. Gọi `layDanhSachLaoDong()` để lấy danh sách lao động từ backend

### 2. Thêm lao động mới
1. User điền form và submit
2. Component gọi `themLaoDong()` API
3. Nếu thành công, thêm vào danh sách local và chuyển sang tab danh sách
4. Nếu thất bại, hiển thị thông báo lỗi

### 3. Cập nhật lao động
1. User click chỉnh sửa từ bảng danh sách
2. Component load dữ liệu vào form
3. User submit form đã chỉnh sửa
4. Component gọi `capNhatLaoDong()` API
5. Cập nhật danh sách local nếu thành công

### 4. Xóa lao động
1. User click xóa từ bảng danh sách
2. Hiển thị dialog xác nhận
3. Component gọi `xoaLaoDong()` API
4. Xóa khỏi danh sách local nếu thành công

### 5. Lưu tờ khai
1. User click "Lưu tờ khai"
2. Component gọi `capNhatToKhai()` API với thông tin cơ bản
3. Danh sách lao động được quản lý riêng thông qua LaoDongApiService

## Xử lý lỗi

- Tất cả API calls đều có error handling
- Lỗi được hiển thị trong UI thông qua biến `loi`
- Loading states được quản lý thông qua `dangTai`, `dangLuu`, `dangXuLyLaoDong`

## Loading States

- `dangTai`: Hiển thị khi đang tải dữ liệu ban đầu
- `dangLuu`: Hiển thị khi đang lưu tờ khai
- `dangXuLyLaoDong`: Hiển thị khi đang thực hiện các thao tác với lao động

## Mapping dữ liệu

LaoDongApiService cung cấp các phương thức mapping:
- `mapLaoDongToRequest()`: Chuyển từ LaoDong model sang ThemLaoDongRequest
- `mapLaoDongToUpdateRequest()`: Chuyển từ LaoDong model sang CapNhatLaoDongRequest

## Cấu hình Backend

Đảm bảo backend có các endpoints sau:

```
GET    /api/to-khai-602/{id}                    - Lấy chi tiết tờ khai
PUT    /api/to-khai-602/{id}                    - Cập nhật tờ khai
GET    /api/ke-khai/{keKhaiId}/lao-dong         - Lấy danh sách lao động
POST   /api/ke-khai/{keKhaiId}/lao-dong         - Thêm lao động
PUT    /api/ke-khai/{keKhaiId}/lao-dong/{id}    - Cập nhật lao động
DELETE /api/ke-khai/{keKhaiId}/lao-dong/{id}    - Xóa lao động
```

## Lưu ý

1. Component sử dụng temporary IDs cho lao động mới (format: `temp_{timestamp}_{random}`)
2. Khi gọi backend API, cần parse ID thực từ temporary ID
3. Tất cả API responses được expect có format: `{ success: boolean, data?: any, message?: string }`
4. Component tự động refresh danh sách lao động sau mỗi thao tác thành công
