import { Injectable } from '@angular/core';
import { FormGroup, FormArray } from '@angular/forms';
import { Observable } from 'rxjs';
import { ToKhai602Service } from '../../../services';
import { LaoDong, ToKhaiFormState, ValidationError, TaoToKhaiRequest } from '../../../models';

/**
 * Service xử lý business logic cho component nhập lao động
 * Tách từ NhapLaoDongComponent để tuân thủ quy tắc 400 dòng
 */
@Injectable({
  providedIn: 'root'
})
export class NhapLaoDongBusinessService {

  constructor(
    private toKhai602Service: ToKhai602Service
  ) {}

  /**
   * Cập nhật form state với dữ liệu từ form hiện tại
   */
  capNhatFormStateVoiDuLieuHienTai(laoDongForm: FormGroup): void {
    const formArray = laoDongForm.get('danhSachLaoDong') as FormArray;
    const danhSachLaoDong: Partial<LaoDong>[] = [];

    for (let i = 0; i < formArray.length; i++) {
      const formGroup = formArray.at(i) as FormGroup;
      const laoDong = formGroup.value as Partial<LaoDong>;
      danhSachLaoDong.push(laoDong);
    }

    // Cập nhật state
    this.toKhai602Service.capNhatDanhSachLaoDong(danhSachLaoDong);
  }

  /**
   * Cập nhật form array dựa trên state
   */
  capNhatFormArray(laoDongForm: FormGroup, formState: ToKhaiFormState | null, taoLaoDongFormGroup: (laoDong: Partial<LaoDong>) => FormGroup): void {
    const formArray = laoDongForm.get('danhSachLaoDong') as FormArray;

    // Clear existing controls
    while (formArray.length !== 0) {
      formArray.removeAt(0);
    }

    // Add controls for each lao dong
    formState?.danhSachLaoDong.forEach((laoDong: Partial<LaoDong>) => {
      formArray.push(taoLaoDongFormGroup(laoDong));
    });
  }

  /**
   * Thêm lao động mới (flow cũ)
   */
  onThemLaoDong(): void {
    this.toKhai602Service.themLaoDongMoi();
  }

  /**
   * Xóa lao động (flow cũ)
   */
  onXoaLaoDongCu(index: number, danhSachLaoDongFormArray: FormArray): void {
    if (danhSachLaoDongFormArray.length > 1) {
      this.toKhai602Service.xoaLaoDong(index);
    }
  }

  /**
   * Cập nhật thông tin lao động khi form thay đổi
   */
  onLaoDongChange(index: number, danhSachLaoDongFormArray: FormArray): void {
    const formGroup = danhSachLaoDongFormArray.at(index) as FormGroup;
    const laoDong = formGroup.value as Partial<LaoDong>;
    this.toKhai602Service.capNhatLaoDong(index, laoDong);
  }

  /**
   * Gửi tờ khai (flow cũ)
   */
  onGuiToKhai(
    laoDongForm: FormGroup, 
    formState: ToKhaiFormState | null,
    onSuccess: () => void,
    onError: (error: any) => void
  ): void {
    // Validate form
    laoDongForm.markAllAsTouched();
    const validationErrors = this.toKhai602Service.validateForm();
    
    if (validationErrors.length > 0) {
      console.error('Form có lỗi:', validationErrors);
      return;
    }

    if (!formState?.daiLyDaChon || !formState?.donViDaChon) {
      console.error('Thiếu thông tin đại lý hoặc đơn vị');
      return;
    }

    // Tạo request
    const request: TaoToKhaiRequest = {
      daiLyId: formState.daiLyDaChon.id,
      donViId: formState.donViDaChon.id,
      thongTinKhac: formState.thongTinKhac as any,
      danhSachLaoDong: formState.danhSachLaoDong as LaoDong[]
    };

    // Gửi request
    this.toKhai602Service.taoToKhai(request).subscribe({
      next: (response: any) => {
        if (response.thanhCong) {
          alert('Tạo tờ khai thành công!');
          onSuccess();
        } else {
          alert('Có lỗi xảy ra: ' + response.thongBao);
        }
      },
      error: (error: any) => {
        console.error('Lỗi khi gửi tờ khai:', error);
        alert('Có lỗi xảy ra khi gửi tờ khai');
        onError(error);
      }
    });
  }

  /**
   * Kiểm tra thông tin lao động qua API
   */
  async onKiemTraThongTinLaoDong(
    index: number, 
    danhSachLaoDongFormArray: FormArray,
    kiemTraThongTinLaoDong: (formGroup: FormGroup) => Promise<void>
  ): Promise<void> {
    const formGroup = danhSachLaoDongFormArray.at(index) as FormGroup;
    await kiemTraThongTinLaoDong(formGroup);
    this.onLaoDongChange(index, danhSachLaoDongFormArray);
  }

  /**
   * Lấy form group của lao động theo index
   */
  getLaoDongFormGroup(index: number, danhSachLaoDongFormArray: FormArray): FormGroup {
    return danhSachLaoDongFormArray.at(index) as FormGroup;
  }

  /**
   * Kiểm tra field có lỗi không
   */
  hasError(
    index: number, 
    fieldName: string, 
    danhSachLaoDongFormArray: FormArray,
    hasErrorFn: (formGroup: FormGroup, fieldName: string) => boolean
  ): boolean {
    const formGroup = danhSachLaoDongFormArray.at(index) as FormGroup;
    return hasErrorFn(formGroup, fieldName);
  }

  /**
   * Lấy thông báo lỗi cho field
   */
  getErrorMessage(
    index: number, 
    fieldName: string, 
    danhSachLaoDongFormArray: FormArray,
    getErrorMessageFn: (formGroup: FormGroup, fieldName: string) => string
  ): string {
    const formGroup = danhSachLaoDongFormArray.at(index) as FormGroup;
    return getErrorMessageFn(formGroup, fieldName);
  }

  /**
   * Validate form state trước khi thực hiện action
   */
  validateFormState(formState: ToKhaiFormState | null): { valid: boolean, message: string } {
    if (!formState?.daiLyDaChon || !formState?.donViDaChon) {
      return { valid: false, message: 'Thiếu thông tin đại lý hoặc đơn vị' };
    }

    if (!formState.thongTinKhac.soSoBHXH) {
      return { valid: false, message: 'Vui lòng nhập số sổ BHXH' };
    }

    return { valid: true, message: '' };
  }

  /**
   * Kiểm tra có thể ghi nhận không
   */
  canGhiNhan(formState: ToKhaiFormState | null, laoDongForm: FormGroup): { canSubmit: boolean, message: string } {
    const formValidation = this.validateFormState(formState);
    if (!formValidation.valid) {
      return { canSubmit: false, message: formValidation.message };
    }

    if (!formState!.draftId) {
      return { canSubmit: false, message: 'Vui lòng tạo tờ khai trước khi ghi nhận' };
    }

    if (laoDongForm.invalid) {
      return { canSubmit: false, message: 'Vui lòng kiểm tra lại thông tin form' };
    }

    return { canSubmit: true, message: '' };
  }

  /**
   * Kiểm tra có thể lưu nháp không
   */
  canLuuNhap(formState: ToKhaiFormState | null): { canSave: boolean, message: string } {
    const formValidation = this.validateFormState(formState);
    if (!formValidation.valid) {
      return { canSave: false, message: formValidation.message };
    }

    return { canSave: true, message: '' };
  }

  /**
   * Format currency
   */
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(value);
  }

  /**
   * Lấy thông báo lỗi cho field - hỗ trợ cả edit mode và normal mode
   */
  getFieldErrorMessage(
    fieldName: string,
    laoDongForm: any,
    isEditMode: boolean,
    editLaoDongService: any
  ): string {
    if (isEditMode) {
      return editLaoDongService.getFieldErrorMessage(laoDongForm, fieldName);
    }

    // Logic cho normal mode
    const field = laoDongForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} là bắt buộc`;
      if (field.errors['pattern']) return `${fieldName} không đúng định dạng`;
      if (field.errors['minlength']) return `${fieldName} quá ngắn`;
      if (field.errors['min']) return `${fieldName} phải lớn hơn hoặc bằng ${field.errors['min'].min}`;
      if (field.errors['max']) return `${fieldName} phải nhỏ hơn hoặc bằng ${field.errors['max'].max}`;
      if (field.errors['email']) return 'Email không đúng định dạng';
    }
    return '';
  }
}
