<div class="lao-dong-form">

  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <!-- Thông tin cơ bản -->
    <div class="mb-6">
      <app-thong-tin-co-ban
        [parentForm]="form"
        [disabled]="disabled"
        (formChange)="onThongTinCoBanChange($event)"
        (bhxhLookupSuccess)="onBhxhLookupSuccess($event)">
      </app-thong-tin-co-ban>
    </div>

    <!-- Thông tin đóng BHXH -->
    <div class="mb-6">
      <app-thong-tin-dong-bhxh
        [parentForm]="form"
        [disabled]="disabled"
        (formChange)="onThongTinDongBhxhChange($event)"
        (bhxhLookupSuccess)="onBhxhLookupSuccess($event)">
      </app-thong-tin-dong-bhxh>
    </div>



    <!-- Actions -->
    <div class="flex justify-end space-x-3 bg-white border border-gray-200 rounded-lg p-4">
      <button
        type="button"
        (click)="huy()"
        [disabled]="disabled"
        class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
      >
        Hủy
      </button>
      <button
        type="submit"
        [disabled]="form.invalid || dangLuu || disabled"
        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
      >
        <span *ngIf="dangLuu || disabled">{{ disabled ? 'Đang xử lý...' : 'Đang lưu...' }}</span>
        <span *ngIf="!dangLuu && !disabled">{{ isEdit ? 'Cập nhật' : 'Thêm mới' }}</span>
      </button>
    </div>
  </form>
</div>
