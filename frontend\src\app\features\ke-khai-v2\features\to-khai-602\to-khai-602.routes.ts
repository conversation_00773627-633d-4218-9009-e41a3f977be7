import { Routes } from '@angular/router';

/**
 * Standalone routing cho feature tờ khai 602
 */
export const TO_KHAI_602_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./components/quan-ly/quan-ly.component')
      .then(m => m.QuanLyComponent),
    data: {
      breadcrumb: 'Quản lý',
      description: '<PERSON>uản lý danh sách tờ khai 602'
    }
  },
  {
    path: 'tao-moi',
    loadComponent: () => import('./components/tao-moi/tao-moi.component')
      .then(m => m.TaoMoiComponent),
    data: {
      breadcrumb: 'Tạo mới',
      description: 'Tạo tờ khai 602 mới'
    }
  },
  {
    path: 'chi-tiet/:id',
    loadComponent: () => import('./components/chi-tiet/chi-tiet.component')
      .then(m => m.default || m.ChiTiet<PERSON>omponent),
    data: {
      breadcrumb: '<PERSON> tiết',
      description: 'Xem chi tiết tờ khai 602'
    }
  },
  {
    path: 'chinh-sua/:id',
    loadComponent: () => import('./components/chinh-sua/chinh-sua.component')
      .then(m => m.ChinhSuaComponent),
    data: {
      breadcrumb: 'Chỉnh sửa',
      description: 'Chỉnh sửa tờ khai 602'
    }
  }
];
