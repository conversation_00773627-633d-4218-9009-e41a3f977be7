import { Injectable } from '@angular/core';
import { CanActivate, CanLoad, Router, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';

import { XacThucService } from '../services';

/**
 * Guard bảo vệ các routes cần xác thực
 * Chuyển hướng đến trang đăng nhập nếu chưa đăng nhập
 */
@Injectable({
  providedIn: 'root'
})
export class XacThucGuard implements CanActivate, CanLoad {

  constructor(
    private xacThucService: XacThucService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean | UrlTree> {
    return this.kiemTraXacThuc();
  }

  canLoad(): Observable<boolean | UrlTree> {
    return this.kiemTraXacThuc();
  }

  /**
   * Kiểm tra trạng thái xác thực
   */
  private kiemTraXacThuc(): Observable<boolean | UrlTree> {
    // Kiểm tra đồng bộ trước để tránh race condition
    const token = this.xacThucService.layToken();
    const nguoiDung = this.xacThucService.layThongTinNguoiDung();

    if (token && nguoiDung && this.xacThucService.kiemTraTokenConHan()) {
      return this.xacThucService.daDangNhap$.pipe(
        take(1),
        map(() => true)
      );
    }

    return this.xacThucService.daDangNhap$.pipe(
      take(1),
      map(daDangNhap => {
        if (daDangNhap) {
          return true;
        } else {
          // Chuyển hướng đến trang đăng nhập
          return this.router.createUrlTree(['/dang-nhap']);
        }
      })
    );
  }
}
