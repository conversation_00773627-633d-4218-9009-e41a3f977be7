namespace HuyPhuc.Application.Common.Models;

/// <summary>
/// Model response chung cho API
/// </summary>
public class ApiResponse
{
    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    public bool ThanhCong { get; set; }

    /// <summary>
    /// Thông báo
    /// </summary>
    public string ThongBao { get; set; } = string.Empty;

    /// <summary>
    /// Danh sách lỗi (nếu có)
    /// </summary>
    public string[]? Loi { get; set; }

    /// <summary>
    /// Tạo response thành công
    /// </summary>
    public static ApiResponse Success(string thongBao = "Thành công")
    {
        return new ApiResponse
        {
            ThanhCong = true,
            ThongBao = thongBao
        };
    }

    /// <summary>
    /// Tạo response thất bại
    /// </summary>
    public static ApiResponse Failure(string thongBao, params string[] loi)
    {
        return new ApiResponse
        {
            ThanhCong = false,
            ThongBao = thongBao,
            Loi = loi.Length > 0 ? loi : null
        };
    }
}

/// <summary>
/// Model response chung cho API với dữ liệu
/// </summary>
/// <typeparam name="T">Kiểu dữ liệu</typeparam>
public class ApiResponse<T> : ApiResponse
{
    /// <summary>
    /// Dữ liệu trả về
    /// </summary>
    public T? DuLieu { get; set; }

    /// <summary>
    /// Tạo response thành công với dữ liệu
    /// </summary>
    public static ApiResponse<T> Success(T duLieu, string thongBao = "Thành công")
    {
        return new ApiResponse<T>
        {
            ThanhCong = true,
            ThongBao = thongBao,
            DuLieu = duLieu
        };
    }

    /// <summary>
    /// Tạo response thất bại với dữ liệu
    /// </summary>
    public static new ApiResponse<T> Failure(string thongBao, params string[] loi)
    {
        return new ApiResponse<T>
        {
            ThanhCong = false,
            ThongBao = thongBao,
            Loi = loi.Length > 0 ? loi : null
        };
    }
}
