using System.Text.RegularExpressions;
using HuyPhuc.Domain.Exceptions;

namespace HuyPhuc.Domain.ValueObjects;

public class SoDienThoai : IEquatable<SoDienThoai>
{
    private static readonly Regex PhoneRegex = new(
        @"^(\+84|84|0)(3[2-9]|5[6|8|9]|7[0|6-9]|8[1-6|8|9]|9[0-4|6-9])[0-9]{7}$",
        RegexOptions.Compiled);

    public string Value { get; private set; }

    private SoDienThoai(string value)
    {
        Value = value;
    }

    public static SoDienThoai Tao(string soDienThoai)
    {
        if (string.IsNullOrWhiteSpace(soDienThoai))
            throw new DomainException("Số điện thoại không được để trống");

        soDienThoai = soDienThoai.Trim().Replace(" ", "").Replace("-", "");

        if (!PhoneRegex.IsMatch(soDienThoai))
            throw new DomainException("Định dạng số điện thoại không hợp lệ");

        // Chuẩn hóa về định dạng +84
        if (soDienThoai.StartsWith("0"))
            soDienThoai = "+84" + soDienThoai[1..];
        else if (soDienThoai.StartsWith("84"))
            soDienThoai = "+" + soDienThoai;
        else if (!soDienThoai.StartsWith("+84"))
            soDienThoai = "+84" + soDienThoai;

        return new SoDienThoai(soDienThoai);
    }

    public bool Equals(SoDienThoai? other)
    {
        return other is not null && Value == other.Value;
    }

    public override bool Equals(object? obj)
    {
        return Equals(obj as SoDienThoai);
    }

    public override int GetHashCode()
    {
        return Value.GetHashCode();
    }

    public override string ToString()
    {
        return Value;
    }

    public static implicit operator string(SoDienThoai soDienThoai)
    {
        return soDienThoai.Value;
    }
}
