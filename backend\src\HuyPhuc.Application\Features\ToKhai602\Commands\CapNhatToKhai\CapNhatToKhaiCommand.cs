using HuyPhuc.Application.DTOs.ToKhai602;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Entities.Base;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.ToKhai602.Commands.CapNhatToKhai;

/// <summary>
/// Command cập nhật tờ khai 602
/// </summary>
public record CapNhatToKhaiCommand(int Id, CapNhatToKhaiRequest Request) : IRequest<ToKhai602ChiTietDto>;

/// <summary>
/// Handler xử lý command cập nhật tờ khai 602
/// </summary>
public class CapNhatToKhaiCommandHandler : IRequestHandler<CapNhatToKhaiCommand, ToKhai602ChiTietDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CapNhatToKhaiCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<ToKhai602ChiTietDto> Handle(CapNhatToKhaiCommand command, CancellationToken cancellationToken)
    {
        var request = command.Request;
        var userIdString = _currentUserService.UserId;
        
        if (userIdString == null || !int.TryParse(userIdString, out var userId))
        {
            throw new UnauthorizedAccessException("Không thể xác định người dùng hiện tại");
        }

        // Tìm tờ khai cần cập nhật
        var toKhai = await _context.ToKhai602
            .Include(t => t.DaiLy)
            .Include(t => t.DonVi)
            .Include(t => t.DanhSachLaoDong)
            .FirstOrDefaultAsync(t => t.Id == command.Id, cancellationToken);

        if (toKhai == null)
        {
            throw new ArgumentException($"Không tìm thấy tờ khai với ID: {command.Id}");
        }

        // Kiểm tra quyền cập nhật (chỉ cho phép cập nhật tờ khai đang soạn)
        if (toKhai.TrangThai != TrangThaiToKhai.DangSoan)
        {
            throw new InvalidOperationException("Chỉ có thể cập nhật tờ khai đang ở trạng thái soạn thảo");
        }

        // Cập nhật thông tin cơ bản
        if (request.DaiLyId.HasValue && request.DaiLyId.Value != toKhai.DaiLyId)
        {
            var daiLy = await _context.DaiLy
                .FirstOrDefaultAsync(x => x.Id == request.DaiLyId.Value, cancellationToken);
            
            if (daiLy == null)
            {
                throw new ArgumentException($"Không tìm thấy đại lý với ID: {request.DaiLyId.Value}");
            }
            
            toKhai.DaiLyId = request.DaiLyId.Value;
        }

        if (request.DonViId.HasValue && request.DonViId.Value != toKhai.DonViId)
        {
            var donVi = await _context.DonVi
                .FirstOrDefaultAsync(x => x.Id == request.DonViId.Value && x.DaiLyId == toKhai.DaiLyId, cancellationToken);
            
            if (donVi == null)
            {
                throw new ArgumentException($"Không tìm thấy đơn vị với ID: {request.DonViId.Value} thuộc đại lý {toKhai.DaiLyId}");
            }
            
            toKhai.DonViId = request.DonViId.Value;
        }

        if (!string.IsNullOrEmpty(request.SoSoBHXH))
        {
            toKhai.SoSoBHXH = request.SoSoBHXH;
        }

        if (request.GhiChu != null)
        {
            toKhai.GhiChu = request.GhiChu;
        }

        // Cập nhật danh sách lao động nếu có
        if (request.DanhSachLaoDong != null)
        {
            // Xóa danh sách lao động cũ
            _context.LaoDongToKhai602.RemoveRange(toKhai.DanhSachLaoDong);

            // Thêm danh sách lao động mới
            foreach (var laoDongDto in request.DanhSachLaoDong)
            {
                var laoDong = new Domain.Entities.LaoDongToKhai602
                {
                    ToKhai602Id = toKhai.Id,
                    Stt = laoDongDto.Stt,
                    MaSoBHXH = laoDongDto.MaSoBHXH,
                    PhuongAn = laoDongDto.PhuongAn,
                    PhuongThuc = laoDongDto.PhuongThuc,
                    ThangBatDau = laoDongDto.ThangBatDau,
                    TienLai = laoDongDto.TienLai,
                    TienThua = laoDongDto.TienThua,
                    TienTuDong = laoDongDto.TienTuDong,
                    TongTien = laoDongDto.TongTien,
                    TienHoTro = laoDongDto.TienHoTro,
                    MucThuNhap = laoDongDto.MucThuNhap
                };

                toKhai.DanhSachLaoDong.Add(laoDong);
            }
        }

        // Cập nhật thông tin audit
        toKhai.NgayCapNhat = DateTime.UtcNow;
        ((IAuditableEntity)toKhai).NguoiCapNhat = _currentUserService.UserName;

        await _context.SaveChangesAsync(cancellationToken);

        // Reload để lấy thông tin mới nhất với includes
        toKhai = await _context.ToKhai602
            .Include(t => t.DaiLy)
            .Include(t => t.DonVi)
            .Include(t => t.DanhSachLaoDong)
                .ThenInclude(ld => ld.Tk1Ts)
            .FirstOrDefaultAsync(t => t.Id == toKhai.Id, cancellationToken);

        if (toKhai == null)
        {
            throw new ArgumentException($"Không tìm thấy tờ khai sau khi cập nhật");
        }

        return new ToKhai602ChiTietDto
        {
            Id = toKhai.Id,
            MaToKhai = toKhai.MaToKhai,
            DaiLyId = toKhai.DaiLyId,
            TenDaiLy = toKhai.DaiLy.TenDaiLy,
            DonViId = toKhai.DonViId,
            TenDonVi = toKhai.DonVi.TenDonVi,
            SoSoBHXH = toKhai.SoSoBHXH,
            KyKeKhai = null, // TODO: Implement if needed
            TrangThai = toKhai.TrangThai.ToString(),
            GhiChu = toKhai.GhiChu,
            NgayTao = toKhai.NgayTao,
            NgayCapNhat = toKhai.NgayCapNhat,
            NguoiTao = ((IAuditableEntity)toKhai).NguoiTao,
            NguoiCapNhat = ((IAuditableEntity)toKhai).NguoiCapNhat,
            DanhSachLaoDong = toKhai.DanhSachLaoDong.Select(ld => new LaoDongToKhaiDto
            {
                Id = ld.Id,
                Stt = ld.Stt,
                MaSoBHXH = ld.MaSoBHXH,
                // Thông tin từ Tk1Ts sẽ được load riêng nếu cần
                HoTen = ld.Tk1Ts?.HoTen ?? "",
                Ccns = ld.Tk1Ts?.Ccns ?? "",
                Cmnd = ld.Tk1Ts?.Cmnd ?? "",
                NgaySinh = ld.Tk1Ts?.NgaySinh ?? "",
                GioiTinh = ld.Tk1Ts?.GioiTinh ?? 1,
                QuocTich = ld.Tk1Ts?.QuocTich ?? "VN",
                DanToc = ld.Tk1Ts?.DanToc ?? "01",
                MaTinhKs = ld.Tk1Ts?.MaTinhKs ?? "",
                MaHuyenKs = ld.Tk1Ts?.MaHuyenKs ?? "",
                MaXaKs = ld.Tk1Ts?.MaXaKs ?? "",
                DienThoaiLh = ld.Tk1Ts?.DienThoaiLh ?? "",
                MaHoGiaDinh = ld.Tk1Ts?.MaHoGiaDinh ?? "",
                PhuongAn = ld.PhuongAn,
                PhuongThuc = ld.PhuongThuc,
                ThangBatDau = ld.ThangBatDau,
                TienLai = ld.TienLai,
                TienThua = ld.TienThua,
                TienTuDong = ld.TienTuDong,
                TongTien = ld.TongTien,
                TienHoTro = ld.TienHoTro,
                MucThuNhap = ld.MucThuNhap,
                TypeId = ld.Tk1Ts?.TypeId ?? "TM",
                IsThamGiaBb = ld.Tk1Ts?.IsThamGiaBb ?? false,
                IsTamHoanHD = ld.Tk1Ts?.IsTamHoanHd ?? false
            }).ToList(),
            TongSoLaoDong = toKhai.DanhSachLaoDong.Count,
            TongTienDong = toKhai.DanhSachLaoDong.Sum(ld => ld.TongTien)
        };
    }
}
