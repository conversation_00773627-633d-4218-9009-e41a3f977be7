using MediatR;

namespace HuyPhuc.Application.KeKhai.Commands.TaoKeKhai;

/// <summary>
/// Command tạo kê khai mới sử dụng bảng danh_sach_ke_khai
/// </summary>
public class TaoKeKhaiCommand : IRequest<TaoKeKhaiResponse>
{
    public TaoKeKhaiRequest Request { get; }

    public TaoKeKhaiCommand(TaoKeKhaiRequest request)
    {
        Request = request;
    }
}

/// <summary>
/// Request tạo kê khai
/// </summary>
public class TaoKeKhaiRequest
{
    public int ThuTucId { get; set; }
    public int DaiLyId { get; set; }
    public int DonViId { get; set; }
    public string SoSoBHXH { get; set; } = "1";
    public Dictionary<string, object>? ThongTinHeader { get; set; }
    public string? GhiChu { get; set; }
}

/// <summary>
/// Response tạo kê khai
/// </summary>
public class TaoKeKhaiResponse
{
    public int KeKhaiId { get; set; }
    public string <PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; } = string.Empty;
    public DateTime NgayTao { get; set; }
    public string TrangThai { get; set; } = string.Empty;
    public ThuTucDto ThuTuc { get; set; } = null!;
    public DaiLyDto DaiLy { get; set; } = null!;
    public DonViDto DonVi { get; set; } = null!;
}

/// <summary>
/// DTO thủ tục
/// </summary>
public class ThuTucDto
{
    public int Id { get; set; }
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
    public string? MoTa { get; set; }
}

/// <summary>
/// DTO đại lý
/// </summary>
public class DaiLyDto
{
    public int Id { get; set; }
    public string MaDaiLy { get; set; } = string.Empty;
    public string TenDaiLy { get; set; } = string.Empty;
}

/// <summary>
/// DTO đơn vị
/// </summary>
public class DonViDto
{
    public int Id { get; set; }
    public string MaDonVi { get; set; } = string.Empty;
    public string TenDonVi { get; set; } = string.Empty;
}
