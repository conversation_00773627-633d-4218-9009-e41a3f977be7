<div class="khai-bao-to-khai-container max-w-6xl mx-auto p-6">
  <!-- Header -->
  <div class="header-section mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">
      <PERSON><PERSON> b<PERSON><PERSON> Tờ khai 602
    </h1>
    <p class="text-gray-600">
      Tạo tờ khai đóng bảo hiểm xã hội cho lao động tự do
    </p>
  </div>

  <!-- Form Container -->
  <div class="form-container bg-white rounded-lg shadow-md p-6">
    <form [formGroup]="khaiBaoForm" (ngSubmit)="onTaoKeKhai()">
      
      <!-- Phần 1: Chọn đại lý và đơn vị -->
      <div class="section-1 mb-8">
        <h2 class="section-title text-xl font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200">
          <svg class="w-5 h-5 inline mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
          Thông tin đại lý và đơn vị
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Dropdown Đại lý -->
          <div class="form-group">
            <label for="daiLyId" class="block text-sm font-medium text-gray-700 mb-2">
              Đại lý <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <select 
                id="daiLyId"
                formControlName="daiLyId"
                class="form-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                [class.border-red-500]="hasError('daiLyId')"
                [disabled]="dangTai">
                <option value="">-- Chọn đại lý --</option>
                <option 
                  *ngFor="let option of daiLyOptions" 
                  [value]="option.value"
                  [disabled]="option.disabled">
                  {{ option.label }}
                </option>
              </select>
              
              <!-- Loading spinner -->
              <div *ngIf="dangTai" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                <svg class="animate-spin h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            </div>
            
            <!-- Error message -->
            <div *ngIf="hasError('daiLyId')" class="mt-1 text-sm text-red-600">
              {{ getErrorMessage('daiLyId') }}
            </div>
          </div>

          <!-- Dropdown Đơn vị -->
          <div class="form-group">
            <label for="donViId" class="block text-sm font-medium text-gray-700 mb-2">
              Đơn vị <span class="text-red-500">*</span>
            </label>
            <div class="relative">
              <select 
                id="donViId"
                formControlName="donViId"
                class="form-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                [class.border-red-500]="hasError('donViId')"
                [disabled]="!daiLyDangChon || dangTai">
                <option value="">-- Chọn đơn vị --</option>
                <option 
                  *ngFor="let option of donViOptions" 
                  [value]="option.value"
                  [disabled]="option.disabled">
                  {{ option.label }}
                </option>
              </select>
            </div>
            
            <!-- Error message -->
            <div *ngIf="hasError('donViId')" class="mt-1 text-sm text-red-600">
              {{ getErrorMessage('donViId') }}
            </div>
            
            <!-- Helper text -->
            <div *ngIf="!daiLyDangChon" class="mt-1 text-sm text-gray-500">
              Vui lòng chọn đại lý trước
            </div>
          </div>
        </div>

        <!-- Removed the selected-info divs for both agency and unit -->
      </div>

      <!-- Phần 2: Thông tin khác -->
      <div class="section-2 mb-8">
        <h2 class="section-title text-xl font-semibold text-gray-800 mb-4 pb-2 border-b border-gray-200">
          <svg class="w-5 h-5 inline mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Thông tin khác
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Kỳ kê khai -->
          <div class="form-group">
            <label for="kyKeKhai" class="block text-sm font-medium text-gray-700 mb-2">
              Kỳ kê khai <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="kyKeKhai"
              formControlName="kyKeKhai"
              class="form-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              [class.border-red-500]="hasError('kyKeKhai')"
              placeholder="MM/yyyy (VD: 01/2024)">

            <!-- Error message -->
            <div *ngIf="hasError('kyKeKhai')" class="mt-1 text-sm text-red-600">
              {{ getErrorMessage('kyKeKhai') }}
            </div>
          </div>

          <!-- Loại biến động -->
          <div class="form-group">
            <label for="loaiBienDong" class="block text-sm font-medium text-gray-700 mb-2">
              Loại biến động <span class="text-red-500">*</span>
            </label>
            <select
              id="loaiBienDong"
              formControlName="loaiBienDong"
              class="form-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              [class.border-red-500]="hasError('loaiBienDong')">
              <option value="tang_moi">Tăng mới</option>
              <option value="dieu_chinh">Điều chỉnh</option>
            </select>

            <!-- Error message -->
            <div *ngIf="hasError('loaiBienDong')" class="mt-1 text-sm text-red-600">
              {{ getErrorMessage('loaiBienDong') }}
            </div>
          </div>

          <!-- Số sổ BHXH -->
          <div class="form-group">
            <label for="soSoBHXH" class="block text-sm font-medium text-gray-700 mb-2">
              Số sổ BHXH <span class="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="soSoBHXH"
              formControlName="soSoBHXH"
              class="form-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              [class.border-red-500]="hasError('soSoBHXH')"
              placeholder="Nhập số sổ BHXH">

            <!-- Error message -->
            <div *ngIf="hasError('soSoBHXH')" class="mt-1 text-sm text-red-600">
              {{ getErrorMessage('soSoBHXH') }}
            </div>
          </div>

          <!-- Ghi chú -->
          <div class="form-group">
            <label for="ghiChu" class="block text-sm font-medium text-gray-700 mb-2">
              Ghi chú
            </label>
            <input
              type="text"
              id="ghiChu"
              formControlName="ghiChu"
              class="form-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Nhập ghi chú (tùy chọn)">
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons flex flex-col sm:flex-row gap-4 justify-end pt-6 border-t border-gray-200">
        <button 
          type="button"
          (click)="onHuyBo()"
          class="btn-secondary px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          Hủy bỏ
        </button>
        
        <button
          type="submit"
          [disabled]="!formHopLe || dangTai || dangTaoKeKhai"
          class="btn-primary px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">

          <!-- Loading spinner khi đang tạo kê khai -->
          <svg *ngIf="dangTaoKeKhai" class="animate-spin w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>

          <!-- Icon bình thường -->
          <svg *ngIf="!dangTaoKeKhai" class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>

          {{ dangTaoKeKhai ? 'Đang tạo...' : 'Tạo kê khai' }}
        </button>
      </div>
    </form>
  </div>
</div>
