namespace HuyPhuc.Application.DTOs.ToKhai602;

/// <summary>
/// Response DTO cho việc tạo tờ khai 602 mới
/// </summary>
public class TaoToKhaiResponse
{
    /// <summary>
    /// ID của tờ khai
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Mã tờ khai
    /// </summary>
    public string MaToKhai { get; set; } = string.Empty;

    /// <summary>
    /// ID đại lý
    /// </summary>
    public int DaiLyId { get; set; }

    /// <summary>
    /// ID đơn vị
    /// </summary>
    public int DonViId { get; set; }

    /// <summary>
    /// Số sổ BHXH
    /// </summary>
    public string SoSoBHXH { get; set; } = string.Empty;

    /// <summary>
    /// Ghi chú
    /// </summary>
    public string? GhiChu { get; set; }

    /// <summary>
    /// Trạng thái
    /// </summary>
    public int TrangThai { get; set; }

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime NgayTao { get; set; }

    /// <summary>
    /// ID người tạo
    /// </summary>
    public int NguoiTaoId { get; set; }

    /// <summary>
    /// Thông tin đại lý
    /// </summary>
    public DaiLyInfo DaiLy { get; set; } = new();

    /// <summary>
    /// Thông tin đơn vị
    /// </summary>
    public DonViInfo DonVi { get; set; } = new();
}
