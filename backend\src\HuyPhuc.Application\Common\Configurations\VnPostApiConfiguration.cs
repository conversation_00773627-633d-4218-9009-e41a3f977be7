namespace HuyPhuc.Application.Common.Configurations;

/// <summary>
/// Configuration cho VNPost API
/// </summary>
public class VnPostApiConfiguration
{
    public const string SectionName = "VnPostApi";

    public string BaseUrl { get; set; } = string.Empty;
    public string BhxhLookupEndpoint { get; set; } = string.Empty;
    public string SecretId { get; set; } = string.Empty;
    public string SecretKey { get; set; } = string.Empty;
    public string AccessToken { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 30;
    public string Referer { get; set; } = string.Empty;
    public string UserAgent { get; set; } = string.Empty;

    /// <summary>
    /// URL đầy đủ cho API tra cứu BHXH
    /// </summary>
    public string FullBhxhLookupUrl => $"{BaseUrl.TrimEnd('/')}{BhxhLookupEndpoint}";
}
