import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

/**
 * Component hiển thị thông báo "Sắp ra mắt"
 */
@Component({
  selector: 'app-coming-soon',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="coming-soon-container">
      <div class="text-center py-16">
        <!-- Icon -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-6">
          <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        
        <!-- Title -->
        <h2 class="text-2xl font-bold text-gray-900 mb-4">
          Tính năng đang phát triển
        </h2>
        
        <!-- Description -->
        <p class="text-gray-600 mb-8 max-w-md mx-auto">
          Chúng tôi đang phát triển tính năng này và sẽ sớm ra mắt trong thời gian tới. 
          Cảm ơn bạn đã kiên nhẫn chờ đợi!
        </p>
        
        <!-- Features list -->
        <div class="bg-gray-50 rounded-lg p-6 max-w-lg mx-auto">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Tính năng sắp có:</h3>
          <ul class="text-left space-y-2 text-gray-600">
            <li class="flex items-center">
              <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              Kê khai điều chỉnh BHXH
            </li>
            <li class="flex items-center">
              <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              Báo cáo thống kê chi tiết
            </li>
            <li class="flex items-center">
              <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              Xuất file Excel/PDF
            </li>
            <li class="flex items-center">
              <svg class="h-5 w-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
              Tích hợp chữ ký số
            </li>
          </ul>
        </div>
        
        <!-- Contact info -->
        <div class="mt-8">
          <p class="text-sm text-gray-500">
            Có thắc mắc? Liên hệ với chúng tôi tại 
            <a href="mailto:support&#64;example.com" class="text-blue-600 hover:text-blue-500">
              support&#64;example.com
            </a>
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .coming-soon-container {
      min-height: 24rem;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1.5rem;
    }

    .coming-soon-container svg {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
  `]
})
export class ComingSoonComponent {
  // Component logic có thể thêm sau nếu cần
}
