using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories.Base;

namespace HuyPhuc.Domain.Repositories;

/// <summary>
/// Repository interface cho DmHuyen
/// </summary>
public interface IDmHuyenRepository : IRepository<DmHuyen>
{
    /// <summary>
    /// Lấy tất cả huyện/quận
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Danh sách huyện/quận</returns>
    Task<List<DmHuyen>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy danh sách huyện theo mã tỉnh
    /// </summary>
    /// <param name="maTinh">Mã tỉnh</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Danh sách huyện thuộc tỉnh</returns>
    Task<List<DmHuyen>> GetByMaTinhAsync(string maTinh, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy huyện theo mã huyện
    /// </summary>
    /// <param name="maHuyen">Mã huyện</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Thông tin huyện</returns>
    Task<DmHuyen?> GetByMaHuyenAsync(string maHuyen, CancellationToken cancellationToken = default);

    /// <summary>
    /// Kiểm tra mã huyện có tồn tại không
    /// </summary>
    /// <param name="maHuyen">Mã huyện</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True nếu tồn tại</returns>
    Task<bool> ExistsByMaHuyenAsync(string maHuyen, CancellationToken cancellationToken = default);
}
