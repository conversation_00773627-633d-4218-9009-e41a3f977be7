using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Events;
using HuyPhuc.Domain.Exceptions;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity danh mục thủ tục hành chính
/// </summary>
public class DanhMucThuTuc : BaseEntity, IAuditableEntity
{
    /// <summary>
    /// Mã thủ tục (unique)
    /// </summary>
    public string Ma { get; private set; } = string.Empty;

    /// <summary>
    /// Tên thủ tục
    /// </summary>
    public string Ten { get; private set; } = string.Empty;

    /// <summary>
    /// Lĩnh vực thủ tục
    /// </summary>
    public LinhVucThuTuc LinhVuc { get; private set; }

    /// <summary>
    /// Tên lĩnh vực (denormalized cho performance)
    /// </summary>
    public string TenLinhVuc { get; private set; } = string.Empty;

    /// <summary>
    /// Ngày áp dụng thủ tục
    /// </summary>
    public DateTime? NgayApDung { get; private set; }

    /// <summary>
    /// Trạng thái thủ tục
    /// </summary>
    public TrangThaiThuTuc TrangThai { get; private set; }

    /// <summary>
    /// Mô tả chi tiết thủ tục
    /// </summary>
    public string? MoTa { get; private set; }

    /// <summary>
    /// Thời gian xử lý (số ngày)
    /// </summary>
    public int? ThoiGianXuLy { get; private set; }

    /// <summary>
    /// Phí thực hiện thủ tục
    /// </summary>
    public decimal? PhiThucHien { get; private set; }

    /// <summary>
    /// Cơ quan thực hiện
    /// </summary>
    public string? CoQuanThucHien { get; private set; }

    /// <summary>
    /// Căn cứ pháp lý
    /// </summary>
    public string? CanCuPhapLy { get; private set; }

    private DanhMucThuTuc() { } // EF Core constructor

    /// <summary>
    /// Tạo danh mục thủ tục mới
    /// </summary>
    public static DanhMucThuTuc Tao(
        string ma,
        string ten,
        LinhVucThuTuc linhVuc,
        string tenLinhVuc,
        DateTime? ngayApDung = null,
        string? moTa = null,
        int? thoiGianXuLy = null,
        decimal? phiThucHien = null,
        string? coQuanThucHien = null,
        string? canCuPhapLy = null)
    {
        if (string.IsNullOrWhiteSpace(ma))
            throw new DomainException("Mã thủ tục không được để trống");

        if (string.IsNullOrWhiteSpace(ten))
            throw new DomainException("Tên thủ tục không được để trống");

        if (string.IsNullOrWhiteSpace(tenLinhVuc))
            throw new DomainException("Tên lĩnh vực không được để trống");

        if (ma.Length > 50)
            throw new DomainException("Mã thủ tục không được vượt quá 50 ký tự");

        if (ten.Length > 1000)
            throw new DomainException("Tên thủ tục không được vượt quá 1000 ký tự");

        if (thoiGianXuLy.HasValue && thoiGianXuLy <= 0)
            throw new DomainException("Thời gian xử lý phải lớn hơn 0");

        if (phiThucHien.HasValue && phiThucHien < 0)
            throw new DomainException("Phí thực hiện không được âm");

        var thuTuc = new DanhMucThuTuc
        {
            Ma = ma.Trim().ToUpper(),
            Ten = ten.Trim(),
            LinhVuc = linhVuc,
            TenLinhVuc = tenLinhVuc.Trim(),
            NgayApDung = ngayApDung,
            TrangThai = TrangThaiThuTuc.HoatDong,
            MoTa = moTa?.Trim(),
            ThoiGianXuLy = thoiGianXuLy,
            PhiThucHien = phiThucHien,
            CoQuanThucHien = coQuanThucHien?.Trim(),
            CanCuPhapLy = canCuPhapLy?.Trim()
        };

        thuTuc.ThemSuKien(new DanhMucThuTucDaTaoEvent(thuTuc));
        return thuTuc;
    }

    /// <summary>
    /// Cập nhật thông tin thủ tục
    /// </summary>
    public void CapNhat(
        string ten,
        LinhVucThuTuc linhVuc,
        string tenLinhVuc,
        DateTime? ngayApDung = null,
        string? moTa = null,
        int? thoiGianXuLy = null,
        decimal? phiThucHien = null,
        string? coQuanThucHien = null,
        string? canCuPhapLy = null)
    {
        if (string.IsNullOrWhiteSpace(ten))
            throw new DomainException("Tên thủ tục không được để trống");

        if (string.IsNullOrWhiteSpace(tenLinhVuc))
            throw new DomainException("Tên lĩnh vực không được để trống");

        if (ten.Length > 1000)
            throw new DomainException("Tên thủ tục không được vượt quá 1000 ký tự");

        if (thoiGianXuLy.HasValue && thoiGianXuLy <= 0)
            throw new DomainException("Thời gian xử lý phải lớn hơn 0");

        if (phiThucHien.HasValue && phiThucHien < 0)
            throw new DomainException("Phí thực hiện không được âm");

        Ten = ten.Trim();
        LinhVuc = linhVuc;
        TenLinhVuc = tenLinhVuc.Trim();
        NgayApDung = ngayApDung;
        MoTa = moTa?.Trim();
        ThoiGianXuLy = thoiGianXuLy;
        PhiThucHien = phiThucHien;
        CoQuanThucHien = coQuanThucHien?.Trim();
        CanCuPhapLy = canCuPhapLy?.Trim();

        ThemSuKien(new DanhMucThuTucDaCapNhatEvent(this));
    }

    /// <summary>
    /// Kích hoạt thủ tục
    /// </summary>
    public void KichHoat()
    {
        if (TrangThai == TrangThaiThuTuc.HoatDong)
            return;

        TrangThai = TrangThaiThuTuc.HoatDong;
        ThemSuKien(new DanhMucThuTucDaKichHoatEvent(this));
    }

    /// <summary>
    /// Tạm ngưng thủ tục
    /// </summary>
    public void TamNgung()
    {
        if (TrangThai == TrangThaiThuTuc.TamNgung)
            return;

        TrangThai = TrangThaiThuTuc.TamNgung;
        ThemSuKien(new DanhMucThuTucDaTamNgungEvent(this));
    }

    /// <summary>
    /// Ngừng hoạt động thủ tục
    /// </summary>
    public void NgungHoatDong()
    {
        if (TrangThai == TrangThaiThuTuc.NgungHoatDong)
            return;

        TrangThai = TrangThaiThuTuc.NgungHoatDong;
        ThemSuKien(new DanhMucThuTucDaNgungHoatDongEvent(this));
    }

    /// <summary>
    /// Kiểm tra thủ tục có đang hoạt động không
    /// </summary>
    public bool DangHoatDong => TrangThai == TrangThaiThuTuc.HoatDong;

    /// <summary>
    /// Kiểm tra thủ tục có hiệu lực không (đã đến ngày áp dụng)
    /// </summary>
    public bool CoHieuLuc => !NgayApDung.HasValue || NgayApDung <= DateTime.UtcNow;

    // IAuditableEntity implementation
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }
}
