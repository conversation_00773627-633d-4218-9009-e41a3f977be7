using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class DonViConfiguration : IEntityTypeConfiguration<DonVi>
{
    public void Configure(EntityTypeBuilder<DonVi> builder)
    {
        builder.ToTable("don_vi");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.MaDonVi)
            .HasColumnName("ma_don_vi")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.TenDonVi)
            .HasColumnName("ten_don_vi")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(x => x.DiaChi)
            .HasColumnName("dia_chi")
            .HasColumnType("text");

        builder.Property(x => x.SoDienThoai)
            .HasColumnName("so_dien_thoai")
            .HasMaxLength(20);

        builder.Property(x => x.Email)
            .HasColumnName("email")
            .HasMaxLength(255);

        builder.Property(x => x.DaiLyId)
            .HasColumnName("dai_ly_id")
            .IsRequired();

        builder.Property(x => x.TrangThaiHoatDong)
            .HasColumnName("trang_thai_hoat_dong")
            .HasDefaultValue(true);

        builder.Property(x => x.GhiChu)
            .HasColumnName("ghi_chu")
            .HasColumnType("text");

        // Audit fields
        builder.Property(x => x.NgayTao)
            .HasColumnName("created_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(x => x.NgayCapNhat)
            .HasColumnName("updated_at");

        builder.Property(x => x.NguoiTao)
            .HasColumnName("created_by")
            .HasMaxLength(255);

        builder.Property(x => x.NguoiCapNhat)
            .HasColumnName("updated_by")
            .HasMaxLength(255);

        // Indexes
        builder.HasIndex(x => x.DaiLyId)
            .HasDatabaseName("idx_don_vi_dai_ly_id");

        builder.HasIndex(x => x.MaDonVi)
            .HasDatabaseName("idx_don_vi_ma_don_vi");

        builder.HasIndex(x => x.TrangThaiHoatDong)
            .HasDatabaseName("idx_don_vi_trang_thai");

        // Unique constraint cho mã đơn vị trong cùng đại lý
        builder.HasIndex(x => new { x.MaDonVi, x.DaiLyId })
            .IsUnique()
            .HasDatabaseName("uk_don_vi_ma_dai_ly");

        // Relationships
        builder.HasOne(x => x.DaiLy)
            .WithMany(x => x.DanhSachDonVi)
            .HasForeignKey(x => x.DaiLyId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
