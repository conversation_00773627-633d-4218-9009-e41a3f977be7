using HuyPhuc.Application.Common.Models;
using HuyPhuc.Application.DTOs.DiaChi;
using HuyPhuc.Domain.Repositories;
using MediatR;

namespace HuyPhuc.Application.Features.DiaChi.Queries.GetHuyenByTinh;

/// <summary>
/// Query để lấy danh sách huyện theo mã tỉnh
/// </summary>
public record GetHuyenByTinhQuery(string MaTinh) : IRequest<Result<List<HuyenOptionDto>>>;

/// <summary>
/// Handler cho GetHuyenByTinhQuery
/// </summary>
public class GetHuyenByTinhQueryHandler : IRequestHandler<GetHuyenByTinhQuery, Result<List<HuyenOptionDto>>>
{
    private readonly IDmHuyenRepository _huyenRepository;

    public GetHuyenByTinhQueryHandler(IDmHuyenRepository huyenRepository)
    {
        _huyenRepository = huyenRepository;
    }

    public async Task<Result<List<HuyenOptionDto>>> Handle(GetHuyenByTinhQuery request, CancellationToken cancellationToken)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.MaTinh))
            {
                return Result<List<HuyenOptionDto>>.Failure("Mã tỉnh không được để trống");
            }

            var huyenList = await _huyenRepository.GetByMaTinhAsync(request.MaTinh, cancellationToken);

            var result = huyenList.Select(h => new HuyenOptionDto
            {
                Value = h.MaHuyen,
                Text = h.TextDisplay,
                Ten = h.TenHuyen,
                Ma = null
            }).ToList();

            return Result<List<HuyenOptionDto>>.Success(result);
        }
        catch (Exception ex)
        {
            return Result<List<HuyenOptionDto>>.Failure($"Lỗi khi lấy danh sách huyện: {ex.Message}");
        }
    }
}
