namespace HuyPhuc.Application.DTOs.DaiLy;

/// <summary>
/// DTO cho thông tin đại lý
/// </summary>
public class DaiLyDto
{
    public int Id { get; set; }
    public string MaDaiLy { get; set; } = string.Empty;
    public string TenDaiLy { get; set; } = string.Empty;
    public string? Dia<PERSON>hi { get; set; }
    public string? SoDienThoai { get; set; }
    public string? Email { get; set; }
    public string? MaSoThue { get; set; }
    public string? NguoiDaiDien { get; set; }
    public bool TrangThaiHoatDong { get; set; }
    public string? GhiChu { get; set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }
}

/// <summary>
/// DTO đơn giản cho dropdown/select
/// </summary>
public class DaiLySelectDto
{
    public int Id { get; set; }
    public string MaDaiLy { get; set; } = string.Empty;
    public string TenDaiLy { get; set; } = string.Empty;
    public string? Dia<PERSON>hi { get; set; }
    public string? SoDienThoai { get; set; }
    public string? Email { get; set; }
    public bool TrangThaiHoatDong { get; set; }
}

/// <summary>
/// DTO cho tạo mới đại lý
/// </summary>
public class TaoDaiLyDto
{
    public string MaDaiLy { get; set; } = string.Empty;
    public string TenDaiLy { get; set; } = string.Empty;
    public string? DiaChi { get; set; }
    public string? SoDienThoai { get; set; }
    public string? Email { get; set; }
    public string? MaSoThue { get; set; }
    public string? NguoiDaiDien { get; set; }
    public string? GhiChu { get; set; }
}

/// <summary>
/// DTO cho cập nhật đại lý
/// </summary>
public class CapNhatDaiLyDto
{
    public string TenDaiLy { get; set; } = string.Empty;
    public string? DiaChi { get; set; }
    public string? SoDienThoai { get; set; }
    public string? Email { get; set; }
    public string? MaSoThue { get; set; }
    public string? NguoiDaiDien { get; set; }
    public string? GhiChu { get; set; }
}
