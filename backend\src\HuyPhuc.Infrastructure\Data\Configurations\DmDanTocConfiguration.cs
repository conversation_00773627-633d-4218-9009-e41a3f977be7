using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration cho DmDanToc
/// </summary>
public class DmDanTocConfiguration : IEntityTypeConfiguration<DmDanToc>
{
    public void Configure(EntityTypeBuilder<DmDanToc> builder)
    {
        // Table name
        builder.ToTable("dm_dan_toc");

        // Primary key
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(e => e.Ma)
            .HasColumnName("ma")
            .HasMaxLength(10)
            .IsRequired();

        builder.Property(e => e.Ten)
            .HasColumnName("ten")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.MaVaTen)
            .HasColumnName("ma_va_ten")
            .HasMaxLength(150)
            .IsRequired();

        builder.Property(e => e.CanCu)
            .HasColumnName("can_cu")
            .HasMaxLength(255);

        builder.Property(e => e.Rownum)
            .HasColumnName("rownum")
            .HasColumnType("decimal(10,1)");

        // Audit fields
        builder.Property(e => e.Created)
            .HasColumnName("created_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(e => e.CreatedBy)
            .HasColumnName("created_by")
            .HasMaxLength(100);

        builder.Property(e => e.LastModified)
            .HasColumnName("updated_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(e => e.LastModifiedBy)
            .HasColumnName("updated_by")
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(e => e.Ma)
            .IsUnique()
            .HasDatabaseName("ix_dm_dan_toc_ma");

        builder.HasIndex(e => e.Rownum)
            .HasDatabaseName("ix_dm_dan_toc_rownum");
    }
}
