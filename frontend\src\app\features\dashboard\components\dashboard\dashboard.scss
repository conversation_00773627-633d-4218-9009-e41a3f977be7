// Dashboard BHYT & BHXH Styles
.dashboard-bhyt-bhxh {
  .page-header {
    h1 {
      background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .nut-tao-moi {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .stat-card {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .text-3xl {
      font-variant-numeric: tabular-nums;
    }
  }

  .dashboard-content {
    animation: fadeInUp 0.6s ease-out;
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Loading spinner
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-bhyt-bhxh {
    .page-header {
      .flex {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }
    }

    .stat-card {
      .text-3xl {
        font-size: 1.875rem;
      }
    }
  }
}