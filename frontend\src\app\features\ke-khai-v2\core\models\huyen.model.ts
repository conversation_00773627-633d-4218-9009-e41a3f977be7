/**
 * Model cho thông tin huyện/quận
 */
export interface Huyen {
  id: number;
  maHuyen: string;
  tenHuyen: string;
  textDisplay: string;
  maTinh: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Response từ API khi lấy danh sách huyện
 */
export interface HuyenResponse {
  data: Huyen[];
  success: boolean;
  message?: string;
  errors?: any;
  status: number;
  traceId?: string;
}

/**
 * Option cho dropdown huyện
 */
export interface HuyenOption {
  value: string;
  text: string;
  ten: string;
  maTinh: string;
}
