using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Events;
using HuyPhuc.Domain.Exceptions;

namespace HuyPhuc.Domain.Entities;

public class DonHang : BaseEntity, IAuditableEntity
{
    public string MaDonHang { get; private set; } = string.Empty;
    public int NguoiDungId { get; private set; }
    public decimal TongGiaTri { get; private set; }
    public decimal PhiVanChuyen { get; private set; }
    public decimal TongThanhToan { get; private set; }
    public TrangThaiDonHang TrangThai { get; private set; }
    public string? GhiChu { get; private set; }
    public DateTime NgayDatHang { get; private set; }
    public DateTime? NgayGiaoHang { get; private set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    // Navigation properties
    public virtual NguoiDung NguoiDung { get; private set; } = null!;
    public virtual ICollection<ChiTietDonHang> DanhSachChiTiet { get; private set; } = new List<ChiTietDonHang>();

    private DonHang() { } // EF Core constructor

    public static DonHang Tao(int nguoiDungId, decimal phiVanChuyen = 0, string? ghiChu = null)
    {
        var maDonHang = TaoMaDonHang();
        
        var donHang = new DonHang
        {
            MaDonHang = maDonHang,
            NguoiDungId = nguoiDungId,
            PhiVanChuyen = phiVanChuyen >= 0 ? phiVanChuyen : 0,
            TrangThai = TrangThaiDonHang.ChoDuyet,
            GhiChu = ghiChu?.Trim(),
            NgayDatHang = DateTime.UtcNow,
            TongGiaTri = 0,
            TongThanhToan = phiVanChuyen >= 0 ? phiVanChuyen : 0
        };

        donHang.ThemSuKien(new DonHangDaTaoEvent(donHang));
        return donHang;
    }

    public void ThemSanPham(SanPham sanPham, int soLuong, decimal giaBan)
    {
        if (TrangThai != TrangThaiDonHang.ChoDuyet)
            throw new BusinessRuleException("DON_HANG_KHONG_THE_SUA", "Không thể sửa đơn hàng đã được duyệt");

        if (!sanPham.CoTheBan(soLuong))
            throw new BusinessRuleException("SAN_PHAM_KHONG_THE_BAN", "Sản phẩm không thể bán hoặc không đủ số lượng");

        var chiTietTonTai = DanhSachChiTiet.FirstOrDefault(x => x.SanPhamId == sanPham.Id);
        
        if (chiTietTonTai != null)
        {
            chiTietTonTai.CapNhatSoLuong(chiTietTonTai.SoLuong + soLuong);
        }
        else
        {
            var chiTietMoi = ChiTietDonHang.Tao(Id, sanPham.Id, soLuong, giaBan);
            DanhSachChiTiet.Add(chiTietMoi);
        }

        TinhLaiTongGiaTri();
    }

    public void XoaSanPham(int sanPhamId)
    {
        if (TrangThai != TrangThaiDonHang.ChoDuyet)
            throw new BusinessRuleException("DON_HANG_KHONG_THE_SUA", "Không thể sửa đơn hàng đã được duyệt");

        var chiTiet = DanhSachChiTiet.FirstOrDefault(x => x.SanPhamId == sanPhamId);
        if (chiTiet != null)
        {
            DanhSachChiTiet.Remove(chiTiet);
            TinhLaiTongGiaTri();
        }
    }

    public void CapNhatSoLuongSanPham(int sanPhamId, int soLuongMoi)
    {
        if (TrangThai != TrangThaiDonHang.ChoDuyet)
            throw new BusinessRuleException("DON_HANG_KHONG_THE_SUA", "Không thể sửa đơn hàng đã được duyệt");

        var chiTiet = DanhSachChiTiet.FirstOrDefault(x => x.SanPhamId == sanPhamId);
        if (chiTiet != null)
        {
            if (soLuongMoi <= 0)
            {
                DanhSachChiTiet.Remove(chiTiet);
            }
            else
            {
                chiTiet.CapNhatSoLuong(soLuongMoi);
            }
            TinhLaiTongGiaTri();
        }
    }

    public void DuyetDonHang()
    {
        if (TrangThai != TrangThaiDonHang.ChoDuyet)
            throw new BusinessRuleException("DON_HANG_DA_DUYET", "Đơn hàng đã được duyệt");

        if (!DanhSachChiTiet.Any())
            throw new BusinessRuleException("DON_HANG_TRONG", "Đơn hàng không có sản phẩm nào");

        TrangThai = TrangThaiDonHang.DaDuyet;
    }

    public void BatDauGiao()
    {
        if (TrangThai != TrangThaiDonHang.DaDuyet)
            throw new BusinessRuleException("DON_HANG_CHUA_DUYET", "Đơn hàng chưa được duyệt");

        TrangThai = TrangThaiDonHang.DangGiao;
    }

    public void HoanThanhGiao()
    {
        if (TrangThai != TrangThaiDonHang.DangGiao)
            throw new BusinessRuleException("DON_HANG_CHUA_GIAO", "Đơn hàng chưa bắt đầu giao");

        TrangThai = TrangThaiDonHang.DaGiao;
        NgayGiaoHang = DateTime.UtcNow;
    }

    public void HuyDonHang()
    {
        if (TrangThai == TrangThaiDonHang.DaGiao)
            throw new BusinessRuleException("DON_HANG_DA_GIAO", "Không thể hủy đơn hàng đã giao");

        if (TrangThai == TrangThaiDonHang.DaHuy)
            throw new BusinessRuleException("DON_HANG_DA_HUY", "Đơn hàng đã bị hủy");

        TrangThai = TrangThaiDonHang.DaHuy;
    }

    private void TinhLaiTongGiaTri()
    {
        TongGiaTri = DanhSachChiTiet.Sum(x => x.ThanhTien);
        TongThanhToan = TongGiaTri + PhiVanChuyen;
    }

    private static string TaoMaDonHang()
    {
        return $"DH{DateTime.UtcNow:yyyyMMddHHmmss}{Random.Shared.Next(100, 999)}";
    }

    public bool CoTheSua()
    {
        return TrangThai == TrangThaiDonHang.ChoDuyet;
    }

    public bool CoTheHuy()
    {
        return TrangThai != TrangThaiDonHang.DaGiao && TrangThai != TrangThaiDonHang.DaHuy;
    }
}
