using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

/// <summary>
/// Entity Framework configuration cho DanhMucThuTuc
/// </summary>
public class DanhMucThuTucConfiguration : IEntityTypeConfiguration<DanhMucThuTuc>
{
    public void Configure(EntityTypeBuilder<DanhMucThuTuc> builder)
    {
        // Table name
        builder.ToTable("danh_muc_thu_tuc");

        // Primary key
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        // Ma - Unique constraint
        builder.Property(x => x.Ma)
            .HasColumnName("ma")
            .HasMaxLength(50)
            .IsRequired();
        
        builder.HasIndex(x => x.Ma)
            .IsUnique()
            .HasDatabaseName("idx_danh_muc_thu_tuc_ma");

        // Ten
        builder.Property(x => x.Ten)
            .HasColumnName("ten")
            .HasColumnType("TEXT")
            .IsRequired();

        // LinhVuc - Enum as integer
        builder.Property(x => x.LinhVuc)
            .HasColumnName("linh_vuc")
            .HasConversion<int>()
            .IsRequired();

        builder.HasIndex(x => x.LinhVuc)
            .HasDatabaseName("idx_danh_muc_thu_tuc_linh_vuc");

        // TenLinhVuc - Denormalized for performance
        builder.Property(x => x.TenLinhVuc)
            .HasColumnName("ten_linh_vuc")
            .HasMaxLength(255)
            .IsRequired();

        // NgayApDung
        builder.Property(x => x.NgayApDung)
            .HasColumnName("ngay_ap_dung")
            .HasColumnType("DATE");

        // TrangThai - Enum as integer
        builder.Property(x => x.TrangThai)
            .HasColumnName("trang_thai")
            .HasConversion<int>()
            .IsRequired()
            .HasDefaultValue(TrangThaiThuTuc.HoatDong);

        builder.HasIndex(x => x.TrangThai)
            .HasDatabaseName("idx_danh_muc_thu_tuc_trang_thai");

        // MoTa
        builder.Property(x => x.MoTa)
            .HasColumnName("mo_ta")
            .HasColumnType("TEXT");

        // ThoiGianXuLy
        builder.Property(x => x.ThoiGianXuLy)
            .HasColumnName("thoi_gian_xu_ly");

        // PhiThucHien
        builder.Property(x => x.PhiThucHien)
            .HasColumnName("phi_thuc_hien")
            .HasColumnType("DECIMAL(18,2)");

        // CoQuanThucHien
        builder.Property(x => x.CoQuanThucHien)
            .HasColumnName("co_quan_thuc_hien")
            .HasMaxLength(500);

        // CanCuPhapLy
        builder.Property(x => x.CanCuPhapLy)
            .HasColumnName("can_cu_phap_ly")
            .HasColumnType("TEXT");

        // Audit fields from IAuditableEntity
        builder.Property(x => x.NgayTao)
            .HasColumnName("created_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP")
            .IsRequired();

        builder.Property(x => x.NguoiTao)
            .HasColumnName("created_by")
            .HasMaxLength(100);

        builder.Property(x => x.NgayCapNhat)
            .HasColumnName("updated_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(x => x.NguoiCapNhat)
            .HasColumnName("updated_by")
            .HasMaxLength(100);

        // Indexes for performance
        builder.HasIndex(x => x.NgayTao)
            .HasDatabaseName("idx_danh_muc_thu_tuc_created");

        builder.HasIndex(x => x.NgayCapNhat)
            .HasDatabaseName("idx_danh_muc_thu_tuc_updated");

        // Composite indexes for common queries
        builder.HasIndex(x => new { x.LinhVuc, x.TrangThai })
            .HasDatabaseName("idx_danh_muc_thu_tuc_linh_vuc_trang_thai");

        builder.HasIndex(x => new { x.TrangThai, x.NgayApDung })
            .HasDatabaseName("idx_danh_muc_thu_tuc_trang_thai_ngay_ap_dung");

        // Full-text search index (if supported by database)
        builder.HasIndex(x => new { x.Ma, x.Ten, x.TenLinhVuc })
            .HasDatabaseName("idx_danh_muc_thu_tuc_search");
    }
}
