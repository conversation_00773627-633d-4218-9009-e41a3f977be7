using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Common.Models;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.VaiTro.Queries;

public record LayDanhSachVaiTroQuery : IRequest<Result<List<VaiTroDto>>>
{
    public bool? TrangThaiHoatDong { get; init; }
    public string? TimKiem { get; init; }
}

public class LayDanhSachVaiTroQueryHandler : IRequestHandler<LayDanhSachVaiTroQuery, Result<List<VaiTroDto>>>
{
    private readonly IApplicationDbContext _context;

    public LayDanhSachVaiTroQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<VaiTroDto>>> Handle(LayDanhSachVaiTroQuery request, CancellationToken cancellationToken)
    {
        var query = _context.VaiTro.AsQueryable();

        // Lọc theo trạng thái hoạt động
        if (request.TrangThaiHoatDong.HasValue)
        {
            query = query.Where(vt => vt.TrangThaiHoatDong == request.TrangThaiHoatDong.Value);
        }

        // Tìm kiếm theo tên vai trò
        if (!string.IsNullOrWhiteSpace(request.TimKiem))
        {
            query = query.Where(vt => vt.TenVaiTro.Contains(request.TimKiem) || 
                                     (vt.MoTa != null && vt.MoTa.Contains(request.TimKiem)));
        }

        var vaiTroList = await query
            .Include(vt => vt.DanhSachQuyen)
                .ThenInclude(vtq => vtq.Quyen)
            .Include(vt => vt.DanhSachNguoiDung)
            .OrderBy(vt => vt.TenVaiTro)
            .ToListAsync(cancellationToken);

        var result = vaiTroList.Select(vt => new VaiTroDto
        {
            Id = vt.Id,
            TenVaiTro = vt.TenVaiTro,
            MoTa = vt.MoTa,
            LaVaiTroHeThong = vt.LaVaiTroHeThong,
            TrangThaiHoatDong = vt.TrangThaiHoatDong,
            SoLuongNguoiDung = vt.DanhSachNguoiDung.Count(ndvt => ndvt.ConHieuLuc()),
            SoLuongQuyen = vt.DanhSachQuyen.Count(vtq => vtq.TrangThaiHoatDong),
            NgayTao = vt.NgayTao,
            NgayCapNhat = vt.NgayCapNhat
        }).ToList();

        return Result<List<VaiTroDto>>.Success(result);
    }
}

public class VaiTroDto
{
    public int Id { get; set; }
    public string TenVaiTro { get; set; } = string.Empty;
    public string? MoTa { get; set; }
    public bool LaVaiTroHeThong { get; set; }
    public bool TrangThaiHoatDong { get; set; }
    public int SoLuongNguoiDung { get; set; }
    public int SoLuongQuyen { get; set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
}
