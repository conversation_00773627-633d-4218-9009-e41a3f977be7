/**
 * Model cho danh mục thủ tục
 */
export interface DanhMucThuTuc {
  id: number;
  ma: string;
  ten: string;
  linhVuc: number;
  tenLinhVuc: string;
  ngayApDung: Date | null;
  trangThai: number;
  moTa?: string;
  thoiGianXuLy?: number;
  phiThucHien?: number;
  coQuanThucHien?: string;
  canCuPhapLy?: string;
  created: Date;
  createdBy?: string;
  lastModified?: Date;
  lastModifiedBy?: string;
  dangHoatDong: boolean;
  coHieuLuc: boolean;
}

/**
 * Model cho filter danh mục thủ tục
 */
export interface DanhMucThuTucFilter {
  tuKhoa?: string;
  linhVuc?: number;
  trangThai?: number;
  trang?: number;
  kichThuocTrang?: number;
}

/**
 * Model cho response API danh mục thủ tục (phân trang)
 */
export interface DanhMucThuTucResponse {
  items: DanhMucThuTuc[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

/**
 * Enum cho trạng thái danh mục thủ tục
 */
export enum TrangThaiDanhMucThuTuc {
  NgungHoatDong = 0,
  HoatDong = 1,
  TamNgung = 2,
  ChoPheDuyet = 3,
  DangSoanThao = 4
}

/**
 * Enum cho lĩnh vực
 */
export enum LinhVucThuTuc {
  ThuBaoHiem = 1,
  CapSoBaoHiem = 2,
  ChiTraBaoHiem = 3,
  KhieuNai = 4,
  ThanhTraKiemTra = 5,
  CapPhepHoatDong = 6,
  DangKyKinhDoanh = 7,
  Thue = 8,
  HaiQuan = 9,
  Khac = 99
}

/**
 * Mapping tên lĩnh vực
 */
export const LINH_VUC_MAPPING: Record<number, string> = {
  [LinhVucThuTuc.ThuBaoHiem]: 'Lĩnh vực thu Bảo hiểm xã hội, bảo hiểm y tế, bảo hiểm thất nghiệp',
  [LinhVucThuTuc.CapSoBaoHiem]: 'Lĩnh vực cấp sổ bảo hiểm xã hội, thẻ bảo hiểm y tế',
  [LinhVucThuTuc.ChiTraBaoHiem]: 'Lĩnh vực chi trả bảo hiểm xã hội, bảo hiểm y tế, bảo hiểm thất nghiệp',
  [LinhVucThuTuc.KhieuNai]: 'Lĩnh vực khiếu nại, tố cáo'
};

/**
 * Mapping trạng thái
 */
export const TRANG_THAI_MAPPING: Record<number, string> = {
  [TrangThaiDanhMucThuTuc.NgungHoatDong]: 'Ngừng hoạt động',
  [TrangThaiDanhMucThuTuc.HoatDong]: 'Hoạt động',
  [TrangThaiDanhMucThuTuc.TamNgung]: 'Tạm ngưng',
  [TrangThaiDanhMucThuTuc.ChoPheDuyet]: 'Chờ phê duyệt',
  [TrangThaiDanhMucThuTuc.DangSoanThao]: 'Đang soạn thảo'
};
