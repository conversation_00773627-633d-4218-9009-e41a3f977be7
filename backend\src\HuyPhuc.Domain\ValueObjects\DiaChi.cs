using HuyPhuc.Domain.Exceptions;

namespace HuyPhuc.Domain.ValueObjects;

public class DiaChi : IEquatable<DiaChi>
{
    public string DiaChiChiTiet { get; private set; }
    public string Phuong { get; private set; }
    public string Quan { get; private set; }
    public string ThanhPho { get; private set; }

    private DiaChi(string diaChiChiTiet, string phuong, string quan, string thanhPho)
    {
        DiaChiChiTiet = diaChiChiTiet;
        Phuong = phuong;
        Quan = quan;
        ThanhPho = thanhPho;
    }

    public static DiaChi Tao(string diaChiChiTiet, string phuong, string quan, string thanhPho)
    {
        if (string.IsNullOrWhiteSpace(diaChiChiTiet))
            throw new DomainException("Địa chỉ chi tiết không được để trống");

        if (string.IsNullOrWhiteSpace(phuong))
            throw new DomainException("Phường/Xã không được để trống");

        if (string.IsNullOrWhiteSpace(quan))
            throw new DomainException("Quận/Huyện không được để trống");

        if (string.IsNullOrWhiteSpace(thanhPho))
            throw new DomainException("Thành phố/Tỉnh không được để trống");

        return new DiaChi(
            diaChiChiTiet.Trim(),
            phuong.Trim(),
            quan.Trim(),
            thanhPho.Trim());
    }

    public string LayDiaChiDayDu()
    {
        return $"{DiaChiChiTiet}, {Phuong}, {Quan}, {ThanhPho}";
    }

    public bool Equals(DiaChi? other)
    {
        return other is not null &&
               DiaChiChiTiet == other.DiaChiChiTiet &&
               Phuong == other.Phuong &&
               Quan == other.Quan &&
               ThanhPho == other.ThanhPho;
    }

    public override bool Equals(object? obj)
    {
        return Equals(obj as DiaChi);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(DiaChiChiTiet, Phuong, Quan, ThanhPho);
    }

    public override string ToString()
    {
        return LayDiaChiDayDu();
    }
}
