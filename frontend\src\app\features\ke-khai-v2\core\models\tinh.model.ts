/**
 * Model cho thông tin tỉnh/thành phố
 */
export interface Tinh {
  id: number;
  maTinh: string;
  tenTinh: string;
  textDisplay: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Response từ API khi lấy danh sách tỉnh
 */
export interface TinhResponse {
  data: Tinh[];
  success: boolean;
  message?: string;
  errors?: any;
  status: number;
  traceId?: string;
}

/**
 * Option cho dropdown tỉnh
 */
export interface TinhOption {
  value: string;
  text: string;
  ten: string;
}
