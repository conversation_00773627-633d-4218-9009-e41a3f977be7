namespace HuyPhuc.Application.Common.Interfaces;

/// <summary>
/// Interface cho password hashing service
/// </summary>
public interface IPasswordHashingService
{
    /// <summary>
    /// Hash mật khẩu
    /// </summary>
    /// <param name="matKhau">Mật khẩu gốc</param>
    /// <returns>Mật khẩu đã hash</returns>
    string HashMatKhau(string matKhau);

    /// <summary>
    /// Xác minh mật khẩu
    /// </summary>
    /// <param name="matKhau">Mật khẩu gốc</param>
    /// <param name="matKhauDaHash">Mật khẩu đã hash</param>
    /// <returns>True nếu mật khẩu đúng</returns>
    bool XacMinhMatKhau(string matKhau, string matKhauDaHash);

    /// <summary>
    /// Ki<PERSON><PERSON> tra mật khẩu có cần hash lại không (để upgrade security)
    /// </summary>
    /// <param name="matKhauDaHash">Mật khẩu đã hash</param>
    /// <returns>True nếu cần hash lại</returns>
    bool CanHashLai(string matKhauDaHash);
}
