using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Domain.Events.VaiTro;
using HuyPhuc.Domain.Exceptions;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity đại diện cho vai trò trong hệ thống
/// </summary>
public class VaiTro : BaseEntity, IAuditableEntity
{
    public string TenVaiTro { get; private set; } = string.Empty;
    public string? MoTa { get; private set; }
    public bool LaVaiTroHeThong { get; private set; } // Không thể xóa các vai trò hệ thống
    public bool TrangThaiHoatDong { get; private set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    // Navigation properties
    public virtual ICollection<NguoiDungVaiTro> DanhSachNguoiDung { get; private set; } = new List<NguoiDungVaiTro>();
    public virtual ICollection<VaiTroQuyen> DanhSachQuyen { get; private set; } = new List<VaiTroQuyen>();

    private VaiTro() { } // EF Core constructor

    /// <summary>
    /// Tạo vai trò mới
    /// </summary>
    public static VaiTro Tao(string tenVaiTro, string? moTa = null, bool laVaiTroHeThong = false)
    {
        if (string.IsNullOrWhiteSpace(tenVaiTro))
            throw new DomainException("Tên vai trò không được để trống");

        if (tenVaiTro.Length > 100)
            throw new DomainException("Tên vai trò không được vượt quá 100 ký tự");

        var vaiTro = new VaiTro
        {
            TenVaiTro = tenVaiTro.Trim(),
            MoTa = moTa?.Trim(),
            LaVaiTroHeThong = laVaiTroHeThong,
            TrangThaiHoatDong = true
        };

        vaiTro.ThemSuKien(new VaiTroDaTaoEvent(vaiTro));
        return vaiTro;
    }

    /// <summary>
    /// Cập nhật thông tin vai trò
    /// </summary>
    public void CapNhatThongTin(string tenVaiTro, string? moTa = null)
    {
        if (string.IsNullOrWhiteSpace(tenVaiTro))
            throw new DomainException("Tên vai trò không được để trống");

        if (tenVaiTro.Length > 100)
            throw new DomainException("Tên vai trò không được vượt quá 100 ký tự");

        TenVaiTro = tenVaiTro.Trim();
        MoTa = moTa?.Trim();

        ThemSuKien(new VaiTroCapNhatEvent(this));
    }

    /// <summary>
    /// Kích hoạt vai trò
    /// </summary>
    public void KichHoat()
    {
        if (TrangThaiHoatDong)
            throw new BusinessRuleException("VAI_TRO_DA_KICH_HOAT", "Vai trò đã được kích hoạt");

        TrangThaiHoatDong = true;
        ThemSuKien(new VaiTroKichHoatEvent(this));
    }

    /// <summary>
    /// Vô hiệu hóa vai trò
    /// </summary>
    public void VoHieuHoa()
    {
        if (LaVaiTroHeThong)
            throw new BusinessRuleException("KHONG_THE_VO_HIEU_HOA_VAI_TRO_HE_THONG", 
                "Không thể vô hiệu hóa vai trò hệ thống");

        if (!TrangThaiHoatDong)
            throw new BusinessRuleException("VAI_TRO_DA_VO_HIEU_HOA", "Vai trò đã bị vô hiệu hóa");

        TrangThaiHoatDong = false;
        ThemSuKien(new VaiTroVoHieuHoaEvent(this));
    }

    /// <summary>
    /// Kiểm tra có thể xóa vai trò không
    /// </summary>
    public bool CoTheXoa()
    {
        return !LaVaiTroHeThong && !DanhSachNguoiDung.Any();
    }

    /// <summary>
    /// Thêm quyền cho vai trò
    /// </summary>
    public void ThemQuyen(int quyenId)
    {
        if (DanhSachQuyen.Any(vq => vq.QuyenId == quyenId))
            throw new BusinessRuleException("QUYEN_DA_TON_TAI", "Quyền đã được gán cho vai trò này");

        var vaiTroQuyen = VaiTroQuyen.Tao(Id, quyenId);
        DanhSachQuyen.Add(vaiTroQuyen);
    }

    /// <summary>
    /// Xóa quyền khỏi vai trò
    /// </summary>
    public void XoaQuyen(int quyenId)
    {
        var vaiTroQuyen = DanhSachQuyen.FirstOrDefault(vq => vq.QuyenId == quyenId);
        if (vaiTroQuyen == null)
            throw new BusinessRuleException("QUYEN_KHONG_TON_TAI", "Quyền không tồn tại trong vai trò này");

        DanhSachQuyen.Remove(vaiTroQuyen);
    }
}
