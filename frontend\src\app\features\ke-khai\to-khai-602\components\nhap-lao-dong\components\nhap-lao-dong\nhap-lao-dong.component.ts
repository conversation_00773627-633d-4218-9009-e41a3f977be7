import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormArray } from '@angular/forms';

import { Subject, takeUntil } from 'rxjs';

import { ToKhai602Service } from '../../../../services';
import { ToKhaiFormState, ValidationError } from '../../../../models';
import { KeKhaiChiTietDto, LaoDongKeKhaiDto } from '../../../../../../../shared/models/ke-khai.model';
import {
  NhapLaoDongService,
  NhapLaoDongValidationService,
  EditLaoDongService,
  NhapLaoDongStateService,
  NhapLaoDongNavigationService,
  NhapLaoDongBusinessService,
  NhapLaoDongFlowService
} from '../../services';

/**
 * Component nhập thông tin lao động cho tờ khai 602
 */
@Component({
  selector: 'app-nhap-lao-dong',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './nhap-lao-dong.component.html',
  styleUrls: ['./nhap-lao-dong.component.scss']
})
export class NhapLaoDongComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // Form
  laoDongForm: FormGroup;

  // State - sử dụng state service
  private _keKhaiId!: number;
  errors: ValidationError[] = [];

  get keKhaiId(): number {
    console.log('🔍 Template accessing keKhaiId:', this._keKhaiId);
    return this._keKhaiId;
  }

  set keKhaiId(value: number) {
    console.log('🔍 Setting keKhaiId:', value);
    this._keKhaiId = value;
  }

  constructor(
    private toKhai602Service: ToKhai602Service,
    private nhapLaoDongService: NhapLaoDongService,
    private validationService: NhapLaoDongValidationService,
    private editLaoDongService: EditLaoDongService,
    private stateService: NhapLaoDongStateService,
    private navigationService: NhapLaoDongNavigationService,
    private businessService: NhapLaoDongBusinessService,
    private flowService: NhapLaoDongFlowService
  ) {
    this.laoDongForm = this.nhapLaoDongService.taoForm();
  }

  // Getters delegate to StateService
  get keKhaiInfo(): KeKhaiChiTietDto | undefined {
    console.log('🔍 Template accessing keKhaiInfo:', this.stateService.keKhaiInfo);
    return this.stateService.keKhaiInfo;
  }
  get danhSachLaoDong(): LaoDongKeKhaiDto[] {
    console.log('🔍 Template accessing danhSachLaoDong:', this.stateService.danhSachLaoDong);
    return this.stateService.danhSachLaoDong;
  }
  get formState(): ToKhaiFormState | null { return this.stateService.formState; }
  get dangTai(): boolean { return this.stateService.dangTai; }
  get dangLuu(): boolean { return this.stateService.dangLuu; }
  get dangGhiNhan(): boolean { return this.stateService.dangGhiNhan; }
  get isEditMode(): boolean { return this.stateService.isEditMode; }
  get editLaoDongId(): number | null { return this.stateService.editLaoDongId; }
  get laoDongDangChinhSua(): LaoDongKeKhaiDto | null { return this.stateService.laoDongDangChinhSua; }

  ngOnInit(): void {
    console.log('🔄 NhapLaoDongComponent ngOnInit');
    console.log('🔍 Current URL:', window.location.href);

    // Debug: Kiểm tra route params
    this.navigationService.getKeKhaiIdFromParentRoute()
      .pipe(takeUntil(this.destroy$))
      .subscribe(keKhaiId => {
        console.log('🔍 Parent route keKhaiId:', keKhaiId);
        if (keKhaiId) {
          this.keKhaiId = keKhaiId;
          console.log('🆔 KeKhaiId set:', this.keKhaiId);
          this.laoDongForm = this.nhapLaoDongService.taoFormChoFlowMoi(); // Khởi tạo form cho flow mới
          this.flowService.loadKeKhaiInfo(this.keKhaiId);
          this.flowService.loadDanhSachLaoDong(this.keKhaiId);
        }
      });

    // Fallback: Lấy từ query params nếu có (để tương thích với code cũ)
    this.navigationService.getKeKhaiIdFromQueryParams()
      .pipe(takeUntil(this.destroy$))
      .subscribe(keKhaiId => {
        console.log('🔍 Query params keKhaiId:', keKhaiId);
        if (keKhaiId && !this.keKhaiId) {
          this.keKhaiId = keKhaiId;
          console.log('🆔 KeKhaiId set from query:', this.keKhaiId);
          this.laoDongForm = this.nhapLaoDongService.taoFormChoFlowMoi();
          this.flowService.loadKeKhaiInfo(this.keKhaiId);
          this.flowService.loadDanhSachLaoDong(this.keKhaiId);
        }
      });

    // Fallback cuối: Lấy từ snapshot
    setTimeout(() => {
      if (!this.keKhaiId) {
        const idFromSnapshot = this.navigationService.getKeKhaiIdFromSnapshot();
        if (idFromSnapshot) {
          this.keKhaiId = idFromSnapshot;
          console.log('🆔 KeKhaiId set from snapshot:', this.keKhaiId);
          this.laoDongForm = this.nhapLaoDongService.taoFormChoFlowMoi();
          this.flowService.loadKeKhaiInfo(this.keKhaiId);
          this.flowService.loadDanhSachLaoDong(this.keKhaiId);
        }
      }
    }, 100);

    // Xử lý draft mode
    this.navigationService.getDraftIdFromQueryParams()
      .pipe(takeUntil(this.destroy$))
      .subscribe(draftId => {
        if (draftId) {
          // Backward compatibility với flow cũ
          this.khoiTaoSubscriptions();
          this.kiemTraFormState();
          this.flowService.kiemTraDraftId(this.destroy$);
        }
      });

    // Xử lý edit mode
    this.navigationService.getEditModeFromQueryParams()
      .pipe(takeUntil(this.destroy$))
      .subscribe(editInfo => {
        if (editInfo.isEdit && editInfo.editId) {
          this.stateService.setIsEditMode(true);
          this.stateService.setEditLaoDongId(editInfo.editId);
          this.loadLaoDongForEdit();
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.stateService.cleanup();
  }



  /**
   * Khởi tạo subscriptions
   */
  private khoiTaoSubscriptions(): void {
    this.toKhai602Service.formState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        this.stateService.setFormState(state);
        this.errors = state.loi;
        this.capNhatFormArray();
      });

    this.toKhai602Service.dangTai$
      .pipe(takeUntil(this.destroy$))
      .subscribe(dangTai => {
        this.stateService.setDangTai(dangTai);
      });
  }

  /**
   * Kiểm tra form state có hợp lệ không
   */
  private kiemTraFormState(): void {
    const formState = this.stateService.formState;
    if (!formState?.daiLyDaChon || !formState?.donViDaChon) {
      // Nếu chưa chọn đại lý/đơn vị, quay về trang trước
      this.navigationService.navigateToToKhaiList();
      return;
    }

    // Nếu chưa có lao động nào, thêm một lao động mặc định
    if (formState.danhSachLaoDong.length === 0) {
      this.toKhai602Service.themLaoDongMoi();
    }
  }



  /**
   * Cập nhật form array dựa trên state
   */
  private capNhatFormArray(): void {
    this.businessService.capNhatFormArray(
      this.laoDongForm,
      this.stateService.formState,
      (laoDong) => this.nhapLaoDongService.taoLaoDongFormGroup(laoDong)
    );
  }

  /**
   * Getter cho form array
   */
  get danhSachLaoDongFormArray(): FormArray {
    return this.laoDongForm.get('danhSachLaoDong') as FormArray;
  }

  /**
   * Thêm lao động mới
   */
  onThemLaoDong(): void {
    this.businessService.onThemLaoDong();
  }

  /**
   * Xóa lao động (flow cũ)
   */
  onXoaLaoDongCu(index: number): void {
    this.businessService.onXoaLaoDongCu(index, this.danhSachLaoDongFormArray);
  }

  /**
   * Cập nhật thông tin lao động khi form thay đổi
   */
  onLaoDongChange(index: number): void {
    this.businessService.onLaoDongChange(index, this.danhSachLaoDongFormArray);
  }

  /**
   * Quay lại trang trước
   */
  onQuayLai(): void {
    this.navigationService.navigateToToKhaiList();
  }

  /**
   * Lưu nháp
   */
  onLuuNhap(): void {
    const validation = this.businessService.canLuuNhap(this.stateService.formState);
    if (!validation.canSave) {
      alert(validation.message);
      return;
    }

    // Cập nhật form state với dữ liệu hiện tại
    this.capNhatFormStateVoiDuLieuHienTai();

    const formState = this.stateService.formState!;
    // Kiểm tra xem đã có draft ID chưa
    if (formState.draftId) {
      // Cập nhật draft hiện có
      this.capNhatDraft();
    } else {
      // Tạo draft mới
      this.taoDraftMoi();
    }
  }

  /**
   * Cập nhật form state với dữ liệu từ form hiện tại
   */
  private capNhatFormStateVoiDuLieuHienTai(): void {
    this.businessService.capNhatFormStateVoiDuLieuHienTai(this.laoDongForm);
  }

  /**
   * Tạo draft mới
   */
  private taoDraftMoi(): void {
    this.flowService.taoDraftMoi();
  }

  /**
   * Cập nhật draft hiện có
   */
  private capNhatDraft(): void {
    this.flowService.capNhatDraft();
  }

  /**
   * Kiểm tra thông tin lao động qua API
   */
  async onKiemTraThongTinLaoDong(index: number): Promise<void> {
    await this.businessService.onKiemTraThongTinLaoDong(
      index,
      this.danhSachLaoDongFormArray,
      (formGroup) => this.validationService.kiemTraThongTinLaoDong(formGroup)
    );
  }

  /**
   * Lấy form group của lao động theo index
   */
  getLaoDongFormGroup(index: number): FormGroup {
    return this.businessService.getLaoDongFormGroup(index, this.danhSachLaoDongFormArray);
  }

  /**
   * Ghi nhận dữ liệu vào database
   */
  onGhiNhan(): void {
    const validation = this.businessService.canGhiNhan(this.stateService.formState, this.laoDongForm);
    if (!validation.canSubmit) {
      alert(validation.message);
      return;
    }

    this.stateService.setDangGhiNhan(true);

    // Cập nhật form state với dữ liệu hiện tại
    this.capNhatFormStateVoiDuLieuHienTai();

    const formState = this.stateService.formState!;
    // Gọi service để lưu vào database
    this.toKhai602Service.ghiNhanToKhai(formState.draftId!, formState.danhSachLaoDong).subscribe({
      next: (response) => {
        console.log('✅ Ghi nhận thành công:', response);

        let message = `Ghi nhận thành công!\n- Mã tờ khai: ${response.maToKhai}\n- Số lao động đã lưu: ${response.soLaoDongDaLuu}\n- Số lao động lỗi: ${response.soLaoDongLoi}`;

        if (response.danhSachLoi.length > 0) {
          message += '\n\nDanh sách lỗi:\n' + response.danhSachLoi.join('\n');
        }

        alert(message);
        this.stateService.setDangGhiNhan(false);
      },
      error: (error) => {
        console.error('❌ Lỗi khi ghi nhận:', error);
        alert('Có lỗi xảy ra khi ghi nhận: ' + (error.message || 'Lỗi không xác định'));
        this.stateService.setDangGhiNhan(false);
      }
    });
  }

  getErrorMessage(index: number, fieldName: string): string {
    return this.businessService.getErrorMessage(
      index,
      fieldName,
      this.danhSachLaoDongFormArray,
      (formGroup, field) => this.validationService.getErrorMessage(formGroup, field)
    );
  }

  /**
   * Gửi tờ khai
   */
  onGuiToKhai(): void {
    this.businessService.onGuiToKhai(
      this.laoDongForm,
      this.stateService.formState,
      () => this.navigationService.navigateToToKhai602DanhSach(),
      (error) => console.error('Lỗi:', error)
    );
  }

  /**
   * Kiểm tra field có lỗi không
   */
  hasError(index: number, fieldName: string): boolean {
    return this.businessService.hasError(
      index,
      fieldName,
      this.danhSachLaoDongFormArray,
      (formGroup, field) => this.validationService.hasError(formGroup, field)
    );
  }

  formatCurrency(value: number): string {
    return this.businessService.formatCurrency(value);
  }

  /**
   * Debug method để lấy current URL
   */
  getCurrentUrl(): string {
    return window.location.href;
  }



  /**
   * Thêm lao động mới - sử dụng API mới
   */
  async onThemLaoDongMoi(): Promise<void> {
    await this.flowService.themLaoDongMoi(this.keKhaiId, this.laoDongForm, () => {
      this.laoDongForm = this.nhapLaoDongService.taoFormChoFlowMoi();
    });
  }

  /**
   * Xóa lao động
   */
  async onXoaLaoDong(laoDongId: number): Promise<void> {
    await this.flowService.xoaLaoDong(this.keKhaiId, laoDongId);
  }

  /**
   * Gửi kê khai - sử dụng API mới
   */
  async onGuiKeKhaiMoi(): Promise<void> {
    await this.flowService.guiKeKhaiMoi(this.keKhaiId);
  }

  /**
   * Load thông tin lao động để chỉnh sửa
   */
  private loadLaoDongForEdit(): void {
    this.flowService.loadLaoDongForEdit(
      this.keKhaiId,
      this.destroy$,
      (laoDong) => {
        this.laoDongForm = this.editLaoDongService.taoFormChinhSua(laoDong);
      }
    );
  }

  /**
   * Lưu thay đổi khi ở edit mode
   */
  async onLuuThayDoi(): Promise<void> {
    this.flowService.luuThayDoi(this.keKhaiId, this.laoDongForm, this.destroy$);
  }

  /**
   * Hủy chỉnh sửa
   */
  onHuyChinhSua(): void {
    this.flowService.huyChinhSua();
  }

  /**
   * Lấy thông báo lỗi cho field - hỗ trợ cả edit mode và normal mode
   */
  getFieldErrorMessage(fieldName: string): string {
    return this.businessService.getFieldErrorMessage(
      fieldName,
      this.laoDongForm,
      this.stateService.isEditMode,
      this.editLaoDongService
    );
  }
}
