import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';

import {
  KeKhaiDto,
  TaoKeKhaiRequest,
  TaoKeKhaiResponse,
  ThemLaoDongKeKhaiRequest,
  ThemLaoDongResponse,
  CapNhatLaoDongKeKhaiRequest,
  ValidateKeKhaiResponse,
  LayDanhSachKeKhaiQuery,
  DanhSachKeKhaiResponse,
  KeKhaiChiTietDto,
  LaoDongKeKhaiDto,
  ThuTucDto
} from '../models/ke-khai.model';
import { environment } from '../../../environments/environment';

/**
 * Service tổng quát cho tất cả loại kê khai
 * Sử dụng bảng danh_sach_ke_khai
 */
@Injectable({
  providedIn: 'root'
})
export class KeKhaiService {
  private readonly API_URL = `${environment.apiUrl}/ke-khai`;

  // State management
  private readonly _dangTai$ = new BehaviorSubject<boolean>(false);
  private readonly _danhSachKeKhai$ = new BehaviorSubject<KeKhaiDto[]>([]);
  private readonly _keKhaiHienTai$ = new BehaviorSubject<KeKhaiChiTietDto | null>(null);

  // Public observables
  readonly dangTai$ = this._dangTai$.asObservable();
  readonly danhSachKeKhai$ = this._danhSachKeKhai$.asObservable();
  readonly keKhaiHienTai$ = this._keKhaiHienTai$.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Chuyển đổi phương thức từ frontend format sang API format
   */
  private mapPhuongThucToApi(frontendPhuongThuc: string): string {
    const mapping: { [key: string]: string } = {
      '1-thang': '1',
      '3-thang': '3',
      '6-thang': '6',
      '12-thang': '12',
      'nam-sau': 'nam-sau',
      'nam-thieu': 'nam-thieu'
    };

    return mapping[frontendPhuongThuc] || frontendPhuongThuc;
  }

  /**
   * Lấy danh sách loại thủ tục có thể tạo kê khai
   */
  layDanhSachLoaiThuTuc(): Observable<ThuTucDto[]> {
    return this.http.get<any>(`${this.API_URL}/loai-thu-tuc`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi lấy danh sách thủ tục');
          }
        })
      );
  }

  /**
   * Tạo kê khai mới
   */
  taoKeKhai(request: TaoKeKhaiRequest): Observable<TaoKeKhaiResponse> {
    this._dangTai$.next(true);

    console.log('🔍 Tạo kê khai với request:', request);

    return this.http.post<any>(this.API_URL, request)
      .pipe(
        map(response => {
          console.log('🔍 API response:', response);
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi tạo kê khai');
          }
        }),
        tap((result) => {
          console.log('✅ Tạo kê khai thành công:', result);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('❌ Lỗi khi tạo kê khai:', error);
          this._dangTai$.next(false);
          throw error;
        })
      );
  }

  /**
   * Lấy chi tiết kê khai
   */
  layChiTiet(keKhaiId: number): Observable<KeKhaiChiTietDto> {
    this._dangTai$.next(true);

    return this.http.get<any>(`${this.API_URL}/${keKhaiId}`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi lấy chi tiết kê khai');
          }
        }),
        tap((result) => {
          this._keKhaiHienTai$.next(result);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('❌ Lỗi khi lấy chi tiết kê khai:', error);
          this._dangTai$.next(false);
          throw error;
        })
      );
  }

  /**
   * Lấy danh sách lao động trong kê khai
   */
  layDanhSachLaoDong(keKhaiId: number): Observable<LaoDongKeKhaiDto[]> {
    return this.http.get<any>(`${this.API_URL}/${keKhaiId}/lao-dong`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi lấy danh sách lao động');
          }
        })
      );
  }

  /**
   * Thêm lao động vào kê khai
   */
  themLaoDong(request: ThemLaoDongKeKhaiRequest): Observable<ThemLaoDongResponse> {
    console.log('🔍 Thêm lao động với request:', request);

    // Map phương thức từ frontend format sang API format
    const mappedRequest = {
      ...request,
      phuongThuc: request.phuongThuc ? this.mapPhuongThucToApi(request.phuongThuc) : request.phuongThuc
    };

    console.log('🔄 Request sau khi map phương thức:', mappedRequest);

    return this.http.post<any>(`${this.API_URL}/${request.keKhaiId}/lao-dong`, mappedRequest)
      .pipe(
        map(response => {
          console.log('🔍 API response:', response);
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi thêm lao động');
          }
        }),
        tap((result) => {
          console.log('✅ Thêm lao động thành công:', result);
        }),
        catchError(error => {
          console.error('❌ Lỗi khi thêm lao động:', error);
          throw error;
        })
      );
  }

  /**
   * Cập nhật lao động trong kê khai
   */
  capNhatLaoDong(request: CapNhatLaoDongKeKhaiRequest): Observable<void> {
    // Map phương thức từ frontend format sang API format
    const mappedRequest = {
      ...request,
      phuongThuc: request.phuongThuc ? this.mapPhuongThucToApi(request.phuongThuc) : request.phuongThuc
    };

    return this.http.put<any>(`${this.API_URL}/${request.keKhaiId}/lao-dong/${request.id}`, mappedRequest)
      .pipe(
        map(response => {
          if (!response.success) {
            throw new Error(response.message || 'Có lỗi xảy ra khi cập nhật lao động');
          }
        })
      );
  }

  /**
   * Xóa lao động khỏi kê khai
   */
  xoaLaoDong(keKhaiId: number, laoDongId: number): Observable<void> {
    return this.http.delete<any>(`${this.API_URL}/${keKhaiId}/lao-dong/${laoDongId}`)
      .pipe(
        map(response => {
          if (!response.success) {
            throw new Error(response.message || 'Có lỗi xảy ra khi xóa lao động');
          }
        })
      );
  }

  /**
   * Validate kê khai trước khi gửi
   */
  validateKeKhai(keKhaiId: number): Observable<ValidateKeKhaiResponse> {
    return this.http.post<any>(`${this.API_URL}/${keKhaiId}/validate`, {})
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi validate kê khai');
          }
        })
      );
  }

  /**
   * Gửi kê khai (chuyển trạng thái từ Đang soạn sang Đã gửi)
   */
  guiKeKhai(keKhaiId: number): Observable<void> {
    return this.http.post<any>(`${this.API_URL}/${keKhaiId}/gui`, {})
      .pipe(
        map(response => {
          if (!response.success) {
            throw new Error(response.message || 'Có lỗi xảy ra khi gửi kê khai');
          }
        })
      );
  }

  /**
   * Lấy danh sách kê khai với phân trang và filter
   */
  layDanhSachKeKhai(query?: LayDanhSachKeKhaiQuery): Observable<DanhSachKeKhaiResponse> {
    this._dangTai$.next(true);

    let params = new HttpParams();
    if (query) {
      if (query.page) params = params.set('page', query.page.toString());
      if (query.pageSize) params = params.set('pageSize', query.pageSize.toString());
      if (query.thuTucId) params = params.set('thuTucId', query.thuTucId.toString());
      if (query.daiLyId) params = params.set('daiLyId', query.daiLyId.toString());
      if (query.donViId) params = params.set('donViId', query.donViId.toString());
      if (query.trangThai !== undefined) params = params.set('trangThai', query.trangThai.toString());
      if (query.tuNgay) params = params.set('tuNgay', query.tuNgay.toISOString());
      if (query.denNgay) params = params.set('denNgay', query.denNgay.toISOString());
      if (query.tuKhoa) params = params.set('tuKhoa', query.tuKhoa);
    }

    return this.http.get<any>(this.API_URL, { params })
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi lấy danh sách kê khai');
          }
        }),
        tap((result) => {
          this._danhSachKeKhai$.next(result.items);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('❌ Lỗi khi lấy danh sách kê khai:', error);
          this._dangTai$.next(false);
          throw error;
        })
      );
  }

  /**
   * Cập nhật thông tin header của kê khai
   */
  capNhatHeader(keKhaiId: number, thongTinHeader: any): Observable<void> {
    return this.http.put<any>(`${this.API_URL}/${keKhaiId}/header`, { thongTinHeader })
      .pipe(
        map(response => {
          if (!response.success) {
            throw new Error(response.message || 'Có lỗi xảy ra khi cập nhật header');
          }
        })
      );
  }

  /**
   * Reset state
   */
  resetState(): void {
    this._danhSachKeKhai$.next([]);
    this._keKhaiHienTai$.next(null);
    this._dangTai$.next(false);
  }
}
