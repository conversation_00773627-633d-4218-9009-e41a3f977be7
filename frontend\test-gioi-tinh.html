<!DOCTYPE html>
<html>
<head>
    <title>Test Giới T<PERSON>h Logic</title>
</head>
<body>
    <h1>Test Logic Xử Lý Giới T<PERSON></h1>
    
    <h2>Test Cases:</h2>
    <div id="test-results"></div>

    <script>
        // Simulate the logic from thong-tin-co-ban.component.ts
        function mapGioiTinh(gioiTinh) {
            return gioiTinh === 1 ? 'Nam' : (gioiTinh === 2 ? 'Nữ' : (gioiTinh === 'Nam' || gioiTinh === 'Nữ' ? gioiTinh : ''));
        }

        // Test cases
        const testCases = [
            { input: 1, expected: 'Nam', description: 'Number 1 should return Nam' },
            { input: 2, expected: 'Nữ', description: 'Number 2 should return Nữ' },
            { input: 'Nam', expected: 'Nam', description: 'String "Nam" should return Nam' },
            { input: 'Nữ', expected: 'Nữ', description: 'String "Nữ" should return Nữ' },
            { input: 0, expected: '', description: 'Number 0 should return empty string' },
            { input: 3, expected: '', description: 'Number 3 should return empty string' },
            { input: 'invalid', expected: '', description: 'Invalid string should return empty string' },
            { input: null, expected: '', description: 'null should return empty string' },
            { input: undefined, expected: '', description: 'undefined should return empty string' }
        ];

        let results = '<ul>';
        let allPassed = true;

        testCases.forEach((testCase, index) => {
            const result = mapGioiTinh(testCase.input);
            const passed = result === testCase.expected;
            allPassed = allPassed && passed;
            
            results += `<li style="color: ${passed ? 'green' : 'red'}">
                Test ${index + 1}: ${testCase.description}<br>
                Input: ${testCase.input}, Expected: "${testCase.expected}", Got: "${result}" 
                ${passed ? '✅' : '❌'}
            </li>`;
        });

        results += '</ul>';
        results += `<h3 style="color: ${allPassed ? 'green' : 'red'}">
            Overall Result: ${allPassed ? 'All tests passed! ✅' : 'Some tests failed! ❌'}
        </h3>`;

        document.getElementById('test-results').innerHTML = results;
    </script>
</body>
</html>
