import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';


import { ToKhai602Service } from '../../services';
import { LoadingSpinnerComponent } from '../../../../shared/components';
import { ToKhai602 } from '../../models';
import { TrangThaiKeKhai, LaoDong } from '../../../../core/models';
import { LaoDongFormComponent } from '../lao-dong-form/lao-dong-form.component';
import { LaoDongTableComponent } from '../lao-dong-table/lao-dong-table.component';
import { LaoDongApiService } from '../../services/lao-dong-api.service';

/**
 * Component xem chi tiết tờ khai 602
 */
@Component({
  selector: 'app-chi-tiet',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    LoadingSpinnerComponent,
    LaoDongFormComponent,
    LaoDongTableComponent
  ],
  template: `
    <div class="chi-tiet-to-khai-602">
      <!-- Loading state -->
      <div *ngIf="dangTai" class="flex justify-center items-center py-12">
        <app-loading-spinner></app-loading-spinner>
      </div>

      <!-- Error state -->
      <div *ngIf="loi && !dangTai" class="p-6">
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Có lỗi xảy ra</h3>
              <p class="mt-1 text-sm text-red-700">{{ loi }}</p>
            </div>
          </div>
        </div>
        <div class="mt-4">
          <button
            (click)="quayLai()"
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200"
          >
            Quay lại
          </button>
        </div>
      </div>

      <!-- Content -->
      <div *ngIf="toKhai && !dangTai" class="p-6">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Chi tiết Tờ khai 602</h1>
            <p class="text-gray-600 mt-1">Mã tờ khai: <span class="font-medium">{{ toKhai.ma || 'Chưa có mã' }}</span></p>
          </div>
          <div class="flex space-x-3">
            <button
              (click)="quayLai()"
              class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200"
            >
              Quay lại
            </button>
          </div>
        </div>

        <div class="bg-white border border-gray-200 rounded-lg p-6">

          <!-- Tab navigation -->
          <div class="border-b border-gray-200 mb-6">
            <nav class="-mb-px flex space-x-8">
              <button
                (click)="chuyenTab('form')"
                [class]="tabHienTai === 'form'
                  ? 'border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'"
              >
                Thêm lao động
              </button>
              <button
                (click)="chuyenTab('danh-sach')"
                [class]="tabHienTai === 'danh-sach'
                  ? 'border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'"
              >
                Danh sách lao động
                <span *ngIf="toKhai?.danhSachLaoDong?.length"
                      class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium">
                  {{ toKhai.danhSachLaoDong.length }}
                </span>
              </button>
            </nav>
          </div>

          <!-- Tab content -->
          <div [ngSwitch]="tabHienTai">
            <!-- Tab Form thêm lao động -->
            <div *ngSwitchCase="'form'">
              <!-- Loading overlay cho form -->
              <div *ngIf="dangXuLyLaoDong" class="relative">
                <div class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                  <app-loading-spinner></app-loading-spinner>
                </div>
              </div>

              <app-lao-dong-form
                [laoDong]="laoDongDangChinhSua"
                [isEdit]="!!laoDongDangChinhSua"
                [disabled]="dangXuLyLaoDong"
                (luuLaoDong)="onLuuLaoDong($event)"
                (huyForm)="onHuyFormLaoDong()"
              ></app-lao-dong-form>
            </div>

            <!-- Tab Danh sách lao động -->
            <div *ngSwitchCase="'danh-sach'">
              <!-- Empty state -->
              <div *ngIf="!toKhai.danhSachLaoDong || toKhai.danhSachLaoDong.length === 0"
                   class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Chưa có lao động nào</h3>
                <p class="mt-1 text-sm text-gray-500">Chuyển sang tab "Thêm lao động" để bắt đầu thêm lao động.</p>
                <div class="mt-6">
                  <button
                    (click)="chuyenTab('form')"
                    [disabled]="dangXuLyLaoDong"
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    + Thêm lao động
                  </button>
                </div>
              </div>

              <!-- Bảng danh sách lao động -->
              <div *ngIf="toKhai.danhSachLaoDong && toKhai.danhSachLaoDong.length > 0">
                <div class="flex justify-between items-center mb-4">
                  <p class="text-sm text-gray-600">
                    Tổng số lao động: <span class="font-medium">{{ toKhai.danhSachLaoDong.length }}</span>
                  </p>
                  <button
                    (click)="chuyenTab('form')"
                    [disabled]="dangXuLyLaoDong"
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    + Thêm lao động
                  </button>
                </div>

                <!-- Loading overlay cho table -->
                <div class="relative">
                  <div *ngIf="dangXuLyLaoDong" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                    <app-loading-spinner></app-loading-spinner>
                  </div>

                  <app-lao-dong-table
                    [danhSachLaoDong]="toKhai.danhSachLaoDong"
                    [disabled]="dangXuLyLaoDong"
                    (chinhSuaLaoDong)="onChinhSuaLaoDong($event)"
                    (xoaLaoDong)="onXoaLaoDong($event)"
                  ></app-lao-dong-table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
})
export class ChiTietComponent implements OnInit {
  // Inject dependencies
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly toKhaiService = inject(ToKhai602Service);
  private readonly laoDongApiService = inject(LaoDongApiService);

  // Component state
  toKhai: ToKhai602 | null = null;
  dangTai = false;
  loi: string | null = null;

  // Form lao động state
  dangHienThiFormLaoDong = true; // Hiển thị form ngay khi vào trang
  laoDongDangChinhSua: LaoDong | null = null;
  dangXuLyLaoDong = false; // Loading state cho các thao tác lao động

  // Tab state
  tabHienTai: 'form' | 'danh-sach' = 'form';

  // Expose enum for template
  readonly TrangThaiKeKhai = TrangThaiKeKhai;

  ngOnInit() {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.taiToKhai(parseInt(id, 10));
    } else {
      this.loi = 'Không tìm thấy ID tờ khai';
    }
  }

  private taiToKhai(id: number) {
    this.dangTai = true;
    this.loi = null;

    this.toKhaiService.layToKhaiTheoId(id).subscribe({
      next: (toKhai) => {
        this.toKhai = toKhai;
        // Tải danh sách lao động từ backend
        this.taiDanhSachLaoDong(id);
      },
      error: (error) => {
        console.error('Lỗi khi tải tờ khai:', error);
        this.loi = 'Không thể tải thông tin tờ khai';
        this.dangTai = false;
      }
    });
  }

  private taiDanhSachLaoDong(keKhaiId: number) {
    this.laoDongApiService.layDanhSachLaoDong(keKhaiId).subscribe({
      next: (response: any) => {
        if (response.success && response.data) {
          // Cập nhật danh sách lao động từ backend
          this.toKhai!.danhSachLaoDong = response.data;
        } else {
          // Nếu không có dữ liệu từ backend, giữ danh sách rỗng
          this.toKhai!.danhSachLaoDong = [];
        }
        this.dangTai = false;
      },
      error: (error: any) => {
        console.error('Lỗi khi tải danh sách lao động:', error);
        // Vẫn hiển thị tờ khai nhưng với danh sách lao động rỗng
        this.toKhai!.danhSachLaoDong = [];
        this.dangTai = false;
      }
    });
  }

  getTrangThaiText(trangThai: TrangThaiKeKhai): string {
    switch (trangThai) {
      case TrangThaiKeKhai.DangSoan: return 'Đang soạn';
      case TrangThaiKeKhai.DaGui: return 'Đã gửi';
      case TrangThaiKeKhai.DaDuyet: return 'Đã duyệt';
      case TrangThaiKeKhai.BiTuChoi: return 'Bị từ chối';
      default: return 'Không xác định';
    }
  }

  chuyenTab(tab: 'form' | 'danh-sach') {
    this.tabHienTai = tab;
    if (tab === 'form') {
      this.dangHienThiFormLaoDong = true;
      this.laoDongDangChinhSua = null;
    }
  }

  themLaoDong() {
    this.chuyenTab('form');
  }

  onLuuLaoDong(laoDong: LaoDong) {
    if (!this.toKhai) return;

    this.dangXuLyLaoDong = true;

    if (this.laoDongDangChinhSua) {
      // Cập nhật lao động hiện có
      this.capNhatLaoDong(laoDong);
    } else {
      // Thêm lao động mới
      this.themLaoDongMoi(laoDong);
    }
  }

  private themLaoDongMoi(laoDong: LaoDong) {
    if (!this.toKhai) return;

    // Tính STT tự động
    const sttMoi = (this.toKhai.danhSachLaoDong?.length || 0) + 1;
    const laoDongMoi = { ...laoDong, stt: sttMoi };

    const request = this.laoDongApiService.mapLaoDongToRequest(laoDongMoi, this.toKhai.id as number);

    this.laoDongApiService.themLaoDong(request).subscribe({
      next: (response: any) => {
        if (response.success) {
          // Reload danh sách lao động từ backend để có ID thực
          this.taiDanhSachLaoDong(this.toKhai!.id as number);

          this.onHuyFormLaoDong();
          this.chuyenTab('danh-sach');
          console.log('Thêm lao động thành công:', response.message);
        } else {
          this.loi = response.message || 'Có lỗi xảy ra khi thêm lao động';
        }
        this.dangXuLyLaoDong = false;
      },
      error: (error: any) => {
        console.error('Lỗi khi thêm lao động:', error);
        this.loi = 'Có lỗi xảy ra khi thêm lao động';
        this.dangXuLyLaoDong = false;
      }
    });
  }

  private capNhatLaoDong(laoDong: LaoDong) {
    if (!this.toKhai || !this.laoDongDangChinhSua) return;

    // Xử lý ID: nếu là string thì parse, nếu là number thì dùng trực tiếp
    let laoDongId: number;
    if (typeof this.laoDongDangChinhSua.id === 'string') {
      // Nếu là temporary ID, không thể cập nhật
      if (this.laoDongDangChinhSua.id.startsWith('temp_')) {
        console.log('Không thể cập nhật lao động tạm thời chưa được lưu');
        this.dangXuLyLaoDong = false;
        return;
      }
      laoDongId = parseInt(this.laoDongDangChinhSua.id, 10);
    } else {
      laoDongId = this.laoDongDangChinhSua.id as number;
    }

    const request = this.laoDongApiService.mapLaoDongToUpdateRequest(
      laoDong,
      this.toKhai.id as number,
      laoDongId
    );

    this.laoDongApiService.capNhatLaoDong(request).subscribe({
      next: (response: any) => {
        if (response.success) {
          // Reload danh sách lao động từ backend để có dữ liệu mới nhất
          this.taiDanhSachLaoDong(this.toKhai!.id as number);

          this.onHuyFormLaoDong();
          this.chuyenTab('danh-sach');
          console.log('Cập nhật lao động thành công:', response.message);
        } else {
          this.loi = response.message || 'Có lỗi xảy ra khi cập nhật lao động';
        }
        this.dangXuLyLaoDong = false;
      },
      error: (error: any) => {
        console.error('Lỗi khi cập nhật lao động:', error);
        this.loi = 'Có lỗi xảy ra khi cập nhật lao động';
        this.dangXuLyLaoDong = false;
      }
    });
  }

  onHuyFormLaoDong() {
    this.dangHienThiFormLaoDong = false;
    this.laoDongDangChinhSua = null;
  }

  onChinhSuaLaoDong(laoDong: LaoDong) {
    this.laoDongDangChinhSua = { ...laoDong };
    this.chuyenTab('form');
  }

  onXoaLaoDong(laoDong: LaoDong) {
    if (!this.toKhai) return;

    // Xác nhận trước khi xóa
    if (!confirm(`Bạn có chắc chắn muốn xóa lao động "${laoDong.hoTen}"?`)) {
      return;
    }

    this.dangXuLyLaoDong = true;

    // Xử lý ID: nếu là string thì parse, nếu là number thì dùng trực tiếp
    let laoDongId: number;
    if (typeof laoDong.id === 'string') {
      // Nếu là temporary ID, bỏ qua việc xóa vì chưa có trong database
      if (laoDong.id.startsWith('temp_')) {
        console.log('Không thể xóa lao động tạm thời chưa được lưu');
        this.dangXuLyLaoDong = false;
        return;
      }
      laoDongId = parseInt(laoDong.id, 10);
    } else {
      laoDongId = laoDong.id as number;
    }

    this.laoDongApiService.xoaLaoDong(this.toKhai.id as number, laoDongId).subscribe({
      next: (response: any) => {
        if (response.success) {
          // Reload danh sách lao động từ backend để có dữ liệu mới nhất
          this.taiDanhSachLaoDong(this.toKhai!.id as number);
          console.log('Xóa lao động thành công:', response.message);
        } else {
          this.loi = response.message || 'Có lỗi xảy ra khi xóa lao động';
        }
        this.dangXuLyLaoDong = false;
      },
      error: (error: any) => {
        console.error('Lỗi khi xóa lao động:', error);
        this.loi = 'Có lỗi xảy ra khi xóa lao động';
        this.dangXuLyLaoDong = false;
      }
    });
  }



  quayLai() {
    this.router.navigate(['/ke-khai/to-khai-602']);
  }
}

// Export default for lazy loading
export default ChiTietComponent;
