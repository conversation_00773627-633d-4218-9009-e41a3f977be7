import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

/**
 * Interface cho tab item
 */
export interface TabItem {
  id: string;
  nhan: string;
  duongDan: string;
  icon?: string;
  disabled?: boolean;
}

/**
 * Shared component cho tab navigation
 * Sử dụng router để điều hướng giữa các tabs
 */
@Component({
  selector: 'app-tabs',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="tabs-container">
      <!-- Tab Navigation -->
      <div class="tab-nav border-b border-gray-200 bg-white">
        <nav class="flex space-x-8 px-6" aria-label="Tabs">
          <a
            *ngFor="let tab of tabs; trackBy: trackByTabId"
            [routerLink]="tab.duongDan"
            routerLinkActive="active"
            [routerLinkActiveOptions]="{ exact: false }"
            class="tab-link group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
            [class.disabled]="tab.disabled"
            [attr.aria-disabled]="tab.disabled"
          >
            <!-- Icon -->
            <svg
              *ngIf="tab.icon"
              class="tab-icon w-5 h-5 mr-2 transition-colors duration-200"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                [attr.d]="tab.icon"
              ></path>
            </svg>

            <!-- Label -->
            <span>{{ tab.nhan }}</span>
          </a>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="tab-content">
        <ng-content></ng-content>
      </div>
    </div>
  `,
  styles: [`
    .tabs-container {
      @apply w-full;
    }

    .tab-nav {
      @apply sticky top-0 z-10;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .tab-link {
      @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300;
      @apply focus:outline-none focus:text-gray-700 focus:border-gray-300;
    }

    .tab-link.active {
      @apply border-blue-500 text-blue-600;
    }

    .tab-link.disabled {
      @apply text-gray-400 cursor-not-allowed;
      @apply hover:text-gray-400 hover:border-transparent;
    }

    .tab-icon {
      @apply text-gray-400 group-hover:text-gray-500;
    }

    .tab-link.active .tab-icon {
      @apply text-blue-500;
    }

    .tab-link.disabled .tab-icon {
      @apply text-gray-300;
    }

    .tab-content {
      @apply flex-1 min-h-0;
    }

    /* Responsive */
    @media (max-width: 640px) {
      .tab-nav nav {
        @apply px-4 space-x-4;
      }
      
      .tab-link {
        @apply text-xs px-0;
      }
      
      .tab-icon {
        @apply w-4 h-4 mr-1;
      }
    }
  `]
})
export class TabsComponent {
  @Input() tabs: TabItem[] = [];

  /**
   * TrackBy function cho ngFor
   */
  trackByTabId(index: number, tab: TabItem): string {
    return tab.id;
  }
}
