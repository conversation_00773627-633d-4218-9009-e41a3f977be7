using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration cho RefreshToken
/// </summary>
public class RefreshTokenConfiguration : IEntityTypeConfiguration<RefreshToken>
{
    public void Configure(EntityTypeBuilder<RefreshToken> builder)
    {
        // Tên bảng
        builder.ToTable("refresh_token");

        // Primary key
        builder.HasKey(rt => rt.Id);
        builder.Property(rt => rt.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        // Token
        builder.Property(rt => rt.Token)
            .HasColumnName("token")
            .HasMaxLength(500)
            .IsRequired();

        // Tạo unique index cho token
        builder.HasIndex(rt => rt.Token)
            .IsUnique()
            .HasDatabaseName("ix_refresh_token_token");

        // NguoiDungId
        builder.Property(rt => rt.NguoiDungId)
            .HasColumnName("nguoi_dung_id")
            .IsRequired();

        // ThoiGianHetHan
        builder.Property(rt => rt.ThoiGianHetHan)
            .HasColumnName("thoi_gian_het_han")
            .HasColumnType("timestamp with time zone")
            .IsRequired();

        // DaThuHoi
        builder.Property(rt => rt.DaThuHoi)
            .HasColumnName("da_thu_hoi")
            .HasDefaultValue(false)
            .IsRequired();

        // LyDoThuHoi
        builder.Property(rt => rt.LyDoThuHoi)
            .HasColumnName("ly_do_thu_hoi")
            .HasMaxLength(500);

        // IpAddress
        builder.Property(rt => rt.IpAddress)
            .HasColumnName("ip_address")
            .HasMaxLength(45); // IPv6 max length

        // UserAgent
        builder.Property(rt => rt.UserAgent)
            .HasColumnName("user_agent")
            .HasMaxLength(1000);

        // Audit fields từ IAuditableEntity
        builder.Property(rt => rt.NgayTao)
            .HasColumnName("created_at")
            .HasColumnType("timestamp with time zone")
            .IsRequired();

        builder.Property(rt => rt.NguoiTao)
            .HasColumnName("created_by")
            .HasMaxLength(100);

        builder.Property(rt => rt.NgayCapNhat)
            .HasColumnName("updated_at")
            .HasColumnType("timestamp with time zone");

        builder.Property(rt => rt.NguoiCapNhat)
            .HasColumnName("updated_by")
            .HasMaxLength(100);

        // Relationship với NguoiDung
        builder.HasOne(rt => rt.NguoiDung)
            .WithMany() // NguoiDung có thể có nhiều RefreshToken
            .HasForeignKey(rt => rt.NguoiDungId)
            .HasConstraintName("fk_refresh_token_nguoi_dung")
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(rt => rt.NguoiDungId)
            .HasDatabaseName("ix_refresh_token_nguoi_dung_id");

        builder.HasIndex(rt => rt.ThoiGianHetHan)
            .HasDatabaseName("ix_refresh_token_thoi_gian_het_han");

        builder.HasIndex(rt => rt.DaThuHoi)
            .HasDatabaseName("ix_refresh_token_da_thu_hoi");

        // Composite index cho performance
        builder.HasIndex(rt => new { rt.NguoiDungId, rt.DaThuHoi, rt.ThoiGianHetHan })
            .HasDatabaseName("ix_refresh_token_composite");
    }
}
