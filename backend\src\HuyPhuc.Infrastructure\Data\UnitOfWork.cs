using HuyPhuc.Application.Common.Interfaces;
using Microsoft.EntityFrameworkCore.Storage;

namespace HuyPhuc.Infrastructure.Data;

/// <summary>
/// Implementation của Unit of Work pattern
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly ApplicationDbContext _context;

    public UnitOfWork(ApplicationDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// Lưu tất cả thay đổi vào database
    /// </summary>
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Lưu tất cả thay đổi vào database (synchronous)
    /// </summary>
    public int SaveChanges()
    {
        return _context.SaveChanges();
    }

    /// <summary>
    /// Bắt đầu transaction
    /// </summary>
    public async Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        return new DbTransactionWrapper(transaction);
    }

    /// <summary>
    /// Bắt đầu transaction (synchronous)
    /// </summary>
    public IDbTransaction BeginTransaction()
    {
        var transaction = _context.Database.BeginTransaction();
        return new DbTransactionWrapper(transaction);
    }
}

/// <summary>
/// Wrapper cho EF Core database transaction
/// </summary>
internal class DbTransactionWrapper : IDbTransaction
{
    private readonly IDbContextTransaction _transaction;
    private bool _disposed = false;

    public DbTransactionWrapper(IDbContextTransaction transaction)
    {
        _transaction = transaction;
    }

    /// <summary>
    /// Commit transaction
    /// </summary>
    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        await _transaction.CommitAsync(cancellationToken);
    }

    /// <summary>
    /// Commit transaction (synchronous)
    /// </summary>
    public void Commit()
    {
        _transaction.Commit();
    }

    /// <summary>
    /// Rollback transaction
    /// </summary>
    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        await _transaction.RollbackAsync(cancellationToken);
    }

    /// <summary>
    /// Rollback transaction (synchronous)
    /// </summary>
    public void Rollback()
    {
        _transaction.Rollback();
    }

    /// <summary>
    /// Dispose transaction
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _transaction?.Dispose();
            _disposed = true;
        }
    }
}
