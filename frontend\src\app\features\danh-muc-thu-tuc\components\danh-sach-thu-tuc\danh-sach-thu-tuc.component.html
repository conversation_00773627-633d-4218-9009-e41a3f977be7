<div class="danh-sach-thu-tuc-container p-6">
  <!-- Header -->
  <div class="header-section mb-6">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900"><PERSON>h mục thủ tục</h1>
        <p class="text-gray-600 mt-1">Quản lý danh sách các thủ tục hành chính</p>
      </div>
      <button class="btn-primary px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        <i class="fas fa-plus mr-2"></i>
        Thêm mới
      </button>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="filter-section bg-white rounded-lg shadow-sm border p-4 mb-6">
    <form [formGroup]="formTimKiem" class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- T<PERSON><PERSON> kiếm theo từ khóa -->
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 mb-2">Tìm kiếm</label>
        <input
          type="text"
          formControlName="tuKhoa"
          placeholder="Nhập mã hoặc tên thủ tục..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      <!-- Lọc theo lĩnh vực -->
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 mb-2">Lĩnh vực</label>
        <select
          formControlName="linhVuc"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Tất cả lĩnh vực</option>
          <option *ngFor="let linhVuc of danhSachLinhVuc" [value]="linhVuc.id">
            {{ linhVuc.ten }}
          </option>
        </select>
      </div>

      <!-- Lọc theo trạng thái -->
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 mb-2">Trạng thái</label>
        <select
          formControlName="trangThai"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Tất cả trạng thái</option>
          <option *ngFor="let trangThai of danhSachTrangThai" [value]="trangThai.id">
            {{ trangThai.ten }}
          </option>
        </select>
      </div>

      <!-- Nút reset -->
      <div class="form-group flex items-end">
        <button
          type="button"
          (click)="resetTimKiem()"
          class="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
        >
          <i class="fas fa-refresh mr-2"></i>
          Reset
        </button>
      </div>
    </form>
  </div>

  <!-- Loading State -->
  <div *ngIf="dangTai" class="loading-section flex justify-center items-center py-12">
    <div class="flex items-center space-x-2">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      <span class="text-gray-600">Đang tải dữ liệu...</span>
    </div>
  </div>

  <!-- Data Table -->
  <div *ngIf="!dangTai" class="table-section bg-white rounded-lg shadow-sm border overflow-hidden">
    <!-- Table Header -->
    <div class="table-header bg-gray-50 px-6 py-3 border-b">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-700">
          Hiển thị {{ (trangHienTai - 1) * kichThuocTrang + 1 }} - 
          {{ Math.min(trangHienTai * kichThuocTrang, tongSoBanGhi) }} 
          trong tổng số {{ tongSoBanGhi }} bản ghi
        </div>
        <div class="flex items-center space-x-2">
          <label class="text-sm text-gray-700">Hiển thị:</label>
          <select
            [value]="kichThuocTrang"
            (change)="onKichThuocTrangChange($event)"
            class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
          <span class="text-sm text-gray-700">bản ghi</span>
        </div>
      </div>
    </div>

    <!-- Table Content -->
    <div class="table-content">
      <table class="w-full">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STT</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mã thủ tục</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên thủ tục</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lĩnh vực</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let thuTuc of danhSachThuTuc; let i = index" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ (trangHienTai - 1) * kichThuocTrang + i + 1 }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="text-sm font-medium text-blue-600">{{ thuTuc.ma }}</span>
            </td>
            <td class="px-6 py-4">
              <div class="text-sm text-gray-900 max-w-md">
                <p class="line-clamp-2" [title]="thuTuc.ten">{{ thuTuc.ten }}</p>
              </div>
            </td>
            <td class="px-6 py-4">
              <div class="text-sm text-gray-600 max-w-xs">
                <p class="line-clamp-2" [title]="thuTuc.tenLinhVuc">{{ thuTuc.tenLinhVuc }}</p>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span 
                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                [class]="layClassTrangThai(thuTuc.trangThai)"
              >
                {{ layTenTrangThai(thuTuc.trangThai) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button
                  (click)="xemChiTiet(thuTuc)"
                  class="text-blue-600 hover:text-blue-900 transition-colors"
                  title="Xem chi tiết"
                >
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  (click)="chinhSua(thuTuc)"
                  class="text-green-600 hover:text-green-900 transition-colors"
                  title="Chỉnh sửa"
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button
                  (click)="xoa(thuTuc)"
                  class="text-red-600 hover:text-red-900 transition-colors"
                  title="Xóa"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Empty State -->
      <div *ngIf="danhSachThuTuc.length === 0" class="empty-state text-center py-12">
        <div class="text-gray-400 mb-4">
          <i class="fas fa-inbox text-4xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Không có dữ liệu</h3>
        <p class="text-gray-600">Không tìm thấy thủ tục nào phù hợp với điều kiện tìm kiếm.</p>
      </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="tongSoTrang > 1" class="pagination-section bg-gray-50 px-6 py-3 border-t">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-700">
          Trang {{ trangHienTai }} / {{ tongSoTrang }}
        </div>
        <nav class="flex space-x-1">
          <!-- Previous Button -->
          <button
            (click)="chuyenTrang(trangHienTai - 1)"
            [disabled]="trangHienTai === 1"
            class="px-3 py-1 text-sm border rounded-md transition-colors"
            [class.bg-gray-100]="trangHienTai === 1"
            [class.text-gray-400]="trangHienTai === 1"
            [class.bg-white]="trangHienTai > 1"
            [class.text-gray-700]="trangHienTai > 1"
            [class.hover:bg-gray-50]="trangHienTai > 1"
          >
            <i class="fas fa-chevron-left"></i>
          </button>

          <!-- Page Numbers -->
          <button
            *ngFor="let trang of layDanhSachTrang()"
            (click)="chuyenTrang(trang)"
            class="px-3 py-1 text-sm border rounded-md transition-colors"
            [class.bg-blue-600]="trang === trangHienTai"
            [class.text-white]="trang === trangHienTai"
            [class.bg-white]="trang !== trangHienTai"
            [class.text-gray-700]="trang !== trangHienTai"
            [class.hover:bg-gray-50]="trang !== trangHienTai"
          >
            {{ trang }}
          </button>

          <!-- Next Button -->
          <button
            (click)="chuyenTrang(trangHienTai + 1)"
            [disabled]="trangHienTai === tongSoTrang"
            class="px-3 py-1 text-sm border rounded-md transition-colors"
            [class.bg-gray-100]="trangHienTai === tongSoTrang"
            [class.text-gray-400]="trangHienTai === tongSoTrang"
            [class.bg-white]="trangHienTai < tongSoTrang"
            [class.text-gray-700]="trangHienTai < tongSoTrang"
            [class.hover:bg-gray-50]="trangHienTai < tongSoTrang"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </nav>
      </div>
    </div>
  </div>
</div>
