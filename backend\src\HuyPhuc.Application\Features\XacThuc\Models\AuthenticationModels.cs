using HuyPhuc.Domain.Entities;

namespace HuyPhuc.Application.Features.XacThuc.Models;

/// <summary>
/// Model cho request đăng nhập
/// </summary>
public class DangNhapRequest
{
    public string Username { get; set; } = string.Empty;
    public string MatKhau { get; set; } = string.Empty;
    public bool GhiNhoDangNhap { get; set; } = false;
}

/// <summary>
/// Model cho response đăng nhập
/// </summary>
public class DangNhapResponse
{
    public AuthNguoiDungDto NguoiDung { get; set; } = null!;
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime ThoiGianHetHan { get; set; }
}

/// <summary>
/// Model cho request làm mới token
/// </summary>
public class LamMoiTokenRequest
{
    public string RefreshToken { get; set; } = string.Empty;
}

/// <summary>
/// Model cho response làm mới token
/// </summary>
public class LamMoiTokenResponse
{
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime ThoiGianHetHan { get; set; }
}

/// <summary>
/// DTO cho thông tin người dùng trong authentication response
/// </summary>
public class AuthNguoiDungDto
{
    public int Id { get; set; }
    public string HoTen { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? SoDienThoai { get; set; }
    public string? AvatarUrl { get; set; }
    public string? Username { get; set; }
    public string TrangThai { get; set; } = string.Empty;
    public DateTime NgayTao { get; set; }
    public DateTime? LastLogin { get; set; }

    /// <summary>
    /// Tạo DTO từ entity
    /// </summary>
    public static AuthNguoiDungDto TuEntity(Domain.Entities.NguoiDung nguoiDung)
    {
        return new AuthNguoiDungDto
        {
            Id = nguoiDung.Id,
            HoTen = nguoiDung.HoTen,
            Email = nguoiDung.Email.Value,
            SoDienThoai = nguoiDung.SoDienThoai?.Value,
            AvatarUrl = nguoiDung.AvatarUrl,
            Username = nguoiDung.Username,
            TrangThai = nguoiDung.TrangThai.ToString(),
            NgayTao = nguoiDung.NgayTao,
            LastLogin = nguoiDung.LastLogin
        };
    }
}
