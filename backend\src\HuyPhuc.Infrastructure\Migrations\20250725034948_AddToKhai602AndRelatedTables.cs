﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HuyPhuc.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddToKhai602AndRelatedTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // <PERSON><PERSON> dropping foreign key from DonHang table as it doesn't exist
            // migrationBuilder.DropForeignKey(
            //     name: "FK_DonHang_dm_nguoi_dung_NguoiDungId",
            //     table: "DonHang");

            // Skip table renaming operations as they have already been done
            // migrationBuilder.DropPrimaryKey(
            //     name: "PK_dm_refresh_token",
            //     table: "dm_refresh_token");

            // migrationBuilder.DropPrimaryKey(
            //     name: "PK_dm_nguoi_dung",
            //     table: "dm_nguoi_dung");

            // migrationBuilder.RenameTable(
            //     name: "dm_refresh_token",
            //     newName: "refresh_token");

            // migrationBuilder.RenameTable(
            //     name: "dm_nguoi_dung",
            //     newName: "nguoi_dung");

            // Skip adding dai_ly_id column as it already exists
            // migrationBuilder.AddColumn<int>(
            //     name: "dai_ly_id",
            //     table: "nguoi_dung",
            //     type: "integer",
            //     nullable: true);

            // Skip adding primary keys as they already exist
            // migrationBuilder.AddPrimaryKey(
            //     name: "PK_refresh_token",
            //     table: "refresh_token",
            //     column: "id");

            // migrationBuilder.AddPrimaryKey(
            //     name: "PK_nguoi_dung",
            //     table: "nguoi_dung",
            //     column: "id");

            migrationBuilder.CreateTable(
                name: "dai_ly",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ma_dai_ly = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ten_dai_ly = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    dia_chi = table.Column<string>(type: "text", nullable: true),
                    so_dien_thoai = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    email = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ma_so_thue = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    nguoi_dai_dien = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    trang_thai_hoat_dong = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ghi_chu = table.Column<string>(type: "text", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    updated_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dai_ly", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "danh_muc_thu_tuc",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ma = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ten = table.Column<string>(type: "TEXT", nullable: false),
                    linh_vuc = table.Column<int>(type: "integer", nullable: false),
                    ten_linh_vuc = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ngay_ap_dung = table.Column<DateTime>(type: "DATE", nullable: true),
                    trang_thai = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    mo_ta = table.Column<string>(type: "TEXT", nullable: true),
                    thoi_gian_xu_ly = table.Column<int>(type: "integer", nullable: true),
                    phi_thuc_hien = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    co_quan_thuc_hien = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    can_cu_phap_ly = table.Column<string>(type: "TEXT", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    created_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    updated_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_danh_muc_thu_tuc", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "quyen",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ten_quyen = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ma_quyen = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    mo_ta = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    nhom_quyen = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    la_quyen_he_thong = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    trang_thai_hoat_dong = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ngay_tao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ngay_cap_nhat = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    nguoi_tao = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    nguoi_cap_nhat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_quyen", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "vai_tro",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ten_vai_tro = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    mo_ta = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    la_vai_tro_he_thong = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    trang_thai_hoat_dong = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ngay_tao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ngay_cap_nhat = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    nguoi_tao = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    nguoi_cap_nhat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vai_tro", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "don_vi",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ma_don_vi = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ten_don_vi = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    dia_chi = table.Column<string>(type: "text", nullable: true),
                    so_dien_thoai = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    email = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    dai_ly_id = table.Column<int>(type: "integer", nullable: false),
                    trang_thai_hoat_dong = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ghi_chu = table.Column<string>(type: "text", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    updated_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_don_vi", x => x.id);
                    table.ForeignKey(
                        name: "FK_don_vi_dai_ly_dai_ly_id",
                        column: x => x.dai_ly_id,
                        principalTable: "dai_ly",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "nguoi_dung_vai_tro",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    nguoi_dung_id = table.Column<int>(type: "integer", nullable: false),
                    vai_tro_id = table.Column<int>(type: "integer", nullable: false),
                    ngay_gan = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ngay_het_han = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    trang_thai_hoat_dong = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ghi_chu = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ngay_tao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ngay_cap_nhat = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    nguoi_tao = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    nguoi_cap_nhat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_nguoi_dung_vai_tro", x => x.id);
                    table.ForeignKey(
                        name: "FK_nguoi_dung_vai_tro_nguoi_dung_nguoi_dung_id",
                        column: x => x.nguoi_dung_id,
                        principalTable: "nguoi_dung",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_nguoi_dung_vai_tro_vai_tro_vai_tro_id",
                        column: x => x.vai_tro_id,
                        principalTable: "vai_tro",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "vai_tro_quyen",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    vai_tro_id = table.Column<int>(type: "integer", nullable: false),
                    quyen_id = table.Column<int>(type: "integer", nullable: false),
                    ngay_gan = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    trang_thai_hoat_dong = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ghi_chu = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ngay_tao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ngay_cap_nhat = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    nguoi_tao = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    nguoi_cap_nhat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_vai_tro_quyen", x => x.id);
                    table.ForeignKey(
                        name: "FK_vai_tro_quyen_quyen_quyen_id",
                        column: x => x.quyen_id,
                        principalTable: "quyen",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_vai_tro_quyen_vai_tro_vai_tro_id",
                        column: x => x.vai_tro_id,
                        principalTable: "vai_tro",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "to_khai_602",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ma_to_khai = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    dai_ly_id = table.Column<int>(type: "integer", nullable: false),
                    don_vi_id = table.Column<int>(type: "integer", nullable: false),
                    so_so_bhxh = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ghi_chu = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    trang_thai = table.Column<int>(type: "integer", nullable: false),
                    nguoi_tao_id = table.Column<int>(type: "integer", nullable: false),
                    ngay_phe_duyet = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    nguoi_phe_duyet_id = table.Column<int>(type: "integer", nullable: true),
                    ly_do_tu_choi = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ngay_tao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_to_khai_602", x => x.Id);
                    table.ForeignKey(
                        name: "fk_to_khai_602_dai_ly",
                        column: x => x.dai_ly_id,
                        principalTable: "dai_ly",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_to_khai_602_don_vi",
                        column: x => x.don_vi_id,
                        principalTable: "don_vi",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_to_khai_602_nguoi_phe_duyet",
                        column: x => x.nguoi_phe_duyet_id,
                        principalTable: "nguoi_dung",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "fk_to_khai_602_nguoi_tao",
                        column: x => x.nguoi_tao_id,
                        principalTable: "nguoi_dung",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "lao_dong_to_khai_602",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    to_khai_602_id = table.Column<int>(type: "integer", nullable: false),
                    ho_ten = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    so_cccd = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ngay_sinh = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    gioi_tinh = table.Column<bool>(type: "boolean", nullable: false),
                    dia_chi = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    so_dien_thoai = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    nghe_nghiep = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    muc_luong = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    ngay_bat_dau = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ngay_ket_thuc = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ghi_chu = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_lao_dong_to_khai_602", x => x.Id);
                    table.ForeignKey(
                        name: "fk_lao_dong_to_khai_602",
                        column: x => x.to_khai_602_id,
                        principalTable: "to_khai_602",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_nguoi_dung_dai_ly_id",
                table: "nguoi_dung",
                column: "dai_ly_id");

            migrationBuilder.CreateIndex(
                name: "idx_dai_ly_ma_dai_ly",
                table: "dai_ly",
                column: "ma_dai_ly",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_dai_ly_trang_thai",
                table: "dai_ly",
                column: "trang_thai_hoat_dong");

            migrationBuilder.CreateIndex(
                name: "idx_danh_muc_thu_tuc_created",
                table: "danh_muc_thu_tuc",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "idx_danh_muc_thu_tuc_linh_vuc",
                table: "danh_muc_thu_tuc",
                column: "linh_vuc");

            migrationBuilder.CreateIndex(
                name: "idx_danh_muc_thu_tuc_linh_vuc_trang_thai",
                table: "danh_muc_thu_tuc",
                columns: new[] { "linh_vuc", "trang_thai" });

            migrationBuilder.CreateIndex(
                name: "idx_danh_muc_thu_tuc_ma",
                table: "danh_muc_thu_tuc",
                column: "ma",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_danh_muc_thu_tuc_search",
                table: "danh_muc_thu_tuc",
                columns: new[] { "ma", "ten", "ten_linh_vuc" });

            migrationBuilder.CreateIndex(
                name: "idx_danh_muc_thu_tuc_trang_thai",
                table: "danh_muc_thu_tuc",
                column: "trang_thai");

            migrationBuilder.CreateIndex(
                name: "idx_danh_muc_thu_tuc_trang_thai_ngay_ap_dung",
                table: "danh_muc_thu_tuc",
                columns: new[] { "trang_thai", "ngay_ap_dung" });

            migrationBuilder.CreateIndex(
                name: "idx_danh_muc_thu_tuc_updated",
                table: "danh_muc_thu_tuc",
                column: "updated_at");

            migrationBuilder.CreateIndex(
                name: "idx_don_vi_dai_ly_id",
                table: "don_vi",
                column: "dai_ly_id");

            migrationBuilder.CreateIndex(
                name: "idx_don_vi_ma_don_vi",
                table: "don_vi",
                column: "ma_don_vi");

            migrationBuilder.CreateIndex(
                name: "idx_don_vi_trang_thai",
                table: "don_vi",
                column: "trang_thai_hoat_dong");

            migrationBuilder.CreateIndex(
                name: "uk_don_vi_ma_dai_ly",
                table: "don_vi",
                columns: new[] { "ma_don_vi", "dai_ly_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_lao_dong_to_khai_602_ngay_bat_dau",
                table: "lao_dong_to_khai_602",
                column: "ngay_bat_dau");

            migrationBuilder.CreateIndex(
                name: "idx_lao_dong_to_khai_602_so_cccd",
                table: "lao_dong_to_khai_602",
                column: "so_cccd");

            migrationBuilder.CreateIndex(
                name: "idx_lao_dong_to_khai_602_to_khai_id",
                table: "lao_dong_to_khai_602",
                column: "to_khai_602_id");

            migrationBuilder.CreateIndex(
                name: "ix_nguoi_dung_vai_tro_composite",
                table: "nguoi_dung_vai_tro",
                columns: new[] { "nguoi_dung_id", "vai_tro_id" });

            migrationBuilder.CreateIndex(
                name: "ix_nguoi_dung_vai_tro_ngay_het_han",
                table: "nguoi_dung_vai_tro",
                column: "ngay_het_han");

            migrationBuilder.CreateIndex(
                name: "ix_nguoi_dung_vai_tro_trang_thai_hoat_dong",
                table: "nguoi_dung_vai_tro",
                column: "trang_thai_hoat_dong");

            migrationBuilder.CreateIndex(
                name: "IX_nguoi_dung_vai_tro_vai_tro_id",
                table: "nguoi_dung_vai_tro",
                column: "vai_tro_id");

            migrationBuilder.CreateIndex(
                name: "ix_quyen_ma_quyen",
                table: "quyen",
                column: "ma_quyen",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_quyen_nhom_quyen",
                table: "quyen",
                column: "nhom_quyen");

            migrationBuilder.CreateIndex(
                name: "ix_quyen_ten_quyen",
                table: "quyen",
                column: "ten_quyen");

            migrationBuilder.CreateIndex(
                name: "ix_quyen_trang_thai_hoat_dong",
                table: "quyen",
                column: "trang_thai_hoat_dong");

            migrationBuilder.CreateIndex(
                name: "IX_to_khai_602_dai_ly_id",
                table: "to_khai_602",
                column: "dai_ly_id");

            migrationBuilder.CreateIndex(
                name: "IX_to_khai_602_don_vi_id",
                table: "to_khai_602",
                column: "don_vi_id");

            migrationBuilder.CreateIndex(
                name: "IX_to_khai_602_ma_to_khai",
                table: "to_khai_602",
                column: "ma_to_khai",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_to_khai_602_ngay_tao",
                table: "to_khai_602",
                column: "ngay_tao");

            migrationBuilder.CreateIndex(
                name: "IX_to_khai_602_nguoi_phe_duyet_id",
                table: "to_khai_602",
                column: "nguoi_phe_duyet_id");

            migrationBuilder.CreateIndex(
                name: "IX_to_khai_602_nguoi_tao_id",
                table: "to_khai_602",
                column: "nguoi_tao_id");

            migrationBuilder.CreateIndex(
                name: "IX_to_khai_602_trang_thai",
                table: "to_khai_602",
                column: "trang_thai");

            migrationBuilder.CreateIndex(
                name: "ix_vai_tro_ten_vai_tro",
                table: "vai_tro",
                column: "ten_vai_tro",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_vai_tro_trang_thai_hoat_dong",
                table: "vai_tro",
                column: "trang_thai_hoat_dong");

            migrationBuilder.CreateIndex(
                name: "ix_vai_tro_quyen_composite",
                table: "vai_tro_quyen",
                columns: new[] { "vai_tro_id", "quyen_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_vai_tro_quyen_quyen_id",
                table: "vai_tro_quyen",
                column: "quyen_id");

            migrationBuilder.CreateIndex(
                name: "ix_vai_tro_quyen_trang_thai_hoat_dong",
                table: "vai_tro_quyen",
                column: "trang_thai_hoat_dong");

            // Skip adding foreign key to DonHang table as it doesn't exist
            // migrationBuilder.AddForeignKey(
            //     name: "FK_DonHang_nguoi_dung_NguoiDungId",
            //     table: "DonHang",
            //     column: "NguoiDungId",
            //     principalTable: "nguoi_dung",
            //     principalColumn: "id",
            //     onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_nguoi_dung_dai_ly_dai_ly_id",
                table: "nguoi_dung",
                column: "dai_ly_id",
                principalTable: "dai_ly",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Skip dropping foreign key from DonHang table as it doesn't exist
            // migrationBuilder.DropForeignKey(
            //     name: "FK_DonHang_nguoi_dung_NguoiDungId",
            //     table: "DonHang");

            migrationBuilder.DropForeignKey(
                name: "FK_nguoi_dung_dai_ly_dai_ly_id",
                table: "nguoi_dung");

            migrationBuilder.DropTable(
                name: "danh_muc_thu_tuc");

            migrationBuilder.DropTable(
                name: "lao_dong_to_khai_602");

            migrationBuilder.DropTable(
                name: "nguoi_dung_vai_tro");

            migrationBuilder.DropTable(
                name: "vai_tro_quyen");

            migrationBuilder.DropTable(
                name: "to_khai_602");

            migrationBuilder.DropTable(
                name: "quyen");

            migrationBuilder.DropTable(
                name: "vai_tro");

            migrationBuilder.DropTable(
                name: "don_vi");

            migrationBuilder.DropTable(
                name: "dai_ly");

            migrationBuilder.DropPrimaryKey(
                name: "PK_refresh_token",
                table: "refresh_token");

            migrationBuilder.DropPrimaryKey(
                name: "PK_nguoi_dung",
                table: "nguoi_dung");

            migrationBuilder.DropIndex(
                name: "IX_nguoi_dung_dai_ly_id",
                table: "nguoi_dung");

            migrationBuilder.DropColumn(
                name: "dai_ly_id",
                table: "nguoi_dung");

            migrationBuilder.RenameTable(
                name: "refresh_token",
                newName: "dm_refresh_token");

            migrationBuilder.RenameTable(
                name: "nguoi_dung",
                newName: "dm_nguoi_dung");

            migrationBuilder.AddPrimaryKey(
                name: "PK_dm_refresh_token",
                table: "dm_refresh_token",
                column: "id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_dm_nguoi_dung",
                table: "dm_nguoi_dung",
                column: "id");

            // Skip adding foreign key to DonHang table as it doesn't exist
            // migrationBuilder.AddForeignKey(
            //     name: "FK_DonHang_dm_nguoi_dung_NguoiDungId",
            //     table: "DonHang",
            //     column: "NguoiDungId",
            //     principalTable: "dm_nguoi_dung",
            //     principalColumn: "id",
            //     onDelete: ReferentialAction.Restrict);
        }
    }
}
