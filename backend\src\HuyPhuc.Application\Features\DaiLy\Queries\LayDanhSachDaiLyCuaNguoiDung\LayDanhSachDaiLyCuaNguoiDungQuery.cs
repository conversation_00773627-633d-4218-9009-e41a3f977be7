using HuyPhuc.Application.DTOs.DaiLy;
using HuyPhuc.Application.Common.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.DaiLy.Queries.LayDanhSachDaiLyCuaNguoiDung;

/// <summary>
/// Query lấy danh sách đại lý của người dùng hiện tại
/// </summary>
public record LayDanhSachDaiLyCuaNguoiDungQuery : IRequest<List<DaiLySelectDto>>;

public class LayDanhSachDaiLyCuaNguoiDungQueryHandler : IRequestHandler<LayDanhSachDaiLyCuaNguoiDungQuery, List<DaiLySelectDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public LayDanhSachDaiLyCuaNguoiDungQueryHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<List<DaiLySelectDto>> Handle(LayDanhSachDaiLyCuaNguoiDungQuery request, CancellationToken cancellationToken)
    {
        var userIdString = _currentUserService.UserId;
        Console.WriteLine($"DEBUG: UserIdString = {userIdString}");

        if (userIdString == null || !int.TryParse(userIdString, out var userId))
        {
            Console.WriteLine($"DEBUG: Cannot parse userId from {userIdString}");
            return new List<DaiLySelectDto>();
        }

        Console.WriteLine($"DEBUG: Parsed userId = {userId}");

        // Lấy thông tin người dùng và đại lý của họ
        var nguoiDung = await _context.NguoiDung
            .Include(x => x.DaiLy)
            .FirstOrDefaultAsync(x => x.Id == userId, cancellationToken);

        Console.WriteLine($"DEBUG: Found user = {nguoiDung?.HoTen}, DaiLy = {nguoiDung?.DaiLy?.TenDaiLy}");

        if (nguoiDung?.DaiLy == null)
        {
            Console.WriteLine("DEBUG: User or DaiLy is null");
            return new List<DaiLySelectDto>();
        }

        // Trả về đại lý của người dùng (hiện tại mỗi user chỉ thuộc 1 đại lý)
        var result = new List<DaiLySelectDto>
        {
            new DaiLySelectDto
            {
                Id = nguoiDung.DaiLy.Id,
                MaDaiLy = nguoiDung.DaiLy.MaDaiLy,
                TenDaiLy = nguoiDung.DaiLy.TenDaiLy,
                DiaChi = nguoiDung.DaiLy.DiaChi,
                SoDienThoai = nguoiDung.DaiLy.SoDienThoai,
                Email = nguoiDung.DaiLy.Email,
                TrangThaiHoatDong = nguoiDung.DaiLy.TrangThaiHoatDong
            }
        };

        Console.WriteLine($"DEBUG: Returning {result.Count} dai ly: {result.FirstOrDefault()?.TenDaiLy}");
        return result;
    }
}
