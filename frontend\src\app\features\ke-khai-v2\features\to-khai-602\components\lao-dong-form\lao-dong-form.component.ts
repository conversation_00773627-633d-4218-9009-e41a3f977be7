import { Component, OnInit, inject, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';

import { LaoDong } from '../../../../core/models';
import { ThongTinCoBanComponent } from './thong-tin-co-ban/thong-tin-co-ban.component';
import { ThongTinDongBhxhComponent } from './thong-tin-dong-bhxh/thong-tin-dong-bhxh.component';

/**
 * Component form nhập thông tin lao động
 */
@Component({
  selector: 'app-lao-dong-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ThongTinCoBanComponent,
    ThongTinDongBhxhComponent
  ],
  templateUrl: './lao-dong-form.component.html',
  styleUrls: ['./lao-dong-form.component.scss']
})
export class LaoDongFormComponent implements OnInit {
  @Input() laoDong: LaoDong | null = null;
  @Input() isEdit = false;
  @Input() disabled = false;
  @Output() luuLaoDong = new EventEmitter<LaoDong>();
  @Output() huyForm = new EventEmitter<void>();

  // Inject dependencies
  private readonly fb = inject(FormBuilder);

  // Component state
  form!: FormGroup;
  dangLuu = false;

  ngOnInit() {
    this.taoForm();
    if (this.laoDong && this.isEdit) {
      this.form.patchValue(this.laoDong);
    }
  }

  private taoForm() {
    this.form = this.fb.group({
      hoTen: ['', [Validators.required]],
      cmnd: ['', [Validators.required]],
      ccns: ['0'], // Mặc định là "Đầy đủ"
      ngaySinh: ['', [Validators.required]],
      gioiTinh: ['', [Validators.required]],
      maSoBHXH: ['', [Validators.required]],
      quocTich: ['Việt Nam'],
      danToc: ['01'], // Mã dân tộc Kinh
      // Địa chỉ (có thể để trống tạm thời)
      maTinhKs: [''],
      maHuyenKs: [''],
      maXaKs: [''],
      dienThoaiLh: [''],
      maHoGiaDinh: [''],
      // Thông tin BHXH
      mucThuNhap: [1500000, [Validators.required, Validators.min(1500000)]],
      thangBatDau: ['', [Validators.required]],
      phuongAn: ['', [Validators.required]],
      phuongThuc: ['', [Validators.required]],
      soThang: ['', [Validators.required]],
      tienLai: [0],
      tienThua: [0],
      tienTuDong: [0],
      tongTien: [0],
      tienHoTro: [0],
      // Thông tin bổ sung
      loai: [1], // Đổi từ typeId thành loai, sử dụng ID từ dm_loai
      loaiNsnn: ['khac'], // Loại NSNN - mặc định là "khác"
      tyLeNsnnHoTro: [20], // Tỷ lệ NSNN hỗ trợ (%) - mặc định 20% cho "khác"
      soThangNsnnHoTro: [0], // Số tháng NSNN hỗ trợ - tự động tính dựa vào số tháng
      ngayBienLai: [''], // Ngày biên lai
      soBienLai: [''], // Số biên lai
      heSoDong: [0], // Hệ số đóng
      tyLeNldDong: [22], // Tỷ lệ NLD đóng (%) - mặc định 22%
      isThamGiaBb: [false],
      isTamHoanHD: [false],
      // Thông tin lỗi/thông báo
      message: [''],
      isError: [false],
      maLoi: [''],
      moTaLoi: [''],
      ghiChu: ['']
    });
  }

  onSubmit() {
    if (this.form.valid) {
      this.dangLuu = true;

      const formValue = this.form.value;

      // Format ngaySinh từ yyyy-MM-dd sang dd/MM/yyyy
      let ngaySinhFormatted = '';
      if (formValue.ngaySinh) {
        try {
          const ngaySinhDate = new Date(formValue.ngaySinh);
          if (!isNaN(ngaySinhDate.getTime())) {
            ngaySinhFormatted = `${ngaySinhDate.getDate().toString().padStart(2, '0')}/${(ngaySinhDate.getMonth() + 1).toString().padStart(2, '0')}/${ngaySinhDate.getFullYear()}`;
          }
        } catch (error) {
          console.warn('Lỗi convert ngày sinh khi submit:', error);
        }
      }

      // Format thangBatDau từ YYYY-MM sang MM/yyyy
      let thangBatDauFormatted = '';
      if (formValue.thangBatDau) {
        try {
          const [year, month] = formValue.thangBatDau.split('-');
          thangBatDauFormatted = `${month}/${year}`;
        } catch (error) {
          console.warn('Lỗi convert tháng bắt đầu khi submit:', error);
        }
      }

      const laoDongData: LaoDong = {
        id: this.laoDong?.id,
        ...formValue,
        ngaySinh: ngaySinhFormatted,
        thangBatDau: thangBatDauFormatted,
        // Thêm các field bắt buộc từ model
        maSoBHXH: formValue.maSoBHXH,
        quocTich: formValue.quocTich || 'Việt Nam',
        danToc: formValue.danToc || 'Kinh',
        maTinhKs: formValue.maTinhKs || '',
        maHuyenKs: formValue.maHuyenKs || '',
        maXaKs: formValue.maXaKs || '',
        dienThoaiLh: formValue.dienThoaiLh || '',
        maHoGiaDinh: formValue.maHoGiaDinh || '',
        ccns: formValue.ccns || ''
      };

      // Simulate API call
      setTimeout(() => {
        this.luuLaoDong.emit(laoDongData);
        this.dangLuu = false;
      }, 1000);
    }
  }

  /**
   * Mapping phương thức đóng từ API sang frontend format
   */
  private mapPhuongThucFromApi(apiPhuongThuc: string): string {
    const mapping: { [key: string]: string } = {
      '1': '1-thang',
      '3': '3-thang',
      '6': '6-thang',
      '12': '12-thang',
      // Có thể API trả về các giá trị khác
      'nam-sau': 'nam-sau',
      'nam-thieu': 'nam-thieu'
    };

    console.log(`🔄 Mapping phương thức từ API: "${apiPhuongThuc}" -> "${mapping[apiPhuongThuc] || apiPhuongThuc}"`);
    return mapping[apiPhuongThuc] || apiPhuongThuc;
  }

  /**
   * Tự động set số tháng dựa trên phương thức đóng từ API
   */
  private autoSetSoThangFromApi(phuongThuc: string) {
    const soThangMapping: { [key: string]: number } = {
      '1-thang': 1,
      '3-thang': 3,
      '6-thang': 6,
      '12-thang': 12
    };

    if (soThangMapping[phuongThuc]) {
      this.form.get('soThang')?.setValue(soThangMapping[phuongThuc]);
      console.log(`🔢 Tự động set số tháng: ${soThangMapping[phuongThuc]} cho phương thức: ${phuongThuc}`);
    }
  }

  /**
   * Tính toán hệ số đóng từ mức thu nhập
   */
  private calculateHeSoDong(mucThuNhap: number) {
    if (mucThuNhap && mucThuNhap >= 1500000) {
      const heSoDong = Math.floor((mucThuNhap - 1500000) / 50000);
      this.form.get('heSoDong')?.setValue(heSoDong);
      console.log(`💰 Tính hệ số đóng từ API: ${heSoDong} cho mức thu nhập ${mucThuNhap.toLocaleString('vi-VN')} VNĐ`);
    } else {
      this.form.get('heSoDong')?.setValue(0);
    }
  }

  /**
   * Xử lý khi tra cứu BHXH thành công từ component con
   */
  onBhxhLookupSuccess(data: any) {
    // Convert tháng bắt đầu từ MM/yyyy sang yyyy-MM cho input month
    let thangBatDauFormatted = '';
    if (data.thangBatDau) {
      try {
        // Nếu API trả về format MM/yyyy
        if (data.thangBatDau.includes('/')) {
          const [month, year] = data.thangBatDau.split('/');
          thangBatDauFormatted = `${year}-${month.padStart(2, '0')}`;
        } else {
          // Nếu API trả về format khác, giữ nguyên
          thangBatDauFormatted = data.thangBatDau;
        }
      } catch (error) {
        console.warn('Lỗi convert tháng bắt đầu:', error);
        thangBatDauFormatted = '';
      }
    }

    // Convert phương thức đóng từ API format sang frontend format
    const phuongThucMapped = this.mapPhuongThucFromApi(data.phuongThuc || '');

    // Điền thông tin BHXH từ API vào form (chỉ các field thuộc thông tin BHXH)
    const formData = {
      // BHXH
      mucThuNhap: data.mucThuNhap || 0,
      phuongAn: data.phuongAn || '',
      phuongThuc: phuongThucMapped,
      thangBatDau: thangBatDauFormatted,

      // Tiền
      tienLai: data.tienLai || 0,
      tienThua: data.tienThua || 0,
      tienTuDong: data.tienTuDong || 0,
      tongTien: data.tongTien || 0,
      tienHoTro: data.tienHoTro || 0,

      // Thông tin bổ sung - chỉ update nếu API có dữ liệu
      ...(data.typeId && { loai: data.typeId }), // Sử dụng typeId từ API
      // Không update loaiNsnn và ngayBienLai nếu API không có - giữ giá trị mặc định
      ...(data.soBienLai && { soBienLai: data.soBienLai }),
      ...(data.heSoDong !== undefined && { heSoDong: data.heSoDong }),
      isThamGiaBb: data.isThamGiaBb || false,
      isTamHoanHD: data.isTamHoanHD || false,

      // Thông tin hệ thống
      message: data.message || '',
      isError: data.isError || false,
      maLoi: data.maLoi || '',
      moTaLoi: data.moTaLoi || ''
    };

    console.log('🔍 Dữ liệu BHXH từ API:', data);
    console.log('🔄 Dữ liệu BHXH sau khi convert:', formData);

    // Patch form với dữ liệu mới (chỉ thông tin BHXH)
    this.form.patchValue(formData);

    // Tự động set số tháng dựa trên phương thức đóng từ API
    this.autoSetSoThangFromApi(phuongThucMapped);

    // Tính toán hệ số đóng từ mức thu nhập nếu chưa có từ API
    if (!data.heSoDong && data.mucThuNhap) {
      this.calculateHeSoDong(data.mucThuNhap);
    }

    // Debug: Kiểm tra giá trị sau khi patch
    setTimeout(() => {
      console.log('✅ Giá trị form BHXH sau khi patch:', this.form.value);
      console.log('📅 Tháng bắt đầu trong form:', this.form.get('thangBatDau')?.value);
      console.log('🔢 Phương thức mapped:', phuongThucMapped);
      console.log('🔢 Số tháng đã set:', this.form.get('soThang')?.value);
      console.log('🎯 Phương án từ API:', data.phuongAn);
      console.log('🏷️ Loại NSNN hiện tại:', this.form.get('loaiNsnn')?.value);
    }, 100);
  }

  huy() {
    this.huyForm.emit();
  }

  /**
   * Xử lý khi form thông tin cơ bản thay đổi
   */
  onThongTinCoBanChange(value: any) {
    // Handle basic info form changes if needed
    console.log('Thông tin cơ bản changed:', value);
  }

  /**
   * Xử lý khi form thông tin đóng BHXH thay đổi
   */
  onThongTinDongBhxhChange(value: any) {
    // Handle BHXH info form changes if needed
    console.log('Thông tin đóng BHXH changed:', value);
  }
}

// Export default for lazy loading
export default LaoDongFormComponent;
