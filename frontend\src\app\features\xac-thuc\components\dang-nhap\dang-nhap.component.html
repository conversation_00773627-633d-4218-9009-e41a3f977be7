<div class="khung-dang-nhap min-h-screen bg-gray-50">
  <!-- Left Panel - Branding -->
  <div class="panel-trai hidden lg:flex lg:w-1/2 bg-slate-900 relative overflow-hidden">
    <div class="noi-dung-brand flex flex-col justify-center px-12 py-16 relative z-10">
      <div class="logo-container mb-8">
        <app-logo
          kichThuoc="large"
          theme="dark"
          [showText]="true">
        </app-logo>
      </div>

      <div class="thong-tin-he-thong">
        <h2 class="text-3xl font-light text-white mb-6 leading-tight">
          Chào mừng đến với<br>
          <span class="font-semibold text-blue-400">Hệ thống quản lý</span>
        </h2>
        <p class="text-slate-300 text-lg leading-relaxed mb-8">
          Truy cập vào nền tảng quản lý toàn diện cho doanh nghiệp của bạn.
          Quản lý nhân sự, dự án và tài nguyên một cách hiệu quả.
        </p>

        <div class="tinh-nang-noi-bat space-y-4">
          <div class="tinh-nang flex items-center text-slate-300">
            <svg class="w-5 h-5 text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Bảo mật cấp doanh nghiệp</span>
          </div>
          <div class="tinh-nang flex items-center text-slate-300">
            <svg class="w-5 h-5 text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Báo cáo và phân tích chi tiết</span>
          </div>
          <div class="tinh-nang flex items-center text-slate-300">
            <svg class="w-5 h-5 text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Hỗ trợ 24/7</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Background Pattern -->
    <div class="pattern-bg absolute inset-0 opacity-10">
      <div class="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-purple-600/10"></div>
    </div>
  </div>

  <!-- Right Panel - Login Form -->
  <div class="panel-phai flex lg:w-1/2 w-full">
    <div class="noi-dung-dang-nhap w-full max-w-md mx-auto flex flex-col justify-center px-6 py-12 lg:px-8">
      <!-- Mobile Logo -->
      <div class="logo-mobile lg:hidden text-center mb-8">
        <app-logo
          kichThuoc="medium"
          theme="light"
          [showText]="true"
          [moTa]="''">
        </app-logo>
      </div>

      <!-- Header -->
      <div class="header-dang-nhap">
        <h2 class="tieu-de text-2xl font-semibold text-gray-900 mb-2">
          Đăng nhập
        </h2>
        <p class="mo-ta text-sm text-gray-600 mb-8">
          Vui lòng nhập thông tin đăng nhập để tiếp tục
        </p>
      </div>

      <!-- Form đăng nhập -->
      <form class="form-dang-nhap space-y-6"
            [formGroup]="formDangNhap"
            (ngSubmit)="onDangNhap()">

        <!-- Thông báo lỗi chung -->
        <div *ngIf="thongBaoLoi"
             class="thong-bao-loi bg-red-50 border-l-4 border-red-400 p-4 rounded-r-md">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clip-rule="evenodd">
                </path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-700">{{ thongBaoLoi }}</p>
            </div>
          </div>
        </div>

        <div class="nhom-truong-nhap space-y-5">
          <!-- Trường Username -->
          <div class="truong-username">
            <label for="username" class="nhan-truong block text-sm font-medium text-gray-700 mb-2">
              Username
            </label>
            <div class="khung-input relative">
              <input id="username"
                     name="username"
                     type="text"
                     autocomplete="username"
                     formControlName="username"
                     class="input-username block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900"
                     [class.border-red-300]="kiemTraLoiTruong('username')"
                     [class.focus:ring-red-500]="kiemTraLoiTruong('username')"
                     [class.focus:border-red-500]="kiemTraLoiTruong('username')"
                     placeholder="Nhập username của bạn">
            </div>
            <div *ngIf="kiemTraLoiTruong('username')"
                 class="thong-bao-loi-truong mt-2 text-sm text-red-600 flex items-center">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              {{ layThongBaoLoiTruong('username') }}
            </div>
          </div>

          <!-- Trường Mật khẩu -->
          <div class="truong-mat-khau">
            <label for="matKhau" class="nhan-truong block text-sm font-medium text-gray-700 mb-2">
              Mật khẩu
            </label>
            <div class="khung-input relative">
              <input id="matKhau"
                     name="matKhau"
                     [type]="anMatKhau ? 'password' : 'text'"
                     autocomplete="current-password"
                     formControlName="matKhau"
                     class="input-mat-khau block w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900"
                     [class.border-red-300]="kiemTraLoiTruong('matKhau')"
                     [class.focus:ring-red-500]="kiemTraLoiTruong('matKhau')"
                     [class.focus:border-red-500]="kiemTraLoiTruong('matKhau')"
                     placeholder="Nhập mật khẩu của bạn">
              <button type="button"
                      class="nut-an-hien-mat-khau absolute inset-y-0 right-0 pr-4 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors"
                      (click)="toggleAnHienMatKhau()">
                <svg *ngIf="anMatKhau" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                  </path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                  </path>
                </svg>
                <svg *ngIf="!anMatKhau" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21">
                  </path>
                </svg>
              </button>
            </div>
            <div *ngIf="kiemTraLoiTruong('matKhau')"
                 class="thong-bao-loi-truong mt-2 text-sm text-red-600 flex items-center">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              {{ layThongBaoLoiTruong('matKhau') }}
            </div>
          </div>
        </div>

        <!-- Tùy chọn bổ sung -->
        <div class="tuy-chon-bo-sung flex items-center justify-between">
          <div class="checkbox-ghi-nho flex items-center">
            <input id="ghiNhoDangNhap"
                   name="ghiNhoDangNhap"
                   type="checkbox"
                   formControlName="ghiNhoDangNhap"
                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors">
            <label for="ghiNhoDangNhap" class="ml-3 block text-sm text-gray-700">
              Ghi nhớ đăng nhập
            </label>
          </div>

          <div class="lien-ket-quen-mat-khau">
            <button type="button"
                    class="text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors"
                    (click)="onQuenMatKhau()">
              Quên mật khẩu?
            </button>
          </div>
        </div>

        <!-- Nút đăng nhập -->
        <div class="nhom-nut-bam mt-8">
          <button type="submit"
                  class="nut-dang-nhap w-full flex justify-center items-center py-3 px-4 border border-transparent text-sm font-semibold rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
                  [disabled]="dangXuLy || formDangNhap.invalid">

            <!-- Loading spinner -->
            <svg *ngIf="dangXuLy"
                 class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                 fill="none"
                 viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>

            <!-- Icon đăng nhập -->
            <svg *ngIf="!dangXuLy" class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
            </svg>

            {{ dangXuLy ? 'Đang đăng nhập...' : 'Đăng nhập' }}
          </button>
        </div>
      </form>

      <!-- Demo Accounts (chỉ hiển thị trong development) -->
      <div *ngIf="danhSachTaiKhoanDemo.length > 0" class="demo-accounts mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h3 class="text-sm font-semibold text-blue-800 mb-3 flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Tài khoản demo để test
        </h3>
        <div class="space-y-2">
          <div *ngFor="let taiKhoan of danhSachTaiKhoanDemo"
               class="flex items-center justify-between p-2 bg-white rounded border hover:bg-gray-50 transition-colors">
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-900">{{ taiKhoan.username }}</div>
              <div class="text-xs text-gray-500">{{ taiKhoan.matKhau }} • {{ taiKhoan.vaiTro }}</div>
            </div>
            <button type="button"
                    class="ml-3 px-3 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded hover:bg-blue-200 transition-colors"
                    (click)="dienNhanhTaiKhoan(taiKhoan.username, taiKhoan.matKhau)">
              Sử dụng
            </button>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer-dang-nhap mt-8 pt-6 border-t border-gray-200">
        <div class="text-center">
          <p class="text-xs text-gray-500 mb-4">
            Cần hỗ trợ? Liên hệ
            <a href="mailto:support&#64;company.com" class="text-blue-600 hover:text-blue-700 font-medium">
              support&#64;company.com
            </a>
          </p>
          <p class="text-xs text-gray-400">
            © 2024 Enterprise Portal. Tất cả quyền được bảo lưu.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
