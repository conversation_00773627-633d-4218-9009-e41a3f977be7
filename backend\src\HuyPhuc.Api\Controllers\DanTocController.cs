using HuyPhuc.Api.Controllers.Base;
using HuyPhuc.Application.Features.DanToc.Queries.GetAllDanToc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// API Controller cho quản lý dân tộc
/// </summary>
[ApiController]
[Route("api/dan-toc")]
// [Authorize] // Temporarily disabled for testing
public class DanTocController : BaseController
{
    /// <summary>
    /// Lấy danh sách tất cả dân tộc
    /// </summary>
    /// <returns>Danh sách dân tộc</returns>
    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllDanTocQuery());
        
        if (result.IsSuccess)
        {
            return Ok(new
            {
                data = result.Data,
                success = true,
                message = (string?)null,
                errors = (object?)null,
                status = 200,
                traceId = HttpContext.TraceIdentifier
            });
        }

        return BadRequest(new
        {
            data = (object?)null,
            success = false,
            message = result.Errors?.FirstOrDefault() ?? "Có lỗi xảy ra khi lấy danh sách dân tộc",
            errors = result.Errors,
            status = 400,
            traceId = HttpContext.TraceIdentifier
        });
    }
}
