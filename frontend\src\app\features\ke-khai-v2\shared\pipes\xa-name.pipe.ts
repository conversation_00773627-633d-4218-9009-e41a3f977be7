import { Pipe, PipeTransform, inject } from '@angular/core';
import { XaService } from '../../core/services';

/**
 * <PERSON><PERSON> để convert mã xã thành tên xã
 * Usage: {{ maXa | xaName }}
 * Usage: {{ maXa | xaName:'display' }} // để hiển thị text display
 */
@Pipe({
  name: 'xaName',
  standalone: true
})
export class XaNamePipe implements PipeTransform {
  private readonly xaService = inject(XaService);

  transform(maXa: string, type: 'name' | 'display' = 'name'): string {
    if (!maXa) {
      return '';
    }

    if (type === 'display') {
      return this.xaService.convertMaToTextDisplay(maXa);
    } else {
      return this.xaService.convertMaToTen(maXa);
    }
  }
}
