using AutoMapper;
using HuyPhuc.Application.Common.Exceptions;
using HuyPhuc.Domain.Repositories;
using MediatR;

namespace HuyPhuc.Application.Features.QuanLyNguoiDung.Queries.LayChiTietNguoiDung;

public class LayChiTietNguoiDungQueryHandler : IRequestHandler<LayChiTietNguoiDungQuery, ChiTietNguoiDungDto?>
{
    private readonly INguoiDungRepository _nguoiDungRepository;
    private readonly IMapper _mapper;

    public LayChiTietNguoiDungQueryHandler(
        INguoiDungRepository nguoiDungRepository,
        IMapper mapper)
    {
        _nguoiDungRepository = nguoiDungRepository;
        _mapper = mapper;
    }

    public async Task<ChiTietNguoiDungDto?> Handle(LayChiTietNguoiDungQuery request, CancellationToken cancellationToken)
    {
        var nguoiDung = await _nguoiDungRepository.LayTheoIdAsync(request.Id, cancellationToken);
        
        if (nguoiDung == null)
            throw new NotFoundException(nameof(nguoiDung), request.Id);

        var chiTietDto = _mapper.Map<ChiTietNguoiDungDto>(nguoiDung);

        // TODO: Implement order statistics when DonHang API is ready
        chiTietDto.TongSoDonHang = 0;
        chiTietDto.TongGiaTriMuaHang = 0;

        return chiTietDto;
    }
}
