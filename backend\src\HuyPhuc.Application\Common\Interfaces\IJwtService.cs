using HuyPhuc.Domain.Entities;
using System.Security.Claims;

namespace HuyPhuc.Application.Common.Interfaces;

/// <summary>
/// Interface cho JWT service
/// </summary>
public interface IJwtService
{
    /// <summary>
    /// Tạo access token từ thông tin người dùng
    /// </summary>
    /// <param name="nguoiDung">Thông tin người dùng</param>
    /// <returns>Access token</returns>
    string TaoAccessToken(NguoiDung nguoiDung);

    /// <summary>
    /// Tạo refresh token
    /// </summary>
    /// <returns>Refresh token</returns>
    string TaoRefreshToken();

    /// <summary>
    /// Lấy thông tin claims từ token
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>Claims principal</returns>
    ClaimsPrincipal? LayClaimsTuToken(string token);

    /// <summary>
    /// Kiểm tra token có hợp lệ không
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>True nếu token hợp lệ</returns>
    bool KiemTraTokenHopLe(string token);

    /// <summary>
    /// Lấy thời gian hết hạn của token
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>Thời gian hết hạn</returns>
    DateTime? LayThoiGianHetHan(string token);
}
