import { Component, Input, Output, EventEmitter, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormBuilder, Validators } from '@angular/forms';

import { BhxhLookupService, BhxhLookupResponse } from '../../../../../core/services/bhxh-lookup.service';
import { TinhService, HuyenService, XaService, DanTocService } from '../../../../../core/services';
import { TinhOption, HuyenOption, XaOption, DanTocOption } from '../../../../../core/models';
import { VnPostAuthService } from '../../../../../core/services/vnpost-auth.service';

/**
 * Component form thông tin cơ bản của lao động
 */
@Component({
  selector: 'app-thong-tin-co-ban',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  templateUrl: './thong-tin-co-ban.component.html',
  styleUrls: ['./thong-tin-co-ban.component.scss']
})
export class ThongTinCoBanComponent implements OnInit {
  @Input() parentForm!: FormGroup;
  @Input() disabled = false;
  @Output() formChange = new EventEmitter<any>();
  @Output() bhxhLookupSuccess = new EventEmitter<any>();

  private readonly fb = inject(FormBuilder);
  private readonly bhxhLookupService = inject(BhxhLookupService);
  private readonly tinhService = inject(TinhService);
  private readonly huyenService = inject(HuyenService);
  private readonly xaService = inject(XaService);
  private readonly danTocService = inject(DanTocService);
  private readonly vnPostAuthService = inject(VnPostAuthService);

  // BHXH lookup state
  dangTraCuu = false;
  thongBaoTraCuu = '';
  loiTraCuu = '';

  // Pending BHXH lookup for auto retry
  pendingBhxhLookup: string | null = null;

  // Danh sách tỉnh, huyện, xã và dân tộc cho dropdown
  tinhOptions: TinhOption[] = [];
  huyenOptions: HuyenOption[] = [];
  xaOptions: XaOption[] = [];
  danTocOptions: DanTocOption[] = [];

  ngOnInit() {
    // Load danh sách tỉnh và dân tộc
    this.loadTinhOptions();
    this.loadDanTocOptions();

    // Lắng nghe thay đổi tỉnh để load huyện tương ứng
    this.parentForm.get('maTinhKs')?.valueChanges.subscribe(maTinh => {
      this.onTinhChange(maTinh);
    });

    // Lắng nghe thay đổi huyện để load xã tương ứng
    this.parentForm.get('maHuyenKs')?.valueChanges.subscribe(maHuyen => {
      this.onHuyenChange(maHuyen);
    });

    // Lắng nghe thay đổi form và emit lên parent
    this.parentForm.valueChanges.subscribe(value => {
      this.formChange.emit(value);
    });

    // Lắng nghe token expired event để auto retry
    this.bhxhLookupService.tokenExpired$.subscribe(isExpired => {
      console.log('🟡 Token expired event received in component:', isExpired);
      if (!isExpired) {
        console.log('🟢 Token refreshed, checking for pending lookup');
        // Auto retry BHXH lookup if we have a pending lookup
        if (this.pendingBhxhLookup) {
          console.log('🔄 Auto retrying BHXH lookup after login success');
          // Set the form value and trigger lookup
          this.parentForm.get('maSoBHXH')?.setValue(this.pendingBhxhLookup);
          this.traCuuThongTinBhxh();
          this.pendingBhxhLookup = null;
        }
      }
    });
  }

  /**
   * Load danh sách tỉnh cho dropdown
   */
  private loadTinhOptions() {
    this.tinhService.getTinhOptions().subscribe({
      next: (options) => {
        this.tinhOptions = options;
        console.log('🏙️ Loaded tỉnh options:', this.tinhOptions.length);
      },
      error: (error) => {
        console.error('🔴 Lỗi khi load danh sách tỉnh:', error);
        this.tinhOptions = [];
      }
    });
  }

  /**
   * Load danh sách dân tộc cho dropdown
   */
  private loadDanTocOptions() {
    this.danTocService.getDanTocOptions().subscribe({
      next: (options) => {
        this.danTocOptions = options;
        console.log('🏷️ Loaded dân tộc options:', this.danTocOptions.length);
      },
      error: (error) => {
        console.error('🔴 Lỗi khi load danh sách dân tộc:', error);
        this.danTocOptions = [];
      }
    });
  }



  /**
   * Xử lý khi tỉnh thay đổi - load danh sách huyện tương ứng
   */
  private onTinhChange(maTinh: string) {
    if (maTinh) {
      this.loadHuyenOptions(maTinh);
    } else {
      this.huyenOptions = [];
      this.xaOptions = [];
      // Reset huyện và xã khi không chọn tỉnh
      this.parentForm.get('maHuyenKs')?.setValue('');
      this.parentForm.get('maXaKs')?.setValue('');
    }
  }

  /**
   * Xử lý khi huyện thay đổi - load danh sách xã tương ứng
   */
  private onHuyenChange(maHuyen: string) {
    if (maHuyen) {
      this.loadXaOptions(maHuyen);
    } else {
      this.xaOptions = [];
      // Reset xã khi không chọn huyện
      this.parentForm.get('maXaKs')?.setValue('');
    }
  }

  /**
   * Load danh sách huyện theo tỉnh
   */
  private loadHuyenOptions(maTinh: string) {
    this.huyenService.getHuyenOptions(maTinh).subscribe({
      next: (options) => {
        this.huyenOptions = options;
        console.log(`🏘️ Loaded huyện options for ${maTinh}:`, this.huyenOptions.length);

        // Reset huyện khi tỉnh thay đổi
        const currentHuyen = this.parentForm.get('maHuyenKs')?.value;
        if (currentHuyen && !this.huyenOptions.find(h => h.value === currentHuyen)) {
          this.parentForm.get('maHuyenKs')?.setValue('');
        }
      },
      error: (error) => {
        console.error('🔴 Lỗi khi load danh sách huyện:', error);
        this.huyenOptions = [];
      }
    });
  }

  /**
   * Load danh sách xã theo huyện
   */
  private loadXaOptions(maHuyen: string) {
    this.xaService.getXaOptions(maHuyen).subscribe({
      next: (options) => {
        this.xaOptions = options;
        console.log(`🏠 Loaded xã options for ${maHuyen}:`, this.xaOptions.length);

        // Reset xã khi huyện thay đổi
        const currentXa = this.parentForm.get('maXaKs')?.value;
        if (currentXa && !this.xaOptions.find(x => x.value === currentXa)) {
          this.parentForm.get('maXaKs')?.setValue('');
        }
      },
      error: (error) => {
        console.error('🔴 Lỗi khi load danh sách xã:', error);
        this.xaOptions = [];
      }
    });
  }

  /**
   * Kiểm tra field có lỗi không
   */
  hasError(fieldName: string): boolean {
    const field = this.parentForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  /**
   * Lấy thông báo lỗi cho field
   */
  getErrorMessage(fieldName: string): string {
    const field = this.parentForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return this.getRequiredMessage(fieldName);
      }
    }
    return '';
  }

  /**
   * Lấy thông báo lỗi required cho từng field
   */
  private getRequiredMessage(fieldName: string): string {
    const messages: { [key: string]: string } = {
      'maSoBHXH': 'Vui lòng nhập mã số BHXH',
      'hoTen': 'Vui lòng nhập họ và tên',
      'cmnd': 'Vui lòng nhập số CCCD/CMND',
      'ngaySinh': 'Vui lòng chọn ngày sinh',
      'gioiTinh': 'Vui lòng chọn giới tính'
    };
    return messages[fieldName] || 'Vui lòng nhập thông tin';
  }

  /**
   * Tra cứu thông tin BHXH khi nhấn Enter trong field mã số BHXH
   */
  onBhxhKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.traCuuThongTinBhxh();
    }
  }

  /**
   * Tra cứu thông tin BHXH
   */
  traCuuThongTinBhxh() {
    const maSoBHXH = this.parentForm.get('maSoBHXH')?.value?.trim();

    if (!maSoBHXH) {
      this.loiTraCuu = 'Vui lòng nhập mã số BHXH';
      return;
    }

    if (!this.bhxhLookupService.isValidBhxhCode(maSoBHXH)) {
      this.loiTraCuu = 'Mã số BHXH không hợp lệ (phải có 10 chữ số)';
      return;
    }

    // Save the lookup for potential retry
    this.pendingBhxhLookup = maSoBHXH;

    this.dangTraCuu = true;
    this.loiTraCuu = '';
    this.thongBaoTraCuu = '';

    console.log('🔍 Starting BHXH lookup for:', maSoBHXH);

    this.bhxhLookupService.lookupBhxhInfo(maSoBHXH).subscribe({
      next: (response: BhxhLookupResponse) => {
        console.log('✅ BHXH lookup success:', response);
        this.dangTraCuu = false;
        this.pendingBhxhLookup = null; // Clear pending lookup on success

        if (response.success && response.data) {
          this.dienThongTinTuApi(response.data);
          this.thongBaoTraCuu = 'Tra cứu thông tin thành công!';

          // Hiển thị thông báo từ hệ thống nếu có
          if (response.data.message) {
            this.thongBaoTraCuu += ` ${response.data.message}`;
          }

          // Emit success event to parent
          this.bhxhLookupSuccess.emit(response.data);
        } else {
          this.loiTraCuu = response.message || 'Không thể tra cứu thông tin BHXH';
        }
      },
      error: (error) => {
        console.log('🔴 BHXH lookup error in component:', error);
        this.dangTraCuu = false;
        this.loiTraCuu = error.message || 'Có lỗi xảy ra khi tra cứu thông tin BHXH';
        this.thongBaoTraCuu = '';
        console.error('🔴 Lỗi tra cứu BHXH:', error);
        console.log('🔍 BHXH lookup error, modal should be handled globally');
      }
    });
  }

  /**
   * Điền thông tin từ API vào form (chỉ các field thuộc thông tin cơ bản)
   */
  private dienThongTinTuApi(data: any) {
    // Convert ngày sinh từ dd/MM/yyyy sang yyyy-MM-dd cho input date
    let ngaySinhFormatted = '';
    if (data.ngaySinh) {
      try {
        // Nếu API trả về format dd/MM/yyyy
        if (data.ngaySinh.includes('/')) {
          const [day, month, year] = data.ngaySinh.split('/');
          ngaySinhFormatted = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        } else {
          // Nếu API trả về format khác, giữ nguyên
          ngaySinhFormatted = data.ngaySinh;
        }
      } catch (error) {
        console.warn('Lỗi convert ngày sinh:', error);
        ngaySinhFormatted = '';
      }
    }

    // Map dữ liệu từ API response vào form (chỉ các field liên quan đến thông tin cơ bản)
    const formData = {
      // Thông tin cá nhân
      hoTen: data.hoTen || '',
      ccns: data.ccns || '',
      ngaySinh: ngaySinhFormatted,
      gioiTinh: data.gioiTinh === 1 ? 'Nam' : (data.gioiTinh === 'Nam' || data.gioiTinh === 'Nữ' ? data.gioiTinh : ''),
      quocTich: data.quocTich || '',
      danToc: data.danToc || '',
      cmnd: data.cmnd || '',

      // Địa chỉ - giữ nguyên mã tỉnh để form có thể select đúng option
      maTinhKs: data.maTinhKs || '',
      maHuyenKs: data.maHuyenKs || '',
      maXaKs: data.maXaKs || '',

      // Liên hệ
      dienThoaiLh: data.dienThoaiLh || '',
      maHoGiaDinh: data.maHoGiaDinh || ''
    };

    console.log('🔍 Dữ liệu từ API:', data);
    console.log('🔄 Dữ liệu sau khi convert:', formData);

    // Patch form với dữ liệu mới
    this.parentForm.patchValue(formData);

    // Load huyện nếu có mã tỉnh
    if (formData.maTinhKs) {
      this.loadHuyenOptions(formData.maTinhKs);

      // Load xã nếu có mã huyện
      if (formData.maHuyenKs) {
        // Delay một chút để huyện load xong trước
        setTimeout(() => {
          this.loadXaOptions(formData.maHuyenKs);
        }, 100);
      }
    }

    // Debug: Kiểm tra giá trị sau khi patch
    setTimeout(() => {
      console.log('✅ Giá trị form sau khi patch:', this.parentForm.value);
      console.log('📅 Ngày sinh trong form:', this.parentForm.get('ngaySinh')?.value);
    }, 100);
  }



  /**
   * Lấy text mô tả cho CCNS
   */
  getCcnsDescription(ccnsValue: string): string {
    const descriptions: { [key: string]: string } = {
      '0': 'Đầy đủ',
      '1': 'Không đầy đủ'
    };
    return descriptions[ccnsValue] || '';
  }


}
