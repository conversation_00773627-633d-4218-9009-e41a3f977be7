import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { DaiLyService, ToKhai602Service } from '../../services';
import { <PERSON><PERSON>y, DonVi, DropdownOption } from '../../models';
import { NotificationService } from '../../../../../shared/services/notification.service';
import { ConfirmationDialogService } from '../../../../../shared/services/confirmation-dialog.service';
import { KeKhaiService } from '../../../../../shared/services/ke-khai.service';
import { TaoKeKhaiRequest, TaoKeKhaiResponse } from '../../../../../shared/models/ke-khai.model';

/**
 * Component ch<PERSON>h cho khai báo tờ khai 602
 * <PERSON><PERSON> gồm chọn đạ<PERSON> lý, đơn vị và thông tin khác
 */
@Component({
  selector: 'app-khai-bao-to-khai-602',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './khai-bao-to-khai-602.component.html',
  styleUrls: ['./khai-bao-to-khai-602.component.scss']
})
export class KhaiBaoToKhai602Component implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // Form
  khaiBaoForm: FormGroup;

  // Data
  danhSachDaiLy: DaiLy[] = [];
  danhSachDonVi: DonVi[] = [];
  daiLyOptions: DropdownOption[] = [];
  donViOptions: DropdownOption[] = [];

  // State
  dangTai = false;
  dangTaoKeKhai = false;
  daiLyDangChon: DaiLy | null = null;
  donViDangChon: DonVi | null = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private daiLyService: DaiLyService,
    private toKhai602Service: ToKhai602Service,
    private keKhaiService: KeKhaiService,
    private notificationService: NotificationService,
    private confirmationDialogService: ConfirmationDialogService
  ) {
    this.khaiBaoForm = this.taoForm();
  }

  ngOnInit(): void {
    this.khoiTaoSubscriptions();
    this.taiDuLieuBanDau();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Tạo reactive form - mở rộng cho tờ khai 602
   */
  private taoForm(): FormGroup {
    return this.fb.group({
      daiLyId: [null, [Validators.required]],
      donViId: [null, [Validators.required]],
      soSoBHXH: ['1', [Validators.required]],
      ghiChu: [''],
      // Thêm fields đặc thù cho 602
      kyKeKhai: ['', [Validators.required]], // MM/yyyy
      loaiBienDong: ['tang_moi', [Validators.required]] // tang_moi, dieu_chinh
    });
  }

  /**
   * Khởi tạo subscriptions
   */
  private khoiTaoSubscriptions(): void {
    // Subscribe to đại lý service
    this.daiLyService.danhSachDaiLy$
      .pipe(takeUntil(this.destroy$))
      .subscribe(danhSach => {
        this.danhSachDaiLy = danhSach;
        this.daiLyOptions = this.daiLyService.chuyenDoiDaiLyThanhOptions(danhSach);
      });

    this.daiLyService.danhSachDonVi$
      .pipe(takeUntil(this.destroy$))
      .subscribe(danhSach => {
        this.danhSachDonVi = danhSach;
        this.donViOptions = this.daiLyService.chuyenDoiDonViThanhOptions(danhSach);
      });

    this.daiLyService.daiLyDangChon$
      .pipe(takeUntil(this.destroy$))
      .subscribe(daiLy => {
        this.daiLyDangChon = daiLy;
      });

    this.daiLyService.donViDangChon$
      .pipe(takeUntil(this.destroy$))
      .subscribe(donVi => {
        this.donViDangChon = donVi;
      });

    this.daiLyService.dangTai$
      .pipe(takeUntil(this.destroy$))
      .subscribe(dangTai => {
        this.dangTai = dangTai;
      });

    // Subscribe to form changes
    this.khaiBaoForm.get('daiLyId')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(daiLyId => {
        if (daiLyId) {
          this.onDaiLyChange(Number(daiLyId));
        }
      });

    this.khaiBaoForm.get('donViId')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(donViId => {
        if (donViId) {
          this.onDonViChange(donViId);
        }
      });
  }

  /**
   * Tải dữ liệu ban đầu
   */
  private taiDuLieuBanDau(): void {
    this.daiLyService.layDanhSachDaiLyCuaNguoiDung().subscribe();
  }

  /**
   * Xử lý khi thay đổi đại lý
   */
  onDaiLyChange(daiLyId: number): void {
    const daiLy = this.danhSachDaiLy.find(dl => dl.id === daiLyId);
    if (daiLy) {
      this.daiLyDangChon = daiLy;
      this.daiLyService.chonDaiLy(daiLy);
      // Reset đơn vị
      this.donViDangChon = null;
      this.khaiBaoForm.patchValue({ donViId: null });
    }
  }

  /**
   * Xử lý khi thay đổi đơn vị
   */
  onDonViChange(donViId: number): void {
    console.log('🔍 onDonViChange called with donViId:', donViId, typeof donViId);
    console.log('🔍 danhSachDonVi:', this.danhSachDonVi);
    console.log('🔍 danhSachDonVi IDs:', this.danhSachDonVi.map(dv => ({ id: dv.id, type: typeof dv.id })));

    const donVi = this.danhSachDonVi.find(dv => dv.id === donViId);
    console.log('🔍 Found donVi:', donVi);

    if (donVi) {
      this.donViDangChon = donVi;
      this.daiLyService.chonDonVi(donVi);
      console.log('🔍 Set donViDangChon:', this.donViDangChon);
    } else {
      console.log('🔍 No donVi found, trying with string conversion...');
      const donViStr = this.danhSachDonVi.find(dv => dv.id.toString() === donViId.toString());
      console.log('🔍 Found donVi with string conversion:', donViStr);
      if (donViStr) {
        this.donViDangChon = donViStr;
        this.daiLyService.chonDonVi(donViStr);
        console.log('🔍 Set donViDangChon with string conversion:', this.donViDangChon);
      }
    }
  }

  /**
   * Kiểm tra form có hợp lệ không
   */
  get formHopLe(): boolean {
    const formValid = this.khaiBaoForm.valid;
    const daiLyValid = !!this.daiLyDangChon;
    const donViValid = !!this.donViDangChon;

    console.log('🔍 Form validation check:', {
      formValid,
      daiLyValid,
      donViValid,
      daiLyDangChon: this.daiLyDangChon,
      donViDangChon: this.donViDangChon,
      formValue: this.khaiBaoForm.value
    });

    return formValid && daiLyValid && donViValid;
  }

  /**
   * Xử lý khi bấm "Tạo kê khai" - cải tiến để sử dụng bảng danh_sach_ke_khai
   */
  async onTaoKeKhai(): Promise<void> {
    if (!this.formHopLe) {
      this.khaiBaoForm.markAllAsTouched();
      return;
    }

    // Hiển thị confirmation dialog
    const confirmed = await this.confirmationDialogService.confirmCreateDraft(
      this.daiLyDangChon?.tenDaiLy || '',
      this.donViDangChon?.tenDonVi || '',
      this.khaiBaoForm.value.soSoBHXH
    );

    if (!confirmed) {
      return;
    }

    this.dangTaoKeKhai = true;

    try {
      const formValue = this.khaiBaoForm.value;

      // Tạo request cho bảng danh_sach_ke_khai
      const request: TaoKeKhaiRequest = {
        thuTucId: 1, // ID của thủ tục 602 trong danh_muc_thu_tuc
        daiLyId: this.daiLyDangChon!.id,
        donViId: this.donViDangChon!.id,
        soSoBHXH: formValue.soSoBHXH,
        thongTinHeader: {
          ky_ke_khai: formValue.kyKeKhai,
          loai_bien_dong: formValue.loaiBienDong,
          ghi_chu_chung: formValue.ghiChu
        },
        ghiChu: formValue.ghiChu
      };

      // Gọi API mới
      const response = await this.keKhaiService.taoKeKhai(request).toPromise();

      console.log('🎯 API Response:', response);

      if (response) {
        // Hiển thị thông báo thành công
        this.notificationService.showSuccess(
          'Tạo kê khai thành công!',
          `Mã kê khai: ${response.maKeKhai}`
        );

        console.log('🚀 Navigating to:', `/ke-khai/to-khai-602/quan-ly/${response.keKhaiId}`);

        // Chuyển đến trang quản lý với keKhaiId mới
        const navigationResult = await this.router.navigate(['/ke-khai/to-khai-602/quan-ly', response.keKhaiId]);
        console.log('📍 Navigation result:', navigationResult);
      }

    } catch (error: any) {
      console.error('Lỗi khi tạo kê khai:', error);

      const errorMessage = error?.error?.message || error?.message || 'Có lỗi xảy ra khi tạo kê khai. Vui lòng thử lại.';

      this.notificationService.showError(
        'Lỗi tạo kê khai',
        errorMessage
      );
    } finally {
      this.dangTaoKeKhai = false;
    }
  }



  /**
   * Xử lý khi bấm "Hủy bỏ"
   */
  onHuyBo(): void {
    this.khaiBaoForm.reset();
    this.daiLyService.resetLuaChon();
    this.toKhai602Service.resetForm();
  }

  /**
   * Kiểm tra field có lỗi không
   */
  hasError(fieldName: string): boolean {
    const field = this.khaiBaoForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  /**
   * Lấy thông báo lỗi cho field
   */
  getErrorMessage(fieldName: string): string {
    const field = this.khaiBaoForm.get(fieldName);
    if (field && field.errors) {
      if (field.errors['required']) {
        switch (fieldName) {
          case 'daiLyId':
            return 'Vui lòng chọn đại lý';
          case 'donViId':
            return 'Vui lòng chọn đơn vị';
          case 'soSoBHXH':
            return 'Vui lòng nhập số sổ BHXH';
          default:
            return 'Trường này là bắt buộc';
        }
      }
    }
    return '';
  }
}
