---
type: "manual"
---

# V<PERSON> dụ quy tắc đặt tên tiếng Việt không dấu cho dự án Angular TypeScript

## 1. Quy tắc chung

### Nguyên tắc cơ bản
- Sử dụng tiếng Việt **KHÔNG DẤU** cho tất cả tên trong code
- Áp dụng các quy chuẩn case theo từng loại (camelCase, kebab-case, PascalCase)
- Comment và documentation sử dụng tiếng Việt có dấu
- Tuân thủ Angular Style Guide

## 2. Đặt tên Components

### Tên file component
```
// ✅ Đúng
ho-so-nguoi-dung.component.ts
danh-sach-san-pham.component.ts
chi-tiet-don-hang.component.ts
bai-viet-tin-tuc.component.ts
```

### Tên class component
```typescript
// ✅ Đúng
export class HoSoNguoiDungComponent { }
export class DanhSachSanPhamComponent { }
export class ChiTietDonHangComponent { }
export class BaiVietTinTucComponent { }

// ❌ Sai
export class HồSơNgườiDùngComponent { }
export class UserProfileComponent { }
```

### Selector component
```typescript
// ✅ Đúng
@Component({
  selector: 'app-ho-so-nguoi-dung',
  selector: 'app-danh-sach-san-pham'
})

// ❌ Sai
@Component({
  selector: 'app-hồ-sơ-người-dùng'
})
```

## 3. Đặt tên Services

### Tên file service
```
// ✅ Đúng
nguoi-dung.service.ts
san-pham.service.ts
don-hang.service.ts
xac-thuc.service.ts
```

### Tên class service
```typescript
// ✅ Đúng
export class NguoiDungService { }
export class SanPhamService { }
export class DonHangService { }
export class XacThucService { }
```

## 4. Đặt tên Models/Interfaces

### Tên file model
```
// ✅ Đúng
nguoi-dung.model.ts
san-pham.interface.ts
don-hang.model.ts
```

### Tên interface/class model
```typescript
// ✅ Đúng
export interface NguoiDung {
  id: number;
  hoTen: string;
  email: string;
  soDienThoai: string;
}

export class SanPham {
  id: number;
  tenSanPham: string;
  giaBan: number;
  moTa: string;
}

export interface DonHang {
  maDonHang: string;
  ngayTao: Date;
  tongGiaTri: number;
  trangThai: TrangThaiDonHang;
}
```

## 5. Đặt tên Properties và Methods

### Properties (camelCase)
```typescript
// ✅ Đúng
hoTen: string;
soDienThoai: string;
diaChiEmail: string;
ngaySinh: Date;
trangThaiHoatDong: boolean;
danhSachSanPham: SanPham[];

// ❌ Sai
họTên: string;
firstName: string;
```

### Methods (camelCase)
```typescript
// ✅ Đúng
layDanhSachNguoiDung(): Observable<NguoiDung[]> { }
taoSanPhamMoi(sanPham: SanPham): void { }
capNhatThongTin(id: number, data: any): void { }
xoaNguoiDung(id: number): Observable<boolean> { }
kiemTraQuyenTruyCap(): boolean { }

// ❌ Sai
lấyDanhSáchNgườiDùng(): void { }
getUserList(): void { }
```

## 6. Đặt tên Constants và Enums

### Constants (UPPER_SNAKE_CASE)
```typescript
// ✅ Đúng
export const API_BASE_URL = 'https://api.example.com';
export const SO_LUONG_TOI_DA = 100;
export const THOI_GIAN_CHO = 5000;
export const TRANG_THAI_MAC_DINH = 'kich_hoat';

// ❌ Sai
export const apiBaseUrl = 'https://api.example.com';
export const SỐ_LƯỢNG_TỐI_ĐA = 100;
```

### Enums (PascalCase)
```typescript
// ✅ Đúng
export enum TrangThaiDonHang {
  ChoDuyet = 'cho_duyet',
  DaDuyet = 'da_duyet',
  DangGiao = 'dang_giao',
  DaGiao = 'da_giao',
  DaHuy = 'da_huy'
}

export enum LoaiNguoiDung {
  Admin = 'admin',
  NhanVien = 'nhan_vien',
  KhachHang = 'khach_hang'
}
```

## 7. Đặt tên Modules

### Tên file module
```
// ✅ Đúng
nguoi-dung.module.ts
san-pham.module.ts
bao-cao.module.ts
chia-se.module.ts (shared module)
```

### Tên class module
```typescript
// ✅ Đúng
export class NguoiDungModule { }
export class SanPhamModule { }
export class BaoCaoModule { }
export class ChiaSeModule { }
```

## 8. Đặt tên Pipes

### Tên file pipe
```
// ✅ Đúng
dinh-dang-tien-te.pipe.ts
cat-chuoi.pipe.ts
dinh-dang-ngay.pipe.ts
```

### Tên class pipe
```typescript
// ✅ Đúng
export class DinhDangTienTePipe implements PipeTransform {
  transform(value: number): string {
    // Logic định dạng tiền tệ
  }
}

// Sử dụng trong template
{{ giaTri | dinhDangTienTe }}
```

## 9. Đặt tên Guards

### Tên file guard
```
// ✅ Đúng
xac-thuc.guard.ts
quyen-admin.guard.ts
```

### Tên class guard
```typescript
// ✅ Đúng
export class XacThucGuard implements CanActivate { }
export class QuyenAdminGuard implements CanActivate { }
```

## 10. Đặt tên CSS Classes (SCSS + Tailwind)

### SCSS Classes (kebab-case)
```scss
// ✅ Đúng
.khung-noi-dung {
  padding: 1rem;
}

.nut-bam-chinh {
  background-color: #007bff;
}

.bang-du-lieu {
  width: 100%;
  
  &__hang {
    border-bottom: 1px solid #ddd;
  }
  
  &__cot {
    padding: 0.5rem;
  }
}

// ❌ Sai
.khungNoiDung { }
.nútBấmChính { }
```

### BEM Methodology với tiếng Việt
```scss
// Block__Element--Modifier
.the-san-pham {
  // Block: thẻ sản phẩm
  
  &__hinh-anh {
    // Element: hình ảnh
  }
  
  &__ten {
    // Element: tên
  }
  
  &__gia {
    // Element: giá
    
    &--giam-gia {
      // Modifier: giảm giá
      color: red;
    }
  }
}
```

## 11. Cấu trúc dự án Feature-Based

### Cấu trúc thư mục chính (kebab-case)
```
src/
├── app/
│   ├── features/                    # Các tính năng chính
│   │   ├── quan-ly-nguoi-dung/     # Feature quản lý người dùng
│   │   ├── quan-ly-san-pham/       # Feature quản lý sản phẩm
│   │   ├── quan-ly-don-hang/       # Feature quản lý đơn hàng
│   │   ├── bao-cao-thong-ke/       # Feature báo cáo thống kê
│   │   └── cai-dat-he-thong/       # Feature cài đặt hệ thống
│   ├── shared/                      # Chia sẻ giữa các features
│   │   ├── components/
│   │   ├── services/
│   │   ├── pipes/
│   │   ├── directives/
│   │   ├── models/
│   │   └── utils/
│   ├── core/                        # Core functionality
│   │   ├── guards/
│   │   ├── interceptors/
│   │   ├── services/
│   │   └── constants/
│   ├── layout/                      # Layout components
│   │   ├── header/
│   │   ├── sidebar/
│   │   ├── footer/
│   │   └── breadcrumb/
│   └── auth/                        # Authentication module
│       ├── components/
│       ├── services/
│       └── guards/
```

### Chi tiết cấu trúc một Feature
```
features/quan-ly-nguoi-dung/
├── components/                      # Components của feature
│   ├── danh-sach-nguoi-dung/
│   │   ├── danh-sach-nguoi-dung.component.ts
│   │   ├── danh-sach-nguoi-dung.component.html
│   │   ├── danh-sach-nguoi-dung.component.scss
│   │   └── danh-sach-nguoi-dung.component.spec.ts
│   ├── chi-tiet-nguoi-dung/
│   ├── them-sua-nguoi-dung/
│   └── loc-tim-kiem-nguoi-dung/
├── services/                        # Services riêng của feature
│   ├── nguoi-dung.service.ts
│   ├── nguoi-dung-api.service.ts
│   └── nguoi-dung-state.service.ts
├── models/                          # Models/Interfaces của feature
│   ├── nguoi-dung.model.ts
│   ├── nguoi-dung-filter.model.ts
│   └── nguoi-dung-response.model.ts
├── pipes/                           # Pipes riêng của feature
│   ├── trang-thai-nguoi-dung.pipe.ts
│   └── dinh-dang-vai-tro.pipe.ts
├── guards/                          # Guards riêng của feature
│   └── quyen-quan-ly-nguoi-dung.guard.ts
├── resolvers/                       # Data resolvers
│   └── nguoi-dung.resolver.ts
├── containers/                      # Smart components (container)
│   ├── trang-quan-ly-nguoi-dung/
│   └── trang-chi-tiet-nguoi-dung/
├── quan-ly-nguoi-dung.module.ts    # Feature module
├── quan-ly-nguoi-dung-routing.module.ts  # Feature routing
└── index.ts                         # Barrel exports
```

### Cấu trúc Shared Module
```
shared/
├── components/                      # Shared components
│   ├── modal/
│   │   ├── modal-xac-nhan/
│   │   ├── modal-thong-bao/
│   │   └── modal-loading/
│   ├── form-controls/
│   │   ├── input-tim-kiem/
│   │   ├── select-don-vi/
│   │   └── date-range-picker/
│   ├── data-display/
│   │   ├── bang-du-lieu/
│   │   ├── phan-trang/
│   │   └── empty-state/
│   └── navigation/
│       ├── breadcrumb/
│       └── tabs/
├── services/                        # Shared services
│   ├── thong-bao.service.ts
│   ├── tai-file.service.ts
│   ├── xuat-excel.service.ts
│   └── validation.service.ts
├── pipes/                           # Shared pipes
│   ├── dinh-dang-tien-te.pipe.ts
│   ├── dinh-dang-ngay.pipe.ts
│   ├── cat-chuoi.pipe.ts
│   └── safe-html.pipe.ts
├── directives/                      # Shared directives
│   ├── chi-so-sap-xep.directive.ts
│   ├── tu-dong-focus.directive.ts
│   └── an-hien-theo-quyen.directive.ts
├── models/                          # Shared models
│   ├── api-response.model.ts
│   ├── phan-trang.model.ts
│   ├── filter-base.model.ts
│   └── lookup.model.ts
├── utils/                           # Utility functions
│   ├── date.util.ts
│   ├── string.util.ts
│   ├── validation.util.ts
│   └── file.util.ts
├── constants/                       # Shared constants
│   ├── app-constants.ts
│   ├── regex-patterns.ts
│   └── api-endpoints.ts
└── shared.module.ts                 # Shared module
```

### Cấu trúc Core Module
```
core/
├── guards/                          # Global guards
│   ├── xac-thuc.guard.ts
│   ├── quyen-truy-cap.guard.ts
│   └── unsaved-changes.guard.ts
├── interceptors/                    # HTTP interceptors
│   ├── auth.interceptor.ts
│   ├── error.interceptor.ts
│   ├── loading.interceptor.ts
│   └── cache.interceptor.ts
├── services/                        # Core services
│   ├── auth.service.ts
│   ├── user-session.service.ts
│   ├── permission.service.ts
│   ├── error-handler.service.ts
│   └── storage.service.ts
├── constants/                       # Application constants
│   ├── app-config.ts
│   ├── storage-keys.ts
│   ├── permission-codes.ts
│   └── error-codes.ts
├── models/                          # Core models
│   ├── user.model.ts
│   ├── permission.model.ts
│   ├── menu.model.ts
│   └── error.model.ts
└── core.module.ts                   # Core module (import once)
```

## 12. Feature Module Structure

### Tên file module theo feature
```
// ✅ Đúng - Feature modules
quan-ly-nguoi-dung.module.ts
quan-ly-san-pham.module.ts
quan-ly-don-hang.module.ts
bao-cao-thong-ke.module.ts

// ✅ Đúng - Routing modules
quan-ly-nguoi-dung-routing.module.ts
quan-ly-san-pham-routing.module.ts
```

### Cấu trúc Feature Module
```typescript
// quan-ly-nguoi-dung.module.ts
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../shared/shared.module';

import { QuanLyNguoiDungRoutingModule } from './quan-ly-nguoi-dung-routing.module';

// Components
import { DanhSachNguoiDungComponent } from './components/danh-sach-nguoi-dung/danh-sach-nguoi-dung.component';
import { ChiTietNguoiDungComponent } from './components/chi-tiet-nguoi-dung/chi-tiet-nguoi-dung.component';
import { ThemSuaNguoiDungComponent } from './components/them-sua-nguoi-dung/them-sua-nguoi-dung.component';

// Containers
import { TrangQuanLyNguoiDungComponent } from './containers/trang-quan-ly-nguoi-dung/trang-quan-ly-nguoi-dung.component';

// Services
import { NguoiDungService } from './services/nguoi-dung.service';
import { NguoiDungApiService } from './services/nguoi-dung-api.service';

@NgModule({
  declarations: [
    DanhSachNguoiDungComponent,
    ChiTietNguoiDungComponent,
    ThemSuaNguoiDungComponent,
    TrangQuanLyNguoiDungComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    QuanLyNguoiDungRoutingModule
  ],
  providers: [
    NguoiDungService,
    NguoiDungApiService
  ]
})
export class QuanLyNguoiDungModule { }
```

### Cấu trúc Feature Routing
```typescript
// quan-ly-nguoi-dung-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { TrangQuanLyNguoiDungComponent } from './containers/trang-quan-ly-nguoi-dung/trang-quan-ly-nguoi-dung.component';
import { ChiTietNguoiDungComponent } from './components/chi-tiet-nguoi-dung/chi-tiet-nguoi-dung.component';
import { QuyenQuanLyNguoiDungGuard } from './guards/quyen-quan-ly-nguoi-dung.guard';
import { NguoiDungResolver } from './resolvers/nguoi-dung.resolver';

const routes: Routes = [
  {
    path: '',
    component: TrangQuanLyNguoiDungComponent,
    canActivate: [QuyenQuanLyNguoiDungGuard],
    children: [
      {
        path: '',
        redirectTo: 'danh-sach',
        pathMatch: 'full'
      },
      {
        path: 'danh-sach',
        component: DanhSachNguoiDungComponent
      },
      {
        path: 'chi-tiet/:id',
        component: ChiTietNguoiDungComponent,
        resolve: { nguoiDung: NguoiDungResolver }
      },
      {
        path: 'them-moi',
        component: ThemSuaNguoiDungComponent
      },
      {
        path: 'chinh-sua/:id',
        component: ThemSuaNguoiDungComponent,
        resolve: { nguoiDung: NguoiDungResolver }
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class QuanLyNguoiDungRoutingModule { }
```

### Barrel Exports (index.ts)
```typescript
// features/quan-ly-nguoi-dung/index.ts
// Components
export * from './components/danh-sach-nguoi-dung/danh-sach-nguoi-dung.component';
export * from './components/chi-tiet-nguoi-dung/chi-tiet-nguoi-dung.component';
export * from './components/them-sua-nguoi-dung/them-sua-nguoi-dung.component';

// Services
export * from './services/nguoi-dung.service';
export * from './services/nguoi-dung-api.service';

// Models
export * from './models/nguoi-dung.model';
export * from './models/nguoi-dung-filter.model';

// Module
export * from './quan-ly-nguoi-dung.module';
```

## 13. Smart/Dumb Components Pattern

### Container Components (Smart)
```typescript
// containers/trang-quan-ly-nguoi-dung/trang-quan-ly-nguoi-dung.component.ts
@Component({
  selector: 'app-trang-quan-ly-nguoi-dung',
  template: `
    <div class="trang-quan-ly">
      <app-header-trang 
        [tieuDe]="'Quản lý người dùng'"
        [duongDan]="duongDanBreadcrumb">
      </app-header-trang>
      
      <router-outlet></router-outlet>
    </div>
  `
})
export class TrangQuanLyNguoiDungComponent {
  duongDanBreadcrumb = [
    { nhan: 'Trang chủ', url: '/' },
    { nhan: 'Quản lý người dùng', url: '/quan-ly-nguoi-dung' }
  ];
}
```

### Presentation Components (Dumb)
```typescript
// components/danh-sach-nguoi-dung/danh-sach-nguoi-dung.component.ts
@Component({
  selector: 'app-danh-sach-nguoi-dung',
  templateUrl: './danh-sach-nguoi-dung.component.html'
})
export class DanhSachNguoiDungComponent implements OnInit {
  @Input() danhSachNguoiDung: NguoiDung[] = [];
  @Input() dangTai = false;
  @Output() chonNguoiDung = new EventEmitter<NguoiDung>();
  @Output() xoaNguoiDung = new EventEmitter<number>();
  @Output() timKiem = new EventEmitter<string>();

  onChonNguoiDung(nguoiDung: NguoiDung): void {
    this.chonNguoiDung.emit(nguoiDung);
  }

  onXoaNguoiDung(id: number): void {
    this.xoaNguoiDung.emit(id);
  }

  onTimKiem(tuKhoa: string): void {
    this.timKiem.emit(tuKhoa);
  }
}
```

```typescript
// ✅ Đúng
const routes: Routes = [
  { path: 'nguoi-dung', component: NguoiDungComponent },
  { path: 'san-pham', component: SanPhamComponent },
  { path: 'don-hang/:id', component: ChiTietDonHangComponent },
  { path: 'bao-cao', loadChildren: () => import('./modules/bao-cao/bao-cao.module').then(m => m.BaoCaoModule) }
];

// ❌ Sai
const routes: Routes = [
  { path: 'người-dùng', component: NguoiDungComponent },
  { path: 'users', component: NguoiDungComponent }
];
```

## 14. App Routing Structure

### Main App Routing
```typescript
// app-routing.module.ts
const routes: Routes = [
  {
    path: '',
    redirectTo: '/trang-chu',
    pathMatch: 'full'
  },
  {
    path: 'dang-nhap',
    loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)
  },
  {
    path: '',
    component: LayoutComponent,
    canActivate: [XacThucGuard],
    children: [
      {
        path: 'trang-chu',
        loadChildren: () => import('./features/trang-chu/trang-chu.module').then(m => m.TrangChuModule)
      },
      {
        path: 'quan-ly-nguoi-dung',
        loadChildren: () => import('./features/quan-ly-nguoi-dung/quan-ly-nguoi-dung.module').then(m => m.QuanLyNguoiDungModule),
        canLoad: [QuyenTruyCapGuard],
        data: { quyen: 'QUAN_LY_NGUOI_DUNG' }
      },
      {
        path: 'quan-ly-san-pham',
        loadChildren: () => import('./features/quan-ly-san-pham/quan-ly-san-pham.module').then(m => m.QuanLySanPhamModule),
        canLoad: [QuyenTruyCapGuard],
        data: { quyen: 'QUAN_LY_SAN_PHAM' }
      },
      {
        path: 'quan-ly-don-hang',
        loadChildren: () => import('./features/quan-ly-don-hang/quan-ly-don-hang.module').then(m => m.QuanLyDonHangModule),
        canLoad: [QuyenTruyCapGuard],
        data: { quyen: 'QUAN_LY_DON_HANG' }
      },
      {
        path: 'bao-cao-thong-ke',
        loadChildren: () => import('./features/bao-cao-thong-ke/bao-cao-thong-ke.module').then(m => m.BaoCaoThongKeModule),
        canLoad: [QuyenTruyCapGuard],
        data: { quyen: 'XEM_BAO_CAO' }
      },
      {
        path: 'cai-dat-he-thong',
        loadChildren: () => import('./features/cai-dat-he-thong/cai-dat-he-thong.module').then(m => m.CaiDatHeThongModule),
        canLoad: [QuyenTruyCapGuard],
        data: { quyen: 'CAI_DAT_HE_THONG' }
      }
    ]
  },
  {
    path: '**',
    loadChildren: () => import('./features/not-found/not-found.module').then(m => m.NotFoundModule)
  }
];
```

## 15. State Management Structure

### Feature State Service
```typescript
// services/nguoi-dung-state.service.ts
@Injectable()
export class NguoiDungStateService {
  private readonly _danhSachNguoiDung$ = new BehaviorSubject<NguoiDung[]>([]);
  private readonly _nguoiDungDangChon$ = new BehaviorSubject<NguoiDung | null>(null);
  private readonly _dangTai$ = new BehaviorSubject<boolean>(false);
  private readonly _boLoc$ = new BehaviorSubject<NguoiDungFilter>({});

  // Selectors
  readonly danhSachNguoiDung$ = this._danhSachNguoiDung$.asObservable();
  readonly nguoiDungDangChon$ = this._nguoiDungDangChon$.asObservable();
  readonly dangTai$ = this._dangTai$.asObservable();
  readonly boLoc$ = this._boLoc$.asObservable();

  // Actions
  capNhatDanhSach(danhSach: NguoiDung[]): void {
    this._danhSachNguoiDung$.next(danhSach);
  }

  chonNguoiDung(nguoiDung: NguoiDung): void {
    this._nguoiDungDangChon$.next(nguoiDung);
  }

  datTrangThaiTai(dangTai: boolean): void {
    this._dangTai$.next(dangTai);
  }

  capNhatBoLoc(boLoc: NguoiDungFilter): void {
    this._boLoc$.next(boLoc);
  }
}
```

## 16. Form Controls theo Feature

```typescript
// ✅ Đúng
this.formDangKy = this.fb.group({
  hoTen: ['', Validators.required],
  email: ['', [Validators.required, Validators.email]],
  matKhau: ['', [Validators.required, Validators.minLength(6)]],
  xacNhanMatKhau: ['', Validators.required],
  soDienThoai: [''],
  diaChi: ['']
});

// Template
<input formControlName="hoTen" placeholder="Họ và tên">
<input formControlName="email" placeholder="Email">
```

## 14. Đặt tên Functions/Methods thông dụng

```typescript
// CRUD Operations
layDanhSach(): void { }          // GET list
layTheoId(id: number): void { }   // GET by ID
taoMoi(data: any): void { }       // CREATE
capNhat(id: number, data: any): void { }  // UPDATE
xoa(id: number): void { }         // DELETE

// UI Actions
moModal(): void { }
dongModal(): void { }
anHien(show: boolean): void { }
chuyenTrang(url: string): void { }

// Validation
kiemTraHopLe(): boolean { }
xacThucDuLieu(): boolean { }
```

## 15. Đặt tên Event Handlers

```typescript
// ✅ Đúng (on + Hành động)
onDangNhap(): void { }
onDangXuat(): void { }
onChonFile(): void { }
onGuiForm(): void { }
onHuyBo(): void { }
onXacNhan(): void { }

// Template events
<button (click)="onDangNhap()">Đăng nhập</button>
<form (ngSubmit)="onGuiForm()">
```

## 16. Comments và Documentation

```typescript
/**
 * Service quản lý thông tin người dùng
 * Bao gồm các chức năng CRUD và xác thực
 */
@Injectable()
export class NguoiDungService {
  
  /**
   * Lấy danh sách tất cả người dùng
   * @returns Observable<NguoiDung[]> Danh sách người dùng
   */
  layDanhSachNguoiDung(): Observable<NguoiDung[]> {
    // Logic lấy dữ liệu từ API
    return this.http.get<NguoiDung[]>(`${this.apiUrl}/nguoi-dung`);
  }
  
  // TODO: Thêm tính năng lọc theo trạng thái
  // FIXME: Xử lý lỗi khi API không phản hồi
}
```

## 17. Testing Files

```
// ✅ Đúng
nguoi-dung.service.spec.ts
san-pham.component.spec.ts
xac-thuc.guard.spec.ts

// Test suites
describe('NguoiDungService', () => {
  it('nen lay danh sach nguoi dung thanh cong', () => {
    // Test logic
  });
  
  it('nen xu ly loi khi API that bai', () => {
    // Test error handling
  });
});
```

## 18. Environment Variables

```typescript
// environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000/api',
  tenUngDung: 'Hệ thống quản lý',
  phienBan: '1.0.0'
};
```

## Backend .NET Naming Conventions

### Controllers (PascalCase)
```csharp
// ✅ Đúng
public class NguoiDungController : ControllerBase { }
public class SanPhamController : ControllerBase { }
public class DonHangController : ControllerBase { }
public class XacThucController : ControllerBase { }

// ❌ Sai
public class UserController : ControllerBase { }
public class NguoiDùngController : ControllerBase { }
```

### Services và Repositories (PascalCase)
```csharp
// ✅ Đúng
public interface INguoiDungService { }
public class NguoiDungService : INguoiDungService { }
public interface INguoiDungRepository { }
public class NguoiDungRepository : INguoiDungRepository { }

// Application Services (CQRS)
public class TaoNguoiDungCommand : IRequest<int> { }
public class TaoNguoiDungCommandHandler : IRequestHandler<TaoNguoiDungCommand, int> { }
public class LayDanhSachNguoiDungQuery : IRequest<List<NguoiDungDto>> { }
```

### Domain Entities (PascalCase)
```csharp
// ✅ Đúng
public class NguoiDung { }
public class SanPham { }
public class DonHang { }
public class ChiTietDonHang { }
public class RefreshToken { }

// Value Objects
public class Email { }
public class SoDienThoai { }
```

### Properties và Methods (PascalCase)
```csharp
// ✅ Đúng
public string HoTen { get; set; }
public string SoDienThoai { get; set; }
public DateTime NgaySinh { get; set; }
public bool TrangThaiHoatDong { get; set; }

// Methods
public async Task<List<NguoiDung>> LayDanhSachNguoiDungAsync() { }
public async Task<NguoiDung> LayNguoiDungTheoIdAsync(int id) { }
public async Task<bool> TaoNguoiDungMoiAsync(NguoiDung nguoiDung) { }
public async Task<bool> CapNhatNguoiDungAsync(int id, NguoiDung nguoiDung) { }
public async Task<bool> XoaNguoiDungAsync(int id) { }
```

### DTOs và Models (PascalCase)
```csharp
// ✅ Đúng
public class NguoiDungDto { }
public class TaoNguoiDungRequest { }
public class CapNhatNguoiDungRequest { }
public class DangNhapRequest { }
public class DangNhapResponse { }
public class AuthNguoiDungDto { }
```

## Database Naming (PostgreSQL)

### Tên bảng (snake_case)
```sql
-- ✅ Đúng
nguoi_dung
san_pham
don_hang
chi_tiet_don_hang
refresh_token

-- ❌ Sai
dm_nguoi_dung (tránh prefix dm_)
NguoiDung (tránh PascalCase)
người_dùng (tránh dấu)
```

### Tên cột (snake_case)
```sql
-- ✅ Đúng
ho_ten
so_dien_thoai
ngay_sinh
trang_thai_hoat_dong
ngay_tao
ngay_cap_nhat

-- ❌ Sai
hoTen (tránh camelCase)
họTên (tránh dấu)
firstName (tránh tiếng Anh)
```

### Indexes và Constraints
```sql
-- ✅ Đúng
idx_nguoi_dung_email
idx_nguoi_dung_username
fk_don_hang_nguoi_dung_id
fk_chi_tiet_don_hang_don_hang_id
uk_nguoi_dung_username
uk_nguoi_dung_email
pk_nguoi_dung_id
```

### Stored Procedures và Functions
```sql
-- ✅ Đúng
sp_lay_danh_sach_nguoi_dung()
fn_tinh_tong_gia_tri_don_hang()
sp_cap_nhat_trang_thai_don_hang()
```

## API Endpoints (kebab-case)

### REST API Routes
```
// ✅ Đúng
GET    /api/nguoi-dung
POST   /api/nguoi-dung
PUT    /api/nguoi-dung/{id}
DELETE /api/nguoi-dung/{id}
GET    /api/nguoi-dung/{id}

GET    /api/san-pham
POST   /api/san-pham
GET    /api/san-pham/danh-muc/{danhMucId}

POST   /api/don-hang
GET    /api/don-hang/{id}
PUT    /api/don-hang/{id}/trang-thai

POST   /api/auth/dang-nhap
POST   /api/auth/dang-xuat
POST   /api/auth/lam-moi-token

GET    /api/bao-cao/doanh-thu
GET    /api/bao-cao/thong-ke-ban-hang

// ❌ Sai
GET /api/NguoiDung
GET /api/nguoiDung
GET /api/users
GET /api/người-dùng
```

### Query Parameters
```
// ✅ Đúng
/api/nguoi-dung?trang-thai=hoat-dong&sap-xep=ho-ten&trang=1&kich-thuoc=10
/api/san-pham?danh-muc=dien-tu&gia-tu=100000&gia-den=500000
/api/don-hang?tu-ngay=2024-01-01&den-ngay=2024-12-31&trang-thai=da-giao

// ❌ Sai
/api/nguoi-dung?trangThai=hoatDong
/api/nguoi-dung?status=active
/api/san-pham?category=electronics
```

## Git Commit Messages

### Conventional Commits (tiếng Việt không dấu)
```bash
# ✅ Đúng
feat(nguoi-dung): them chuc nang dang ky tai khoan
fix(san-pham): sua loi hien thi gia san pham
docs(readme): cap nhat huong dan cai dat
style(header): dinh dang lai component header
refactor(auth): toi uu hoa service xac thuc
test(don-hang): them unit test cho service don hang
chore(deps): cap nhat angular len phien ban 19
perf(api): toi uu hoa query lay danh sach nguoi dung
ci(github): them workflow build va deploy

# ❌ Sai
feat(người-dùng): thêm chức năng đăng ký
fix(user): fix display price bug
FEAT: add new feature
feat: thêm tính năng mới
```

### Commit Types
- `feat`: Tính năng mới
- `fix`: Sửa lỗi
- `docs`: Cập nhật tài liệu
- `style`: Thay đổi format code (không ảnh hưởng logic)
- `refactor`: Tái cấu trúc code
- `test`: Thêm/sửa test
- `chore`: Cập nhật build tools, dependencies
- `perf`: Cải thiện performance
- `ci`: Thay đổi CI/CD
- `revert`: Hoàn tác commit trước đó

### Commit Scopes (kebab-case)
```bash
# Feature scopes
feat(quan-ly-nguoi-dung): ***
feat(quan-ly-san-pham): ***
feat(quan-ly-don-hang): ***
feat(bao-cao-thong-ke): ***

# Component scopes
fix(danh-sach-nguoi-dung): ***
style(form-dang-nhap): ***
refactor(header-component): ***

# Service scopes
feat(nguoi-dung-service): ***
fix(auth-service): ***
perf(api-service): ***

# Infrastructure scopes
chore(docker): ***
ci(github-actions): ***
docs(api-docs): ***
```

## Environment Variables

### Frontend (Angular)
```typescript
// environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:5000/api',
  tenUngDung: 'HuyPhuc Management System',
  phienBan: '1.0.0',
  thoiGianTokenHetHan: 3600000, // 1 hour in milliseconds
  soLuongItemTrenTrang: 10,
  ngonNguMacDinh: 'vi',
  cheDoBaoTri: false
};

// environment.prod.ts
export const environment = {
  production: true,
  apiUrl: 'https://api.huyphuc.com/api',
  tenUngDung: 'HuyPhuc Management System',
  phienBan: '1.0.0',
  thoiGianTokenHetHan: 3600000,
  soLuongItemTrenTrang: 20,
  ngonNguMacDinh: 'vi',
  cheDoBaoTri: false
};
```

### Backend (.NET)
```json
// appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=huyphuc_db;Username=postgres;Password=***"
  },
  "JwtSettings": {
    "SecretKey": "***",
    "Issuer": "HuyPhuc.API",
    "Audience": "HuyPhuc.Client",
    "ThoiGianHetHanAccessToken": 60,
    "ThoiGianHetHanRefreshToken": 10080
  },
  "CauHinhUngDung": {
    "TenUngDung": "HuyPhuc API",
    "PhienBan": "1.0.0",
    "MoTa": "API cho hệ thống quản lý HuyPhuc",
    "CheDoBaoTri": false
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

### Docker Environment
```bash
# .env
POSTGRES_DB=huyphuc_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password
API_PORT=5000
CLIENT_PORT=4200
JWT_SECRET_KEY=your_jwt_secret_key
ASPNETCORE_ENVIRONMENT=Development
```

## Lưu ý quan trọng

1. **Nhất quán**: Toàn bộ team phải tuân thủ cùng một bộ quy tắc
2. **Không dấu**: Tuyệt đối không sử dụng dấu tiếng Việt trong code
3. **Có ý nghĩa**: Tên phải thể hiện rõ chức năng, mục đích
4. **Ngắn gọn**: Tránh tên quá dài, khó đọc
5. **Chuẩn Angular**: Tuân thủ Angular Style Guide về cấu trúc và naming convention
6. **Chuẩn .NET**: Tuân thủ Microsoft .NET Naming Guidelines
7. **Chuẩn Database**: Tuân thủ PostgreSQL naming conventions

## Checklist Kiểm tra

### Trước khi commit code:
- [ ] Tất cả tên class, method, property sử dụng tiếng Việt không dấu
- [ ] Tuân thủ case convention (camelCase, PascalCase, kebab-case, snake_case)
- [ ] Không có từ tiếng Anh trộn lẫn (trừ technical terms)
- [ ] Comment và documentation có dấu tiếng Việt
- [ ] API endpoints sử dụng kebab-case
- [ ] Database schema sử dụng snake_case
- [ ] Commit message tuân thủ conventional commits
- [ ] Environment variables có tên rõ ràng
- [ ] File và folder structure tuân thủ quy tắc

### Code Review Checklist:
- [ ] Naming conventions được tuân thủ
- [ ] Không có magic numbers/strings
- [ ] Comments và documentation đầy đủ
- [ ] Error handling phù hợp
- [ ] Performance considerations
- [ ] Security best practices

## Công cụ hỗ trợ

### Frontend Tools:
- ESLint rules cho naming conventions
- Prettier cho format code
- Angular CLI schematics tự động tạo file theo chuẩn
- VS Code extensions: Angular Language Service, Angular Snippets
- Commitlint cho commit message validation

### Backend Tools:
- EditorConfig cho consistent formatting
- StyleCop Analyzers cho .NET naming rules
- SonarQube cho code quality
- Entity Framework Power Tools
- Swagger/OpenAPI cho API documentation

### Database Tools:
- pgAdmin cho PostgreSQL management
- Database naming convention checkers
- Migration scripts validation

### Git Tools:
- Husky cho git hooks
- Commitlint cho conventional commits
- Conventional Changelog cho release notes
- GitHub Actions cho CI/CD