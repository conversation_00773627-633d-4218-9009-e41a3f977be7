using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration cho DmXa
/// </summary>
public class DmXaConfiguration : IEntityTypeConfiguration<DmXa>
{
    public void Configure(EntityTypeBuilder<DmXa> builder)
    {
        // Table name
        builder.ToTable("dm_xa");

        // Primary key
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(x => x.MaXa)
            .HasColumnName("ma_xa")
            .HasMaxLength(5)
            .IsRequired();

        builder.Property(x => x.TenXa)
            .HasColumnName("ten_xa")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(x => x.TextDisplay)
            .HasColumnName("text_display")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(x => x.MaHuyen)
            .HasColumnName("ma_huyen")
            .HasMaxLength(3)
            .IsRequired();

        builder.Property(x => x.MaTinh)
            .HasColumnName("ma_tinh")
            .HasMaxLength(2)
            .IsRequired();

        // Audit fields
        builder.Property(x => x.Created)
            .HasColumnName("created_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(x => x.LastModified)
            .HasColumnName("updated_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        // Ignore properties that don't exist in database
        builder.Ignore(x => x.CreatedBy);
        builder.Ignore(x => x.LastModifiedBy);
        builder.Ignore(x => x.NgayTao);
        builder.Ignore(x => x.NgayCapNhat);
        builder.Ignore(x => x.NguoiTao);
        builder.Ignore(x => x.NguoiCapNhat);

        // Indexes
        builder.HasIndex(x => x.MaXa)
            .IsUnique()
            .HasDatabaseName("idx_dm_xa_ma_xa");

        builder.HasIndex(x => x.MaHuyen)
            .HasDatabaseName("idx_dm_xa_ma_huyen");

        builder.HasIndex(x => x.MaTinh)
            .HasDatabaseName("idx_dm_xa_ma_tinh");

        builder.HasIndex(x => x.TenXa)
            .HasDatabaseName("idx_dm_xa_ten_xa");

        // Relationships
        builder.HasOne(x => x.Huyen)
            .WithMany(h => h.DanhSachXa)
            .HasForeignKey(x => x.MaHuyen)
            .HasPrincipalKey(h => h.MaHuyen)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(x => x.Tinh)
            .WithMany(t => t.DanhSachXa)
            .HasForeignKey(x => x.MaTinh)
            .HasPrincipalKey(t => t.MaTinh)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
