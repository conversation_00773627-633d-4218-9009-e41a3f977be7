using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Infrastructure.Settings;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace HuyPhuc.Infrastructure.Services;

/// <summary>
/// Service xử lý JWT token
/// </summary>
public class JwtService : IJwtService
{
    private readonly JwtSettings _jwtSettings;
    private readonly JwtSecurityTokenHandler _tokenHandler;

    public JwtService(IOptions<JwtSettings> jwtSettings)
    {
        _jwtSettings = jwtSettings.Value;
        _tokenHandler = new JwtSecurityTokenHandler();
    }

    /// <summary>
    /// Tạo access token từ thông tin người dùng
    /// </summary>
    public string TaoAccessToken(NguoiDung nguoiDung)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, nguoiDung.Id.ToString()),
            new(ClaimTypes.Name, nguoiDung.HoTen),
            new(ClaimTypes.Email, nguoiDung.Email.Value),
            new("username", nguoiDung.Username ?? string.Empty),
            new("trang_thai", nguoiDung.TrangThai.ToString()),
            new("jti", Guid.NewGuid().ToString()),
            new("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        // Thêm số điện thoại nếu có
        if (nguoiDung.SoDienThoai != null)
        {
            claims.Add(new Claim(ClaimTypes.MobilePhone, nguoiDung.SoDienThoai.Value));
        }

        // Thêm vai trò và quyền
        var danhSachVaiTro = nguoiDung.LayDanhSachVaiTroHieuLuc();
        foreach (var vaiTro in danhSachVaiTro)
        {
            claims.Add(new Claim(ClaimTypes.Role, vaiTro.TenVaiTro));
        }

        // Thêm quyền
        var danhSachQuyen = nguoiDung.LayTatCaQuyen();
        foreach (var quyen in danhSachQuyen)
        {
            claims.Add(new Claim("permission", quyen));
        }

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(_jwtSettings.ExpiryInMinutes),
            Issuer = _jwtSettings.Issuer,
            Audience = _jwtSettings.Audience,
            SigningCredentials = credentials
        };

        var token = _tokenHandler.CreateToken(tokenDescriptor);
        return _tokenHandler.WriteToken(token);
    }

    /// <summary>
    /// Tạo refresh token
    /// </summary>
    public string TaoRefreshToken()
    {
        var randomBytes = new byte[64];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        return Convert.ToBase64String(randomBytes);
    }

    /// <summary>
    /// Lấy thông tin claims từ token
    /// </summary>
    public ClaimsPrincipal? LayClaimsTuToken(string token)
    {
        try
        {
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey));
            
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = key,
                ValidateIssuer = true,
                ValidIssuer = _jwtSettings.Issuer,
                ValidateAudience = true,
                ValidAudience = _jwtSettings.Audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = _tokenHandler.ValidateToken(token, tokenValidationParameters, out var validatedToken);
            
            if (validatedToken is JwtSecurityToken jwtToken &&
                jwtToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
            {
                return principal;
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Kiểm tra token có hợp lệ không
    /// </summary>
    public bool KiemTraTokenHopLe(string token)
    {
        return LayClaimsTuToken(token) != null;
    }

    /// <summary>
    /// Lấy thời gian hết hạn của token
    /// </summary>
    public DateTime? LayThoiGianHetHan(string token)
    {
        try
        {
            var jwtToken = _tokenHandler.ReadJwtToken(token);
            return jwtToken.ValidTo;
        }
        catch
        {
            return null;
        }
    }
}
