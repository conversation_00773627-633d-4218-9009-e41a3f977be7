import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { Subject, takeUntil, firstValueFrom } from 'rxjs';

import { ToKhai602Service } from '../../services/to-khai-602.service';

import { LaoDongKeKhaiDto } from '../../../../../shared/models/ke-khai.model';
import { NotificationService } from '../../../../../shared/services/notification.service';

/**
 * Interface cho hiển thị lao động (đã convert từ API data)
 */
interface LaoDongDisplay {
  id: number;
  hoTen?: string;
  soCCCD?: string;
  soBHXH?: string;
  gioiTinh?: string;
  ngaySinh?: Date;
  diaChi?: string;
  soDienThoai?: string;
  email?: string;
  trangThai?: string;
}
import { ConfirmationDialogService } from '../../../../../shared/services/confirmation-dialog.service';
import { KeKhaiService } from '../../../../../shared/services/ke-khai.service';
import { EditLaoDongService } from '../nhap-lao-dong/services/edit-lao-dong.service';
import { EditLaoDongModalComponent } from '../nhap-lao-dong/components/edit-lao-dong-modal';
import { GioiTinhPipe } from '../../../../../shared/pipes/gioi-tinh.pipe';

/**
 * Component hiển thị danh sách người lao động đã nhập
 */
@Component({
  selector: 'app-danh-sach-lao-dong',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule, EditLaoDongModalComponent, GioiTinhPipe],
  template: `
    <div class="danh-sach-lao-dong-container p-6">
      <!-- Header -->
      <div class="header-section mb-6">
        <div class="flex justify-between items-center">
          <div>
            <h2 class="text-2xl font-bold text-gray-900">Danh sách người lao động</h2>
            <p class="text-gray-600 mt-1">Quản lý thông tin người lao động đã khai báo</p>
            <!-- Debug info -->
            <div class="text-xs text-gray-500 mt-1">
              Debug: keKhaiId = {{ keKhaiId || 'undefined' }},
              Tổng: {{ tongSoBanGhi }},
              Đang tải: {{ dangTai }}
            </div>
          </div>
          <div class="flex space-x-3">
            <button
              type="button"
              (click)="onLamMoi()"
              class="btn-secondary px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              [disabled]="dangTai"
            >
              <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Làm mới
            </button>
            <button
              type="button"
              routerLink="../nhap-lao-dong"
              class="btn-primary px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Thêm lao động
            </button>
          </div>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="filter-section bg-white rounded-lg shadow-sm border p-4 mb-6">
        <form [formGroup]="formTimKiem" class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Tìm kiếm -->
          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Tìm kiếm
            </label>
            <input
              type="text"
              formControlName="tuKhoa"
              placeholder="Họ tên, CCCD, BHXH..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <!-- Giới tính -->
          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Giới tính
            </label>
            <select
              formControlName="gioiTinh"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Tất cả</option>
              <option value="Nam">Nam</option>
              <option value="Nữ">Nữ</option>
            </select>
          </div>

          <!-- Trạng thái -->
          <div class="form-group">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              Trạng thái
            </label>
            <select
              formControlName="trangThai"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Tất cả</option>
              <option value="active">Hoạt động</option>
              <option value="inactive">Không hoạt động</option>
            </select>
          </div>

          <!-- Actions -->
          <div class="form-group flex items-end">
            <button
              type="button"
              (click)="formTimKiem.reset()"
              class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Xóa bộ lọc
            </button>
          </div>
        </form>
      </div>

      <!-- Loading State -->
      <div *ngIf="dangTai" class="loading-section flex justify-center items-center py-12">
        <div class="flex items-center space-x-3">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span class="text-gray-600">Đang tải dữ liệu...</span>
        </div>
      </div>

      <!-- Data Table -->
      <div *ngIf="!dangTai" class="table-section bg-white rounded-lg shadow-sm border overflow-hidden">
        <!-- Table Header -->
        <div class="table-header bg-gray-50 px-6 py-3 border-b">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-700">
              Hiển thị {{ (trangHienTai - 1) * kichThuocTrang + 1 }} -
              {{ Math.min(trangHienTai * kichThuocTrang, tongSoBanGhi) }}
              trong tổng số {{ tongSoBanGhi }} bản ghi
            </div>
            <div class="flex items-center space-x-2">
              <label class="text-sm text-gray-700">Hiển thị:</label>
              <select
                [value]="kichThuocTrang"
                (change)="thayDoiKichThuocTrang($event)"
                class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="danhSachLaoDongFiltered.length === 0" class="empty-state text-center py-12">
          <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Không có dữ liệu</h3>
          <p class="text-gray-500">Chưa có người lao động nào được khai báo hoặc không tìm thấy kết quả phù hợp.</p>
        </div>

        <!-- Table Content -->
        <div *ngIf="danhSachLaoDongFiltered.length > 0" class="table-content overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STT</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Họ và tên</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CCCD</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số BHXH</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giới tính</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ngày sinh</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số điện thoại</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngFor="let laoDong of danhSachLaoDongTrangHienTai; let i = index" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ (trangHienTai - 1) * kichThuocTrang + i + 1 }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ laoDong.hoTen }}</div>
                  <div class="text-sm text-gray-500">{{ laoDong.email }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ laoDong.soCCCD }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ laoDong.soBHXH }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ laoDong.gioiTinh | gioiTinh }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ laoDong.ngaySinh | date:'dd/MM/yyyy' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ laoDong.soDienThoai }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button (click)="xemChiTiet(laoDong)" class="text-blue-600 hover:text-blue-900 transition-colors" title="Xem chi tiết">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                      </svg>
                    </button>
                    <button (click)="chinhSua(laoDong)" class="text-green-600 hover:text-green-900 transition-colors" title="Chỉnh sửa">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    </button>
                    <button (click)="xoaLaoDong(laoDong)" class="text-red-600 hover:text-red-900 transition-colors" title="Xóa" [disabled]="dangXoa">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div *ngIf="tongSoTrang > 1" class="pagination bg-white px-6 py-3 border-t">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">Trang {{ trangHienTai }} / {{ tongSoTrang }}</div>
            <div class="flex space-x-1">
              <button (click)="chuyenTrang(trangHienTai - 1)" [disabled]="trangHienTai === 1" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">Trước</button>
              <button *ngFor="let trang of [].constructor(tongSoTrang); let i = index" (click)="chuyenTrang(i + 1)" [class.bg-blue-600]="trangHienTai === i + 1" [class.text-white]="trangHienTai === i + 1" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">{{ i + 1 }}</button>
              <button (click)="chuyenTrang(trangHienTai + 1)" [disabled]="trangHienTai === tongSoTrang" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">Sau</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal chỉnh sửa lao động -->
    <app-edit-lao-dong-modal
      [isOpen]="showEditModal"
      [laoDong]="laoDongDangChinhSua ? convertToLaoDongKeKhaiDto(laoDongDangChinhSua) : null"
      [keKhaiId]="keKhaiId"
      [isFlowMoi]="true"
      (closeModal)="onCloseEditModal()"
      (saveSuccess)="onEditSaveSuccess($event)">
    </app-edit-lao-dong-modal>
  `,
  styleUrls: ['./danh-sach-lao-dong.component.scss']
})
export class DanhSachLaoDongComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // Expose Math to template
  Math = Math;

  // Form tìm kiếm
  formTimKiem: FormGroup;

  // State
  keKhaiId!: number;
  danhSachLaoDong: LaoDongDisplay[] = [];
  danhSachLaoDongFiltered: LaoDongDisplay[] = [];
  dangTai = false;
  dangXoa = false;

  // Modal states
  showEditModal = false;
  laoDongDangChinhSua: LaoDongDisplay | null = null;

  // Pagination
  trangHienTai = 1;
  kichThuocTrang = 10;
  tongSoBanGhi = 0;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toKhai602Service: ToKhai602Service,
    private notificationService: NotificationService,
    private confirmationService: ConfirmationDialogService,
    private keKhaiService: KeKhaiService,
    private editLaoDongService: EditLaoDongService
  ) {
    this.formTimKiem = this.taoFormTimKiem();
  }

  ngOnInit(): void {
    console.log('🔍 DanhSachLaoDongComponent ngOnInit');
    this.layKeKhaiId();
    this.khoiTaoSubscriptions();
    // taiDanhSachLaoDong sẽ được gọi từ layKeKhaiId khi có keKhaiId
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Tạo form tìm kiếm
   */
  private taoFormTimKiem(): FormGroup {
    return this.fb.group({
      tuKhoa: [''],
      gioiTinh: [''],
      trangThai: ['']
    });
  }

  /**
   * Lấy keKhaiId từ route params
   */
  private layKeKhaiId(): void {
    console.log('🔍 layKeKhaiId - checking route params');
    console.log('🔍 Current route:', this.route);
    console.log('🔍 Parent route:', this.route.parent);
    console.log('🔍 Current URL:', this.router.url);

    // Thử lấy từ parent route trước
    this.route.parent?.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      console.log('🔍 Parent route params:', params);
      if (params['id']) {
        this.keKhaiId = +params['id'];
        console.log('✅ keKhaiId from parent route:', this.keKhaiId);
        // Tải lại dữ liệu khi có keKhaiId
        this.taiDanhSachLaoDong();
      } else {
        console.log('❌ No id found in parent route params');
      }
    });

    // Fallback: thử parse từ URL
    const urlSegments = this.router.url.split('/');
    const quanLyIndex = urlSegments.findIndex(segment => segment === 'quan-ly');
    if (quanLyIndex !== -1 && urlSegments[quanLyIndex + 1]) {
      const idFromUrl = +urlSegments[quanLyIndex + 1];
      if (!isNaN(idFromUrl)) {
        this.keKhaiId = idFromUrl;
        console.log('✅ keKhaiId from URL parsing:', this.keKhaiId);
        // Tải lại dữ liệu khi có keKhaiId
        this.taiDanhSachLaoDong();
      }
    }
  }

  /**
   * Khởi tạo subscriptions
   */
  private khoiTaoSubscriptions(): void {
    // Subscribe to form changes for search
    this.formTimKiem.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.timKiem();
      });

    // Subscribe to service state
    this.toKhai602Service.dangTai$
      .pipe(takeUntil(this.destroy$))
      .subscribe(dangTai => {
        this.dangTai = dangTai;
      });
  }

  /**
   * Làm mới dữ liệu
   */
  onLamMoi(): void {
    console.log('🔄 Làm mới dữ liệu');
    this.layKeKhaiId();
  }

  /**
   * Tải danh sách lao động
   */
  async taiDanhSachLaoDong(): Promise<void> {
    console.log('🔍 taiDanhSachLaoDong called, keKhaiId:', this.keKhaiId);

    if (!this.keKhaiId) {
      console.warn('❌ Không có keKhaiId để tải danh sách lao động');
      this.danhSachLaoDong = [];
      this.danhSachLaoDongFiltered = [];
      this.tongSoBanGhi = 0;
      return;
    }

    console.log('🔍 Tải danh sách lao động cho keKhaiId:', this.keKhaiId);
    this.dangTai = true;

    try {
      // Lấy danh sách lao động trực tiếp từ API
      console.log('🔍 Calling keKhaiService.layDanhSachLaoDong with ID:', this.keKhaiId);
      const danhSachLaoDongFromAPI = await firstValueFrom(this.keKhaiService.layDanhSachLaoDong(this.keKhaiId));

      console.log('🔍 API Response (danh sách lao động):', danhSachLaoDongFromAPI);

      if (danhSachLaoDongFromAPI && danhSachLaoDongFromAPI.length > 0) {
        console.log('🔍 Raw danhSachLaoDong from API:', danhSachLaoDongFromAPI);

        // Convert dữ liệu từ API format sang display format
        this.danhSachLaoDong = danhSachLaoDongFromAPI.map(item => this.convertLaoDongData(item));
        this.danhSachLaoDongFiltered = [...this.danhSachLaoDong];
        this.tongSoBanGhi = this.danhSachLaoDong.length;

        console.log('✅ Converted data:', this.danhSachLaoDong);
        console.log('✅ Đã tải được', this.danhSachLaoDong.length, 'lao động từ database');
      } else {
        // Nếu chưa có lao động nào trong database
        this.danhSachLaoDong = [];
        this.danhSachLaoDongFiltered = [];
        this.tongSoBanGhi = 0;

        console.log('ℹ️ Kê khai chưa có lao động nào trong database');
      }

    } catch (error) {
      console.error('❌ Lỗi khi tải danh sách lao động từ API:', error);

      // Hiển thị lỗi cho user
      this.notificationService.showError('Không thể tải danh sách lao động từ server');

      // Set empty data
      this.danhSachLaoDong = [];
      this.danhSachLaoDongFiltered = [];
      this.tongSoBanGhi = 0;


    } finally {
      this.dangTai = false;
    }
  }

  /**
   * Tìm kiếm lao động
   */
  timKiem(): void {
    const { tuKhoa, gioiTinh, trangThai } = this.formTimKiem.value;
    
    this.danhSachLaoDongFiltered = this.danhSachLaoDong.filter(laoDong => {
      const matchTuKhoa = !tuKhoa ||
        laoDong.hoTen?.toLowerCase().includes(tuKhoa.toLowerCase()) ||
        laoDong.soCCCD?.includes(tuKhoa) ||
        laoDong.soBHXH?.includes(tuKhoa);

      const matchGioiTinh = !gioiTinh || laoDong.gioiTinh === gioiTinh;
      const matchTrangThai = !trangThai || laoDong.trangThai === trangThai;

      return matchTuKhoa && matchGioiTinh && matchTrangThai;
    });

    this.tongSoBanGhi = this.danhSachLaoDongFiltered.length;
    this.trangHienTai = 1; // Reset về trang đầu
  }

  /**
   * Lấy danh sách lao động cho trang hiện tại
   */
  get danhSachLaoDongTrangHienTai(): LaoDongDisplay[] {
    const startIndex = (this.trangHienTai - 1) * this.kichThuocTrang;
    const endIndex = startIndex + this.kichThuocTrang;
    return this.danhSachLaoDongFiltered.slice(startIndex, endIndex);
  }

  /**
   * Xem chi tiết lao động
   */
  xemChiTiet(laoDong: LaoDongDisplay): void {
    console.log('Xem chi tiết lao động:', laoDong);
    // TODO: Mở modal hoặc navigate to detail page
  }

  /**
   * Chỉnh sửa lao động - mở modal
   */
  chinhSua(laoDong: LaoDongDisplay): void {
    console.log('Chỉnh sửa lao động:', laoDong);
    this.laoDongDangChinhSua = laoDong;
    this.showEditModal = true;
  }

  /**
   * Xóa lao động
   */
  async xoaLaoDong(laoDong: LaoDongDisplay): Promise<void> {
    const confirmed = await this.confirmationService.confirm(
      'Xác nhận xóa',
      `Bạn có chắc chắn muốn xóa lao động "${laoDong.hoTen}"?`,
      'Xóa',
      'Hủy'
    );

    if (!confirmed) return;

    this.dangXoa = true;
    try {
      // Gọi API xóa lao động thực
      await firstValueFrom(this.keKhaiService.xoaLaoDong(this.keKhaiId, laoDong.id));

      this.notificationService.showSuccess('Xóa lao động thành công');

      // Tải lại danh sách sau khi xóa
      await this.taiDanhSachLaoDong();

    } catch (error) {
      console.error('Lỗi khi xóa lao động:', error);
      this.notificationService.showError('Không thể xóa lao động');
    } finally {
      this.dangXoa = false;
    }
  }

  /**
   * Chuyển trang
   */
  chuyenTrang(trang: number): void {
    this.trangHienTai = trang;
  }

  /**
   * Thay đổi kích thước trang
   */
  thayDoiKichThuocTrang(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.kichThuocTrang = +target.value;
    this.trangHienTai = 1;
  }

  /**
   * Lấy tổng số trang
   */
  get tongSoTrang(): number {
    return Math.ceil(this.tongSoBanGhi / this.kichThuocTrang);
  }

  /**
   * Convert dữ liệu từ API format sang display format
   */
  private convertLaoDongData(apiData: LaoDongKeKhaiDto): LaoDongDisplay {
    // Xử lý họ tên - nếu là placeholder thì hiển thị "Lao động + mã số BHXH"
    let hoTenDisplay = apiData.hoTen;
    if (!hoTenDisplay ||
        hoTenDisplay === '[Cần cập nhật họ tên]' ||
        hoTenDisplay === '[Chưa có thông tin]' ||
        hoTenDisplay.startsWith('Lao động ')) {
      hoTenDisplay = `Lao động ${apiData.maSoBHXH}`;
    }

    // Xử lý CMND - nếu là placeholder thì hiển thị "Chưa cập nhật"
    let cmndDisplay = apiData.cmnd;
    if (!cmndDisplay ||
        cmndDisplay === '000000000000' ||
        cmndDisplay === apiData.maSoBHXH ||
        cmndDisplay.length < 9) {
      cmndDisplay = 'Chưa cập nhật';
    }

    return {
      id: apiData.id,
      hoTen: hoTenDisplay,
      soCCCD: cmndDisplay,
      soBHXH: apiData.maSoBHXH, // API sử dụng maSoBHXH
      gioiTinh: apiData.gioiTinh === 1 ? 'Nam' : apiData.gioiTinh === 2 ? 'Nữ' : undefined,
      ngaySinh: apiData.ngaySinh ? new Date(apiData.ngaySinh) : undefined,
      diaChi: '', // API không có trường này, có thể tính từ mã tỉnh/huyện/xã
      soDienThoai: apiData.dienThoaiLh,
      email: '', // API không có email
      trangThai: 'active' // Mặc định active
    };
  }

  /**
   * Đóng modal chỉnh sửa
   */
  onCloseEditModal(): void {
    this.showEditModal = false;
    this.laoDongDangChinhSua = null;
  }

  /**
   * Xử lý khi lưu thành công từ modal
   */
  onEditSaveSuccess(updatedLaoDong: any): void {
    console.log('Lưu thành công:', updatedLaoDong);
    this.onCloseEditModal();
    // Tải lại danh sách để cập nhật dữ liệu mới
    this.taiDanhSachLaoDong();
  }

  /**
   * Chuyển đổi LaoDongDisplay sang LaoDongKeKhaiDto để truyền vào modal
   */
  convertToLaoDongKeKhaiDto(laoDong: LaoDongDisplay): LaoDongKeKhaiDto {
    return {
      id: laoDong.id,
      keKhaiId: this.keKhaiId,
      maSoBHXH: laoDong.soBHXH || '',
      stt: 1, // Default value
      hoTen: laoDong.hoTen,
      cmnd: laoDong.soCCCD,
      gioiTinh: laoDong.gioiTinh === 'Nam' ? 1 : laoDong.gioiTinh === 'Nữ' ? 2 : undefined,
      ngaySinh: laoDong.ngaySinh ? laoDong.ngaySinh.toISOString().split('T')[0] : undefined,
      dienThoaiLh: laoDong.soDienThoai,
      created: new Date() // Required field
    };
  }

}
