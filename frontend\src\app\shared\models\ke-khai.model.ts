/**
 * Models tổng quát cho hệ thống kê khai
 * Sử dụng bảng danh_sach_ke_khai
 */

// Import các models cơ bản từ ke-khai/to-khai-602
export type { DaiLy, DonVi } from '../../features/ke-khai/to-khai-602/models';

/**
 * Enum trạng thái kê khai
 */
export enum TrangThaiKeKhai {
  DangSoan = 0,
  DaGui = 1,
  DaDuyet = 2,
  BiTuChoi = 3
}

/**
 * Interface thủ tục
 */
export interface ThuTucDto {
  id: number;
  ma: string;
  ten: string;
  linhVuc: number;
  tenLinhVuc: string;
  moTa?: string;
  trangThai: number;
  schemaHeader?: any;
  schemaChiTiet?: any;
  validationRules?: any;
}

/**
 * Interface kê khai tổng quát
 */
export interface KeKhaiDto {
  id: number;
  maKeKhai: string;
  thuTucId: number;
  daiLyId: number;
  donViId: number;
  soSoBHXH: string;
  thongTinHeader?: any;
  trangThai: number;
  nguoiTaoId: number;
  ngayTao: Date;
  nguoiPheDuyetId?: number;
  ngayPheDuyet?: Date;
  lyDoTuChoi?: string;
  fileXmlPath?: string;
  chuKySo?: string;
  digestValue?: string;
  signatureValue?: string;
  ghiChu?: string;
  created: Date;
  createdBy?: string;
  lastModified?: Date;
  lastModifiedBy?: string;

  // Navigation properties
  thuTuc?: ThuTucDto;
  daiLy?: any;
  donVi?: any;
  nguoiTao?: any;
  nguoiPheDuyet?: any;
  danhSachLaoDong?: LaoDongKeKhaiDto[];
}

/**
 * Request tạo kê khai mới
 */
export interface TaoKeKhaiRequest {
  thuTucId: number;
  daiLyId: number;
  donViId: number;
  soSoBHXH: string;
  thongTinHeader?: any;
  ghiChu?: string;
}

/**
 * Response tạo kê khai
 */
export interface TaoKeKhaiResponse {
  keKhaiId: number;
  maKeKhai: string;
  ngayTao: Date;
  trangThai: string;
  thuTuc: ThuTucDto;
  daiLy: any;
  donVi: any;
}

/**
 * DTO lao động trong kê khai
 */
export interface LaoDongKeKhaiDto {
  id: number;
  keKhaiId: number;
  maSoBHXH: string;
  stt: number;

  // Thông tin từ tk1_ts
  hoTen?: string;
  ccns?: string;
  cmnd?: string;
  ngaySinh?: string;
  gioiTinh?: number;
  quocTich?: string;
  danToc?: string;
  maTinhKs?: string;
  maHuyenKs?: string;
  maXaKs?: string;
  dienThoaiLh?: string;
  maHoGiaDinh?: string;

  // Thông tin đặc thù cho 602
  phuongAn?: string;
  phuongThuc?: string;
  thangBatDau?: string;
  mucThuNhap?: number;
  tienLai?: number;
  tienThua?: number;
  tienTuDong?: number;
  tongTien?: number;
  tienHoTro?: number;
  tyLe?: number;
  soThang?: number;

  // Thông tin đặc thù cho 603
  ngayBienLai?: string;
  soBienLai?: string;
  ghiChuBienLai?: string;

  // Thông tin bổ sung
  tyLeNsnn?: number;
  loaiNsnn?: string;
  tyLeNsnnHoTro?: number;
  heSoDong?: number;
  heSo?: number;
  tyLeNsdp?: number;
  tienNsdp?: number;
  tyLeHoTroKhac?: number;
  tienHoTroKhac?: number;

  // Địa chỉ đăng ký
  diaChiDangSs?: string;
  maTinhDangSs?: string;
  maHuyenDangSs?: string;
  maXaDangSs?: string;

  // Nhân viên thu
  maNhanVienThu?: string;

  // Error handling
  isError?: boolean;
  maLoi?: string;
  moTaLoi?: string;
  message?: string;

  // Audit
  created: Date;
  createdBy?: string;
  lastModified?: Date;
  lastModifiedBy?: string;
}

/**
 * Request thêm lao động vào kê khai
 */
export interface ThemLaoDongKeKhaiRequest {
  keKhaiId: number;
  maSoBHXH: string;
  stt?: number; // Optional - sẽ được tự động tạo nếu không có

  // Thông tin cá nhân (sẽ được lưu vào tk1_ts)
  hoTen?: string;
  cmnd?: string;
  ngaySinh?: string;
  gioiTinh?: number;
  dienThoai?: string;
  maTinhKs?: string;
  maHuyenKs?: string;
  maXaKs?: string;

  // Thông tin đặc thù cho 602/603
  phuongAn?: string;
  phuongThuc?: string;
  thangBatDau?: string;
  mucThuNhap?: number;
  tienLai?: number;
  tienThua?: number;
  tienTuDong?: number;
  tongTien?: number;
  tienHoTro?: number;
  tyLe?: number;
  soThang?: number;
  ngayBienLai?: string;
  soBienLai?: string;
  ghiChuBienLai?: string;
  loaiNsnn?: string;
  tyLeNsnnHoTro?: number;
  heSoDong?: number;
}

/**
 * Response thêm lao động
 */
export interface ThemLaoDongResponse {
  id: number;
  success: boolean;
  message: string;
}

/**
 * Request cập nhật lao động
 */
export interface CapNhatLaoDongKeKhaiRequest extends ThemLaoDongKeKhaiRequest {
  id: number;
}

/**
 * Response validate kê khai
 */
export interface ValidateKeKhaiResponse {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Query lấy danh sách kê khai
 */
export interface LayDanhSachKeKhaiQuery {
  page?: number;
  pageSize?: number;
  thuTucId?: number;
  daiLyId?: number;
  donViId?: number;
  trangThai?: number;
  tuNgay?: Date;
  denNgay?: Date;
  tuKhoa?: string;
}

/**
 * Response danh sách kê khai
 */
export interface DanhSachKeKhaiResponse {
  items: KeKhaiDto[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * Chi tiết kê khai với đầy đủ thông tin
 */
export interface KeKhaiChiTietDto extends KeKhaiDto {
  danhSachLaoDong: LaoDongKeKhaiDto[];
  tongSoLaoDong: number;
  tongTien: number;
}
