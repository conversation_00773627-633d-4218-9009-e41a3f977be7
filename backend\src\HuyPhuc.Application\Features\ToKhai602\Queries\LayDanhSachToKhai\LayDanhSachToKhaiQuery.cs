using HuyPhuc.Application.DTOs.ToKhai602;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Entities.Base;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.ToKhai602.Queries.LayDanhSachToKhai;

/// <summary>
/// Query lấy danh sách tờ khai 602
/// </summary>
public record LayDanhSachToKhaiQuery(LayDanhSachToKhaiRequest Request) : IRequest<LayDanhSachToKhaiResponse>;

/// <summary>
/// Handler xử lý query lấy danh sách tờ khai 602
/// </summary>
public class LayDanhSachToKhaiQueryHandler : IRequestHandler<LayDanhSachToKhaiQuery, LayDanhSachToKhaiResponse>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public LayDanhSachToKhaiQueryHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<LayDanhSachToKhaiResponse> Handle(LayDanhSachToKhaiQuery query, CancellationToken cancellationToken)
    {
        var request = query.Request;
        var userIdString = _currentUserService.UserId;
        
        if (userIdString == null || !int.TryParse(userIdString, out var userId))
        {
            throw new UnauthorizedAccessException("Không thể xác định người dùng hiện tại");
        }

        // Base query
        var baseQuery = _context.ToKhai602
            .Include(t => t.DaiLy)
            .Include(t => t.DonVi)
            .Include(t => t.DanhSachLaoDong)
            .AsQueryable();

        // Apply filters
        if (request.DaiLyId.HasValue)
        {
            baseQuery = baseQuery.Where(t => t.DaiLyId == request.DaiLyId.Value);
        }

        if (request.DonViId.HasValue)
        {
            baseQuery = baseQuery.Where(t => t.DonViId == request.DonViId.Value);
        }

        if (!string.IsNullOrEmpty(request.TrangThai))
        {
            if (Enum.TryParse<TrangThaiToKhai>(request.TrangThai, out var trangThai))
            {
                baseQuery = baseQuery.Where(t => t.TrangThai == trangThai);
            }
        }

        if (!string.IsNullOrEmpty(request.SoSoBHXH))
        {
            baseQuery = baseQuery.Where(t => t.SoSoBHXH.Contains(request.SoSoBHXH));
        }

        if (!string.IsNullOrEmpty(request.TuKhoa))
        {
            var tuKhoa = request.TuKhoa.ToLower();
            baseQuery = baseQuery.Where(t => 
                t.MaToKhai.ToLower().Contains(tuKhoa) ||
                t.SoSoBHXH.ToLower().Contains(tuKhoa) ||
                (t.GhiChu != null && t.GhiChu.ToLower().Contains(tuKhoa))
            );
        }

        if (request.TuNgay.HasValue)
        {
            baseQuery = baseQuery.Where(t => t.NgayTao >= request.TuNgay.Value);
        }

        if (request.DenNgay.HasValue)
        {
            var denNgay = request.DenNgay.Value.Date.AddDays(1);
            baseQuery = baseQuery.Where(t => t.NgayTao < denNgay);
        }

        // Get total count
        var tongSo = await baseQuery.CountAsync(cancellationToken);

        // Apply pagination and get entities first
        var entities = await baseQuery
            .OrderByDescending(t => t.NgayTao)
            .Skip((request.Trang - 1) * request.KichThuoc)
            .Take(request.KichThuoc)
            .ToListAsync(cancellationToken);

        // Map to DTOs in memory to avoid LINQ translation issues
        var danhSach = entities.Select(t => new ToKhai602Dto
        {
            Id = t.Id,
            MaToKhai = t.MaToKhai,
            DaiLyId = t.DaiLyId,
            TenDaiLy = t.DaiLy?.TenDaiLy ?? "",
            DonViId = t.DonViId,
            TenDonVi = t.DonVi?.TenDonVi ?? "",
            SoSoBHXH = t.SoSoBHXH,
            KyKeKhai = null, // TODO: Implement if needed
            TrangThai = t.TrangThai.ToString(),
            TongSoLaoDong = t.DanhSachLaoDong?.Count ?? 0,
            TongTienDong = t.DanhSachLaoDong?.Sum(ld => ld.TongTien) ?? 0,
            GhiChu = t.GhiChu,
            NgayTao = t.NgayTao,
            NgayCapNhat = t.NgayCapNhat,
            NguoiTao = ((IAuditableEntity)t).NguoiTao,
            NguoiCapNhat = ((IAuditableEntity)t).NguoiCapNhat
        }).ToList();

        var tongTrang = (int)Math.Ceiling((double)tongSo / request.KichThuoc);

        return new LayDanhSachToKhaiResponse
        {
            DanhSach = danhSach,
            PhanTrang = new PaginationInfo
            {
                Trang = request.Trang,
                KichThuoc = request.KichThuoc,
                TongSo = tongSo,
                TongTrang = tongTrang
            }
        };
    }
}
