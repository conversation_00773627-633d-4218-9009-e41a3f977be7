using HuyPhuc.Application.DTOs.DonVi;
using HuyPhuc.Application.Features.DonVi.Queries.LayDanhSachDonViTheoDaiLy;
using HuyPhuc.Api.Controllers.Base;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// Controller quản lý đơn vị
/// </summary>
[ApiController]
[Route("api/don-vi")]
[Authorize]
public class DonViController : BaseController
{
    /// <summary>
    /// Lấy danh sách đơn vị theo đại lý
    /// </summary>
    /// <param name="daiLyId">ID đại lý</param>
    /// <returns>Danh sách đơn vị</returns>
    [HttpGet("theo-dai-ly/{daiLyId}")]
    public async Task<ActionResult<List<DonViSelectDto>>> LayDanhSachDonViTheoDaiLy(int daiLyId)
    {
        try
        {
            var result = await Mediator.Send(new LayDanhSachDonViTheoDaiLyQuery(daiLyId));
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Có lỗi xảy ra khi lấy danh sách đơn vị", error = ex.Message });
        }
    }
}
