using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class ToKhai602Configuration : IEntityTypeConfiguration<ToKhai602>
{
    public void Configure(EntityTypeBuilder<ToKhai602> builder)
    {
        builder.ToTable("to_khai_602");

        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id)
            .HasColumnName("id");

        builder.Property(e => e.MaToKhai)
            .HasColumnName("ma_to_khai")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.DaiLyId)
            .HasColumnName("dai_ly_id")
            .IsRequired();

        builder.Property(e => e.DonViId)
            .HasColumnName("don_vi_id")
            .IsRequired();

        builder.Property(e => e.SoSoBHXH)
            .HasColumnName("so_so_bhxh")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.GhiChu)
            .HasColumnName("ghi_chu")
            .HasMaxLength(500);

        builder.Property(e => e.TrangThai)
            .HasColumnName("trang_thai")
            .HasConversion<int>()
            .IsRequired();

        builder.Property(e => e.NgayTao)
            .HasColumnName("ngay_tao")
            .IsRequired();

        builder.Property(e => e.NguoiTaoId)
            .HasColumnName("nguoi_tao_id")
            .IsRequired();

        builder.Property(e => e.NgayPheDuyet)
            .HasColumnName("ngay_phe_duyet");

        builder.Property(e => e.NguoiPheDuyetId)
            .HasColumnName("nguoi_phe_duyet_id");

        builder.Property(e => e.LyDoTuChoi)
            .HasColumnName("ly_do_tu_choi")
            .HasMaxLength(1000);

        // Audit properties from IAuditableEntity
        builder.Property(e => e.NgayCapNhat)
            .HasColumnName("last_modified");

        // Explicit interface implementation properties
        builder.Property<string?>("IAuditableEntity.NguoiTao")
            .HasColumnName("created_by")
            .HasMaxLength(100);

        builder.Property<string?>("IAuditableEntity.NguoiCapNhat")
            .HasColumnName("last_modified_by")
            .HasMaxLength(100);

        // Relationships
        builder.HasOne(e => e.DaiLy)
            .WithMany()
            .HasForeignKey(e => e.DaiLyId)
            .HasConstraintName("fk_to_khai_602_dai_ly")
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.DonVi)
            .WithMany()
            .HasForeignKey(e => e.DonViId)
            .HasConstraintName("fk_to_khai_602_don_vi")
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.NguoiTao)
            .WithMany()
            .HasForeignKey(e => e.NguoiTaoId)
            .HasConstraintName("fk_to_khai_602_nguoi_tao")
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.NguoiPheDuyet)
            .WithMany()
            .HasForeignKey(e => e.NguoiPheDuyetId)
            .HasConstraintName("fk_to_khai_602_nguoi_phe_duyet")
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(e => e.DanhSachLaoDong)
            .WithOne(e => e.ToKhai602)
            .HasForeignKey(e => e.ToKhai602Id)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(e => e.MaToKhai)
            .IsUnique();

        builder.HasIndex(e => e.DaiLyId);
        builder.HasIndex(e => e.DonViId);
        builder.HasIndex(e => e.NgayTao);
        builder.HasIndex(e => e.TrangThai);
    }
}
