using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration cho DmHuyen
/// </summary>
public class DmHuyenConfiguration : IEntityTypeConfiguration<DmHuyen>
{
    public void Configure(EntityTypeBuilder<DmHuyen> builder)
    {
        // Table name
        builder.ToTable("dm_huyen");

        // Primary key
        builder.HasKey(h => h.Id);
        builder.Property(h => h.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(h => h.<PERSON>Huyen)
            .HasColumnName("ma_huyen")
            .HasMaxLength(3)
            .IsRequired();

        builder.Property(h => h.TenHuyen)
            .HasColumnName("ten_huyen")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(h => h.TextDisplay)
            .HasColumnName("text_display")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(h => h.MaTinh)
            .HasColumnName("ma_tinh")
            .HasMaxLength(2)
            .IsRequired();

        // Audit fields
        builder.Property(h => h.Created)
            .HasColumnName("created_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(h => h.LastModified)
            .HasColumnName("updated_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        // Ignore properties that don't exist in database
        builder.Ignore(h => h.CreatedBy);
        builder.Ignore(h => h.LastModifiedBy);
        builder.Ignore(h => h.NgayTao);
        builder.Ignore(h => h.NgayCapNhat);
        builder.Ignore(h => h.NguoiTao);
        builder.Ignore(h => h.NguoiCapNhat);

        // Indexes
        builder.HasIndex(h => h.MaHuyen)
            .IsUnique()
            .HasDatabaseName("idx_dm_huyen_ma_huyen");

        builder.HasIndex(h => h.MaTinh)
            .HasDatabaseName("idx_dm_huyen_ma_tinh");

        builder.HasIndex(h => h.TenHuyen)
            .HasDatabaseName("idx_dm_huyen_ten_huyen");

        // Relationships
        builder.HasOne(h => h.Tinh)
            .WithMany(t => t.DanhSachHuyen)
            .HasForeignKey(h => h.MaTinh)
            .HasPrincipalKey(t => t.MaTinh)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(h => h.DanhSachXa)
            .WithOne(x => x.Huyen)
            .HasForeignKey(x => x.MaHuyen)
            .HasPrincipalKey(h => h.MaHuyen)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
