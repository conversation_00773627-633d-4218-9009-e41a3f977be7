using AutoMapper;
using HuyPhuc.Application.Common.Exceptions;
using HuyPhuc.Application.Features.DanhMucThuTuc.Common;
using HuyPhuc.Domain.Repositories;
using MediatR;

namespace HuyPhuc.Application.Features.DanhMucThuTuc.Queries.GetDanhMucThuTucById;

/// <summary>
/// Query lấy chi tiết danh mục thủ tục theo ID
/// </summary>
public record GetDanhMucThuTucByIdQuery(int Id) : IRequest<DanhMucThuTucDto>;

/// <summary>
/// Handler cho GetDanhMucThuTucByIdQuery
/// </summary>
public class GetDanhMucThuTucByIdQueryHandler : IRequestHandler<GetDanhMucThuTucByIdQuery, DanhMucThuTucDto>
{
    private readonly IDanhMucThuTucRepository _repository;
    private readonly IMapper _mapper;

    public GetDanhMucThuTucByIdQueryHandler(
        IDanhMucThuTucRepository repository,
        IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    public async Task<DanhMucThuTucDto> Handle(
        GetDanhMucThuTucByIdQuery request,
        CancellationToken cancellationToken)
    {
        var entity = await _repository.LayTheoIdAsync(request.Id, cancellationToken);

        if (entity == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.DanhMucThuTuc), request.Id);
        }

        return _mapper.Map<DanhMucThuTucDto>(entity);
    }
}
