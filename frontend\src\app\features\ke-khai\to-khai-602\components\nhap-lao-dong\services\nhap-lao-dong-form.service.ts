import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { LaoDong, ValidationError } from '../../../models';

/**
 * Service xử lý form creation và management cho nhập lao động
 * Tách từ NhapLaoDongService để tuân thủ quy tắc 400 dòng
 */
@Injectable({
  providedIn: 'root'
})
export class NhapLaoDongFormService {

  constructor(private fb: FormBuilder) {}

  /**
   * Tạo form group chính
   */
  taoForm(): FormGroup {
    return this.fb.group({
      danhSachLaoDong: this.fb.array([])
    });
  }

  /**
   * Tạo form cho flow mới (kê khai) - bao gồm đầy đủ các field
   * Sử dụng tên field theo backend API
   */
  taoFormChoFlowMoi(): FormGroup {
    return this.fb.group({
      // Thông tin cá nhân
      maSoBHXH: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      hoTen: ['', [Validators.required]],
      cmnd: ['', [Validators.required]], // Backend sử dụng cmnd thay vì soCCCD
      ngaySinh: ['', [Validators.required]],
      gioiTinh: ['', [Validators.required]],
      dienThoaiLh: [''], // Backend sử dụng dienThoaiLh thay vì soDienThoai
      quocTich: ['VN'],
      danToc: ['01'],

      // Địa chỉ thường trú
      maTinhKs: ['89'],
      maHuyenKs: ['890'],
      maXaKs: ['30538'],
      maHoGiaDinh: [''],

      // Thông tin đóng BHXH
      phuongAn: ['DB', Validators.required],
      phuongThuc: ['1', Validators.required],
      thangBatDau: [this.layThangHienTai(), Validators.required],
      mucThuNhap: [1500000, [Validators.required, Validators.min(0)]],

      // Thông tin tiền (tự động tính)
      tienLai: [0, [Validators.min(0)]],
      tienThua: [0, [Validators.min(0)]],
      tienTuDong: [0, [Validators.min(0)]],
      tongTien: [0, [Validators.min(0)]],
      tienHoTro: [0, [Validators.min(0)]],

      // Thông tin khác
      ccns: ['0'],
      typeId: ['TM'],
      isThamGiaBb: [false],
      isTamHoanHD: [false],

      // Thông tin lỗi/cảnh báo
      message: [''],
      isError: [false],
      maLoi: [''],
      moTaLoi: [''],

      // Thông tin bổ sung
      tyLe: [22, [Validators.required, Validators.min(0), Validators.max(100)]],
      soThang: [1, [Validators.required, Validators.min(1), Validators.max(12)]]
    });
  }

  /**
   * Tạo form group cho một lao động
   */
  taoLaoDongFormGroup(laoDong: Partial<LaoDong>): FormGroup {
    return this.fb.group({
      // Thông tin cá nhân
      id: [laoDong.id],
      stt: [laoDong.stt || 1],
      maSoBHXH: [laoDong.maSoBHXH || '', [Validators.required]],
      hoTen: [laoDong.hoTen || '', [Validators.required]],
      ccns: [laoDong.ccns || '0'],
      ngaySinh: [laoDong.ngaySinh || '', [Validators.required]],
      gioiTinh: [laoDong.gioiTinh || 1, [Validators.required]],
      quocTich: [laoDong.quocTich || 'VN'],
      danToc: [laoDong.danToc || '01'],
      cmnd: [laoDong.cmnd || '', [Validators.required]],

      // Địa chỉ thường trú
      maTinhKs: [laoDong.maTinhKs || '89'],
      maHuyenKs: [laoDong.maHuyenKs || '890'],
      maXaKs: [laoDong.maXaKs || '30538'],

      // Thông tin liên hệ
      dienThoaiLh: [laoDong.dienThoaiLh || ''],
      maHoGiaDinh: [laoDong.maHoGiaDinh || ''],

      // Thông tin đóng BHXH
      phuongAn: [laoDong.phuongAn || 'DB', [Validators.required]],
      phuongThuc: [laoDong.phuongThuc || '1', [Validators.required]],
      thangBatDau: [laoDong.thangBatDau || this.layThangHienTai(), [Validators.required]],

      // Thông tin tiền
      tienLai: [laoDong.tienLai || 0],
      tienThua: [laoDong.tienThua || 0],
      tienTuDong: [laoDong.tienTuDong || 0],
      tongTien: [laoDong.tongTien || 0],
      tienHoTro: [laoDong.tienHoTro || 0],
      mucThuNhap: [laoDong.mucThuNhap || 1500000, [Validators.required, Validators.min(1500000)]],

      // Thông tin khác
      typeId: [laoDong.typeId || 'TM'],
      isThamGiaBb: [laoDong.isThamGiaBb || false],
      isTamHoanHD: [laoDong.isTamHoanHD || false],

      // Thông tin lỗi/cảnh báo
      message: [laoDong.message || ''],
      isError: [laoDong.isError || false],
      maLoi: [laoDong.maLoi || ''],
      moTaLoi: [laoDong.moTaLoi || '']
    });
  }

  /**
   * Cập nhật form array với danh sách lao động
   */
  capNhatFormArray(form: FormGroup, danhSachLaoDong: Partial<LaoDong>[]): void {
    const formArray = form.get('danhSachLaoDong') as FormArray;

    // Clear existing controls
    while (formArray.length !== 0) {
      formArray.removeAt(0);
    }

    // Add new controls
    danhSachLaoDong.forEach(laoDong => {
      formArray.push(this.taoLaoDongFormGroup(laoDong));
    });
  }

  /**
   * Thêm lao động mới vào form array
   */
  themLaoDongVaoForm(form: FormGroup): void {
    const formArray = form.get('danhSachLaoDong') as FormArray;
    const laoDongMoi: Partial<LaoDong> = {
      id: this.taoIdTamThoi(),
      stt: 1,
      maSoBHXH: '',
      hoTen: '',
      ccns: '0',
      ngaySinh: '',
      gioiTinh: 1,
      quocTich: 'VN',
      danToc: '01',
      cmnd: '',
      maTinhKs: '89',
      maHuyenKs: '890',
      maXaKs: '30538',
      dienThoaiLh: '',
      maHoGiaDinh: '',
      phuongAn: 'DB',
      phuongThuc: '1',
      thangBatDau: this.layThangHienTai(),
      tienLai: 0,
      tienThua: 0,
      tienTuDong: 0,
      tongTien: 0,
      tienHoTro: 0,
      mucThuNhap: 1500000,
      typeId: 'TM',
      isThamGiaBb: false,
      isTamHoanHD: false,
      message: '',
      isError: false,
      maLoi: '',
      moTaLoi: ''
    };

    formArray.push(this.taoLaoDongFormGroup(laoDongMoi));
  }

  /**
   * Xóa lao động khỏi form array
   */
  xoaLaoDongKhoiForm(form: FormGroup, index: number): void {
    const formArray = form.get('danhSachLaoDong') as FormArray;
    if (formArray.length > 1) {
      formArray.removeAt(index);
    }
  }

  /**
   * Validate form và trả về danh sách lỗi
   */
  validateForm(form: FormGroup): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!form.valid) {
      // Validate form array
      const formArray = form.get('danhSachLaoDong') as FormArray;

      formArray.controls.forEach((control, index) => {
        const formGroup = control as FormGroup;

        Object.keys(formGroup.controls).forEach(fieldName => {
          const fieldControl = formGroup.get(fieldName);

          if (fieldControl && fieldControl.invalid && fieldControl.touched) {
            const fieldErrors = fieldControl.errors;

            if (fieldErrors) {
              Object.keys(fieldErrors).forEach(errorType => {
                errors.push({
                  field: `danhSachLaoDong.${index}.${fieldName}`,
                  message: this.getErrorMessage(fieldName, errorType, fieldErrors[errorType])
                });
              });
            }
          }
        });
      });
    }

    return errors;
  }

  /**
   * Kiểm tra field có lỗi không
   */
  hasError(form: FormGroup, index: number, fieldName: string): boolean {
    const formArray = form.get('danhSachLaoDong') as FormArray;
    const formGroup = formArray.at(index) as FormGroup;
    const field = formGroup.get(fieldName);

    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  /**
   * Lấy thông báo lỗi cho field
   */
  getErrorMessage(fieldName: string, errorType: string, errorValue?: any): string {
    const fieldLabels: { [key: string]: string } = {
      maSoBHXH: 'Mã số BHXH',
      hoTen: 'Họ tên',
      ngaySinh: 'Ngày sinh',
      soCCCD: 'Số CCCD',
      diaChi: 'Địa chỉ',
      soDienThoai: 'Số điện thoại',
      email: 'Email',
      mucLuong: 'Mức lương',
      ngayBatDauLamViec: 'Ngày bắt đầu làm việc',
      mucThuNhap: 'Mức thu nhập',
      tyLe: 'Tỷ lệ',
      soThang: 'Số tháng'
    };

    const fieldLabel = fieldLabels[fieldName] || fieldName;

    switch (errorType) {
      case 'required':
        return `${fieldLabel} là bắt buộc`;
      case 'pattern':
        if (fieldName === 'maSoBHXH') {
          return 'Mã số BHXH phải có 10 chữ số';
        }
        if (fieldName === 'soCCCD') {
          return 'Số CCCD phải có 12 chữ số';
        }
        if (fieldName === 'soDienThoai') {
          return 'Số điện thoại phải có 10-11 chữ số';
        }
        return `${fieldLabel} không đúng định dạng`;
      case 'email':
        return 'Email không đúng định dạng';
      case 'min':
        return `${fieldLabel} phải lớn hơn hoặc bằng ${errorValue.min}`;
      case 'max':
        return `${fieldLabel} phải nhỏ hơn hoặc bằng ${errorValue.max}`;
      default:
        return `${fieldLabel} không hợp lệ`;
    }
  }

  /**
   * Lấy tháng hiện tại theo định dạng YYYY-MM
   */
  private layThangHienTai(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }

  /**
   * Tạo ID tạm thời cho lao động mới
   */
  private taoIdTamThoi(): string {
    return 'temp_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }
}
