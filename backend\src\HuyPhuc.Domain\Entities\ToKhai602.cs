using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Domain.Enums;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity tờ khai 602
/// </summary>
public class ToKhai602 : BaseEntity, IAuditableEntity
{
    /// <summary>
    /// Mã tờ khai (tự động generate)
    /// </summary>
    public string MaToKhai { get; set; } = string.Empty;

    /// <summary>
    /// ID đại lý
    /// </summary>
    public int DaiLyId { get; set; }

    /// <summary>
    /// ID đơn vị
    /// </summary>
    public int DonViId { get; set; }

    /// <summary>
    /// Số sổ BHXH
    /// </summary>
    public string SoSoBHXH { get; set; } = string.Empty;

    /// <summary>
    /// Ghi chú
    /// </summary>
    public string? GhiChu { get; set; }

    /// <summary>
    /// Trạng thái tờ khai
    /// </summary>
    public TrangThaiToKhai TrangThai { get; set; }

    /// <summary>
    /// ID người tạo
    /// </summary>
    public int NguoiTaoId { get; set; }

    /// <summary>
    /// Ngày phê duyệt
    /// </summary>
    public DateTime? NgayPheDuyet { get; set; }

    /// <summary>
    /// ID người phê duyệt
    /// </summary>
    public int? NguoiPheDuyetId { get; set; }

    /// <summary>
    /// Lý do từ chối (nếu có)
    /// </summary>
    public string? LyDoTuChoi { get; set; }

    // Navigation properties
    /// <summary>
    /// Đại lý
    /// </summary>
    public virtual DaiLy DaiLy { get; set; } = null!;

    /// <summary>
    /// Đơn vị
    /// </summary>
    public virtual DonVi DonVi { get; set; } = null!;

    /// <summary>
    /// Người tạo
    /// </summary>
    public virtual NguoiDung NguoiTao { get; set; } = null!;

    /// <summary>
    /// Người phê duyệt
    /// </summary>
    public virtual NguoiDung? NguoiPheDuyet { get; set; }

    /// <summary>
    /// Danh sách lao động trong tờ khai
    /// </summary>
    public virtual ICollection<LaoDongToKhai602> DanhSachLaoDong { get; set; } = new List<LaoDongToKhai602>();

    // IAuditableEntity implementation
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    string? IAuditableEntity.NguoiTao { get; set; }
    string? IAuditableEntity.NguoiCapNhat { get; set; }
}
