using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Repositories;
using HuyPhuc.Infrastructure.Repositories.Base;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Infrastructure.Repositories;

public class SanPhamRepository : Repository<SanPham>, ISanPhamRepository
{
    public SanPhamRepository(IApplicationDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<SanPham>> TimKiemTheoTenAsync(string tuKhoa, CancellationToken cancellationToken = default)
    {
        var tuKhoaLower = tuKhoa.ToLower();
        return await _dbSet
            .Where(x => x.TenSanPham.ToLower().Contains(tuKhoaLower) || 
                       x.MoTa.ToLower().Contains(tuKhoaLower))
            .OrderBy(x => x.TenSanPham)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<SanPham>> LayTheoLoaiAsync(LoaiSanPham loaiSanPham, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.LoaiSanPham == loaiSanPham)
            .OrderBy(x => x.TenSanPham)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<SanPham>> LaySanPhamDangKinhDoanhAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.DangKinhDoanh && x.SoLuongTon > 0)
            .OrderBy(x => x.TenSanPham)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<SanPham>> LaySanPhamSapHetHangAsync(int soLuongToiThieu = 10, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.DangKinhDoanh && x.SoLuongTon <= soLuongToiThieu && x.SoLuongTon > 0)
            .OrderBy(x => x.SoLuongTon)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<SanPham>> LaySanPhamBanChayAsync(int soLuong = 10, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.DangKinhDoanh)
            .Include(x => x.DanhSachChiTietDonHang)
            .OrderByDescending(x => x.DanhSachChiTietDonHang.Sum(ct => ct.SoLuong))
            .Take(soLuong)
            .ToListAsync(cancellationToken);
    }
}
