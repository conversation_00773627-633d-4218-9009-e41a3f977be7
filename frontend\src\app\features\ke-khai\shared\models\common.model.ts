/**
 * Common models đư<PERSON>c sử dụng chung cho tất cả các loại kê khai
 */

/**
 * Interface cho dropdown options
 */
export interface DropdownOption {
  value: number | string;
  label: string;
  disabled?: boolean;
}

/**
 * Interface cho validation errors
 */
export interface ValidationError {
  field: string;
  message: string;
  index?: number; // Cho validation trong array
}

/**
 * Interface cho pagination
 */
export interface PhanTrang {
  trang: number;
  kichThuoc: number;
  tongSo: number;
  tongTrang: number;
}

/**
 * Interface cho API response
 */
export interface ApiResponse<T = any> {
  thanhCong: boolean;
  thongBao: string;
  duLieu?: T;
  loi?: string[];
}
