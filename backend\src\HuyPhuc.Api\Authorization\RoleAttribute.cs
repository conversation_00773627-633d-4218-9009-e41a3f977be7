using Microsoft.AspNetCore.Authorization;

namespace HuyPhuc.Api.Authorization;

/// <summary>
/// Attribute để kiểm tra vai trò người dùng
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public class RoleAttribute : AuthorizeAttribute
{
    public RoleAttribute(params string[] roles) : base()
    {
        Roles = string.Join(",", roles);
    }
}
