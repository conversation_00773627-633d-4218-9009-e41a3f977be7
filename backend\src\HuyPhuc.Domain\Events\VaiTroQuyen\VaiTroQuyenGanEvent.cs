using MediatR;

namespace HuyPhuc.Domain.Events.VaiTroQuyen;

public record VaiTroQuyenGanEvent(Entities.VaiTroQuyen VaiTroQuyen) : INotification;
public record VaiTroQuyenCapNhatEvent(Entities.VaiTroQuyen VaiTroQuyen) : INotification;
public record VaiTroQuyenKichHoatEvent(Entities.VaiTroQuyen VaiTroQuyen) : INotification;
public record VaiTroQuyenVoHieuHoaEvent(Entities.VaiTroQuyen VaiTroQuyen) : INotification;
