using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Common.Models;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.NguoiDung.Queries;

public record LayQuyenNguoiDungQuery(int NguoiDungId) : IRequest<Result<QuyenNguoiDungDto>>;

public class LayQuyenNguoiDungQueryHandler : IRequestHandler<LayQuyenNguoiDungQuery, Result<QuyenNguoiDungDto>>
{
    private readonly IApplicationDbContext _context;

    public LayQuyenNguoiDungQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<QuyenNguoiDungDto>> Handle(LayQuyenNguoiDungQuery request, CancellationToken cancellationToken)
    {
        var nguoiDung = await _context.NguoiDung
            .Include(nd => nd.DanhSachVaiTro.Where(ndvt => 
                ndvt.TrangThaiHoatDong && 
                (!ndvt.NgayHetHan.HasValue || ndvt.NgayHetHan.Value > DateTime.UtcNow)))
                .ThenInclude(ndvt => ndvt.VaiTro)
                    .ThenInclude(vt => vt.DanhSachQuyen.Where(vtq => vtq.TrangThaiHoatDong))
                        .ThenInclude(vtq => vtq.Quyen)
            .FirstOrDefaultAsync(nd => nd.Id == request.NguoiDungId, cancellationToken);

        if (nguoiDung == null)
        {
            return Result<QuyenNguoiDungDto>.Failure("Người dùng không tồn tại");
        }

        var danhSachVaiTro = nguoiDung.DanhSachVaiTro
            .Where(ndvt => ndvt.TrangThaiHoatDong && 
                          (!ndvt.NgayHetHan.HasValue || ndvt.NgayHetHan.Value > DateTime.UtcNow))
            .Select(ndvt => new VaiTroNguoiDungDto
            {
                Id = ndvt.VaiTro.Id,
                TenVaiTro = ndvt.VaiTro.TenVaiTro,
                MoTa = ndvt.VaiTro.MoTa,
                NgayGan = ndvt.NgayGan,
                NgayHetHan = ndvt.NgayHetHan,
                DanhSachQuyen = ndvt.VaiTro.DanhSachQuyen
                    .Where(vtq => vtq.TrangThaiHoatDong)
                    .Select(vtq => new QuyenDto
                    {
                        Id = vtq.Quyen.Id,
                        TenQuyen = vtq.Quyen.TenQuyen,
                        MaQuyen = vtq.Quyen.MaQuyen,
                        MoTa = vtq.Quyen.MoTa,
                        NhomQuyen = vtq.Quyen.NhomQuyen
                    }).ToList()
            }).ToList();

        var tatCaQuyen = danhSachVaiTro
            .SelectMany(vt => vt.DanhSachQuyen)
            .GroupBy(q => q.MaQuyen)
            .Select(g => g.First())
            .OrderBy(q => q.NhomQuyen)
            .ThenBy(q => q.TenQuyen)
            .ToList();

        var result = new QuyenNguoiDungDto
        {
            NguoiDungId = nguoiDung.Id,
            HoTen = nguoiDung.HoTen,
            Email = nguoiDung.Email.Value,
            DanhSachVaiTro = danhSachVaiTro,
            TatCaQuyen = tatCaQuyen
        };

        return Result<QuyenNguoiDungDto>.Success(result);
    }
}

public class QuyenNguoiDungDto
{
    public int NguoiDungId { get; set; }
    public string HoTen { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public List<VaiTroNguoiDungDto> DanhSachVaiTro { get; set; } = new();
    public List<QuyenDto> TatCaQuyen { get; set; } = new();
}

public class VaiTroNguoiDungDto
{
    public int Id { get; set; }
    public string TenVaiTro { get; set; } = string.Empty;
    public string? MoTa { get; set; }
    public DateTime NgayGan { get; set; }
    public DateTime? NgayHetHan { get; set; }
    public List<QuyenDto> DanhSachQuyen { get; set; } = new();
}

public class QuyenDto
{
    public int Id { get; set; }
    public string TenQuyen { get; set; } = string.Empty;
    public string MaQuyen { get; set; } = string.Empty;
    public string? MoTa { get; set; }
    public string? NhomQuyen { get; set; }
}
