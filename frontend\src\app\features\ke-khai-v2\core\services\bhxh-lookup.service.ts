import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { VnPostAuthService } from './vnpost-auth.service';

export interface BhxhLookupRequest {
  maSoBHXH: string;
}

export interface BhxhLookupResponse {
  data: {
    maSoBHXH: string;
    hoTen: string;
    ccns: string;
    ngaySinh: string;
    gioiTinh: number;
    quocTich: string;
    danToc: string;
    cmnd: string;
    maTinhKs: string;
    maHuyenKs: string;
    maXaKs: string;
    dienThoaiLh: string;
    maHoGiaDinh: string;
    phuongAn: string;
    phuongThuc: string;
    thangBatDau: string;
    tienLai: number;
    tienThua: number;
    tienTuDong: number;
    tongTien: number;
    tienHoTro: number;
    mucThuNhap: number;
    typeId: string;
    isThamGiaBb: boolean;
    isTamHoanHD: boolean;
    message: string;
    isError: boolean;
    maLoi: string;
    moTaLoi: string;
  };
  success: boolean;
  message: string | null;
  errors: any;
  status: number;
  traceId: string;
}

@Injectable({
  providedIn: 'root'
})
export class BhxhLookupService {
  private readonly apiUrl = 'http://localhost:5129/api/bhxh/tra-cuu';
  private readonly apiUrlWithUpdate = 'http://localhost:5129/api/bhxh/tra-cuu-va-cap-nhat';

  // Subject để thông báo khi cần đăng nhập lại
  private tokenExpiredSubject = new BehaviorSubject<boolean>(false);
  public tokenExpired$ = this.tokenExpiredSubject.asObservable();

  constructor(
    private http: HttpClient,
    private vnPostAuthService: VnPostAuthService
  ) {
    // Listen to VnPost auth service token events
    this.vnPostAuthService.tokenExpired$.subscribe(expired => {
      this.tokenExpiredSubject.next(expired);
    });
  }

  /**
   * Tra cứu thông tin BHXH theo mã số BHXH
   */
  lookupBhxhInfo(maSoBHXH: string): Observable<BhxhLookupResponse> {
    const requestBody: BhxhLookupRequest = {
      maSoBHXH: maSoBHXH
    };

    return this.http.post<BhxhLookupResponse>(this.apiUrl, requestBody).pipe(
      map(response => {
        console.log('🔍 BHXH API Response:', response);
        if (!response.success) {
          throw new Error(response.message || 'Không thể tra cứu thông tin BHXH');
        }
        return response;
      }),
      catchError(error => {
        console.error('🔴 Lỗi khi tra cứu BHXH:', error);
        console.log('🔍 Error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message,
          error: error.error
        });

        // Xử lý các loại lỗi khác nhau
        if (error.status === 0) {
          return throwError(() => new Error('Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.'));
        } else if (error.status === 401) {
          // Token hết hạn, thông báo cần đăng nhập lại
          console.log('🔴 401 Unauthorized detected from BHXH API');
          console.log('🔍 Error details:', {
            status: error.status,
            statusText: error.statusText,
            url: error.url,
            message: error.message,
            error: error.error
          });

          // Kiểm tra xem đây có phải là lỗi VNPost token không
          // Vì đây là BHXH lookup service, tất cả 401 errors đều liên quan đến VNPost token
          console.log('🔴 VNPost token expired, triggering modal');
          this.tokenExpiredSubject.next(true);

          return throwError(() => new Error('Token đã hết hạn. Vui lòng đăng nhập lại VNPost.'));
        } else if (error.status === 404) {
          return throwError(() => new Error('Không tìm thấy thông tin BHXH với mã số này.'));
        } else if (error.status >= 500) {
          return throwError(() => new Error('Lỗi server. Vui lòng thử lại sau.'));
        } else {
          return throwError(() => new Error(error.message || 'Có lỗi xảy ra khi tra cứu thông tin BHXH.'));
        }
      })
    );
  }

  /**
   * Tra cứu và cập nhật thông tin BHXH vào bảng tk1_ts
   */
  lookupAndUpdateBhxhInfo(maSoBHXH: string): Observable<BhxhLookupResponse> {
    const requestBody: BhxhLookupRequest = {
      maSoBHXH: maSoBHXH
    };

    return this.http.post<BhxhLookupResponse>(this.apiUrlWithUpdate, requestBody).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message || 'Không thể tra cứu thông tin BHXH');
        }
        return response;
      }),
      catchError(error => {
        console.error('Lỗi khi tra cứu và cập nhật BHXH:', error);

        // Xử lý các loại lỗi khác nhau
        if (error.status === 0) {
          return throwError(() => new Error('Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.'));
        } else if (error.status === 401) {
          // Token hết hạn, thông báo cần đăng nhập lại
          console.log('🔴 401 Unauthorized detected from BHXH API (method 2)');
          console.log('🔍 Error details:', {
            status: error.status,
            statusText: error.statusText,
            url: error.url,
            message: error.message,
            error: error.error
          });

          // Kiểm tra xem đây có phải là lỗi VNPost token không
          // Vì đây là BHXH lookup service, tất cả 401 errors đều liên quan đến VNPost token
          console.log('🔴 VNPost token expired, triggering modal (method 2)');
          this.tokenExpiredSubject.next(true);

          return throwError(() => new Error('Token đã hết hạn. Vui lòng đăng nhập lại VNPost.'));
        } else if (error.status === 404) {
          return throwError(() => new Error('Không tìm thấy thông tin BHXH với mã số này.'));
        } else if (error.status >= 500) {
          return throwError(() => new Error('Lỗi server. Vui lòng thử lại sau.'));
        } else {
          return throwError(() => new Error(error.message || 'Có lỗi xảy ra khi tra cứu thông tin BHXH.'));
        }
      })
    );
  }

  /**
   * Kiểm tra tính hợp lệ của mã số BHXH
   */
  isValidBhxhCode(maSoBHXH: string): boolean {
    if (!maSoBHXH) return false;

    // Mã BHXH thường có 10 chữ số
    const bhxhPattern = /^\d{10}$/;
    return bhxhPattern.test(maSoBHXH);
  }

  /**
   * Reset trạng thái token expired sau khi đăng nhập thành công
   */
  resetTokenExpiredState(): void {
    this.tokenExpiredSubject.next(false);
  }

  /**
   * Trigger token expired event (for testing purposes)
   */
  triggerTokenExpiredEvent(): void {
    this.tokenExpiredSubject.next(true);
  }
}
