import { Loai<PERSON><PERSON><PERSON><PERSON>, Trang<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './enums';

/**
 * Base interface cho tất cả các loại kê khai
 */
export interface KeKhaiBase {
  id?: number;
  ma?: string;
  daiLyId: number;
  donViId: number;
  kyKeKhai?: string;
  loaiKeKhai: LoaiKeKhai;
  trangThai: TrangThaiKeKhai;
  ngayTao?: Date;
  ngayCapNhat?: Date;
  nguoiTao?: string;
  nguoiCapNhat?: string;
  ghiChu?: string;
}

/**
 * Interface cho response API chung
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: string[];
}

/**
 * Interface cho pagination
 */
export interface PaginationInfo {
  trang: number;
  kichThuoc: number;
  tongSo: number;
  tongTrang: number;
}

/**
 * Interface cho filter chung
 */
export interface BaseFilter {
  trang?: number;
  kichThuoc?: number;
  tuKhoa?: string;
  daiLyId?: number;
  donViId?: number;
  trangThai?: TrangThaiKeKhai;
  tuNgay?: Date;
  denNgay?: Date;
}
