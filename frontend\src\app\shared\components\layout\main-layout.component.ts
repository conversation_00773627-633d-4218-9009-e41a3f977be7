import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { HeaderComponent } from './header.component';
import { SidebarComponent } from './sidebar.component';
import { SidebarService } from '../../services/sidebar.service';
import { SidebarState, SidebarConfig } from '../../models/menu-item.model';

/**
 * Component layout chính cho toàn bộ ứng dụng
 * Bao gồm sidebar, header, content area và footer
 */
@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent],
  template: `
    <div class="main-layout min-h-screen bg-gray-50">
      <!-- Sidebar -->
      <app-sidebar></app-sidebar>

      <!-- Main Content Area -->
      <div class="content-wrapper transition-all duration-300 ease-in-out"
           [style.margin-left.px]="contentMarginLeft">

        <!-- Header -->
        <app-header [showMobileMenuButton]="true"></app-header>

        <!-- Main content -->
        <main class="main-content">
          <div class="container-content px-3 sm:px-4 lg:px-6 py-6">
            <router-outlet></router-outlet>
          </div>
        </main>


      </div>
    </div>
  `,
  styles: [`
    .main-layout {
      display: flex;
      min-height: 100vh;
      position: relative;
    }

    .content-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      background: #f9fafb;
      position: relative;
    }

    .main-content {
      flex: 1;
      padding-top: 4.5rem; /* 72px để tránh chồng chéo với header h-16 + margin */
      min-height: calc(100vh - 4.5rem);
      position: relative;
      z-index: 10;
    }



    // Mobile styles
    @media (max-width: 768px) {
      .content-wrapper {
        margin-left: 0 !important;
      }
    }

    // Desktop styles - ensure proper spacing
    @media (min-width: 769px) {
      .content-wrapper {
        min-height: 100vh;
      }
    }

    // Smooth transitions
    .content-wrapper {
      transition: margin-left 0.3s ease-in-out;
      will-change: margin-left;
    }
  `]
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  sidebarState: SidebarState = {
    isCollapsed: false,
    isMobileOpen: false,
    isMobile: false,
    expandedMenuIds: []
  };

  sidebarConfig: SidebarConfig;

  constructor(private sidebarService: SidebarService) {
    this.sidebarConfig = this.sidebarService.getConfig();
  }

  ngOnInit(): void {
    // Subscribe to sidebar state changes
    this.sidebarService.state$
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        this.sidebarState = state;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Calculate content margin left based on sidebar state
   */
  get contentMarginLeft(): number {
    if (this.sidebarState.isMobile) {
      return 0;
    }

    return this.sidebarState.isCollapsed
      ? this.sidebarConfig.width.collapsed
      : this.sidebarConfig.width.expanded;
  }
}
