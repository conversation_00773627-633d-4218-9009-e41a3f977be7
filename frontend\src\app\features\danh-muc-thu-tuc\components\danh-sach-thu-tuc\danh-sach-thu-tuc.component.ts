import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

import { DanhMucThuTucService } from '../../services/danh-muc-thu-tuc.service';
import { 
  DanhMucThuTuc, 
  DanhMucThuTucFilter,
  LinhVucThuTuc,
  TrangThaiDanhMucThuTuc,
  LINH_VUC_MAPPING,
  TRANG_THAI_MAPPING
} from '../../models/danh-muc-thu-tuc.model';

/**
 * Component hiển thị danh sách thủ tục
 */
@Component({
  selector: 'app-danh-sach-thu-tuc',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './danh-sach-thu-tuc.component.html',
  styleUrls: ['./danh-sach-thu-tuc.component.scss']
})
export class DanhSachThuTucComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Form tìm kiếm và lọc
  formTimKiem: FormGroup;

  // Data
  danhSachThuTuc: DanhMucThuTuc[] = [];
  tongSoBanGhi = 0;
  dangTai = false;

  // Pagination
  trangHienTai = 1;
  kichThuocTrang = 10;
  tongSoTrang = 0;

  // Expose Math to template
  Math = Math;
  
  // Filter options
  danhSachLinhVuc = Object.entries(LINH_VUC_MAPPING).map(([key, value]) => ({
    id: parseInt(key),
    ten: value
  }));
  
  danhSachTrangThai = Object.entries(TRANG_THAI_MAPPING).map(([key, value]) => ({
    id: parseInt(key),
    ten: value
  }));

  constructor(
    private danhMucThuTucService: DanhMucThuTucService,
    private fb: FormBuilder
  ) {
    this.formTimKiem = this.fb.group({
      tuKhoa: [''],
      linhVuc: [null],
      trangThai: [null]
    });
  }

  ngOnInit(): void {
    this.khoiTaoForm();
    this.khoiTaoSubscriptions();
    // Tải dữ liệu khi component khởi tạo
    this.taiDuLieu();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Khởi tạo form tìm kiếm
   */
  private khoiTaoForm(): void {
    // Lắng nghe thay đổi từ khóa tìm kiếm
    this.formTimKiem.get('tuKhoa')?.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.trangHienTai = 1;
        this.taiDuLieu();
      });

    // Lắng nghe thay đổi lĩnh vực
    this.formTimKiem.get('linhVuc')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.trangHienTai = 1;
        this.taiDuLieu();
      });

    // Lắng nghe thay đổi trạng thái
    this.formTimKiem.get('trangThai')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.trangHienTai = 1;
        this.taiDuLieu();
      });
  }

  /**
   * Khởi tạo subscriptions
   */
  private khoiTaoSubscriptions(): void {
    // Subscribe to service state
    this.danhMucThuTucService.danhSachThuTuc$
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        this.danhSachThuTuc = data;
      });

    this.danhMucThuTucService.tongSoBanGhi$
      .pipe(takeUntil(this.destroy$))
      .subscribe(total => {
        this.tongSoBanGhi = total;
        this.tongSoTrang = Math.ceil(total / this.kichThuocTrang);
      });

    this.danhMucThuTucService.dangTai$
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => {
        this.dangTai = loading;
      });
  }

  /**
   * Tải dữ liệu từ service
   */
  taiDuLieu(): void {
    const filter: DanhMucThuTucFilter = {
      tuKhoa: this.formTimKiem.get('tuKhoa')?.value || undefined,
      linhVuc: this.formTimKiem.get('linhVuc')?.value || undefined,
      trangThai: this.formTimKiem.get('trangThai')?.value || undefined,
      trang: this.trangHienTai,
      kichThuocTrang: this.kichThuocTrang
    };

    // Gọi API thật
    this.danhMucThuTucService.layDanhSachThuTuc(filter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Dữ liệu đã tải thành công:', response);
        },
        error: (error) => {
          console.error('Lỗi khi tải dữ liệu:', error);
          // TODO: Hiển thị thông báo lỗi thân thiện hơn thay vì alert
          // alert('Không thể tải dữ liệu. Vui lòng thử lại sau.');
        }
      });
  }

  /**
   * Chuyển trang
   */
  chuyenTrang(trang: number): void {
    if (trang >= 1 && trang <= this.tongSoTrang && trang !== this.trangHienTai) {
      this.trangHienTai = trang;
      this.taiDuLieu();
    }
  }

  /**
   * Thay đổi kích thước trang
   */
  thayDoiKichThuocTrang(kichThuoc: number): void {
    this.kichThuocTrang = kichThuoc;
    this.trangHienTai = 1;
    this.taiDuLieu();
  }

  /**
   * Xử lý event thay đổi kích thước trang
   */
  onKichThuocTrangChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.thayDoiKichThuocTrang(+target.value);
  }

  /**
   * Reset form tìm kiếm
   */
  resetTimKiem(): void {
    this.formTimKiem.reset();
    this.trangHienTai = 1;
    this.taiDuLieu();
  }

  /**
   * Lấy tên lĩnh vực theo ID
   */
  layTenLinhVuc(linhVucId: number): string {
    return LINH_VUC_MAPPING[linhVucId] || 'Không xác định';
  }

  /**
   * Lấy tên trạng thái theo ID
   */
  layTenTrangThai(trangThaiId: number): string {
    return TRANG_THAI_MAPPING[trangThaiId] || 'Không xác định';
  }

  /**
   * Lấy class CSS cho trạng thái
   */
  layClassTrangThai(trangThai: number): string {
    switch (trangThai) {
      case TrangThaiDanhMucThuTuc.HoatDong:
        return 'bg-green-100 text-green-800';
      case TrangThaiDanhMucThuTuc.TamNgung:
        return 'bg-yellow-100 text-yellow-800';
      case TrangThaiDanhMucThuTuc.NgungHoatDong:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  /**
   * Xem chi tiết thủ tục
   */
  xemChiTiet(thuTuc: DanhMucThuTuc): void {
    console.log('Xem chi tiết thủ tục:', thuTuc);
    // TODO: Navigate to detail page
  }

  /**
   * Chỉnh sửa thủ tục
   */
  chinhSua(thuTuc: DanhMucThuTuc): void {
    console.log('Chỉnh sửa thủ tục:', thuTuc);
    // TODO: Navigate to edit page
  }

  /**
   * Xóa thủ tục
   */
  xoa(thuTuc: DanhMucThuTuc): void {
    if (confirm(`Bạn có chắc chắn muốn xóa thủ tục "${thuTuc.ten}"?`)) {
      this.danhMucThuTucService.xoaThuTuc(thuTuc.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (success) => {
            if (success) {
              console.log('Xóa thủ tục thành công');
              this.taiDuLieu();
            }
          },
          error: (error) => {
            console.error('Lỗi khi xóa thủ tục:', error);
          }
        });
    }
  }

  /**
   * Tạo mảng số trang để hiển thị pagination
   */
  layDanhSachTrang(): number[] {
    const pages: number[] = [];
    const maxPages = 5; // Hiển thị tối đa 5 trang
    
    let startPage = Math.max(1, this.trangHienTai - Math.floor(maxPages / 2));
    let endPage = Math.min(this.tongSoTrang, startPage + maxPages - 1);
    
    if (endPage - startPage + 1 < maxPages) {
      startPage = Math.max(1, endPage - maxPages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }
}
