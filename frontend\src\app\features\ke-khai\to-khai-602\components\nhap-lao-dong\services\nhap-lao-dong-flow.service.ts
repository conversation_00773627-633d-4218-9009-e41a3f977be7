import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Observable } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ToKhai602Service } from '../../../services';
import { TaoDraftToKhaiRequest } from '../../../models';
import { LaoDongKeKhaiDto } from '../../../../../../shared/models/ke-khai.model';
import { NhapLaoDongApiService } from './nhap-lao-dong-api.service';
import { NhapLaoDongStateService } from './nhap-lao-dong-state.service';
import { NhapLaoDongNavigationService } from './nhap-lao-dong-navigation.service';
import { EditLaoDongService } from './edit-lao-dong.service';

/**
 * Service xử lý flow logic cho component nhập lao động
 * Tách từ NhapLaoDongComponent để tuân thủ quy tắc 400 dòng
 */
@Injectable({
  providedIn: 'root'
})
export class NhapLaoDongFlowService {

  constructor(
    private toKhai602Service: ToKhai602Service,
    private apiService: NhapLaoDongApiService,
    private stateService: NhapLaoDongStateService,
    private navigationService: NhapLaoDongNavigationService,
    private editLaoDongService: EditLaoDongService
  ) {}

  /**
   * Tạo draft mới
   */
  taoDraftMoi(): void {
    const formState = this.stateService.formState!;
    const request: TaoDraftToKhaiRequest = {
      daiLyId: formState.daiLyDaChon!.id,
      donViId: formState.donViDaChon!.id,
      soSoBHXH: formState.thongTinKhac.soSoBHXH!,
      ghiChu: formState.thongTinKhac.ghiChu
    };

    this.toKhai602Service.taoDraftToKhai(request).subscribe({
      next: (response: any) => {
        console.log('✅ Tạo draft thành công:', response);

        // Cập nhật draft ID vào form state
        this.toKhai602Service.capNhatDraftId(response.draftId);

        // Hiển thị thông báo thành công
        alert(`Lưu nháp thành công! Mã tờ khai: ${response.maToKhai}`);

        // Cập nhật URL với draft ID
        this.navigationService.updateUrlWithDraftId(response.draftId);
      },
      error: (error: any) => {
        console.error('❌ Lỗi khi tạo draft:', error);
        alert('Có lỗi xảy ra khi lưu nháp: ' + (error.message || 'Lỗi không xác định'));
      }
    });
  }

  /**
   * Cập nhật draft hiện có
   */
  capNhatDraft(): void {
    // TODO: Implement update draft functionality
    // Hiện tại backend chưa có API cập nhật draft, chỉ hiển thị thông báo
    console.log('Cập nhật draft với ID:', this.stateService.formState?.draftId);
    alert('Đã lưu thay đổi vào nháp!');
  }

  /**
   * Load thông tin kê khai từ bảng danh_sach_ke_khai
   */
  async loadKeKhaiInfo(keKhaiId: number): Promise<void> {
    this.stateService.setDangTai(true);
    const keKhaiInfo = await this.apiService.loadKeKhaiInfo(keKhaiId);
    this.stateService.setKeKhaiInfo(keKhaiInfo);
    this.stateService.setDangTai(false);
  }

  /**
   * Load danh sách lao động từ bảng chi_tiet_lao_dong_ke_khai_v2
   */
  async loadDanhSachLaoDong(keKhaiId: number): Promise<void> {
    const danhSach = await this.apiService.loadDanhSachLaoDong(keKhaiId);
    this.stateService.setDanhSachLaoDong(danhSach);
  }

  /**
   * Thêm lao động mới - sử dụng API mới
   */
  async themLaoDongMoi(keKhaiId: number, laoDongForm: FormGroup, taoFormChoFlowMoi: () => void): Promise<void> {
    if (!laoDongForm.valid) {
      laoDongForm.markAllAsTouched();
      return;
    }

    this.stateService.setDangLuu(true);

    const success = await this.apiService.themLaoDongMoi(keKhaiId, laoDongForm.value);

    if (success) {
      laoDongForm.reset();
      taoFormChoFlowMoi(); // Reset với giá trị mặc định cho flow mới
      await this.loadDanhSachLaoDong(keKhaiId); // Reload danh sách
    }

    this.stateService.setDangLuu(false);
  }

  /**
   * Xóa lao động
   */
  async xoaLaoDong(keKhaiId: number, laoDongId: number): Promise<void> {
    if (!this.apiService.xacNhanXoaLaoDong()) {
      return;
    }

    const success = await this.apiService.xoaLaoDong(keKhaiId, laoDongId);
    if (success) {
      await this.loadDanhSachLaoDong(keKhaiId);
    }
  }

  /**
   * Gửi kê khai - sử dụng API mới
   */
  async guiKeKhaiMoi(keKhaiId: number): Promise<void> {
    if (this.apiService.kiemTraDanhSachRong(this.stateService.danhSachLaoDong)) {
      return;
    }

    const success = await this.apiService.guiKeKhai(keKhaiId);
    if (success) {
      this.navigationService.navigateToKeKhaiList();
    }
  }

  /**
   * Load thông tin lao động để chỉnh sửa
   */
  loadLaoDongForEdit(keKhaiId: number, destroy$: Observable<void>, createFormFn: (laoDong: LaoDongKeKhaiDto) => void): void {
    const editLaoDongId = this.stateService.editLaoDongId;
    if (!editLaoDongId || !keKhaiId) {
      return;
    }

    try {
      this.stateService.setDangTai(true);

      // Lấy thông tin lao động từ service
      this.editLaoDongService.layThongTinLaoDong(keKhaiId, editLaoDongId)
        .pipe(takeUntil(destroy$))
        .subscribe({
          next: (laoDong: LaoDongKeKhaiDto) => {
            this.stateService.setLaoDongDangChinhSua(laoDong);
            // Tạo form với dữ liệu lao động
            createFormFn(laoDong);
            this.stateService.setDangTai(false);
          },
          error: (error) => {
            console.error('Lỗi khi load thông tin lao động:', error);
            this.stateService.setDangTai(false);
            // Quay về danh sách nếu không load được
            this.navigationService.navigateToDanhSachLaoDong();
          }
        });
    } catch (error) {
      console.error('Lỗi khi load lao động để chỉnh sửa:', error);
      this.stateService.setDangTai(false);
    }
  }

  /**
   * Lưu thay đổi khi ở edit mode
   */
  luuThayDoi(keKhaiId: number, laoDongForm: FormGroup, destroy$: Observable<void>): void {
    if (!this.stateService.isEditMode || !keKhaiId || !this.stateService.editLaoDongId) {
      return;
    }

    if (!this.editLaoDongService.validateForm(laoDongForm)) {
      return;
    }

    this.editLaoDongService.capNhatLaoDong(keKhaiId, laoDongForm.value)
      .pipe(takeUntil(destroy$))
      .subscribe({
        next: (success) => {
          if (success) {
            // Quay về danh sách sau khi lưu thành công
            this.navigationService.navigateToDanhSachLaoDong();
          }
        },
        error: (error) => {
          console.error('Lỗi khi cập nhật lao động:', error);
        }
      });
  }

  /**
   * Hủy chỉnh sửa
   */
  huyChinhSua(): void {
    this.navigationService.navigateToDanhSachLaoDong();
  }

  /**
   * Kiểm tra draft ID từ URL params
   */
  kiemTraDraftId(destroy$: Observable<void>): void {
    this.navigationService.getDraftIdFromQueryParams()
      .pipe(takeUntil(destroy$))
      .subscribe(draftId => {
        if (draftId && !isNaN(Number(draftId))) {
          this.toKhai602Service.capNhatDraftId(Number(draftId));
          console.log('📝 Đã cập nhật draft ID từ URL:', draftId);
        }
      });
  }
}
