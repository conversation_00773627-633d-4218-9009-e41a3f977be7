namespace HuyPhuc.Application.DTOs.DiaChi;

/// <summary>
/// DTO cho thông tin loại
/// </summary>
public class LoaiDto
{
    /// <summary>
    /// ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Tên loại
    /// </summary>
    public string TenLoai { get; set; } = string.Empty;
}

/// <summary>
/// Response từ API khi lấy danh sách loại
/// </summary>
public class LoaiResponse
{
    /// <summary>
    /// Tổng số bản ghi
    /// </summary>
    public double TotalCount { get; set; }

    /// <summary>
    /// Danh sách items
    /// </summary>
    public List<LoaiDto> Items { get; set; } = new List<LoaiDto>();
}

/// <summary>
/// Response wrapper cho API
/// </summary>
public class LoaiApiResponse
{
    /// <summary>
    /// Dữ liệu
    /// </summary>
    public LoaiResponse? Data { get; set; }

    /// <summary>
    /// Trạng thái thành công
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Thông báo
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Lỗi
    /// </summary>
    public object? Errors { get; set; }

    /// <summary>
    /// Status code
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// Trace ID
    /// </summary>
    public string? TraceId { get; set; }
}
