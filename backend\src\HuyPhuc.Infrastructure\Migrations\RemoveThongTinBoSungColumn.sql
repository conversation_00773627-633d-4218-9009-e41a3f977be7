-- Migration: Remove thong_tin_bo_sung column from chi_tiet_lao_dong_ke_khai_v2
-- Date: 2025-07-29
-- Reason: Simplify data structure and remove redundant JSONB storage

-- 1. Backup existing data (already done via export)
-- Data was exported to JSON format before deletion

-- 2. Drop the column
ALTER TABLE chi_tiet_lao_dong_ke_khai_v2 
DROP COLUMN IF EXISTS thong_tin_bo_sung;

-- 3. Drop related index (automatically dropped with column)
-- DROP INDEX IF EXISTS idx_lao_dong_bo_sung_gin;

-- 4. Verify column is removed
-- SELECT column_name FROM information_schema.columns 
-- WHERE table_name = 'chi_tiet_lao_dong_ke_khai_v2' 
--   AND column_name = 'thong_tin_bo_sung';

-- Note: 
-- - Backup data is available in exported JSON format
-- - All code references have been updated
-- - This change improves data consistency by relying on tk1_ts table for personal information
