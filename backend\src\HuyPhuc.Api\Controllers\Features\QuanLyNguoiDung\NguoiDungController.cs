using HuyPhuc.Api.Authorization;
using HuyPhuc.Api.Controllers.Base;
using HuyPhuc.Application.Common.Models;
using HuyPhuc.Application.Features.QuanLyNguoiDung.Commands.TaoNguoiDung;
using HuyPhuc.Application.Features.QuanLyNguoiDung.Queries.LayChiTietNguoiDung;
using HuyPhuc.Application.Features.QuanLyNguoiDung.Queries.LayDanhSachNguoiDung;
using HuyPhuc.Application.Features.NguoiDung.Commands;
using HuyPhuc.Application.Features.NguoiDung.Queries;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers.Features.QuanLyNguoiDung;

[Route("api/nguoi-dung")]
[Authorize] // Yêu cầu JWT authentication cho tất cả endpoints
public class NguoiDungController : BaseController
{
    /// <summary>
    /// L<PERSON>y danh sách người dùng có phân trang
    /// </summary>
    /// <param name="query"><PERSON>ham số tìm kiếm và phân trang</param>
    /// <returns>Danh sách người dùng</returns>
    [HttpGet]
    [Permission("NGUOI_DUNG_XEM")]
    [ProducesResponseType(typeof(PaginatedList<NguoiDungDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PaginatedList<NguoiDungDto>>> LayDanhSach([FromQuery] LayDanhSachNguoiDungQuery query)
    {
        var result = await Mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Lấy chi tiết người dùng theo ID
    /// </summary>
    /// <param name="id">ID người dùng</param>
    /// <returns>Chi tiết người dùng</returns>
    [HttpGet("{id}")]
    [Permission("NGUOI_DUNG_XEM")]
    [ProducesResponseType(typeof(ChiTietNguoiDungDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ChiTietNguoiDungDto>> LayChiTiet(int id)
    {
        var result = await Mediator.Send(new LayChiTietNguoiDungQuery(id));
        
        if (result == null)
            return NotFound($"Không tìm thấy người dùng với ID: {id}");

        return Ok(result);
    }

    /// <summary>
    /// Tạo người dùng mới
    /// </summary>
    /// <param name="command">Thông tin người dùng mới</param>
    /// <returns>ID của người dùng được tạo</returns>
    [HttpPost]
    [Permission("NGUOI_DUNG_TAO")]
    [ProducesResponseType(typeof(Result<int>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(Result<int>), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Result<int>>> Tao([FromBody] TaoNguoiDungCommand command)
    {
        var result = await Mediator.Send(command);
        
        if (result.IsSuccess)
            return CreatedAtAction(nameof(LayChiTiet), new { id = result.Data }, result);

        return BadRequest(result);
    }

    /// <summary>
    /// Cập nhật thông tin người dùng
    /// </summary>
    /// <param name="id">ID người dùng</param>
    /// <param name="command">Thông tin cập nhật</param>
    /// <returns>Kết quả cập nhật</returns>
    [HttpPut("{id}")]
    [Permission("NGUOI_DUNG_SUA")]
    [ProducesResponseType(typeof(Result), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(Result), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Result>> CapNhat(int id, [FromBody] object command)
    {
        // TODO: Implement CapNhatNguoiDungCommand
        return Ok(Result.Success());
    }

    /// <summary>
    /// Xóa người dùng
    /// </summary>
    /// <param name="id">ID người dùng</param>
    /// <returns>Kết quả xóa</returns>
    [HttpDelete("{id}")]
    [Permission("NGUOI_DUNG_XOA")]
    [ProducesResponseType(typeof(Result), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Result>> Xoa(int id)
    {
        // TODO: Implement XoaNguoiDungCommand
        return Ok(Result.Success());
    }

    /// <summary>
    /// Gán vai trò cho người dùng
    /// </summary>
    /// <param name="id">ID người dùng</param>
    /// <param name="danhSachVaiTroId">Danh sách ID vai trò</param>
    /// <returns>Kết quả gán vai trò</returns>
    [HttpPost("{id}/vai-tro")]
    [Permission("NGUOI_DUNG_PHAN_QUYEN")]
    [ProducesResponseType(typeof(Result), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Result>> GanVaiTro(int id, [FromBody] List<int> danhSachVaiTroId)
    {
        var command = new GanVaiTroChoNguoiDungCommand
        {
            NguoiDungId = id,
            DanhSachVaiTroId = danhSachVaiTroId
        };

        var result = await Mediator.Send(command);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                success = true,
                message = "Gán vai trò cho người dùng thành công"
            });
        }

        return BadRequest(new
        {
            success = false,
            message = result.Errors.FirstOrDefault() ?? "Có lỗi xảy ra"
        });
    }

    /// <summary>
    /// Lấy thông tin quyền của người dùng
    /// </summary>
    /// <param name="id">ID người dùng</param>
    /// <returns>Thông tin quyền của người dùng</returns>
    [HttpGet("{id}/quyen")]
    [Permission("NGUOI_DUNG_XEM")]
    [ProducesResponseType(typeof(QuyenNguoiDungDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> LayQuyenNguoiDung(int id)
    {
        var query = new LayQuyenNguoiDungQuery(id);
        var result = await Mediator.Send(query);

        if (result.IsSuccess)
        {
            return Ok(new
            {
                success = true,
                data = result.Data,
                message = "Lấy thông tin quyền người dùng thành công"
            });
        }

        return BadRequest(new
        {
            success = false,
            message = result.Errors.FirstOrDefault() ?? "Có lỗi xảy ra"
        });
    }
}
