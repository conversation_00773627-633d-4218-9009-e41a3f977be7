namespace HuyPhuc.Application.DTOs.DiaChi;

/// <summary>
/// DTO cho thông tin xã/phường
/// </summary>
public class XaDto
{
    /// <summary>
    /// ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Mã xã
    /// </summary>
    public string MaXa { get; set; } = string.Empty;

    /// <summary>
    /// Tên xã/phường
    /// </summary>
    public string TenXa { get; set; } = string.Empty;

    /// <summary>
    /// Text hiển thị (mã - tên)
    /// </summary>
    public string TextDisplay { get; set; } = string.Empty;

    /// <summary>
    /// Mã huyện
    /// </summary>
    public string MaHuyen { get; set; } = string.Empty;

    /// <summary>
    /// Mã tỉnh
    /// </summary>
    public string MaTinh { get; set; } = string.Empty;

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Ng<PERSON>y cập nhật
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// DTO cho option dropdown xã
/// </summary>
public class XaOptionDto
{
    /// <summary>
    /// Giá trị (mã xã)
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Text hiển thị
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Tên xã
    /// </summary>
    public string Ten { get; set; } = string.Empty;

    /// <summary>
    /// Mã (null cho tương thích với frontend)
    /// </summary>
    public string? Ma { get; set; } = null;
}
