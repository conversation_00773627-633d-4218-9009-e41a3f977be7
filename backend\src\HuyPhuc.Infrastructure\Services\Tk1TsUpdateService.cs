using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using HuyPhuc.Application.Features.BHXH.DTOs;
using HuyPhuc.Application.Features.BHXH.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Infrastructure.Data;

namespace HuyPhuc.Infrastructure.Services;

/// <summary>
/// Service cập nhật thông tin vào bảng tk1_ts
/// </summary>
public class Tk1TsUpdateService : ITk1TsUpdateService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<Tk1TsUpdateService> _logger;

    public Tk1TsUpdateService(
        ApplicationDbContext context,
        ILogger<Tk1TsUpdateService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<bool> CapNhatThongTinBhxhAsync(BhxhDataDto bhxhData, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("<PERSON>ắt đầu cập nhật thông tin BHXH vào tk1_ts cho mã số: {MaSoBHXH}", bhxhData.MaSoBHXH);

            // Tìm bản ghi hiện tại
            var existingRecord = await _context.Tk1Ts
                .FirstOrDefaultAsync(x => x.MaSoBHXH == bhxhData.MaSoBHXH, cancellationToken);

            if (existingRecord != null)
            {
                // Cập nhật bản ghi hiện tại
                existingRecord.CapNhatThongTin(
                    hoTen: bhxhData.HoTen,
                    ngaySinh: bhxhData.NgaySinh,
                    gioiTinh: bhxhData.GioiTinh,
                    cmnd: bhxhData.CMND,
                    ccns: bhxhData.CCNS,
                    dienThoaiLh: bhxhData.DienThoaiLh,
                    maTinhKs: bhxhData.MaTinhKs,
                    maHuyenKs: bhxhData.MaHuyenKs,
                    maXaKs: bhxhData.MaXaKs,
                    maHoGiaDinh: bhxhData.MaHoGiaDinh
                );

                // Cập nhật các thuộc tính khác
                if (!string.IsNullOrWhiteSpace(bhxhData.QuocTich))
                    existingRecord.QuocTich = bhxhData.QuocTich;

                if (!string.IsNullOrWhiteSpace(bhxhData.DanToc))
                    existingRecord.DanToc = bhxhData.DanToc;

                if (!string.IsNullOrWhiteSpace(bhxhData.TypeId))
                    existingRecord.TypeId = bhxhData.TypeId;

                existingRecord.IsThamGiaBb = bhxhData.IsThamGiaBb;
                existingRecord.IsTamHoanHd = bhxhData.IsTamHoanHD;

                _logger.LogInformation("Đã cập nhật thông tin tk1_ts cho mã số: {MaSoBHXH}", bhxhData.MaSoBHXH);
            }
            else
            {
                // Tạo mới bản ghi
                var newRecord = Tk1Ts.Tao(
                    maSoBHXH: bhxhData.MaSoBHXH,
                    hoTen: bhxhData.HoTen ?? "[Cần cập nhật họ tên]",
                    ngaySinh: bhxhData.NgaySinh ?? "01/01/1990",
                    gioiTinh: bhxhData.GioiTinh == 0 ? 1 : bhxhData.GioiTinh,
                    cmnd: bhxhData.CMND ?? "000000000000",
                    ccns: bhxhData.CCNS,
                    dienThoaiLh: bhxhData.DienThoaiLh,
                    maTinhKs: bhxhData.MaTinhKs,
                    maHuyenKs: bhxhData.MaHuyenKs,
                    maXaKs: bhxhData.MaXaKs,
                    maHoGiaDinh: bhxhData.MaHoGiaDinh
                );

                // Set các thuộc tính khác
                newRecord.QuocTich = bhxhData.QuocTich ?? "VN";
                newRecord.DanToc = bhxhData.DanToc ?? "01";
                newRecord.TypeId = bhxhData.TypeId ?? "TM";
                newRecord.IsThamGiaBb = bhxhData.IsThamGiaBb;
                newRecord.IsTamHoanHd = bhxhData.IsTamHoanHD;

                _context.Tk1Ts.Add(newRecord);
                _logger.LogInformation("Đã tạo mới tk1_ts cho mã số: {MaSoBHXH}", bhxhData.MaSoBHXH);
            }

            // Lưu thay đổi
            await _context.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Đã lưu thành công thông tin tk1_ts cho mã số: {MaSoBHXH}", bhxhData.MaSoBHXH);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi cập nhật thông tin tk1_ts cho mã số: {MaSoBHXH}", bhxhData.MaSoBHXH);
            return false;
        }
    }

    public async Task<bool> KiemTraTonTaiAsync(string maSoBHXH, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Tk1Ts
                .AnyAsync(x => x.MaSoBHXH == maSoBHXH, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi kiểm tra tồn tại tk1_ts cho mã số: {MaSoBHXH}", maSoBHXH);
            return false;
        }
    }
}
