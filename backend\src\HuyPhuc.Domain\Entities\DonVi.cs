using HuyPhuc.Domain.Entities.Base;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity đại diện cho đơn vị thuộc đại lý
/// </summary>
public class DonVi : BaseEntity, IAuditableEntity
{
    /// <summary>
    /// Mã đơn vị
    /// </summary>
    public string MaDonVi { get; private set; } = string.Empty;

    /// <summary>
    /// Tên đơn vị
    /// </summary>
    public string TenDonVi { get; private set; } = string.Empty;

    /// <summary>
    /// Địa chỉ đơn vị
    /// </summary>
    public string? Dia<PERSON>hi { get; private set; }

    /// <summary>
    /// Số điện thoại
    /// </summary>
    public string? SoDienThoai { get; private set; }

    /// <summary>
    /// Email
    /// </summary>
    public string? Email { get; private set; }

    /// <summary>
    /// ID đại lý
    /// </summary>
    public int DaiLyId { get; private set; }

    /// <summary>
    /// Trạng thái hoạt động
    /// </summary>
    public bool TrangThaiHoatDong { get; private set; } = true;

    /// <summary>
    /// Ghi chú
    /// </summary>
    public string? GhiChu { get; private set; }

    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    // Navigation properties
    /// <summary>
    /// Đại lý mà đơn vị này thuộc về
    /// </summary>
    public virtual DaiLy DaiLy { get; private set; } = null!;

    private DonVi() { } // EF Core constructor

    public static DonVi Tao(string maDonVi, string tenDonVi, int daiLyId, 
        string? diaChi = null, string? soDienThoai = null, string? email = null, string? ghiChu = null)
    {
        if (string.IsNullOrWhiteSpace(maDonVi))
            throw new ArgumentException("Mã đơn vị không được để trống", nameof(maDonVi));

        if (string.IsNullOrWhiteSpace(tenDonVi))
            throw new ArgumentException("Tên đơn vị không được để trống", nameof(tenDonVi));

        if (daiLyId <= 0)
            throw new ArgumentException("ID đại lý không hợp lệ", nameof(daiLyId));

        return new DonVi
        {
            MaDonVi = maDonVi.Trim(),
            TenDonVi = tenDonVi.Trim(),
            DaiLyId = daiLyId,
            DiaChi = diaChi?.Trim(),
            SoDienThoai = soDienThoai?.Trim(),
            Email = email?.Trim(),
            GhiChu = ghiChu?.Trim(),
            TrangThaiHoatDong = true
        };
    }

    public void CapNhatThongTin(string tenDonVi, string? diaChi = null, 
        string? soDienThoai = null, string? email = null, string? ghiChu = null)
    {
        if (string.IsNullOrWhiteSpace(tenDonVi))
            throw new ArgumentException("Tên đơn vị không được để trống", nameof(tenDonVi));

        TenDonVi = tenDonVi.Trim();
        DiaChi = diaChi?.Trim();
        SoDienThoai = soDienThoai?.Trim();
        Email = email?.Trim();
        GhiChu = ghiChu?.Trim();
    }

    public void KichHoat()
    {
        TrangThaiHoatDong = true;
    }

    public void VoHieuHoa()
    {
        TrangThaiHoatDong = false;
    }

    public void ChuyenDaiLy(int daiLyIdMoi)
    {
        if (daiLyIdMoi <= 0)
            throw new ArgumentException("ID đại lý mới không hợp lệ", nameof(daiLyIdMoi));

        DaiLyId = daiLyIdMoi;
    }
}
