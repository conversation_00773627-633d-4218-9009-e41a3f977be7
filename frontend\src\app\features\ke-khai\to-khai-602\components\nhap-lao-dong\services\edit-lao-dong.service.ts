import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { LaoDong } from '../../../models/to-khai-602.model';
import { LaoDongKeKhaiDto, CapNhatLaoDongKeKhaiRequest } from '../../../../../../shared/models/ke-khai.model';
import { KeKhaiService } from '../../../../../../shared/services/ke-khai.service';
import { NotificationService } from '../../../../../../shared/services/notification.service';

/**
 * Service xử lý logic chỉnh sửa lao động
 * Hỗ trợ cả flow cũ (form array) và flow mới (API)
 */
@Injectable({
  providedIn: 'root'
})
export class EditLaoDongService {
  private _dangTai$ = new BehaviorSubject<boolean>(false);
  private _dangLuu$ = new BehaviorSubject<boolean>(false);

  constructor(
    private fb: FormBuilder,
    private keKhaiService: KeKhaiService,
    private notificationService: NotificationService
  ) {}

  /**
   * Observable cho trạng thái đang tải
   */
  get dangTai$(): Observable<boolean> {
    return this._dangTai$.asObservable();
  }

  /**
   * Observable cho trạng thái đang lưu
   */
  get dangLuu$(): Observable<boolean> {
    return this._dangLuu$.asObservable();
  }

  /**
   * Tạo form chỉnh sửa lao động
   */
  taoFormChinhSua(laoDong?: LaoDong | LaoDongKeKhaiDto): FormGroup {
    return this.fb.group({
      id: [laoDong?.id || null],
      maSoBHXH: [
        this.layGiaTriMaSoBHXH(laoDong) || '', 
        [Validators.required, Validators.pattern(/^\d{10}$/)]
      ],
      hoTen: [laoDong?.hoTen || '', [Validators.required, Validators.minLength(2)]],
      soCCCD: [
        this.layGiaTriSoCCCD(laoDong) || '', 
        [Validators.required, Validators.pattern(/^\d{9,12}$/)]
      ],
      ngaySinh: [this.layGiaTriNgaySinh(laoDong) || '', [Validators.required]],
      gioiTinh: [this.layGiaTriGioiTinh(laoDong) || '', [Validators.required]],
      soDienThoai: [this.layGiaTriSoDienThoai(laoDong) || ''],
      email: [this.layGiaTriEmail(laoDong) || '', [Validators.email]],
      diaChi: [this.layGiaTriDiaChi(laoDong) || ''],
      
      // Thông tin BHXH
      phuongAn: [this.layGiaTriPhuongAn(laoDong) || 'DB', [Validators.required]],
      phuongThuc: [this.layGiaTriPhuongThuc(laoDong) || '1', [Validators.required]],
      thangBatDau: [this.layGiaTriThangBatDau(laoDong) || this.layThangHienTai(), [Validators.required]],
      mucThuNhap: [
        this.layGiaTriMucThuNhap(laoDong) || 1500000, 
        [Validators.required, Validators.min(0)]
      ],
      
      // Thông tin tiền
      tienLai: [this.layGiaTriTienLai(laoDong) || 0, [Validators.min(0)]],
      tienThua: [this.layGiaTriTienThua(laoDong) || 0, [Validators.min(0)]],
      tienTuDong: [this.layGiaTriTienTuDong(laoDong) || 0, [Validators.min(0)]],
      tongTien: [this.layGiaTriTongTien(laoDong) || 0, [Validators.min(0)]],
      tienHoTro: [this.layGiaTriTienHoTro(laoDong) || 0, [Validators.min(0)]],
      
      // Thông tin khác
      tyLeDong: [this.layGiaTriTyLeDong(laoDong) || 22, [Validators.min(0), Validators.max(100)]],
      soThang: [1, [Validators.required, Validators.min(1), Validators.max(12)]]
    });
  }

  /**
   * Lấy thông tin lao động từ API (flow mới)
   */
  layThongTinLaoDong(keKhaiId: number, laoDongId: number): Observable<LaoDongKeKhaiDto> {
    this._dangTai$.next(true);
    
    return this.keKhaiService.layDanhSachLaoDong(keKhaiId).pipe(
      map((danhSach: LaoDongKeKhaiDto[]) => {
        const laoDong = danhSach.find((ld: LaoDongKeKhaiDto) => ld.id === laoDongId);
        if (!laoDong) {
          throw new Error('Không tìm thấy thông tin lao động');
        }
        return laoDong;
      }),
      catchError(error => {
        console.error('Lỗi khi lấy thông tin lao động:', error);
        this.notificationService.showError('Lỗi', 'Không thể lấy thông tin lao động');
        throw error;
      }),
      map(result => {
        this._dangTai$.next(false);
        return result;
      })
    );
  }

  /**
   * Cập nhật thông tin lao động (flow mới)
   */
  capNhatLaoDong(keKhaiId: number, formValue: any): Observable<boolean> {
    this._dangLuu$.next(true);

    const request: CapNhatLaoDongKeKhaiRequest = {
      id: formValue.id,
      keKhaiId: keKhaiId,
      maSoBHXH: formValue.maSoBHXH,
      phuongAn: formValue.phuongAn,
      phuongThuc: formValue.phuongThuc,
      thangBatDau: formValue.thangBatDau,
      mucThuNhap: formValue.mucThuNhap,
      tienLai: formValue.tienLai || 0,
      tienThua: formValue.tienThua || 0,
      tienTuDong: formValue.tienTuDong || 0,
      tongTien: formValue.tongTien || 0,
      tienHoTro: formValue.tienHoTro || 0,
      tyLe: formValue.tyLeDong || 22,
      soThang: formValue.soThang || 1
    };

    return this.keKhaiService.capNhatLaoDong(request).pipe(
      map(() => {
        this._dangLuu$.next(false);
        this.notificationService.showSuccess('Thành công', 'Cập nhật thông tin lao động thành công!');
        return true;
      }),
      catchError(error => {
        this._dangLuu$.next(false);
        console.error('Lỗi cập nhật lao động:', error);
        this.notificationService.showError('Lỗi', 'Có lỗi xảy ra khi cập nhật thông tin lao động');
        return [false];
      })
    );
  }

  /**
   * Validate form trước khi submit
   */
  validateForm(form: FormGroup): boolean {
    if (form.invalid) {
      form.markAllAsTouched();
      this.notificationService.showWarning('Cảnh báo', 'Vui lòng kiểm tra lại thông tin đã nhập');
      return false;
    }
    return true;
  }

  /**
   * Lấy thông báo lỗi cho field
   */
  getFieldErrorMessage(form: FormGroup, fieldName: string): string {
    const field = form.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${this.getFieldLabel(fieldName)} là bắt buộc`;
      if (field.errors['pattern']) return `${this.getFieldLabel(fieldName)} không đúng định dạng`;
      if (field.errors['minlength']) return `${this.getFieldLabel(fieldName)} quá ngắn`;
      if (field.errors['min']) return `${this.getFieldLabel(fieldName)} phải lớn hơn hoặc bằng ${field.errors['min'].min}`;
      if (field.errors['max']) return `${this.getFieldLabel(fieldName)} phải nhỏ hơn hoặc bằng ${field.errors['max'].max}`;
      if (field.errors['email']) return 'Email không đúng định dạng';
    }
    return '';
  }

  /**
   * Lấy label cho field
   */
  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      maSoBHXH: 'Mã số BHXH',
      hoTen: 'Họ và tên',
      soCCCD: 'Số CCCD',
      ngaySinh: 'Ngày sinh',
      gioiTinh: 'Giới tính',
      soDienThoai: 'Số điện thoại',
      email: 'Email',
      diaChi: 'Địa chỉ',
      phuongAn: 'Phương án',
      phuongThuc: 'Phương thức',
      thangBatDau: 'Tháng bắt đầu',
      mucThuNhap: 'Mức thu nhập',
      tienLai: 'Tiền lãi',
      tienThua: 'Tiền thừa',
      tienTuDong: 'Tiền tự đóng',
      tongTien: 'Tổng tiền',
      tienHoTro: 'NSNN hỗ trợ',
      tyLeDong: 'Tỷ lệ đóng',
      soThang: 'Số tháng'
    };
    return labels[fieldName] || fieldName;
  }

  // Helper methods để lấy giá trị từ các loại object khác nhau
  private layGiaTriMaSoBHXH(laoDong?: LaoDong | LaoDongKeKhaiDto): string {
    if (!laoDong) return '';
    return 'maSoBHXH' in laoDong ? (laoDong.maSoBHXH || '') : '';
  }

  private layGiaTriSoCCCD(laoDong?: LaoDong | LaoDongKeKhaiDto): string {
    if (!laoDong) return '';
    return 'cmnd' in laoDong ? (laoDong.cmnd || '') : '';
  }

  private layGiaTriNgaySinh(laoDong?: LaoDong | LaoDongKeKhaiDto): string {
    if (!laoDong) return '';
    if ('ngaySinh' in laoDong) {
      return typeof laoDong.ngaySinh === 'string' ? laoDong.ngaySinh : 
             laoDong.ngaySinh ? new Date(laoDong.ngaySinh).toLocaleDateString('vi-VN') : '';
    }
    return '';
  }

  private layGiaTriGioiTinh(laoDong?: LaoDong | LaoDongKeKhaiDto): string {
    if (!laoDong) return '';
    if ('gioiTinh' in laoDong && laoDong.gioiTinh !== undefined) {
      if (typeof laoDong.gioiTinh === 'number') {
        return laoDong.gioiTinh === 1 ? 'Nam' : 'Nữ';
      }
      return String(laoDong.gioiTinh);
    }
    return '';
  }

  private layGiaTriSoDienThoai(laoDong?: LaoDong | LaoDongKeKhaiDto): string {
    if (!laoDong) return '';
    return 'dienThoaiLh' in laoDong ? (laoDong.dienThoaiLh || '') : '';
  }

  private layGiaTriPhuongAn(laoDong?: LaoDong | LaoDongKeKhaiDto): string {
    if (!laoDong) return '';
    return 'phuongAn' in laoDong ? (laoDong.phuongAn || '') : '';
  }

  private layGiaTriPhuongThuc(laoDong?: LaoDong | LaoDongKeKhaiDto): string {
    if (!laoDong) return '';
    return 'phuongThuc' in laoDong ? (laoDong.phuongThuc || '') : '';
  }

  private layGiaTriThangBatDau(laoDong?: LaoDong | LaoDongKeKhaiDto): string {
    if (!laoDong) return '';
    return 'thangBatDau' in laoDong ? (laoDong.thangBatDau || '') : '';
  }

  private layGiaTriMucThuNhap(laoDong?: LaoDong | LaoDongKeKhaiDto): number {
    if (!laoDong) return 0;
    return 'mucThuNhap' in laoDong ? (laoDong.mucThuNhap || 0) : 0;
  }

  private layGiaTriTienLai(laoDong?: LaoDong | LaoDongKeKhaiDto): number {
    if (!laoDong) return 0;
    return 'tienLai' in laoDong ? (laoDong.tienLai || 0) : 0;
  }

  private layGiaTriTienThua(laoDong?: LaoDong | LaoDongKeKhaiDto): number {
    if (!laoDong) return 0;
    return 'tienThua' in laoDong ? (laoDong.tienThua || 0) : 0;
  }

  private layGiaTriTienTuDong(laoDong?: LaoDong | LaoDongKeKhaiDto): number {
    if (!laoDong) return 0;
    return 'tienTuDong' in laoDong ? (laoDong.tienTuDong || 0) : 0;
  }

  private layGiaTriTongTien(laoDong?: LaoDong | LaoDongKeKhaiDto): number {
    if (!laoDong) return 0;
    return 'tongTien' in laoDong ? (laoDong.tongTien || 0) : 0;
  }

  private layGiaTriTienHoTro(laoDong?: LaoDong | LaoDongKeKhaiDto): number {
    if (!laoDong) return 0;
    return 'tienHoTro' in laoDong ? (laoDong.tienHoTro || 0) : 0;
  }

  private layGiaTriTyLeDong(laoDong?: LaoDong | LaoDongKeKhaiDto): number {
    if (!laoDong) return 22;
    return 'tyLe' in laoDong ? (laoDong.tyLe || 22) : 22;
  }

  private layGiaTriEmail(laoDong?: LaoDong | LaoDongKeKhaiDto): string {
    // LaoDongKeKhaiDto từ shared models không có email field
    return '';
  }

  private layGiaTriDiaChi(laoDong?: LaoDong | LaoDongKeKhaiDto): string {
    // LaoDongKeKhaiDto từ shared models không có diaChi field
    return '';
  }

  /**
   * Lấy tháng hiện tại theo format MM/yyyy
   */
  private layThangHienTai(): string {
    const now = new Date();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const year = now.getFullYear();
    return `${month}/${year}`;
  }
}
