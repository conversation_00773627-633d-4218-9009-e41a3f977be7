<div class="thong-tin-dong-bhxh bg-white border border-gray-200 rounded-lg p-6" [formGroup]="parentForm">
  <h4 class="text-lg font-semibold text-gray-900 mb-6">Thông tin đóng BHXH</h4>

  <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
    <!-- Hàng 1: Thông tin BHXH theo thứ tự yêu cầu -->

    <!-- 1. Loại -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Loại
      </label>
      <select
        formControlName="loai"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      >
        <option value="">Chọn loại</option>
        <option *ngFor="let loai of loaiOptions" [value]="loai.value">
          {{ loai.text }}
        </option>
      </select>
    </div>

    <!-- 2. <PERSON><PERSON><PERSON>ng án * -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Phương án <span class="text-red-500">*</span>
      </label>
      <select
        formControlName="phuongAn"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      >
        <option value="">Chọn phương án</option>
        <option value="TM">TM - Tăng mới</option>
        <option value="DT">DT - Đóng tiếp</option>
        <option value="DL">DL - Dừng lại</option>
        <option value="GH">GH - Dừng đóng</option>
        <option value="DB">DB - Đóng bù</option>
      </select>
      <div *ngIf="hasError('phuongAn')" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('phuongAn') }}
      </div>
    </div>

    <!-- 3. Mức thu nhập lao động -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Mức thu nhập
      </label>
      <input
        type="text"
        [value]="getFormattedMucThuNhap()"
        (input)="onMucThuNhapInput($event)"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        placeholder="1,500,000"
      />
      <div *ngIf="hasError('mucThuNhap')" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('mucThuNhap') }}
      </div>
    </div>

    <!-- 4. Hệ số đóng * -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Hệ số đóng <span class="text-red-500">*</span>
      </label>
      <input
        type="number"
        formControlName="heSoDong"
        [disabled]="disabled"
        readonly
        class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
        placeholder="Tự động tính theo mức thu nhập"
        min="0"
        step="1"
      />
    </div>

    <!-- 5. Phương thức đóng * -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Phương thức đóng <span class="text-red-500">*</span>
      </label>
      <select
        formControlName="phuongThuc"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      >
        <option value="">Chọn phương thức đóng</option>
        <option *ngFor="let phuongThuc of phuongThucOptions" [value]="phuongThuc.value">
          {{ phuongThuc.text }}
        </option>
      </select>
      <div *ngIf="hasError('phuongThuc')" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('phuongThuc') }}
      </div>
    </div>

    <!-- 6. Số tháng * -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Số tháng <span class="text-red-500">*</span>
      </label>
      <select
        formControlName="soThang"
        [disabled]="disabled || soThangOptions.length === 0"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      >
        <option value="">Chọn số tháng</option>
        <option *ngFor="let soThang of soThangOptions" [value]="soThang.value">
          {{ soThang.text }}
        </option>
      </select>
      <div *ngIf="hasError('soThang')" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('soThang') }}
      </div>
    </div>

    <!-- Hàng 2: Thông tin thời gian và NSNN -->

    <!-- Tháng bắt đầu * -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Tháng bắt đầu <span class="text-red-500">*</span>
      </label>
      <input
        type="month"
        formControlName="thangBatDau"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      />
      <div *ngIf="hasError('thangBatDau')" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('thangBatDau') }}
      </div>
    </div>

    <!-- Số tháng NSNN hỗ trợ -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Số tháng NSNN hỗ trợ
      </label>
      <input
        type="number"
        formControlName="soThangNsnnHoTro"
        [disabled]="disabled"
        readonly
        class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
        placeholder="Tự động tính theo số tháng"
        min="0"
        step="1"
      />
    </div>

    <!-- Loại NSNN -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Loại NSNN
      </label>
      <select
        formControlName="loaiNsnn"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      >
        <option value="">Chọn loại NSNN</option>
        <option *ngFor="let loaiNsnn of loaiNsnnOptions" [value]="loaiNsnn.value">
          {{ loaiNsnn.text }}
        </option>
      </select>
      <div *ngIf="hasError('loaiNsnn')" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('loaiNsnn') }}
      </div>
    </div>

    <!-- Tỷ lệ NSNN hỗ trợ -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Tỷ lệ NSNN hỗ trợ (%)
      </label>
      <input
        type="number"
        formControlName="tyLeNsnnHoTro"
        [disabled]="disabled"
        readonly
        class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
        placeholder="Tự động cập nhật theo loại NSNN"
        min="0"
        max="100"
      />
    </div>

    <!-- Tỷ lệ NLD đóng (%) -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Tỷ lệ NLD đóng (%)
      </label>
      <input
        type="number"
        formControlName="tyLeNldDong"
        [disabled]="disabled"
        readonly
        class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
        placeholder="22"
        min="0"
        max="100"
        step="0.1"
      />
    </div>

    <!-- Hàng 3: Nhóm các field tiền -->

    <!-- Tiền lãi -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Tiền lãi
      </label>
      <input
        type="text"
        [value]="getFormattedValue('tienLai')"
        readonly
        class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
        placeholder="0"
      />
    </div>

    <!-- Tiền thừa -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Tiền thừa
      </label>
      <input
        type="text"
        [value]="getFormattedValue('tienThua')"
        readonly
        class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
        placeholder="0"
      />
    </div>

    <!-- Tiền tự đóng -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Tiền tự đóng
      </label>
      <input
        type="text"
        [value]="getFormattedValue('tienTuDong')"
        readonly
        class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
        placeholder="0"
      />
    </div>

    <!-- Tiền hỗ trợ -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Tiền hỗ trợ
      </label>
      <input
        type="text"
        [value]="getFormattedValue('tienHoTro')"
        readonly
        class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
        placeholder="0"
      />
    </div>

    <!-- Tổng tiền -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Tổng tiền
      </label>
      <input
        type="text"
        [value]="getFormattedValue('tongTien')"
        readonly
        class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
        placeholder="0"
      />
    </div>

    <!-- Hàng 4: Thông tin biên lai -->

    <!-- Ngày biên lai -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Ngày biên lai
      </label>
      <input
        type="date"
        formControlName="ngayBienLai"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      />
    </div>

    <!-- Số biên lai -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Số biên lai
      </label>
      <input
        type="text"
        formControlName="soBienLai"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        placeholder="Nhập số biên lai"
      />
    </div>





    <!-- Tham gia bảo hiểm và Tạm hoãn hợp đồng - Hidden -->
    <input type="hidden" formControlName="isThamGiaBb" value="false">
    <input type="hidden" formControlName="isTamHoanHD" value="false">

    <!-- Thông tin hệ thống - Hidden -->
    <input type="hidden" formControlName="message">

    <!-- Mã lỗi và isError - Hidden -->
    <input type="hidden" formControlName="maLoi">
    <input type="hidden" formControlName="isError" value="false">

    <!-- Mô tả lỗi - Hidden -->
    <input type="hidden" formControlName="moTaLoi">

    <!-- Ghi chú -->
    <div class="lg:col-span-6">
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Ghi chú
      </label>
      <textarea
        formControlName="ghiChu"
        rows="3"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        placeholder="Nhập ghi chú (nếu có)"
      ></textarea>
    </div>
  </div>
</div>
