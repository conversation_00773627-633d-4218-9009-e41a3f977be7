import { Injectable } from '@angular/core';
import { FormGroup, FormArray } from '@angular/forms';
import { LaoDong, ValidationError } from '../../../models';

/**
 * Service xử lý validation cho component nhập lao động
 */
@Injectable({
  providedIn: 'root'
})
export class NhapLaoDongValidationService {

  constructor() {}

  /**
   * Kiểm tra thông tin lao động từ mã số BHXH
   */
  async kiemTraThongTinLaoDong(formGroup: FormGroup): Promise<void> {
    const maSoBHXH = formGroup.get('maSoBHXH')?.value;

    if (!maSoBHXH || maSoBHXH.length < 10) {
      return;
    }

    try {
      // Mock API call - thay thế bằng API thực tế
      const mockData = {
        hoTen: 'Nguyễn Văn A',
        ngaySinh: '1990-01-01',
        gioiTinh: 'Nam',
        soCCCD: '123456789012',
        dia<PERSON>hi: '123 Đường ABC, Quận 1, TP.HCM',
        soDien<PERSON><PERSON><PERSON>: '0901234567',
        email: '<EMAIL>'
      };

      // Cập nhật form với dữ liệu từ API
      formGroup.patchValue(mockData);
      
      console.log('✅ Đã tự động điền thông tin lao động từ mã số BHXH:', maSoBHXH);
    } catch (error) {
      console.error('❌ Lỗi khi kiểm tra thông tin lao động:', error);
    }
  }

  /**
   * Lấy thông báo lỗi cho field
   */
  getErrorMessage(formGroup: FormGroup, fieldName: string): string {
    const field = formGroup.get(fieldName);

    if (field?.errors) {
      if (field.errors['required']) {
        return this.getRequiredMessage(fieldName);
      }
      if (field.errors['pattern']) {
        return this.getPatternMessage(fieldName);
      }
      if (field.errors['email']) {
        return 'Email không đúng định dạng';
      }
      if (field.errors['min']) {
        return `Giá trị phải lớn hơn hoặc bằng ${field.errors['min'].min}`;
      }
      if (field.errors['max']) {
        return `Giá trị phải nhỏ hơn hoặc bằng ${field.errors['max'].max}`;
      }
    }

    return '';
  }

  /**
   * Kiểm tra field có lỗi không
   */
  hasError(formGroup: FormGroup, fieldName: string): boolean {
    const field = formGroup.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  /**
   * Validate toàn bộ form và trả về danh sách lỗi
   */
  validateForm(form: FormGroup): ValidationError[] {
    const errors: ValidationError[] = [];
    
    if (!form.valid) {
      // Validate form array nếu có
      const formArray = form.get('danhSachLaoDong') as FormArray;
      
      if (formArray) {
        formArray.controls.forEach((control, index) => {
          const formGroup = control as FormGroup;
          
          Object.keys(formGroup.controls).forEach(fieldName => {
            const fieldControl = formGroup.get(fieldName);
            
            if (fieldControl && fieldControl.invalid && fieldControl.touched) {
              const fieldErrors = fieldControl.errors;
              
              if (fieldErrors) {
                Object.keys(fieldErrors).forEach(errorType => {
                  errors.push({
                    field: `danhSachLaoDong.${index}.${fieldName}`,
                    message: this.getErrorMessageByType(fieldName, errorType, fieldErrors[errorType])
                  });
                });
              }
            }
          });
        });
      } else {
        // Validate single form
        Object.keys(form.controls).forEach(fieldName => {
          const fieldControl = form.get(fieldName);
          
          if (fieldControl && fieldControl.invalid && fieldControl.touched) {
            const fieldErrors = fieldControl.errors;
            
            if (fieldErrors) {
              Object.keys(fieldErrors).forEach(errorType => {
                errors.push({
                  field: fieldName,
                  message: this.getErrorMessageByType(fieldName, errorType, fieldErrors[errorType])
                });
              });
            }
          }
        });
      }
    }
    
    return errors;
  }

  /**
   * Lấy thông báo lỗi required
   */
  private getRequiredMessage(fieldName: string): string {
    const fieldLabels: { [key: string]: string } = {
      maSoBHXH: 'Mã số BHXH',
      hoTen: 'Họ tên',
      ngaySinh: 'Ngày sinh',
      cmnd: 'Số CCCD', // Backend sử dụng cmnd
      soCCCD: 'Số CCCD', // Để tương thích với code cũ
      dienThoaiLh: 'Số điện thoại', // Backend sử dụng dienThoaiLh
      soDienThoai: 'Số điện thoại', // Để tương thích với code cũ
      gioiTinh: 'Giới tính',
      quocTich: 'Quốc tịch',
      danToc: 'Dân tộc',
      maTinhKs: 'Mã tỉnh KS',
      maHuyenKs: 'Mã huyện KS',
      maXaKs: 'Mã xã KS',
      maHoGiaDinh: 'Mã hộ gia đình',
      ccns: 'CCNS',
      diaChi: 'Địa chỉ',
      mucLuong: 'Mức lương',
      ngayBatDauLamViec: 'Ngày bắt đầu làm việc',
      phuongAn: 'Phương án',
      phuongThuc: 'Phương thức',
      thangBatDau: 'Tháng bắt đầu',
      mucThuNhap: 'Mức thu nhập',
      tyLe: 'Tỷ lệ',
      soThang: 'Số tháng',
      typeId: 'Type ID',
      isThamGiaBb: 'Tham gia BB',
      isTamHoanHD: 'Tạm hoãn HĐ'
    };

    const fieldLabel = fieldLabels[fieldName] || fieldName;
    return `${fieldLabel} là bắt buộc`;
  }

  /**
   * Lấy thông báo lỗi pattern
   */
  private getPatternMessage(fieldName: string): string {
    switch (fieldName) {
      case 'maSoBHXH':
        return 'Mã số BHXH phải có 10 chữ số';
      case 'cmnd':
      case 'soCCCD':
        return 'Số CCCD phải có 12 chữ số';
      case 'dienThoaiLh':
      case 'soDienThoai':
        return 'Số điện thoại phải có 10-11 chữ số';
      default:
        return 'Định dạng không hợp lệ';
    }
  }

  /**
   * Lấy thông báo lỗi theo loại
   */
  private getErrorMessageByType(fieldName: string, errorType: string, errorValue?: any): string {
    const fieldLabels: { [key: string]: string } = {
      maSoBHXH: 'Mã số BHXH',
      hoTen: 'Họ tên',
      ngaySinh: 'Ngày sinh',
      soCCCD: 'Số CCCD',
      diaChi: 'Địa chỉ',
      soDienThoai: 'Số điện thoại',
      email: 'Email',
      mucLuong: 'Mức lương',
      ngayBatDauLamViec: 'Ngày bắt đầu làm việc',
      mucThuNhap: 'Mức thu nhập',
      tyLe: 'Tỷ lệ',
      soThang: 'Số tháng'
    };

    const fieldLabel = fieldLabels[fieldName] || fieldName;

    switch (errorType) {
      case 'required':
        return `${fieldLabel} là bắt buộc`;
      case 'pattern':
        return this.getPatternMessage(fieldName);
      case 'email':
        return 'Email không đúng định dạng';
      case 'min':
        return `${fieldLabel} phải lớn hơn hoặc bằng ${errorValue.min}`;
      case 'max':
        return `${fieldLabel} phải nhỏ hơn hoặc bằng ${errorValue.max}`;
      default:
        return `${fieldLabel} không hợp lệ`;
    }
  }

  /**
   * Mark all fields as touched để hiển thị lỗi
   */
  markAllAsTouched(form: FormGroup): void {
    form.markAllAsTouched();
    
    // Nếu có form array, mark tất cả controls trong array
    const formArray = form.get('danhSachLaoDong') as FormArray;
    if (formArray) {
      formArray.controls.forEach(control => {
        if (control instanceof FormGroup) {
          control.markAllAsTouched();
        }
      });
    }
  }

  /**
   * Reset validation errors
   */
  resetValidation(form: FormGroup): void {
    form.markAsUntouched();
    form.markAsPristine();
    
    // Nếu có form array, reset tất cả controls trong array
    const formArray = form.get('danhSachLaoDong') as FormArray;
    if (formArray) {
      formArray.controls.forEach(control => {
        if (control instanceof FormGroup) {
          control.markAsUntouched();
          control.markAsPristine();
        }
      });
    }
  }
}
