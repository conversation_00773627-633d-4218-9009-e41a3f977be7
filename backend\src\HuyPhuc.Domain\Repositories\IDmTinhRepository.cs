using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories.Base;

namespace HuyPhuc.Domain.Repositories;

/// <summary>
/// Repository interface cho DmTinh
/// </summary>
public interface IDmTinhRepository : IRepository<DmTinh>
{
    /// <summary>
    /// Lấy tất cả tỉnh/thành phố
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Danh sách tỉnh/thành phố</returns>
    Task<List<DmTinh>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy tỉnh theo mã tỉnh
    /// </summary>
    /// <param name="maTinh">Mã tỉnh</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Thông tin tỉnh</returns>
    Task<DmTinh?> GetByMaTinhAsync(string maTinh, CancellationToken cancellationToken = default);

    /// <summary>
    /// Kiểm tra mã tỉnh có tồn tại không
    /// </summary>
    /// <param name="maTinh">Mã tỉnh</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True nếu tồn tại</returns>
    Task<bool> ExistsByMaTinhAsync(string maTinh, CancellationToken cancellationToken = default);
}
