using FluentValidation;
using HuyPhuc.Domain.Enums;

namespace HuyPhuc.Application.Features.DanhMucThuTuc.Commands.CreateDanhMucThuTuc;

/// <summary>
/// Validator cho CreateDanhMucThuTucCommand
/// </summary>
public class CreateDanhMucThuTucCommandValidator : AbstractValidator<CreateDanhMucThuTucCommand>
{
    public CreateDanhMucThuTucCommandValidator()
    {
        RuleFor(x => x.Ma)
            .NotEmpty()
            .WithMessage("Mã thủ tục không được để trống")
            .MaximumLength(50)
            .WithMessage("Mã thủ tục không được vượt quá 50 ký tự")
            .Matches(@"^[A-Za-z0-9]+$")
            .WithMessage("Mã thủ tục chỉ được chứa chữ cái và số");

        RuleFor(x => x.Ten)
            .NotEmpty()
            .WithMessage("Tên thủ tục không được để trống")
            .MaximumLength(1000)
            .WithMessage("Tên thủ tục không được vượt quá 1000 ký tự");

        RuleFor(x => x.LinhVuc)
            .IsInEnum()
            .WithMessage("Lĩnh vực không hợp lệ");

        RuleFor(x => x.TenLinhVuc)
            .NotEmpty()
            .WithMessage("Tên lĩnh vực không được để trống")
            .MaximumLength(255)
            .WithMessage("Tên lĩnh vực không được vượt quá 255 ký tự");

        RuleFor(x => x.NgayApDung)
            .GreaterThanOrEqualTo(DateTime.Today)
            .When(x => x.NgayApDung.HasValue)
            .WithMessage("Ngày áp dụng không được nhỏ hơn ngày hiện tại");

        RuleFor(x => x.MoTa)
            .MaximumLength(5000)
            .When(x => !string.IsNullOrEmpty(x.MoTa))
            .WithMessage("Mô tả không được vượt quá 5000 ký tự");

        RuleFor(x => x.ThoiGianXuLy)
            .GreaterThan(0)
            .When(x => x.ThoiGianXuLy.HasValue)
            .WithMessage("Thời gian xử lý phải lớn hơn 0");

        RuleFor(x => x.PhiThucHien)
            .GreaterThanOrEqualTo(0)
            .When(x => x.PhiThucHien.HasValue)
            .WithMessage("Phí thực hiện không được âm");

        RuleFor(x => x.CoQuanThucHien)
            .MaximumLength(500)
            .When(x => !string.IsNullOrEmpty(x.CoQuanThucHien))
            .WithMessage("Cơ quan thực hiện không được vượt quá 500 ký tự");

        RuleFor(x => x.CanCuPhapLy)
            .MaximumLength(2000)
            .When(x => !string.IsNullOrEmpty(x.CanCuPhapLy))
            .WithMessage("Căn cứ pháp lý không được vượt quá 2000 ký tự");
    }
}
