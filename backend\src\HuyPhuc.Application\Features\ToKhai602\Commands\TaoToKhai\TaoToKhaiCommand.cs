using HuyPhuc.Application.DTOs.ToKhai602;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using MediatR;
using Microsoft.EntityFrameworkCore;
using ToKhai602Entity = HuyPhuc.Domain.Entities.ToKhai602;

namespace HuyPhuc.Application.Features.ToKhai602.Commands.TaoToKhai;

/// <summary>
/// Command tạo tờ khai 602 mới (lưu vào cả danh_sach_ke_khai và to_khai_602)
/// </summary>
public record TaoToKhaiCommand(TaoDraftToKhaiRequest Request) : IRequest<TaoToKhaiResponse>;

public class TaoToKhaiCommandHandler : IRequestHandler<TaoToKhaiCommand, TaoToKhaiResponse>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public TaoToKhaiCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<TaoToKhaiResponse> Handle(TaoToKhaiCommand request, CancellationToken cancellationToken)
    {
        var userIdString = _currentUserService.UserId;
        if (userIdString == null || !int.TryParse(userIdString, out var userId))
        {
            throw new UnauthorizedAccessException("Không thể xác định người dùng hiện tại");
        }

        // Validate đại lý và đơn vị
        var daiLy = await _context.DaiLy
            .FirstOrDefaultAsync(x => x.Id == request.Request.DaiLyId, cancellationToken);
        if (daiLy == null)
        {
            throw new ArgumentException("Đại lý không tồn tại");
        }

        var donVi = await _context.DonVi
            .FirstOrDefaultAsync(x => x.Id == request.Request.DonViId && x.DaiLyId == request.Request.DaiLyId, cancellationToken);
        if (donVi == null)
        {
            throw new ArgumentException("Đơn vị không tồn tại hoặc không thuộc đại lý đã chọn");
        }

        // Tạo mã tờ khai tự động
        var maToKhai = await TaoMaToKhai(cancellationToken);

        // 1. Tạo record trong bảng danh_sach_ke_khai
        var keKhai = new Domain.Entities.KeKhai
        {
            MaKeKhai = maToKhai,
            ThuTucId = 1, // ID thủ tục cho tờ khai 602 (ma = "602")
            DaiLyId = request.Request.DaiLyId,
            DonViId = request.Request.DonViId,
            SoSoBHXH = request.Request.SoSoBHXH,
            TrangThai = 0, // Đang soạn
            NguoiTaoId = userId,
            NgayTao = DateTime.UtcNow,
            GhiChu = request.Request.GhiChu,
            Created = DateTime.UtcNow,
            CreatedBy = userId.ToString()
        };

        _context.DanhSachKeKhai.Add(keKhai);
        await _context.SaveChangesAsync(cancellationToken);

        // 2. Tạo record trong bảng to_khai_602
        var toKhai602 = new ToKhai602Entity
        {
            MaToKhai = maToKhai,
            DaiLyId = request.Request.DaiLyId,
            DonViId = request.Request.DonViId,
            SoSoBHXH = request.Request.SoSoBHXH,
            GhiChu = request.Request.GhiChu,
            TrangThai = TrangThaiToKhai.DangSoan,
            NgayTao = DateTime.UtcNow,
            NguoiTaoId = userId
        };

        _context.ToKhai602.Add(toKhai602);
        await _context.SaveChangesAsync(cancellationToken);

        return new TaoToKhaiResponse
        {
            Id = toKhai602.Id,
            MaToKhai = maToKhai,
            DaiLyId = request.Request.DaiLyId,
            DonViId = request.Request.DonViId,
            SoSoBHXH = request.Request.SoSoBHXH,
            GhiChu = request.Request.GhiChu,
            TrangThai = 0,
            NgayTao = DateTime.UtcNow,
            NguoiTaoId = userId,
            DaiLy = new DaiLyInfo
            {
                Id = daiLy.Id,
                MaDaiLy = daiLy.MaDaiLy,
                TenDaiLy = daiLy.TenDaiLy,
                DiaChi = daiLy.DiaChi ?? "",
                SoDienThoai = daiLy.SoDienThoai ?? ""
            },
            DonVi = new DonViInfo
            {
                Id = donVi.Id,
                MaDonVi = donVi.MaDonVi,
                TenDonVi = donVi.TenDonVi,
                DiaChi = donVi.DiaChi ?? ""
            }
        };
    }

    /// <summary>
    /// Tạo mã tờ khai tự động theo format: TK602-YYYYMMDD-XXX
    /// </summary>
    private async Task<string> TaoMaToKhai(CancellationToken cancellationToken)
    {
        var today = DateTime.Today;
        var prefix = $"TK602-{today:yyyyMMdd}";
        
        // Đếm số tờ khai đã tạo trong ngày
        var count = await _context.ToKhai602
            .Where(x => x.MaToKhai.StartsWith(prefix))
            .CountAsync(cancellationToken);
        
        return $"{prefix}-{(count + 1):D3}";
    }
}
