using HuyPhuc.Domain.Common;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity cho danh mục huyện/quận
/// </summary>
public class DmHuyen : BaseAuditableEntity
{
    /// <summary>
    /// Mã huyện (3 ký tự)
    /// </summary>
    public string <PERSON><PERSON><PERSON><PERSON> { get; set; } = string.Empty;

    /// <summary>
    /// Tên huyện/quận
    /// </summary>
    public string TenHuyen { get; set; } = string.Empty;

    /// <summary>
    /// Text hiển thị (mã - tên)
    /// </summary>
    public string TextDisplay { get; set; } = string.Empty;

    /// <summary>
    /// Mã tỉnh (foreign key)
    /// </summary>
    public string MaTinh { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property - Tỉnh mà huyện này thuộc về
    /// </summary>
    public virtual DmTinh? Tinh { get; set; }

    /// <summary>
    /// Navigation property - Danh sách xã thuộc huyện này
    /// </summary>
    public virtual ICollection<DmXa> DanhSachXa { get; set; } = new List<DmXa>();
}
