# Test Users cho Authentication

Dưới đây là danh sách các test users đã được tạo trong database để test chức năng authentication:

## 📋 Danh sách Test Users

### 1. Admin User
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Username**: `admin`
- **Họ tên**: <PERSON>u<PERSON>n trị viên
- **Số điện thoại**: 0901234567
- **Vai trò**: Admin

### 2. Nhân viên
- **Email**: `<EMAIL>`
- **Password**: `nhanvien123`
- **Username**: `nhanvien`
- **Họ tên**: Nguyễn Văn A
- **Số điện thoại**: 0912345678
- **Vai trò**: Nhân viên

### 3. Kh<PERSON><PERSON> hàng
- **Email**: `<EMAIL>`
- **Password**: `khachhang123`
- **Username**: `khachhang`
- **Họ tên**: <PERSON>r<PERSON><PERSON> Thị B
- **Số điện thoại**: 0923456789
- **Vai trò**: Khách hàng

### 4. Test User
- **Email**: `<EMAIL>`
- **Password**: `test123`
- **Username**: `testuser`
- **Họ tên**: Test User
- **Số điện thoại**: 0934567890
- **Vai trò**: User

## 🔐 Thông tin bảo mật

- Tất cả mật khẩu đã được hash bằng BCrypt với work factor 12
- Trạng thái tất cả users: `active`
- Được tạo bởi: `system`

## 🧪 Cách sử dụng để test

1. **Test đăng nhập**: Sử dụng bất kỳ email và password nào từ danh sách trên
2. **Test JWT**: Sau khi đăng nhập thành công, sử dụng access token để gọi các API protected
3. **Test refresh token**: Sử dụng refresh token để làm mới access token
4. **Test đăng xuất**: Sử dụng refresh token để đăng xuất

## 📝 Ghi chú

- Các user này chỉ dành cho môi trường development/testing
- Trong production, cần xóa hoặc thay đổi mật khẩu của các user này
- Mật khẩu được thiết kế đơn giản để dễ nhớ khi test
