using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HuyPhuc.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveThongTinBoSungColumn : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Xóa index trước khi xóa cột
            migrationBuilder.DropIndex(
                name: "idx_lao_dong_bo_sung_gin",
                table: "chi_tiet_lao_dong_ke_khai_v2");

            // Xóa cột thong_tin_bo_sung
            migrationBuilder.DropColumn(
                name: "thong_tin_bo_sung",
                table: "chi_tiet_lao_dong_ke_khai_v2");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Thêm lại cột thong_tin_bo_sung
            migrationBuilder.AddColumn<string>(
                name: "thong_tin_bo_sung",
                table: "chi_tiet_lao_dong_ke_khai_v2",
                type: "jsonb",
                nullable: true);

            // Tạo lại index
            migrationBuilder.CreateIndex(
                name: "idx_lao_dong_bo_sung_gin",
                table: "chi_tiet_lao_dong_ke_khai_v2",
                column: "thong_tin_bo_sung")
                .Annotation("Npgsql:IndexMethod", "gin");
        }
    }
}
