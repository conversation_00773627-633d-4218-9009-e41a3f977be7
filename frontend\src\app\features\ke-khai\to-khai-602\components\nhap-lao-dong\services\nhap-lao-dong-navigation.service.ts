import { Injectable, Inject } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

/**
 * Service xử lý navigation logic cho component nhập lao động
 * Tách từ NhapLaoDongComponent để tuân thủ quy tắc 400 dòng
 */
@Injectable({
  providedIn: 'root'
})
export class NhapLaoDongNavigationService {

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  /**
   * Lấy keKhaiId từ parent route params
   */
  getKeKhaiIdFromParentRoute(): Observable<number | null> {
    console.log('🔍 Navigation service - checking parent route');
    console.log('🔍 Route parent:', this.route.parent);
    console.log('🔍 Route snapshot params:', this.route.snapshot.params);
    console.log('🔍 Route parent snapshot params:', this.route.parent?.snapshot.params);

    return this.route.parent?.params.pipe(
      map(params => {
        console.log('🔍 Parent route params:', params);
        const id = params['id'];
        console.log('🔍 Extracted id:', id);
        return id ? +id : null;
      })
    ) || new Observable(observer => {
      console.log('🔍 No parent route, returning null');
      observer.next(null);
    });
  }

  /**
   * Lấy keKhaiId từ query params (fallback)
   */
  getKeKhaiIdFromQueryParams(): Observable<number | null> {
    return this.route.queryParams.pipe(
      map(params => {
        const keKhaiId = params['keKhaiId'];
        return keKhaiId ? +keKhaiId : null;
      })
    );
  }

  /**
   * Lấy keKhaiId từ route snapshot (fallback cuối)
   */
  getKeKhaiIdFromSnapshot(): number | null {
    console.log('🔍 Getting keKhaiId from snapshot');

    // Thử lấy từ parent route snapshot
    if (this.route.parent?.snapshot.params['id']) {
      const id = +this.route.parent.snapshot.params['id'];
      console.log('🔍 Found id in parent snapshot:', id);
      return id;
    }

    // Thử lấy từ current route snapshot
    if (this.route.snapshot.params['id']) {
      const id = +this.route.snapshot.params['id'];
      console.log('🔍 Found id in current snapshot:', id);
      return id;
    }

    // Thử parse từ URL
    const urlParts = window.location.pathname.split('/');
    const quantLyIndex = urlParts.findIndex(part => part === 'quan-ly');
    if (quantLyIndex !== -1 && urlParts[quantLyIndex + 1]) {
      const idFromUrl = parseInt(urlParts[quantLyIndex + 1], 10);
      if (!isNaN(idFromUrl)) {
        console.log('🔍 Found id from URL:', idFromUrl);
        return idFromUrl;
      }
    }

    console.log('🔍 No keKhaiId found');
    return null;
  }

  /**
   * Lấy draftId từ query params
   */
  getDraftIdFromQueryParams(): Observable<number | null> {
    return this.route.queryParams.pipe(
      map(params => {
        const draftId = params['draftId'];
        return draftId && !isNaN(Number(draftId)) ? Number(draftId) : null;
      })
    );
  }

  /**
   * Kiểm tra edit mode từ query params
   */
  getEditModeFromQueryParams(): Observable<{ isEdit: boolean, editId: number | null }> {
    return this.route.queryParams.pipe(
      map(params => {
        const edit = params['edit'];
        return {
          isEdit: !!edit,
          editId: edit ? +edit : null
        };
      })
    );
  }

  /**
   * Quay lại trang danh sách tờ khai
   */
  navigateToToKhaiList(): void {
    this.router.navigate(['/ke-khai/to-khai-602']);
  }

  /**
   * Quay lại trang danh sách kê khai
   */
  navigateToKeKhaiList(): void {
    this.router.navigate(['/ke-khai/danh-sach']);
  }

  /**
   * Quay lại trang danh sách lao động (relative navigation)
   */
  navigateToDanhSachLaoDong(): void {
    this.router.navigate(['../danh-sach'], { relativeTo: this.route });
  }

  /**
   * Navigate đến trang danh sách tờ khai 602
   */
  navigateToToKhai602DanhSach(): void {
    this.router.navigate(['/ke-khai/to-khai-602/danh-sach']);
  }

  /**
   * Cập nhật URL với draft ID
   */
  updateUrlWithDraftId(draftId: number): void {
    const currentUrl = this.router.url.split('?')[0];
    this.router.navigate([currentUrl], {
      queryParams: { draftId: draftId },
      replaceUrl: true
    });
  }

  /**
   * Cập nhật URL với edit mode
   */
  updateUrlWithEditMode(editId: number): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { edit: editId },
      queryParamsHandling: 'merge'
    });
  }

  /**
   * Xóa edit mode khỏi URL
   */
  removeEditModeFromUrl(): void {
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { edit: null },
      queryParamsHandling: 'merge'
    });
  }

  /**
   * Kiểm tra có thể navigate không (không đang trong quá trình xử lý)
   */
  canNavigate(isDangXuLy: boolean): boolean {
    if (isDangXuLy) {
      console.warn('Không thể navigate khi đang xử lý');
      return false;
    }
    return true;
  }

  /**
   * Navigate với confirmation nếu có thay đổi chưa lưu
   */
  navigateWithConfirmation(
    navigateFunction: () => void,
    hasUnsavedChanges: boolean = false,
    confirmMessage: string = 'Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn rời khỏi trang này?'
  ): void {
    if (hasUnsavedChanges) {
      if (confirm(confirmMessage)) {
        navigateFunction();
      }
    } else {
      navigateFunction();
    }
  }

  /**
   * Lấy current route URL
   */
  getCurrentUrl(): string {
    return this.router.url;
  }

  /**
   * Lấy current route path (không có query params)
   */
  getCurrentPath(): string {
    return this.router.url.split('?')[0];
  }

  /**
   * Kiểm tra có đang ở trang edit không
   */
  isOnEditPage(): boolean {
    return this.getCurrentUrl().includes('edit=');
  }

  /**
   * Kiểm tra có đang ở trang với draft ID không
   */
  isOnDraftPage(): boolean {
    return this.getCurrentUrl().includes('draftId=');
  }

  /**
   * Navigate đến trang cụ thể với error handling
   */
  safeNavigate(path: string[], options?: any): Promise<boolean> {
    try {
      return this.router.navigate(path, options);
    } catch (error) {
      console.error('Lỗi khi navigate:', error);
      return Promise.resolve(false);
    }
  }

  /**
   * Navigate back trong browser history
   */
  navigateBack(): void {
    window.history.back();
  }

  /**
   * Reload trang hiện tại
   */
  reloadCurrentPage(): void {
    window.location.reload();
  }
}
