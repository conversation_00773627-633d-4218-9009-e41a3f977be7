/**
 * Model cho thông tin dân tộc
 */
export interface DanToc {
  id: number;
  ma: string;
  ten: string;
  maVaTen: string;
  canCu?: string | null;
  rownum?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Response từ API khi lấy danh sách dân tộc
 */
export interface DanTocResponse {
  data: DanToc[];
  success: boolean;
  message?: string;
  errors?: any;
  status: number;
  traceId?: string;
}

/**
 * Option cho dropdown dân tộc
 */
export interface DanTocOption {
  value: string;
  text: string;
  ten: string;
  ma: string;
}
