using HuyPhuc.Application.Features.BHXH.DTOs;

namespace HuyPhuc.Application.Features.BHXH.Interfaces;

/// <summary>
/// Interface cho VNPost Authentication Service
/// </summary>
public interface IVnPostAuthService
{
    /// <summary>
    /// L<PERSON>y captcha từ VNPost
    /// </summary>
    Task<VnPostCaptchaResponseDto> GetCaptchaAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Đăng nhập VNPost và lấy access token
    /// </summary>
    Task<VnPostLoginResponseDto> LoginAsync(VnPostLoginRequestDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Refresh access token hiện tại
    /// </summary>
    Task<bool> RefreshTokenAsync(RefreshTokenRequestDto request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy access token hiện tại
    /// </summary>
    string GetCurrentAccessToken();

    /// <summary>
    /// Ki<PERSON>m tra access token có hết hạn không
    /// </summary>
    bool IsTokenExpired();

    /// <summary>
    /// Force expire token for testing purposes
    /// </summary>
    void ForceTokenExpired();
}
