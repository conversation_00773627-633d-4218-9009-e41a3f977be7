import { Component, signal, OnInit, isDevMode } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { AsyncPipe } from '@angular/common';
import { ToastComponent } from './shared/components/toast/toast.component';
import { ModalXacNhanComponent } from './shared/components/modal/modal-xac-nhan/modal-xac-nhan.component';
import { ConfirmationDialogService } from './shared/services/confirmation-dialog.service';
import { VnPostLoginModalComponent } from './features/ke-khai-v2/components/vnpost-login-modal/vnpost-login-modal.component';
import { BhxhLookupService } from './features/ke-khai-v2/core/services/bhxh-lookup.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, AsyncPipe, ToastComponent, ModalXacNhanComponent, VnPostLoginModalComponent],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App implements OnInit {
  protected readonly title = signal('frontend');

  // Modal state - will be initialized in constructor
  modalData$!: any;
  isModalOpen$!: any;

  // VNPost modal state
  showVnPostLoginModal = false;

  constructor(
    private confirmationService: ConfirmationDialogService,
    private bhxhLookupService: BhxhLookupService
  ) {
    // Initialize modal observables after service injection
    this.modalData$ = this.confirmationService.modalData$;
    this.isModalOpen$ = this.confirmationService.isOpen$;

    // Listen to VNPost token expired events globally
    this.bhxhLookupService.tokenExpired$.subscribe(isExpired => {
      console.log('🌍 Global VNPost token expired event:', isExpired);
      console.log('🌍 Current showVnPostLoginModal state:', this.showVnPostLoginModal);
      this.showVnPostLoginModal = isExpired;
      console.log('🌍 Updated showVnPostLoginModal state:', this.showVnPostLoginModal);
    });
  }

  async ngOnInit() {
    // Initialize stagewise toolbar only in development mode
    if (isDevMode()) {
      try {
        const { initToolbar } = await import('@stagewise/toolbar');
        initToolbar({
          plugins: [],
        });
      } catch (error) {
        console.warn('Failed to load stagewise toolbar:', error);
      }
    }

    // Expose debug methods to global window in development mode
    if (isDevMode()) {
      (window as any).testVnPostModal = () => this.testVnPostModal();
      console.log('🧪 Debug methods exposed to window: testVnPostModal()');
    }
  }

  /**
   * Xử lý kết quả từ modal xác nhận
   */
  onModalResult(result: boolean): void {
    this.confirmationService.handleModalResult(result);
  }

  /**
   * Đóng modal
   */
  onModalClose(): void {
    this.confirmationService.closeModal();
  }

  /**
   * Xử lý khi đăng nhập VNPost thành công
   */
  onVnPostLoginSuccess(): void {
    console.log('🌍 Global VNPost login success');
    this.showVnPostLoginModal = false;
    this.bhxhLookupService.resetTokenExpiredState();
  }

  /**
   * Xử lý khi đóng modal VNPost
   */
  onVnPostModalClosed(): void {
    console.log('🌍 Global VNPost modal closed');
    this.showVnPostLoginModal = false;
  }

  /**
   * Debug method để test modal từ browser console
   * Gọi: window.testVnPostModal()
   */
  testVnPostModal(): void {
    console.log('🧪 Testing VNPost modal from App component');
    console.log('🧪 Current showVnPostLoginModal:', this.showVnPostLoginModal);
    this.showVnPostLoginModal = true;
    console.log('🧪 Updated showVnPostLoginModal:', this.showVnPostLoginModal);
  }
}
