/* Simplified styles without @apply */

.danh-sach-thu-tuc-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 1rem;
}

.header-section h1 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
}

.header-section p {
  color: #6b7280;
  margin-top: 0.25rem;
}

.btn-primary {
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.filter-section {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 0;
}

.animate-spin {
  border-radius: 50%;
  height: 1.5rem;
  width: 1.5rem;
  border-bottom: 2px solid #2563eb;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.table-section {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.table-header {
  background-color: #f9fafb;
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

table {
  width: 100%;
}

thead {
  background-color: #f9fafb;
}

thead th {
  padding: 0.75rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

tbody {
  background-color: white;
}

tbody tr {
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s;
}

tbody tr:hover {
  background-color: #f9fafb;
}

tbody td {
  padding: 1rem 1.5rem;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.empty-state {
  text-align: center;
  padding: 3rem 0;
}

.empty-state i {
  color: #9ca3af;
  margin-bottom: 1rem;
  font-size: 2.25rem;
}

.empty-state h3 {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: #6b7280;
}

.pagination-section {
  background-color: #f9fafb;
  padding: 0.75rem 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.pagination-section nav {
  display: flex;
  gap: 0.25rem;
}

.pagination-section button {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: all 0.2s;
  cursor: pointer;
}

.pagination-section button:disabled {
  background-color: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

.pagination-section button:not(:disabled) {
  background-color: white;
  color: #374151;
}

.pagination-section button:not(:disabled):hover {
  background-color: #f9fafb;
}

.pagination-section button.active {
  background-color: #2563eb;
  color: white;
  border-color: #2563eb;
}

.table-content {
  overflow-x: auto;
}

.table-content::-webkit-scrollbar {
  height: 8px;
}

.table-content::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 0.25rem;
}

.table-content::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 0.25rem;
}

.table-content::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

@media (max-width: 768px) {
  .danh-sach-thu-tuc-container {
    padding: 1rem;
  }
  
  .filter-section form {
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 0.75rem;
  }
  
  .table-content {
    overflow-x: auto;
  }
  
  .table-content table {
    min-width: 800px;
  }
  
  .header-section .flex {
    flex-direction: column;
  }
  
  .header-section .flex > * + * {
    margin-top: 1rem;
  }
  
  .btn-primary {
    width: 100%;
  }
}
