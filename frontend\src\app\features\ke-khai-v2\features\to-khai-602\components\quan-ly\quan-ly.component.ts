import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';

import { ToKhai602Store } from '../../stores';
import { DaiLyStore, DonViStore } from '../../../../core/stores';

/**
 * Component quản lý danh sách tờ khai 602
 */
@Component({
  selector: 'app-quan-ly',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink
  ],
  template: `
    <div class="quan-ly-to-khai-602">
      <!-- Breadcrumb -->
      <nav class="flex mb-4" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
              </svg>
              Trang chủ
            </a>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Kê khai</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Header -->
      <div class="flex justify-between items-center mb-4 p-4 border-b border-gray-200 bg-white rounded-lg shadow-sm">
        <div>
          <h1 class="text-xl font-bold text-gray-900">Quản lý Tờ khai 602</h1>
          <p class="text-gray-600 text-sm mt-1">Kê khai đóng BHXH cho lao động tự do</p>
        </div>
        <div class="flex space-x-3">
          <a
            routerLink="tao-moi"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200"
          >
            Tạo mới
          </a>
        </div>
      </div>

      <!-- Content -->
      <div class="bg-white rounded-lg shadow-sm p-4">
        <div *ngIf="toKhaiStore.dangTai()" class="text-center py-8">
          <p class="text-gray-500">Đang tải...</p>
        </div>

        <div *ngIf="toKhaiStore.loi()" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p class="text-red-800">{{ toKhaiStore.loi() }}</p>
        </div>

        <div *ngIf="!toKhaiStore.dangTai() && !toKhaiStore.coToKhai()" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Chưa có tờ khai nào</h3>
          <p class="text-gray-500 mb-4">Bắt đầu bằng cách tạo tờ khai 602 đầu tiên của bạn.</p>
          <a
            routerLink="tao-moi"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200"
          >
            Tạo tờ khai mới
          </a>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .quan-ly-to-khai-602 {
      @apply min-h-full;
    }

    /* Tối ưu hóa spacing */
    .quan-ly-to-khai-602 .bg-white {
      @apply shadow-sm;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .quan-ly-to-khai-602 .p-4 {
        @apply p-3;
      }

      .quan-ly-to-khai-602 .flex {
        @apply flex-col space-y-3;
      }

      .quan-ly-to-khai-602 .flex.space-x-3 {
        @apply space-x-0;
      }
    }
  `]
})
export class QuanLyComponent implements OnInit {
  // Inject stores
  readonly toKhaiStore = inject(ToKhai602Store);
  readonly daiLyStore = inject(DaiLyStore);
  readonly donViStore = inject(DonViStore);

  ngOnInit() {
    // Load initial data
    this.toKhaiStore.taiDanhSachToKhai();
    this.daiLyStore.taiDaiLyOptions();
  }

  onDaiLyFilterChange(daiLyId: number | null) {
    this.toKhaiStore.setFilter({ daiLyId, donViId: null });
    this.toKhaiStore.taiDanhSachToKhai();
    
    if (daiLyId) {
      this.donViStore.taiDonViOptionsTheoDaiLy(daiLyId);
    }
  }

  onDonViFilterChange(donViId: number | null) {
    this.toKhaiStore.setFilter({ donViId });
    this.toKhaiStore.taiDanhSachToKhai();
  }

  onSearchChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.toKhaiStore.setFilter({ tuKhoa: target.value });
    
    // Debounce search
    setTimeout(() => {
      this.toKhaiStore.taiDanhSachToKhai();
    }, 500);
  }

  trackByToKhaiId(index: number, item: any) {
    return item.id;
  }
}
