using System.Reflection;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Infrastructure.Data.Interceptors;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Infrastructure.Data;

public class ApplicationDbContext : DbContext, IApplicationDbContext
{
    private readonly AuditableEntityInterceptor _auditableEntityInterceptor;
    private readonly DomainEventInterceptor _domainEventInterceptor;

    public ApplicationDbContext(
        DbContextOptions<ApplicationDbContext> options,
        AuditableEntityInterceptor auditableEntityInterceptor,
        DomainEventInterceptor domainEventInterceptor) : base(options)
    {
        _auditableEntityInterceptor = auditableEntityInterceptor;
        _domainEventInterceptor = domainEventInterceptor;
    }

    // Constructor for design time (migrations)
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
        _auditableEntityInterceptor = null!;
        _domainEventInterceptor = null!;
    }

    public DbSet<NguoiDung> NguoiDung => Set<NguoiDung>();
    public DbSet<SanPham> SanPham => Set<SanPham>();
    public DbSet<DonHang> DonHang => Set<DonHang>();
    public DbSet<ChiTietDonHang> ChiTietDonHang => Set<ChiTietDonHang>();
    public DbSet<RefreshToken> RefreshToken => Set<RefreshToken>();
    public DbSet<DanhMucThuTuc> DanhMucThuTuc => Set<DanhMucThuTuc>();

    // Đại lý và đơn vị entities
    public DbSet<DaiLy> DaiLy => Set<DaiLy>();
    public DbSet<DonVi> DonVi => Set<DonVi>();

    // Role-based Authorization entities
    public DbSet<VaiTro> VaiTro => Set<VaiTro>();
    public DbSet<Quyen> Quyen => Set<Quyen>();
    public DbSet<NguoiDungVaiTro> NguoiDungVaiTro => Set<NguoiDungVaiTro>();
    public DbSet<VaiTroQuyen> VaiTroQuyen => Set<VaiTroQuyen>();

    // Tờ khai 602 entities
    public DbSet<ToKhai602> ToKhai602 => Set<ToKhai602>();
    public DbSet<LaoDongToKhai602> LaoDongToKhai602 => Set<LaoDongToKhai602>();

    // Danh mục địa chỉ entities
    public DbSet<DmTinh> DmTinh => Set<DmTinh>();
    public DbSet<DmHuyen> DmHuyen => Set<DmHuyen>();
    public DbSet<DmXa> DmXa => Set<DmXa>();
    public DbSet<DmDanToc> DmDanToc => Set<DmDanToc>();

    // Danh mục loại entities
    public DbSet<DmLoai> DmLoai => Set<DmLoai>();
    public DbSet<Tk1Ts> Tk1Ts => Set<Tk1Ts>();

    // Hệ thống kê khai tổng quát entities
    public DbSet<KeKhai> DanhSachKeKhai => Set<KeKhai>();
    public DbSet<ChiTietLaoDongKeKhaiV2> ChiTietLaoDongKeKhaiV2 => Set<ChiTietLaoDongKeKhaiV2>();

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Ignore BaseEvent - it's not an entity, just a base class for domain events
        builder.Ignore<HuyPhuc.Domain.Events.Base.BaseEvent>();

        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (_auditableEntityInterceptor != null && _domainEventInterceptor != null)
        {
            optionsBuilder.AddInterceptors(_auditableEntityInterceptor, _domainEventInterceptor);
        }
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await base.SaveChangesAsync(cancellationToken);
    }
}
