using HuyPhuc.Application.DTOs.DonVi;
using HuyPhuc.Application.Common.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.DonVi.Queries.LayDanhSachDonViTheoDaiLy;

/// <summary>
/// Query lấy danh sách đơn vị theo đại lý
/// </summary>
public record LayDanhSachDonViTheoDaiLyQuery(int DaiLyId) : IRequest<List<DonViSelectDto>>;

public class LayDanhSachDonViTheoDaiLyQueryHandler : IRequestHandler<LayDanhSachDonViTheoDaiLyQuery, List<DonViSelectDto>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public LayDanhSachDonViTheoDaiLyQueryHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<List<DonViSelectDto>> Handle(LayDanhSachDonViTheoDaiLyQuery request, CancellationToken cancellationToken)
    {
        var userIdString = _currentUserService.UserId;
        if (userIdString == null || !int.TryParse(userIdString, out var userId))
        {
            return new List<DonViSelectDto>();
        }

        // Kiểm tra người dùng có quyền truy cập đại lý này không
        var nguoiDung = await _context.NguoiDung
            .Include(x => x.DanhSachVaiTro)
                .ThenInclude(x => x.VaiTro)
            .FirstOrDefaultAsync(x => x.Id == userId, cancellationToken);

        // Admin có thể xem tất cả đơn vị, người dùng khác chỉ xem đơn vị thuộc đại lý của mình
        var isAdmin = nguoiDung?.DanhSachVaiTro
            .Any(x => x.VaiTro.TenVaiTro.Contains("Admin") || x.VaiTro.TenVaiTro.Contains("Super")) == true;

        if (!isAdmin && nguoiDung?.DaiLyId != request.DaiLyId)
        {
            // Người dùng không thuộc đại lý này và không phải admin, không có quyền xem đơn vị
            return new List<DonViSelectDto>();
        }

        // Lấy danh sách đơn vị thuộc đại lý
        var danhSachDonVi = await _context.DonVi
            .Where(x => x.DaiLyId == request.DaiLyId)
            .OrderBy(x => x.MaDonVi)
            .Select(x => new DonViSelectDto
            {
                Id = x.Id,
                MaDonVi = x.MaDonVi,
                TenDonVi = x.TenDonVi,
                DaiLyId = x.DaiLyId,
                TrangThaiHoatDong = x.TrangThaiHoatDong
            })
            .ToListAsync(cancellationToken);

        return danhSachDonVi ?? new List<DonViSelectDto>();
    }
}
