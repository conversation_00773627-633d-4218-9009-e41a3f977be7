using HuyPhuc.Application.Common.Models;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories;
using MediatR;

namespace HuyPhuc.Application.Features.QuanLyNguoiDung.Commands.TaoNguoiDung;

public class TaoNguoiDungCommandHandler : IRequestHandler<TaoNguoiDungCommand, Result<int>>
{
    private readonly INguoiDungRepository _nguoiDungRepository;

    public TaoNguoiDungCommandHandler(INguoiDungRepository nguoiDungRepository)
    {
        _nguoiDungRepository = nguoiDungRepository;
    }

    public async Task<Result<int>> Handle(TaoNguoiDungCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Kiểm tra email đã tồn tại
            var emailDaTonTai = await _nguoiDungRepository.EmailDaTonTaiAsync(request.Email, cancellationToken: cancellationToken);
            if (emailDaTonTai)
            {
                return Result<int>.Failure("Email đã được sử dụng bởi người dùng khác");
            }

            // Tạo người dùng mới (TODO: Hash password before saving)
            var nguoiDung = Domain.Entities.NguoiDung.Tao(request.HoTen, request.Email, request.MatKhau, request.SoDienThoai, request.Username);

            await _nguoiDungRepository.ThemAsync(nguoiDung, cancellationToken);

            return Result<int>.Success(nguoiDung.Id);
        }
        catch (Exception ex)
        {
            return Result<int>.Failure($"Lỗi khi tạo người dùng: {ex.Message}");
        }
    }
}
