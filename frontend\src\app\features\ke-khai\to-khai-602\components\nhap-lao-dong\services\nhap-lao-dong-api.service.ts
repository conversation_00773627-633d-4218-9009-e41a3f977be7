import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { KeKhaiService } from '../../../../../../shared/services/ke-khai.service';
import { KeKhaiChiTietDto, LaoDongKeKhaiDto, ThemLaoDongKeKhaiRequest } from '../../../../../../shared/models/ke-khai.model';
import { NotificationService } from '../../../../../../shared/services/notification.service';

/**
 * Service xử lý các API calls cho nhập lao động
 * Tách từ NhapLaoDongService để tuân thủ quy tắc 400 dòng
 */
@Injectable({
  providedIn: 'root'
})
export class NhapLaoDongApiService {

  constructor(
    private keKhaiService: KeKhaiService,
    private notificationService: NotificationService
  ) {}

  /**
   * <PERSON>yển đổi phương thức từ frontend format sang API format
   */
  private mapPhuongThucToApi(frontendPhuongThuc: string): string {
    const mapping: { [key: string]: string } = {
      '1-thang': '1',
      '3-thang': '3',
      '6-thang': '6',
      '12-thang': '12',
      'nam-sau': 'nam-sau',
      'nam-thieu': 'nam-thieu'
    };

    return mapping[frontendPhuongThuc] || frontendPhuongThuc;
  }

  /**
   * Load thông tin kê khai từ bảng danh_sach_ke_khai
   */
  async loadKeKhaiInfo(keKhaiId: number): Promise<KeKhaiChiTietDto | undefined> {
    try {
      return await this.keKhaiService.layChiTiet(keKhaiId).toPromise();
    } catch (error) {
      console.error('Lỗi load thông tin kê khai:', error);
      this.notificationService.showError('Lỗi', 'Có lỗi xảy ra khi tải thông tin kê khai');
      return undefined;
    }
  }

  /**
   * Load danh sách lao động từ bảng chi_tiet_lao_dong_ke_khai_v2
   */
  async loadDanhSachLaoDong(keKhaiId: number): Promise<LaoDongKeKhaiDto[]> {
    try {
      const result = await this.keKhaiService.layDanhSachLaoDong(keKhaiId).toPromise();
      return result || [];
    } catch (error) {
      console.error('Lỗi load danh sách lao động:', error);
      this.notificationService.showError('Lỗi', 'Có lỗi xảy ra khi tải danh sách lao động');
      return [];
    }
  }

  /**
   * Thêm lao động mới - sử dụng API mới
   */
  async themLaoDongMoi(keKhaiId: number, formValue: any): Promise<boolean> {
    try {
      const request: ThemLaoDongKeKhaiRequest = {
        keKhaiId: keKhaiId,
        maSoBHXH: formValue.maSoBHXH,

        // Thông tin cá nhân
        hoTen: formValue.hoTen,
        cmnd: formValue.cmnd,
        ngaySinh: formValue.ngaySinh,
        gioiTinh: this.mapGioiTinhToNumber(formValue.gioiTinh),
        dienThoai: formValue.dienThoaiLh,
        maTinhKs: formValue.maTinhKs,
        maHuyenKs: formValue.maHuyenKs,
        maXaKs: formValue.maXaKs,

        // Thông tin BHXH
        phuongAn: formValue.phuongAn,
        phuongThuc: this.mapPhuongThucToApi(formValue.phuongThuc || ''),
        thangBatDau: formValue.thangBatDau,
        mucThuNhap: formValue.mucThuNhap,
        tienLai: formValue.tienLai || 0,
        tienThua: formValue.tienThua || 0,
        tienTuDong: formValue.tienTuDong || 0,
        tongTien: formValue.tongTien || 0,
        tienHoTro: formValue.tienHoTro || 0,
        tyLe: formValue.tyLe || 22,
        soThang: formValue.soThang || 1
      };

      const response = await this.keKhaiService.themLaoDong(request).toPromise();
      console.log('🔍 Response từ backend:', response);

      if (response && response.success) {
        this.notificationService.showSuccess('Thành công', 'Thêm lao động thành công!');
        return true;
      } else if (response && !response.success) {
        console.log('🔍 Lỗi validation:', response.message);
        this.notificationService.showError('Lỗi validation', response.message || 'Có lỗi xảy ra khi thêm lao động');
        return false;
      } else {
        console.log('🔍 Response không hợp lệ:', response);
        this.notificationService.showError('Lỗi', 'Response không hợp lệ từ server');
        return false;
      }
    } catch (error) {
      console.error('Lỗi thêm lao động:', error);
      this.notificationService.showError('Lỗi', 'Có lỗi xảy ra khi thêm lao động');
      return false;
    }
  }

  /**
   * Xóa lao động
   */
  async xoaLaoDong(keKhaiId: number, laoDongId: number): Promise<boolean> {
    try {
      await this.keKhaiService.xoaLaoDong(keKhaiId, laoDongId).toPromise();
      this.notificationService.showSuccess('Thành công', 'Xóa lao động thành công!');
      return true;
    } catch (error) {
      console.error('Lỗi xóa lao động:', error);
      this.notificationService.showError('Lỗi', 'Có lỗi xảy ra khi xóa lao động');
      return false;
    }
  }

  /**
   * Gửi kê khai - sử dụng API mới
   */
  async guiKeKhai(keKhaiId: number): Promise<boolean> {
    try {
      await this.keKhaiService.guiKeKhai(keKhaiId).toPromise();
      this.notificationService.showSuccess('Thành công', 'Gửi kê khai thành công!');
      return true;
    } catch (error) {
      console.error('Lỗi gửi kê khai:', error);
      this.notificationService.showError('Lỗi', 'Có lỗi xảy ra khi gửi kê khai');
      return false;
    }
  }

  /**
   * Hiển thị confirmation dialog cho xóa lao động
   */
  xacNhanXoaLaoDong(): boolean {
    return confirm('Bạn có chắc chắn muốn xóa lao động này?');
  }

  /**
   * Kiểm tra danh sách lao động có rỗng không
   */
  kiemTraDanhSachRong(danhSach: any[]): boolean {
    if (danhSach.length === 0) {
      this.notificationService.showWarning('Cảnh báo', 'Vui lòng thêm ít nhất một lao động trước khi gửi kê khai');
      return true;
    }
    return false;
  }

  /**
   * Chuyển đổi giới tính từ string sang number cho API
   */
  private mapGioiTinhToNumber(gioiTinh: any): number | undefined {
    if (typeof gioiTinh === 'number') {
      return gioiTinh;
    }

    if (typeof gioiTinh === 'string') {
      switch (gioiTinh.toLowerCase()) {
        case 'nam':
        case 'male':
        case '1':
          return 1;
        case 'nữ':
        case 'nu':
        case 'female':
        case '2':
        case '0':
          return 2; // Map cả 0 và 2 thành 2 (Nữ)
        default:
          return undefined;
      }
    }

    return undefined;
  }
}
