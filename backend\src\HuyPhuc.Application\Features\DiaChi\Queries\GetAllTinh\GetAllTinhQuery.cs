using HuyPhuc.Application.Common.Models;
using HuyPhuc.Application.DTOs.DiaChi;
using HuyPhuc.Domain.Repositories;
using MediatR;

namespace HuyPhuc.Application.Features.DiaChi.Queries.GetAllTinh;

/// <summary>
/// Query để lấy tất cả tỉnh/thành phố
/// </summary>
public record GetAllTinhQuery : IRequest<Result<List<TinhOptionDto>>>;

/// <summary>
/// Handler cho GetAllTinhQuery
/// </summary>
public class GetAllTinhQueryHandler : IRequestHandler<GetAllTinhQuery, Result<List<TinhOptionDto>>>
{
    private readonly IDmTinhRepository _tinhRepository;

    public GetAllTinhQueryHandler(IDmTinhRepository tinhRepository)
    {
        _tinhRepository = tinhRepository;
    }

    public async Task<Result<List<TinhOptionDto>>> Handle(GetAllTinhQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var tinhList = await _tinhRepository.GetAllAsync(cancellationToken);

            var result = tinhList.Select(t => new TinhOptionDto
            {
                Value = t.MaTinh,
                Text = t.TextDisplay,
                Ten = t.TenTinh,
                Ma = null
            }).ToList();

            return Result<List<TinhOptionDto>>.Success(result);
        }
        catch (Exception ex)
        {
            return Result<List<TinhOptionDto>>.Failure($"Lỗi khi lấy danh sách tỉnh: {ex.Message}");
        }
    }
}
