import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';

import { DaiLy, DonVi, DropdownOption } from '../models';
import { environment } from '../../../../../environments/environment';

/**
 * Service quản lý thông tin đại lý và đơn vị
 */
@Injectable({
  providedIn: 'root'
})
export class DaiLyService {
  private readonly API_URL = environment.apiUrl;
  
  // State management
  private readonly _danhSachDaiLy$ = new BehaviorSubject<DaiLy[]>([]);
  private readonly _danhSachDonVi$ = new BehaviorSubject<DonVi[]>([]);
  private readonly _daiLyDangChon$ = new BehaviorSubject<DaiLy | null>(null);
  private readonly _donViDangChon$ = new BehaviorSubject<DonVi | null>(null);
  private readonly _dangTai$ = new BehaviorSubject<boolean>(false);

  // Public observables
  public readonly danhSachDaiLy$ = this._danhSachDaiLy$.asObservable();
  public readonly danhSachDonVi$ = this._danhSachDonVi$.asObservable();
  public readonly daiLyDangChon$ = this._daiLyDangChon$.asObservable();
  public readonly donViDangChon$ = this._donViDangChon$.asObservable();
  public readonly dangTai$ = this._dangTai$.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Lấy danh sách đại lý của người dùng hiện tại
   */
  layDanhSachDaiLyCuaNguoiDung(): Observable<DaiLy[]> {
    this._dangTai$.next(true);
    
    // TODO: Thay thế bằng real API call
    if (!environment.production) {
      return this.getMockDaiLy().pipe(
        tap(danhSach => {
          this._danhSachDaiLy$.next(danhSach);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('Lỗi khi lấy danh sách đại lý:', error);
          this._dangTai$.next(false);
          return of([]);
        })
      );
    }

    return this.http.get<DaiLy[]>(`${this.API_URL}/DaiLy/cua-nguoi-dung`)
      .pipe(
        tap(danhSach => {
          this._danhSachDaiLy$.next(danhSach);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('Lỗi khi lấy danh sách đại lý:', error);
          this._dangTai$.next(false);
          return of([]);
        })
      );
  }

  /**
   * Lấy danh sách đơn vị theo đại lý
   */
  layDanhSachDonViTheoDaiLy(daiLyId: number): Observable<DonVi[]> {
    console.log('🔍 layDanhSachDonViTheoDaiLy called with daiLyId:', daiLyId);
    console.log('🔧 Environment production:', environment.production);
    this._dangTai$.next(true);

    // TODO: Thay thế bằng real API call
    if (!environment.production) {
      console.log('📝 Using mock data');
      return this.getMockDonVi(daiLyId).pipe(
        tap(danhSach => {
          console.log('📝 Mock data result:', danhSach);
          this._danhSachDonVi$.next(danhSach);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('Lỗi khi lấy danh sách đơn vị:', error);
          this._dangTai$.next(false);
          return of([]);
        })
      );
    }

    console.log('🌐 Calling real API:', `${this.API_URL}/DonVi/theo-dai-ly/${daiLyId}`);
    return this.http.get<DonVi[]>(`${this.API_URL}/DonVi/theo-dai-ly/${daiLyId}`)
      .pipe(
        tap(danhSach => {
          console.log('🌐 API response:', danhSach);
          this._danhSachDonVi$.next(danhSach);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('❌ API Error:', error);
          this._dangTai$.next(false);
          return of([]);
        })
      );
  }

  /**
   * Chọn đại lý
   */
  chonDaiLy(daiLy: DaiLy): void {
    console.log('Chọn đại lý:', daiLy);
    this._daiLyDangChon$.next(daiLy);
    // Reset đơn vị khi chọn đại lý mới
    this._donViDangChon$.next(null);
    this._danhSachDonVi$.next([]);

    // Tự động load danh sách đơn vị
    console.log('Bắt đầu load đơn vị cho đại lý ID:', daiLy.id);
    this.layDanhSachDonViTheoDaiLy(daiLy.id).subscribe({
      next: (donViList) => {
        console.log('Đã load được danh sách đơn vị:', donViList);
      },
      error: (error) => {
        console.error('Lỗi khi load đơn vị:', error);
      }
    });
  }

  /**
   * Chọn đơn vị
   */
  chonDonVi(donVi: DonVi): void {
    this._donViDangChon$.next(donVi);
  }

  /**
   * Reset selections
   */
  resetLuaChon(): void {
    this._daiLyDangChon$.next(null);
    this._donViDangChon$.next(null);
    this._danhSachDonVi$.next([]);
  }

  /**
   * Chuyển đổi danh sách đại lý thành dropdown options
   */
  chuyenDoiDaiLyThanhOptions(danhSach: DaiLy[]): DropdownOption[] {
    return danhSach.map(daiLy => ({
      value: daiLy.id,
      label: `${daiLy.maDaiLy} - ${daiLy.tenDaiLy}`,
      disabled: !daiLy.trangThaiHoatDong
    }));
  }

  /**
   * Chuyển đổi danh sách đơn vị thành dropdown options
   */
  chuyenDoiDonViThanhOptions(danhSach: DonVi[]): DropdownOption[] {
    return danhSach.map(donVi => ({
      value: donVi.id,
      label: `${donVi.maDonVi} - ${donVi.tenDonVi}`,
      disabled: !donVi.trangThaiHoatDong
    }));
  }

  /**
   * Mock data cho development
   */
  private getMockDaiLy(): Observable<DaiLy[]> {
    const mockData: DaiLy[] = [
      {
        id: 1,
        maDaiLy: 'DL001',
        tenDaiLy: 'Đại lý An Giang',
        diaChi: 'Châu Phú, An Giang',
        soDienThoai: '0296.123.456',
        email: '<EMAIL>',
        trangThaiHoatDong: true
      },
      {
        id: 2,
        maDaiLy: 'DL002',
        tenDaiLy: 'Đại lý Cần Thơ',
        diaChi: 'Ninh Kiều, Cần Thơ',
        soDienThoai: '0292.123.456',
        email: '<EMAIL>',
        trangThaiHoatDong: true
      }
    ];

    return of(mockData);
  }

  /**
   * Mock data đơn vị cho development
   */
  private getMockDonVi(daiLyId: number): Observable<DonVi[]> {
    const mockData: { [key: number]: DonVi[] } = {
      1: [
        {
          id: 1,
          maDonVi: 'DV001',
          tenDonVi: 'Chi nhánh Châu Phú',
          diaChi: 'Thị trấn Châu Phú, An Giang',
          daiLyId: 1,
          trangThaiHoatDong: true
        },
        {
          id: 2,
          maDonVi: 'DV002',
          tenDonVi: 'Chi nhánh Tịnh Biên',
          diaChi: 'Thị trấn Tịnh Biên, An Giang',
          daiLyId: 1,
          trangThaiHoatDong: true
        }
      ],
      2: [
        {
          id: 3,
          maDonVi: 'DV003',
          tenDonVi: 'Chi nhánh Ninh Kiều',
          diaChi: 'Quận Ninh Kiều, Cần Thơ',
          daiLyId: 2,
          trangThaiHoatDong: true
        }
      ]
    };

    return of(mockData[daiLyId] || []);
  }

  /**
   * Lấy thông tin đại lý theo ID
   */
  layDaiLyTheoId(id: number): Observable<DaiLy | null> {
    return this.danhSachDaiLy$.pipe(
      map(danhSach => danhSach.find(dl => dl.id === id) || null)
    );
  }

  /**
   * Lấy thông tin đơn vị theo ID
   */
  layDonViTheoId(id: number): Observable<DonVi | null> {
    return this.danhSachDonVi$.pipe(
      map(danhSach => danhSach.find(dv => dv.id === id) || null)
    );
  }
}
