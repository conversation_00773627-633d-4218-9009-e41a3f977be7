/**
 * Model định nghĩa thông tin người dùng
 */
export interface NguoiDung {
  id: number;
  hoTen: string;
  email: string;
  username: string;
  soDienThoai?: string;
  avatar?: string;
  vaiTro: VaiTro;
  trangThaiHoatDong: boolean;
  ngayTao: Date;
  ngayCapNhat?: Date;
}

/**
 * Enum định nghĩa các vai trò người dùng
 */
export enum VaiTro {
  Admin = 'admin',
  NhanVien = 'nhan_vien',
  KhachHang = 'khach_hang'
}

/**
 * Model cho thông tin đăng nhập
 */
export interface ThongTinDangNhap {
  username: string;
  matKhau: string;
  ghiNhoDangNhap?: boolean;
}

/**
 * Model cho response đăng nhập từ API
 */
export interface KetQuaDangNhap {
  thanhCong: boolean;
  thongBao: string;
  duLieu?: {
    nguoiDung: NguoiDung;
    accessToken: string;
    refreshToken: string;
    thoiGianHetHan: number; // timestamp
  };
}

/**
 * Model cho thông tin đăng ký
 */
export interface ThongTinDangKy {
  hoTen: string;
  email: string;
  matKhau: string;
  xacNhanMatKhau: string;
  soDienThoai?: string;
}

/**
 * Model cho response API chung
 */
export interface ApiResponse<T = any> {
  thanhCong: boolean;
  thongBao: string;
  duLieu?: T;
  loi?: string[];
}

/**
 * Model cho thông tin session người dùng
 */
export interface PhienLamViec {
  nguoiDung: NguoiDung;
  accessToken: string;
  refreshToken: string;
  thoiGianHetHan: number;
  thoiGianDangNhap: Date;
}
