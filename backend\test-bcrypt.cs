using BCrypt.Net;

// Test BCrypt hashing and verification
var password = "admin123";
var storedHash = "$2a$12$LQv3c1yqBw2YwjjMRRVAue8kHTXg5wHdHGbdJzjdHQyqd5iRw5AQC";

Console.WriteLine($"Password: {password}");
Console.WriteLine($"Stored Hash: {storedHash}");

// Test verification
var isValid = BCrypt.Net.BCrypt.Verify(password, storedHash);
Console.WriteLine($"Verification Result: {isValid}");

// Generate new hash for comparison
var newHash = BCrypt.Net.BCrypt.HashPassword(password, 12);
Console.WriteLine($"New Hash: {newHash}");

// Test new hash
var newHashValid = BCrypt.Net.BCrypt.Verify(password, newHash);
Console.WriteLine($"New Hash Valid: {newHashValid}");
