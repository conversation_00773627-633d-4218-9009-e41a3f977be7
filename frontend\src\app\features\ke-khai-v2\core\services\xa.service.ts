import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

import { Xa, XaResponse, XaOption } from '../models';
import { environment } from '../../../../../environments/environment';

/**
 * Service để quản lý thông tin xã/phường
 */
@Injectable({
  providedIn: 'root'
})
export class XaService {
  private readonly http = inject(HttpClient);
  private readonly apiUrl = `${environment.apiUrl}/xa`;

  // Cache danh sách xã theo huyện
  private xaByHuyenSubject = new BehaviorSubject<{ [maHuyen: string]: Xa[] }>({});
  public xaByHuyen$ = this.xaByHuyenSubject.asObservable();

  // Cache đã load hay chưa
  private loadedHuyen = new Set<string>();

  /**
   * L<PERSON><PERSON> danh sách xã theo mã huyện
   */
  getXaByHuyen(maHuyen: string): Observable<Xa[]> {
    const currentCache = this.xaByHuyenSubject.value;

    if (this.loadedHuyen.has(maHuyen) && currentCache[maHuyen]) {
      return of(currentCache[maHuyen]);
    }

    return this.http.get<any>(`${this.apiUrl}?maHuyen=${maHuyen}`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.map((item: any) => ({
            id: item.id || 0,
            maXa: item.value || item.maXa || '',
            tenXa: item.ten || item.tenXa || '',
            textDisplay: item.text || item.textDisplay || '',
            maHuyen: maHuyen,
            maTinh: item.maTinh || '',
            createdAt: item.createdAt,
            updatedAt: item.updatedAt
          }));
        }
        return [];
      }),
      tap(xaList => {
        const updatedCache = { ...currentCache };
        updatedCache[maHuyen] = xaList;
        this.xaByHuyenSubject.next(updatedCache);
        this.loadedHuyen.add(maHuyen);
      }),
      catchError(error => {
        console.error('Lỗi khi lấy danh sách xã:', error);
        // Fallback to mock data if API fails
        return this.getMockXaData(maHuyen).pipe(
          tap(xaList => {
            const updatedCache = { ...currentCache };
            updatedCache[maHuyen] = xaList;
            this.xaByHuyenSubject.next(updatedCache);
            this.loadedHuyen.add(maHuyen);
          })
        );
      })
    );
  }

  /**
   * Lấy danh sách xã dưới dạng options cho dropdown
   */
  getXaOptions(maHuyen: string): Observable<XaOption[]> {
    return this.getXaByHuyen(maHuyen).pipe(
      map(xaList => 
        xaList.map(xa => ({
          value: xa.maXa,
          text: xa.textDisplay,
          ten: xa.tenXa,
          maHuyen: xa.maHuyen,
          maTinh: xa.maTinh
        }))
      )
    );
  }

  /**
   * Tìm xã theo mã xã
   */
  getXaByMa(maXa: string): Observable<Xa | null> {
    const currentCache = this.xaByHuyenSubject.value;
    
    // Tìm trong tất cả cache
    for (const maHuyen in currentCache) {
      const xa = currentCache[maHuyen].find(x => x.maXa === maXa);
      if (xa) {
        return of(xa);
      }
    }
    
    return of(null);
  }

  /**
   * Lấy tên xã theo mã xã
   */
  getTenXaByMa(maXa: string): Observable<string> {
    return this.getXaByMa(maXa).pipe(
      map(xa => xa ? xa.tenXa : maXa)
    );
  }

  /**
   * Lấy text display theo mã xã
   */
  getTextDisplayByMa(maXa: string): Observable<string> {
    return this.getXaByMa(maXa).pipe(
      map(xa => xa ? xa.textDisplay : maXa)
    );
  }

  /**
   * Convert mã xã thành tên xã (sync version cho pipe)
   */
  convertMaToTen(maXa: string): string {
    const currentCache = this.xaByHuyenSubject.value;
    
    for (const maHuyen in currentCache) {
      const xa = currentCache[maHuyen].find(x => x.maXa === maXa);
      if (xa) {
        return xa.tenXa;
      }
    }
    
    return maXa;
  }

  /**
   * Convert mã xã thành text display (sync version cho pipe)
   */
  convertMaToTextDisplay(maXa: string): string {
    const currentCache = this.xaByHuyenSubject.value;
    
    for (const maHuyen in currentCache) {
      const xa = currentCache[maHuyen].find(x => x.maXa === maXa);
      if (xa) {
        return xa.textDisplay;
      }
    }
    
    return maXa;
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.xaByHuyenSubject.next({});
    this.loadedHuyen.clear();
  }

  /**
   * Mock data cho testing (tạm thời)
   */
  private getMockXaData(maHuyen: string): Observable<Xa[]> {
    // Chỉ có data cho thị xã Tịnh Biên (890)
    if (maHuyen === '890') {
      const mockData: Xa[] = [
        { id: 1, maXa: '30502', tenXa: 'Phường Nhà Bàng', textDisplay: '30502 - Phường Nhà Bàng', maHuyen: '890', maTinh: '89' },
        { id: 2, maXa: '30505', tenXa: 'Phường Chi Lăng', textDisplay: '30505 - Phường Chi Lăng', maHuyen: '890', maTinh: '89' },
        { id: 3, maXa: '30508', tenXa: 'Phường Núi Voi', textDisplay: '30508 - Phường Núi Voi', maHuyen: '890', maTinh: '89' },
        { id: 4, maXa: '30511', tenXa: 'Phường Nhơn Hưng', textDisplay: '30511 - Phường Nhơn Hưng', maHuyen: '890', maTinh: '89' },
        { id: 5, maXa: '30514', tenXa: 'Phường An Phú', textDisplay: '30514 - Phường An Phú', maHuyen: '890', maTinh: '89' },
        { id: 6, maXa: '30517', tenXa: 'Phường Thới Sơn', textDisplay: '30517 - Phường Thới Sơn', maHuyen: '890', maTinh: '89' },
        { id: 7, maXa: '30520', tenXa: 'Phường Tịnh Biên', textDisplay: '30520 - Phường Tịnh Biên', maHuyen: '890', maTinh: '89' },
        { id: 8, maXa: '30523', tenXa: 'Xã Văn Giáo', textDisplay: '30523 - Xã Văn Giáo', maHuyen: '890', maTinh: '89' },
        { id: 9, maXa: '30526', tenXa: 'Xã An Cư', textDisplay: '30526 - Xã An Cư', maHuyen: '890', maTinh: '89' },
        { id: 10, maXa: '30529', tenXa: 'Xã An Nông', textDisplay: '30529 - Xã An Nông', maHuyen: '890', maTinh: '89' },
        { id: 11, maXa: '30532', tenXa: 'Xã Vĩnh Trung', textDisplay: '30532 - Xã Vĩnh Trung', maHuyen: '890', maTinh: '89' },
        { id: 12, maXa: '30535', tenXa: 'Xã Tân Lợi', textDisplay: '30535 - Xã Tân Lợi', maHuyen: '890', maTinh: '89' },
        { id: 13, maXa: '30538', tenXa: 'Xã An Hảo', textDisplay: '30538 - Xã An Hảo', maHuyen: '890', maTinh: '89' },
        { id: 14, maXa: '30541', tenXa: 'Xã Tân Lập', textDisplay: '30541 - Xã Tân Lập', maHuyen: '890', maTinh: '89' }
      ];
      return of(mockData);
    }
    
    // Trả về empty array cho các huyện khác
    return of([]);
  }
}
