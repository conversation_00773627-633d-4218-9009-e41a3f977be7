using HuyPhuc.Application.Features.BHXH.DTOs;

namespace HuyPhuc.Application.Features.BHXH.Interfaces;

/// <summary>
/// Interface cho service cập nhật thông tin vào bảng tk1_ts
/// </summary>
public interface ITk1TsUpdateService
{
    /// <summary>
    /// Cập nhật thông tin BHXH vào bảng tk1_ts
    /// </summary>
    /// <param name="bhxhData">Dữ liệu BHXH từ tra cứu</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True nếu cập nhật thành công, False nếu thất bại</returns>
    Task<bool> CapNhatThongTinBhxhAsync(BhxhDataDto bhxhData, CancellationToken cancellationToken = default);

    /// <summary>
    /// Kiểm tra xem mã số BHXH đã tồn tại trong bảng tk1_ts chưa
    /// </summary>
    /// <param name="maSoBHXH">Mã số BHXH cần kiểm tra</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True nếu đã tồn tại, False nếu chưa tồn tại</returns>
    Task<bool> KiemTraTonTaiAsync(string maSoBHXH, CancellationToken cancellationToken = default);
}
