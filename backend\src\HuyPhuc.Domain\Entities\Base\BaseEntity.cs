using MediatR;

namespace HuyPhuc.Domain.Entities.Base;

public abstract class BaseEntity
{
    public int Id { get; protected set; }

    private readonly List<INotification> _domainEvents = new();
    public IReadOnlyCollection<INotification> DomainEvents => _domainEvents.AsReadOnly();

    protected void ThemSu<PERSON>ien(INotification domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void XoaSuKien(INotification domainEvent)
    {
        _domainEvents.Remove(domainEvent);
    }

    public void XoaTatCaSuKien()
    {
        _domainEvents.Clear();
    }
}
