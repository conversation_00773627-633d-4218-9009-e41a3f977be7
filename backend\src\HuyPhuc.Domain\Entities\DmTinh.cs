using HuyPhuc.Domain.Common;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity cho danh mục tỉnh/thành phố
/// </summary>
public class DmTinh : BaseAuditableEntity
{
    /// <summary>
    /// Mã tỉnh (2 ký tự)
    /// </summary>
    public string MaTinh { get; set; } = string.Empty;

    /// <summary>
    /// Tên tỉnh/thành phố
    /// </summary>
    public string TenTinh { get; set; } = string.Empty;

    /// <summary>
    /// Text hiển thị (mã - tên)
    /// </summary>
    public string TextDisplay { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property - Danh sách huyện thuộc tỉnh này
    /// </summary>
    public virtual ICollection<DmHuyen> DanhSachHuyen { get; set; } = new List<DmHuyen>();

    /// <summary>
    /// Navigation property - Danh sách xã thuộc tỉnh này
    /// </summary>
    public virtual ICollection<DmXa> DanhSachXa { get; set; } = new List<DmXa>();
}
