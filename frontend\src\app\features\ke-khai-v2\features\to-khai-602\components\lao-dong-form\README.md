# Lao Dong Form Components

Form lao động đã được chia thành 2 phần chính để dễ quản lý và bảo trì:

## 1. Thông tin cơ bản (`ThongTinCoBanComponent`)

**Đường dẫn:** `./thong-tin-co-ban/thong-tin-co-ban.component.ts`

**Chức năng:** Quản lý tất cả thông tin cơ bản của lao động trong 1 card duy nhất bao gồm:
- **Mã số BHXH** (vị trí đầu tiên) với tính năng tra cứu BHXH và VNPost login modal
- Thông tin cá nhân (họ tên, CCCD/CMND, ng<PERSON>y sinh, giới tính, CCNS, quốc tịch, dân tộc)
- Địa chỉ thường trú (tỉnh/thành phố, quận/huyện, phường/xã)
- Thông tin liên hệ (số điện thoại, mã hộ gia đình)

**Giao diện:** Tất cả các field được hiển thị trong 1 card với tiêu đề "Thông tin cơ bản"
**Tính năng đặc biệt:**
- Tra cứu BHXH tự động điền thông tin cá nhân, địa chỉ và liên hệ
- Dropdown tỉnh/thành phố với dữ liệu từ database `dm_tinh`
**Format handling:** Tự động convert format ngày sinh từ API (dd/MM/yyyy) sang HTML input date (yyyy-MM-dd)

**Input:**
- `parentForm: FormGroup` - Form group chính từ component cha
- `disabled: boolean` - Trạng thái disable form

**Output:**
- `formChange: EventEmitter<any>` - Emit khi form thay đổi

## 2. Thông tin đóng BHXH (`ThongTinDongBhxhComponent`)

**Đường dẫn:** `./thong-tin-dong-bhxh/thong-tin-dong-bhxh.component.ts`

**Chức năng:** Quản lý tất cả thông tin liên quan đến BHXH trong 1 card duy nhất bao gồm:
- Thông tin BHXH (số thứ tự, mức thu nhập, tháng bắt đầu, phương án, phương thức)
- Thông tin tiền (tiền lãi, tiền thừa, Tiền tự đóng, tổng tiền, NSNN hỗ trợ)
- Thông tin bổ sung (type ID, tham gia bảo hiểm, tạm hoãn hợp đồng)
- Thông tin hệ thống (thông báo, mã lỗi, mô tả lỗi)
- Ghi chú

**Giao diện:** Tất cả các field được hiển thị trong 1 card với tiêu đề "Thông tin đóng BHXH" trong 1 grid liền mạch
**Lưu ý:** Mã số BHXH đã được chuyển sang component "Thông tin cơ bản"
**Format handling:** Tự động convert format tháng bắt đầu từ API (MM/yyyy) sang HTML input month (yyyy-MM)

**Input:**
- `parentForm: FormGroup` - Form group chính từ component cha
- `disabled: boolean` - Trạng thái disable form

**Output:**
- `formChange: EventEmitter<any>` - Emit khi form thay đổi
- `bhxhLookupSuccess: EventEmitter<any>` - Emit khi tra cứu BHXH thành công

## 3. Component chính (`LaoDongFormComponent`)

**Chức năng:** 
- Quản lý form chính và điều phối giữa 2 component con
- Xử lý submit form
- Xử lý dữ liệu từ tra cứu BHXH và cập nhật thông tin cơ bản

**Cách sử dụng:**

```html
<app-lao-dong-form
  [laoDong]="selectedLaoDong"
  [isEdit]="isEditMode"
  [disabled]="isProcessing"
  (luuLaoDong)="onSaveLaoDong($event)"
  (huyForm)="onCancelForm()">
</app-lao-dong-form>
```

## Luồng hoạt động

1. **Component chính** tạo form group với tất cả các field
2. **Thông tin cơ bản** nhận parentForm và hiển thị mã số BHXH + thông tin cá nhân
3. **Thông tin đóng BHXH** nhận parentForm và hiển thị các field liên quan đến BHXH (trừ mã số)
4. Khi tra cứu BHXH thành công từ component cơ bản:
   - Component cơ bản tự động điền thông tin cá nhân, địa chỉ, liên hệ
   - Component cơ bản emit event `bhxhLookupSuccess` lên component chính
   - Component chính nhận event và cập nhật thông tin BHXH tương ứng
5. Khi submit, component chính xử lý toàn bộ dữ liệu từ form

## Giao diện mới

- **2 card riêng biệt:** Mỗi component hiển thị trong 1 card độc lập với border và background
- **Thông tin cơ bản:** 1 card duy nhất chứa tất cả thông tin cá nhân, địa chỉ và liên hệ trong 1 grid liền mạch
- **Thông tin đóng BHXH:** 1 card duy nhất chứa tất cả thông tin BHXH trong 1 grid liền mạch (không chia sub-section)
- **Actions:** Nút Hủy/Lưu được đặt trong card riêng ở cuối form

## Lợi ích của việc chia tách

1. **Tách biệt trách nhiệm:** Mỗi component chỉ quản lý một nhóm thông tin cụ thể
2. **Dễ bảo trì:** Thay đổi logic BHXH không ảnh hưởng đến thông tin cơ bản và ngược lại
3. **Tái sử dụng:** Có thể sử dụng riêng lẻ từng component nếu cần
4. **Dễ test:** Có thể test riêng từng phần
5. **Code sạch hơn:** Mỗi file nhỏ hơn, dễ đọc và hiểu
6. **Giao diện rõ ràng:** Mỗi card có ranh giới rõ ràng, dễ phân biệt
7. **Database integration:** Sử dụng bảng `dm_tinh` để quản lý danh sách tỉnh/thành phố
8. **Pipe support:** TinhNamePipe để convert mã tỉnh thành tên tỉnh ở các nơi khác

## Cấu trúc thư mục

```
lao-dong-form/
├── lao-dong-form.component.ts          # Component chính
├── lao-dong-form.component.html        # Template chính
├── lao-dong-form.component.scss        # Styles chính
├── thong-tin-co-ban/                   # Component thông tin cơ bản
│   ├── thong-tin-co-ban.component.ts
│   ├── thong-tin-co-ban.component.html
│   └── thong-tin-co-ban.component.scss
├── thong-tin-dong-bhxh/                # Component thông tin BHXH
│   ├── thong-tin-dong-bhxh.component.ts
│   ├── thong-tin-dong-bhxh.component.html
│   └── thong-tin-dong-bhxh.component.scss
└── README.md                           # Tài liệu này

## Database Schema

### Bảng `dm_tinh`
```sql
CREATE TABLE dm_tinh (
    id SERIAL PRIMARY KEY,
    ma_tinh VARCHAR(2) NOT NULL UNIQUE,
    ten_tinh VARCHAR(255) NOT NULL,
    text_display VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Pipes Available

### TinhNamePipe
```typescript
// Sử dụng trong template
{{ maTinh | tinhName }}           // Hiển thị tên tỉnh
{{ maTinh | tinhName:'display' }} // Hiển thị text display (mã - tên)
```
```
