namespace HuyPhuc.Application.DTOs.DonVi;

/// <summary>
/// DTO cho thông tin đơn vị
/// </summary>
public class DonViDto
{
    public int Id { get; set; }
    public string MaDonVi { get; set; } = string.Empty;
    public string TenDonVi { get; set; } = string.Empty;
    public string? Dia<PERSON>hi { get; set; }
    public string? SoDienThoai { get; set; }
    public string? Email { get; set; }
    public int DaiLyId { get; set; }
    public string? TenDaiLy { get; set; }
    public bool TrangThaiHoatDong { get; set; }
    public string? GhiChu { get; set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }
}

/// <summary>
/// DTO đơn giản cho dropdown/select
/// </summary>
public class DonViSelectDto
{
    public int Id { get; set; }
    public string MaDonVi { get; set; } = string.Empty;
    public string TenDonVi { get; set; } = string.Empty;
    public int DaiLyId { get; set; }
    public bool TrangThaiHoatDong { get; set; }
}

/// <summary>
/// DTO cho tạo mới đơn vị
/// </summary>
public class TaoDonViDto
{
    public string MaDonVi { get; set; } = string.Empty;
    public string TenDonVi { get; set; } = string.Empty;
    public string? DiaChi { get; set; }
    public string? SoDienThoai { get; set; }
    public string? Email { get; set; }
    public int DaiLyId { get; set; }
    public string? GhiChu { get; set; }
}

/// <summary>
/// DTO cho cập nhật đơn vị
/// </summary>
public class CapNhatDonViDto
{
    public string TenDonVi { get; set; } = string.Empty;
    public string? DiaChi { get; set; }
    public string? SoDienThoai { get; set; }
    public string? Email { get; set; }
    public string? GhiChu { get; set; }
}
