import { Component, OnInit, OnD<PERSON>roy, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { XacThucService } from '../../../features/xac-thuc/services';
import { NguoiDung } from '../../../features/xac-thuc/models';
import { SidebarService } from '../../services/sidebar.service';
import { SidebarState } from '../../models/menu-item.model';

/**
 * Component header chính cho toàn bộ ứng dụng
 * Hiển thị logo, navigation menu, thông tin user và các chức năng chung
 */
@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule],
  template: `
    <header class="header-main bg-white shadow-sm border-b border-gray-200 fixed top-0 right-0 z-50 transition-all duration-300 ease-in-out"
            [style.left.px]="headerLeftMargin">
      <div class="container-header px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Left side - Mobile menu button và breadcrumb -->
          <div class="flex items-center space-x-4">
            <!-- Mobile menu button -->
            <button *ngIf="showMobileMenuButton"
                    class="mobile-menu-btn lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors"
                    (click)="toggleMobileSidebar()"
                    [attr.aria-label]="'Mở menu điều hướng'">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <!-- Breadcrumb placeholder - có thể mở rộng sau -->
            <nav class="hidden sm:flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-2 text-sm text-gray-500">
                <li>
                  <span class="font-medium text-gray-900">{{ getCurrentPageTitle() }}</span>
                </li>
              </ol>
            </nav>
          </div>

          <!-- Right side actions -->
          <div class="flex items-center space-x-4">
            <!-- Notifications button -->
            <button class="notification-btn relative p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                    [attr.aria-label]="'Thông báo'">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12M1.01 11.88l3.12 3.12"></path>
              </svg>
              <!-- Badge cho số thông báo -->
              <span class="notification-badge absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                3
              </span>
            </button>

            <!-- User dropdown -->
            <div class="user-menu relative">
              <button class="nut-user-menu flex items-center space-x-3 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                      (click)="toggleUserMenu()"
                      [attr.aria-label]="'Menu người dùng'">
                <div class="avatar w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-medium">
                    {{ layTenVietTat(nguoiDungHienTai?.hoTen || '') }}
                  </span>
                </div>
                <div class="hidden md:block text-left">
                  <div class="text-sm font-medium text-gray-900">{{ nguoiDungHienTai?.hoTen }}</div>
                  <div class="text-xs text-gray-500">{{ nguoiDungHienTai?.vaiTro }}</div>
                </div>
                <svg class="w-4 h-4 text-gray-400 transition-transform duration-200"
                     [class.rotate-180]="hienThiUserMenu"
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>

              <!-- Dropdown menu -->
              <div *ngIf="hienThiUserMenu"
                   class="user-dropdown absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                <a href="/ho-so-ca-nhan"
                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                  <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  Hồ sơ cá nhân
                </a>
                <a href="/cai-dat"
                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                  <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  </svg>
                  Cài đặt
                </a>
                <hr class="my-1">
                <button (click)="onDangXuat()"
                        class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                  <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                  Đăng xuất
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  `,
  styles: [`
    .header-main {
      .nav-link {
        &.active {
          color: #2563eb;
          background-color: #eff6ff;
        }
      }

      .user-dropdown {
        animation: fadeInDown 0.2s ease-out;
      }
    }

    @keyframes fadeInDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `]
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Input() showMobileMenuButton = false;

  nguoiDungHienTai: NguoiDung | null = null;
  hienThiUserMenu = false;
  headerLeftMargin = 0;

  private readonly destroy$ = new Subject<void>();

  constructor(
    private xacThucService: XacThucService,
    private router: Router,
    private sidebarService: SidebarService
  ) {}

  ngOnInit(): void {
    this.xacThucService.nguoiDungHienTai$
      .pipe(takeUntil(this.destroy$))
      .subscribe(nguoiDung => {
        this.nguoiDungHienTai = nguoiDung;
      });

    // Subscribe to sidebar state for header positioning
    this.sidebarService.state$
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        this.updateHeaderMargin(state);
      });

    // Close user menu when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.user-menu')) {
        this.hienThiUserMenu = false;
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Update header margin based on sidebar state
   */
  private updateHeaderMargin(state: SidebarState): void {
    if (state.isMobile) {
      this.headerLeftMargin = 0;
    } else {
      const config = this.sidebarService.getConfig();
      this.headerLeftMargin = state.isCollapsed ? config.width.collapsed : config.width.expanded;
    }
  }

  /**
   * Toggle mobile sidebar
   */
  toggleMobileSidebar(): void {
    this.sidebarService.toggleMobile();
  }

  /**
   * Get current page title for breadcrumb
   */
  getCurrentPageTitle(): string {
    const url = this.router.url;
    const titleMap: { [key: string]: string } = {
      '/dashboard': 'Dashboard',
      '/ho-so-bhyt': 'Hồ sơ BHYT',
      '/ho-so-bhxh': 'Hồ sơ BHXH',
      '/bao-cao': 'Báo cáo',
      '/cai-dat': 'Cài đặt'
    };

    // Find matching route
    for (const route in titleMap) {
      if (url.startsWith(route)) {
        return titleMap[route];
      }
    }

    return 'Trang chủ';
  }

  /**
   * Lấy tên viết tắt từ họ tên
   */
  layTenVietTat(hoTen: string): string {
    if (!hoTen) return '';
    const words = hoTen.trim().split(' ');
    if (words.length === 1) return words[0].charAt(0).toUpperCase();
    return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
  }

  /**
   * Toggle user menu
   */
  toggleUserMenu(): void {
    this.hienThiUserMenu = !this.hienThiUserMenu;
  }

  /**
   * Xử lý đăng xuất
   */
  onDangXuat(): void {
    this.xacThucService.dangXuat()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.router.navigate(['/dang-nhap']);
      });
  }
}
