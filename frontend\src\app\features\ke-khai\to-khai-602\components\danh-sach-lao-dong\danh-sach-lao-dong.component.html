<div class="danh-sach-lao-dong-container p-6">
  <!-- Header -->
  <div class="header-section mb-6">
    <div class="flex justify-between items-center">
      <div>
        <h2 class="text-2xl font-bold text-gray-900"><PERSON>h sách người lao động</h2>
        <p class="text-gray-600 mt-1"><PERSON><PERSON><PERSON><PERSON> lý thông tin người lao động đã khai báo</p>
      </div>
      <div class="flex space-x-3">
        <button
          type="button"
          (click)="taiDanhSachLaoDong()"
          class="btn-secondary px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          [disabled]="dangTai"
        >
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Làm mới
        </button>
        <button
          type="button"
          routerLink="../nhap-lao-dong"
          class="btn-primary px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Thêm lao động
        </button>
      </div>
    </div>
  </div>

  <!-- Search and Filter -->
  <div class="filter-section bg-white rounded-lg shadow-sm border p-4 mb-6">
    <form [formGroup]="formTimKiem" class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Tìm kiếm -->
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Tìm kiếm
        </label>
        <input
          type="text"
          formControlName="tuKhoa"
          placeholder="Họ tên, CCCD, BHXH..."
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      <!-- Giới tính -->
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Giới tính
        </label>
        <select
          formControlName="gioiTinh"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Tất cả</option>
          <option value="Nam">Nam</option>
          <option value="Nữ">Nữ</option>
        </select>
      </div>

      <!-- Trạng thái -->
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Trạng thái
        </label>
        <select
          formControlName="trangThai"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">Tất cả</option>
          <option value="active">Hoạt động</option>
          <option value="inactive">Không hoạt động</option>
        </select>
      </div>

      <!-- Actions -->
      <div class="form-group flex items-end">
        <button
          type="button"
          (click)="formTimKiem.reset()"
          class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Xóa bộ lọc
        </button>
      </div>
    </form>
  </div>

  <!-- Loading State -->
  <div *ngIf="dangTai" class="loading-section flex justify-center items-center py-12">
    <div class="flex items-center space-x-3">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      <span class="text-gray-600">Đang tải dữ liệu...</span>
    </div>
  </div>

  <!-- Data Table -->
  <div *ngIf="!dangTai" class="table-section bg-white rounded-lg shadow-sm border overflow-hidden">
    <!-- Table Header -->
    <div class="table-header bg-gray-50 px-6 py-3 border-b">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-700">
          Hiển thị {{ (trangHienTai - 1) * kichThuocTrang + 1 }} - 
          {{ Math.min(trangHienTai * kichThuocTrang, tongSoBanGhi) }} 
          trong tổng số {{ tongSoBanGhi }} bản ghi
        </div>
        <div class="flex items-center space-x-2">
          <label class="text-sm text-gray-700">Hiển thị:</label>
          <select
            [value]="kichThuocTrang"
            (change)="thayDoiKichThuocTrang($event)"
            class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="danhSachLaoDongFiltered.length === 0" class="empty-state text-center py-12">
      <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">Không có dữ liệu</h3>
      <p class="text-gray-500">Chưa có người lao động nào được khai báo hoặc không tìm thấy kết quả phù hợp.</p>
    </div>

    <!-- Table Content -->
    <div *ngIf="danhSachLaoDongFiltered.length > 0" class="table-content overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              STT
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Họ và tên
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              CCCD
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Số BHXH
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Giới tính
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Ngày sinh
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Số điện thoại
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Thao tác
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr *ngFor="let laoDong of danhSachLaoDongTrangHienTai; let i = index" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ (trangHienTai - 1) * kichThuocTrang + i + 1 }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">{{ laoDong.hoTen || ('Lao động ' + laoDong.soBHXH) }}</div>
              <div class="text-sm text-gray-500">{{ laoDong.email }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ laoDong.soCCCD === '000000000000' ? 'Chưa cập nhật' : laoDong.soCCCD }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ laoDong.soBHXH }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ laoDong.gioiTinh }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ laoDong.ngaySinh | date:'dd/MM/yyyy' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ laoDong.soDienThoai }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button
                  (click)="xemChiTiet(laoDong)"
                  class="text-blue-600 hover:text-blue-900 transition-colors"
                  title="Xem chi tiết"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </button>
                <button
                  (click)="chinhSua(laoDong)"
                  class="text-green-600 hover:text-green-900 transition-colors"
                  title="Chỉnh sửa"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </button>
                <button
                  (click)="xoaLaoDong(laoDong)"
                  class="text-red-600 hover:text-red-900 transition-colors"
                  title="Xóa"
                  [disabled]="dangXoa"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div *ngIf="tongSoTrang > 1" class="pagination bg-white px-6 py-3 border-t">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Trang {{ trangHienTai }} / {{ tongSoTrang }}
        </div>
        <div class="flex space-x-1">
          <button
            (click)="chuyenTrang(trangHienTai - 1)"
            [disabled]="trangHienTai === 1"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Trước
          </button>
          <button
            *ngFor="let trang of [].constructor(tongSoTrang); let i = index"
            (click)="chuyenTrang(i + 1)"
            [class.bg-blue-600]="trangHienTai === i + 1"
            [class.text-white]="trangHienTai === i + 1"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50"
          >
            {{ i + 1 }}
          </button>
          <button
            (click)="chuyenTrang(trangHienTai + 1)"
            [disabled]="trangHienTai === tongSoTrang"
            class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Sau
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
