using FluentValidation;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Common.Models;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.NguoiDung.Commands;

public record GanVaiTroChoNguoiDungCommand : IRequest<Result<Unit>>
{
    public int NguoiDungId { get; init; }
    public List<int> DanhSachVaiTroId { get; init; } = new();
}

public class GanVaiTroChoNguoiDungCommandValidator : AbstractValidator<GanVaiTroChoNguoiDungCommand>
{
    public GanVaiTroChoNguoiDungCommandValidator()
    {
        RuleFor(x => x.NguoiDungId)
            .GreaterThan(0).WithMessage("ID người dùng không hợp lệ");

        RuleFor(x => x.DanhSachVaiTroId)
            .NotEmpty().WithMessage("Danh sách vai trò không được để trống")
            .Must(list => list.All(id => id > 0)).WithMessage("ID vai trò không hợp lệ");
    }
}

public class GanVaiTroChoNguoiDungCommandHandler : IRequestHandler<GanVaiTroChoNguoiDungCommand, Result<Unit>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GanVaiTroChoNguoiDungCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<Result<Unit>> Handle(GanVaiTroChoNguoiDungCommand request, CancellationToken cancellationToken)
    {
        // Tìm người dùng
        var nguoiDung = await _context.NguoiDung
            .Include(nd => nd.DanhSachVaiTro)
            .FirstOrDefaultAsync(nd => nd.Id == request.NguoiDungId, cancellationToken);

        if (nguoiDung == null)
        {
            return Result<Unit>.Failure("Người dùng không tồn tại");
        }

        // Kiểm tra các vai trò có tồn tại không
        var existingVaiTroIds = await _context.VaiTro
            .Where(vt => request.DanhSachVaiTroId.Contains(vt.Id) && vt.TrangThaiHoatDong)
            .Select(vt => vt.Id)
            .ToListAsync(cancellationToken);

        var invalidVaiTroIds = request.DanhSachVaiTroId.Except(existingVaiTroIds).ToList();
        if (invalidVaiTroIds.Any())
        {
            return Result<Unit>.Failure($"Các vai trò không tồn tại: {string.Join(", ", invalidVaiTroIds)}");
        }

        // Vô hiệu hóa tất cả vai trò hiện tại
        var currentVaiTroIds = nguoiDung.DanhSachVaiTro
            .Where(ndvt => ndvt.TrangThaiHoatDong && 
                          (!ndvt.NgayHetHan.HasValue || ndvt.NgayHetHan.Value > DateTime.UtcNow))
            .Select(ndvt => ndvt.VaiTroId)
            .ToList();

        foreach (var vaiTroId in currentVaiTroIds)
        {
            nguoiDung.XoaVaiTro(vaiTroId);
        }

        // Gán vai trò mới
        foreach (var vaiTroId in existingVaiTroIds)
        {
            nguoiDung.GanVaiTro(vaiTroId, null, $"Gán bởi {_currentUserService.UserId}");
        }

        await _context.SaveChangesAsync(cancellationToken);

        return Result<Unit>.Success(Unit.Value);
    }
}
