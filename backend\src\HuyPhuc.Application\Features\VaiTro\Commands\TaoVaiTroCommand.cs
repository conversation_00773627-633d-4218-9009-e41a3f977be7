using FluentValidation;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Common.Models;
using HuyPhuc.Domain.Entities;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.VaiTro.Commands;

public record TaoVaiTroCommand : IRequest<Result<int>>
{
    public string TenVaiTro { get; init; } = string.Empty;
    public string? MoTa { get; init; }
    public List<int> DanhSachQuyenId { get; init; } = new();
}

public class TaoVaiTroCommandValidator : AbstractValidator<TaoVaiTroCommand>
{
    public TaoVaiTroCommandValidator()
    {
        RuleFor(x => x.TenVaiTro)
            .NotEmpty().WithMessage("Tên vai trò không được để trống")
            .MaximumLength(100).WithMessage("Tên vai trò không được vượt quá 100 ký tự");

        RuleFor(x => x.MoTa)
            .MaximumLength(500).WithMessage("Mô tả không được vượt quá 500 ký tự");
    }
}

public class TaoVaiTroCommandHandler : IRequestHandler<TaoVaiTroCommand, Result<int>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public TaoVaiTroCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<Result<int>> Handle(TaoVaiTroCommand request, CancellationToken cancellationToken)
    {
        // Kiểm tra tên vai trò đã tồn tại chưa
        var existingVaiTro = await _context.VaiTro
            .FirstOrDefaultAsync(vt => vt.TenVaiTro == request.TenVaiTro, cancellationToken);

        if (existingVaiTro != null)
        {
            return Result<int>.Failure("Tên vai trò đã tồn tại");
        }

        // Tạo vai trò mới
        var vaiTro = Domain.Entities.VaiTro.Tao(request.TenVaiTro, request.MoTa);
        
        // Set audit fields
        vaiTro.NguoiTao = _currentUserService.UserId?.ToString();

        _context.VaiTro.Add(vaiTro);

        // Thêm quyền cho vai trò nếu có
        if (request.DanhSachQuyenId.Any())
        {
            // Kiểm tra các quyền có tồn tại không
            var existingQuyenIds = await _context.Quyen
                .Where(q => request.DanhSachQuyenId.Contains(q.Id) && q.TrangThaiHoatDong)
                .Select(q => q.Id)
                .ToListAsync(cancellationToken);

            foreach (var quyenId in existingQuyenIds)
            {
                vaiTro.ThemQuyen(quyenId);
            }
        }

        await _context.SaveChangesAsync(cancellationToken);

        return Result<int>.Success(vaiTro.Id);
    }
}
