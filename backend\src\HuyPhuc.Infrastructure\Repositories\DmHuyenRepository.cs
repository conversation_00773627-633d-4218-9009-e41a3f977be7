using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories;
using HuyPhuc.Infrastructure.Repositories.Base;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Infrastructure.Repositories;

/// <summary>
/// Repository implementation cho DmHuyen
/// </summary>
public class DmHuyenRepository : Repository<DmHuyen>, IDmHuyenRepository
{
    public DmHuyenRepository(IApplicationDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Lấy tất cả huyện/quận
    /// </summary>
    public async Task<List<DmHuyen>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmHuyen>()
            .Include(h => h.Tinh)
            .OrderBy(h => h.MaTinh)
            .ThenBy(h => h.<PERSON>)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// L<PERSON>y danh sách huyện theo mã tỉnh
    /// </summary>
    public async Task<List<DmHuyen>> GetByMaTinhAsync(string maTinh, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmHuyen>()
            .Include(h => h.Tinh)
            .Where(h => h.MaTinh == maTinh)
            .OrderBy(h => h.MaHuyen)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Lấy huyện theo mã huyện
    /// </summary>
    public async Task<DmHuyen?> GetByMaHuyenAsync(string maHuyen, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmHuyen>()
            .Include(h => h.Tinh)
            .FirstOrDefaultAsync(h => h.MaHuyen == maHuyen, cancellationToken);
    }

    /// <summary>
    /// Kiểm tra mã huyện có tồn tại không
    /// </summary>
    public async Task<bool> ExistsByMaHuyenAsync(string maHuyen, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmHuyen>()
            .AnyAsync(h => h.MaHuyen == maHuyen, cancellationToken);
    }
}
