using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Domain.Events.VaiTroQuyen;
using HuyPhuc.Domain.Exceptions;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity đại diện cho mối quan hệ nhiều-nhiều giữa VaiTro và Quyen
/// </summary>
public class VaiTroQuyen : BaseEntity, IAuditableEntity
{
    public int VaiTroId { get; private set; }
    public int QuyenId { get; private set; }
    public DateTime NgayGan { get; private set; }
    public bool TrangThaiHoatDong { get; private set; }
    public string? GhiChu { get; private set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    // Navigation properties
    public virtual VaiTro VaiTro { get; private set; } = null!;
    public virtual Quyen Quyen { get; private set; } = null!;

    private VaiTroQuyen() { } // EF Core constructor

    /// <summary>
    /// Tạo mối quan hệ vai trò - quyền mới
    /// </summary>
    public static VaiTroQuyen Tao(int vaiTroId, int quyenId, string? ghiChu = null)
    {
        if (vaiTroId <= 0)
            throw new DomainException("ID vai trò không hợp lệ");

        if (quyenId <= 0)
            throw new DomainException("ID quyền không hợp lệ");

        var vaiTroQuyen = new VaiTroQuyen
        {
            VaiTroId = vaiTroId,
            QuyenId = quyenId,
            NgayGan = DateTime.UtcNow,
            TrangThaiHoatDong = true,
            GhiChu = ghiChu?.Trim()
        };

        vaiTroQuyen.ThemSuKien(new VaiTroQuyenGanEvent(vaiTroQuyen));
        return vaiTroQuyen;
    }

    /// <summary>
    /// Cập nhật thông tin gán quyền
    /// </summary>
    public void CapNhatThongTin(string? ghiChu = null)
    {
        GhiChu = ghiChu?.Trim();
        ThemSuKien(new VaiTroQuyenCapNhatEvent(this));
    }

    /// <summary>
    /// Kích hoạt quyền cho vai trò
    /// </summary>
    public void KichHoat()
    {
        if (TrangThaiHoatDong)
            throw new BusinessRuleException("QUYEN_DA_KICH_HOAT", "Quyền đã được kích hoạt cho vai trò này");

        TrangThaiHoatDong = true;
        ThemSuKien(new VaiTroQuyenKichHoatEvent(this));
    }

    /// <summary>
    /// Vô hiệu hóa quyền cho vai trò
    /// </summary>
    public void VoHieuHoa()
    {
        if (!TrangThaiHoatDong)
            throw new BusinessRuleException("QUYEN_DA_VO_HIEU_HOA", "Quyền đã bị vô hiệu hóa cho vai trò này");

        TrangThaiHoatDong = false;
        ThemSuKien(new VaiTroQuyenVoHieuHoaEvent(this));
    }
}
