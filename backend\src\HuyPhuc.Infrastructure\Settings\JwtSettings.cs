namespace HuyPhuc.Infrastructure.Settings;

/// <summary>
/// C<PERSON><PERSON> hình JWT settings
/// </summary>
public class JwtSettings
{
    public const string SectionName = "JwtSettings";

    /// <summary>
    /// Secret key để ký JWT token
    /// </summary>
    public string SecretKey { get; set; } = string.Empty;

    /// <summary>
    /// Issuer của JWT token
    /// </summary>
    public string Issuer { get; set; } = string.Empty;

    /// <summary>
    /// Audience của JWT token
    /// </summary>
    public string Audience { get; set; } = string.Empty;

    /// <summary>
    /// Thời gian hết hạn của access token (phút)
    /// </summary>
    public int ExpiryInMinutes { get; set; } = 60;

    /// <summary>
    /// Thời gian hết hạn của refresh token (ngày)
    /// </summary>
    public int RefreshTokenExpiryInDays { get; set; } = 7;
}
