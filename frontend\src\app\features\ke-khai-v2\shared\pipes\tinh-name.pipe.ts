import { Pipe, PipeTransform, inject } from '@angular/core';
import { TinhService } from '../../core/services';

/**
 * <PERSON><PERSON> để convert mã tỉnh thành tên tỉnh
 * Usage: {{ maTinh | tinhName }}
 * Usage: {{ maTinh | tinhName:'display' }} // để hiển thị text display
 */
@Pipe({
  name: 'tinhName',
  standalone: true
})
export class TinhNamePipe implements PipeTransform {
  private readonly tinhService = inject(TinhService);

  transform(maTinh: string, type: 'name' | 'display' = 'name'): string {
    if (!maTinh) {
      return '';
    }

    if (type === 'display') {
      return this.tinhService.convertMaToTextDisplay(maTinh);
    } else {
      return this.tinhService.convertMaToTen(maTinh);
    }
  }
}
