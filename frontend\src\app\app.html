<!-- Angular 19 + Tailwind CSS 4 + SCSS Application -->
<router-outlet></router-outlet>

<!-- Toast Notifications -->
<app-toast></app-toast>

<!-- Global Confirmation Modal -->
<app-modal-xac-nhan
  [isOpen]="(isModalOpen$ | async) || false"
  [data]="modalData$ | async"
  (confirm)="onModalResult($event)"
  (cancel)="onModalClose()"
  (close)="onModalClose()">
</app-modal-xac-nhan>

<!-- Global VNPost Login Modal -->
<app-vnpost-login-modal
  [isVisible]="showVnPostLoginModal"
  (loginSuccess)="onVnPostLoginSuccess()"
  (modalClosed)="onVnPostModalClosed()">
</app-vnpost-login-modal>