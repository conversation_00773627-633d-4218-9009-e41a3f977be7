import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';

import {
  ThongKeDashboard,
  HoatDongGanDay,
  ThongBaoHeThong,
  TrangThaiHoSo,
  LoaiBaoHiem
} from '../models';

/**
 * Service xử lý dữ liệu dashboard
 * <PERSON><PERSON> gồm thống kê, danh sách hồ sơ và hoạt động
 */
@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  constructor() { }

  /**
   * Lấy thống kê tổng quan dashboard
   */
  layThongKeDashboard(): Observable<ThongKeDashboard> {
    const thongKe: ThongKeDashboard = {
      tongHoSo: 1247,
      hoSoChoXuLy: 89,
      hoSoDaDuyet: 1098,
      hoSoTuChoi: 60,
      
      thongKeBHYT: {
        tongSo: 756,
        choXuLy: 52,
        daDuyet: 658,
        tuChoi: 46
      },
      
      thongKeBHXH: {
        tongSo: 491,
        choXuLy: 37,
        daDuyet: 440,
        tuChoi: 14
      },
      
      thongKeTheoThang: [
        { thang: '01/2024', soLuong: 98, loai: LoaiBaoHiem.BHYT },
        { thang: '01/2024', soLuong: 67, loai: LoaiBaoHiem.BHXH },
        { thang: '02/2024', soLuong: 112, loai: LoaiBaoHiem.BHYT },
        { thang: '02/2024', soLuong: 78, loai: LoaiBaoHiem.BHXH },
        { thang: '03/2024', soLuong: 134, loai: LoaiBaoHiem.BHYT },
        { thang: '03/2024', soLuong: 89, loai: LoaiBaoHiem.BHXH }
      ],
      
      tongDoanhThu: 2847500000, // 2.8 tỷ
      doanhThuThang: 456200000   // 456 triệu
    };

    return of(thongKe).pipe(delay(800));
  }

  /**
   * Lấy hoạt động gần đây
   */
  layHoatDongGanDay(soLuong: number = 10): Observable<HoatDongGanDay[]> {
    const hoatDong: HoatDongGanDay[] = [
      {
        id: 1,
        loai: 'duyet',
        tieuDe: 'Duyệt hồ sơ BHXH',
        moTa: 'Đã duyệt hồ sơ BHXH-2024-002 của Trần Thị Bình',
        thoiGian: new Date('2024-07-23T10:30:00'),
        nguoiThucHien: 'Lê Văn C',
        maHoSo: 'BHXH-2024-002'
      },
      {
        id: 2,
        loai: 'tao_moi',
        tieuDe: 'Tạo hồ sơ BHYT mới',
        moTa: 'Tạo hồ sơ BHYT-2024-001 cho Nguyễn Văn An',
        thoiGian: new Date('2024-07-23T09:15:00'),
        nguoiThucHien: 'Nguyễn Thị D',
        maHoSo: 'BHYT-2024-001'
      }
    ];

    return of(hoatDong.slice(0, soLuong)).pipe(delay(500));
  }

  /**
   * Lấy thông báo hệ thống
   */
  layThongBaoHeThong(soLuong: number = 5): Observable<ThongBaoHeThong[]> {
    const thongBao: ThongBaoHeThong[] = [
      {
        id: 1,
        tieuDe: 'Cập nhật quy định mới',
        noiDung: 'Quy định về mức đóng BHYT tự nguyện năm 2024 đã được cập nhật',
        loai: 'thong_tin',
        thoiGian: new Date('2024-07-23T08:00:00'),
        daDoc: false
      }
    ];

    return of(thongBao.slice(0, soLuong)).pipe(delay(400));
  }
}
