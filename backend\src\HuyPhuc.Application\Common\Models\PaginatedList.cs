namespace HuyPhuc.Application.Common.Models;

public class PaginatedList<T>
{
    public IReadOnlyCollection<T> Items { get; }
    public int TrangHienTai { get; }
    public int TongSoTrang { get; }
    public int KichThuocTrang { get; }
    public int TongSoMuc { get; }
    public bool CoTrangTruoc => TrangHienTai > 1;
    public bool CoTrangSau => TrangHienTai < TongSoTrang;

    public PaginatedList(IReadOnlyCollection<T> items, int tongSoMuc, int trangHienTai, int kichThuocTrang)
    {
        Items = items;
        TongSoMuc = tongSoMuc;
        TrangHienTai = trangHienTai;
        KichThuocTrang = kichThuocTrang;
        TongSoTrang = (int)Math.Ceiling(tongSoMuc / (double)kichThuocTrang);
    }

    public static PaginatedList<T> Tao(IReadOnlyCollection<T> items, int tongSoMuc, int trangHienTai, int kichThuocTrang)
    {
        return new PaginatedList<T>(items, tongSoMuc, trangHienTai, kichThuocTrang);
    }
}
