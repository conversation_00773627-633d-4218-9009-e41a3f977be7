-- <PERSON><PERSON>t tạo dữ liệu test users cho authentication
-- <PERSON><PERSON><PERSON> khẩu đã được hash bằng BCrypt với work factor 12

-- User 1: Admin
INSERT INTO dm_nguoi_dung (
    ho_ten, 
    email, 
    mat_khau, 
    so_dien_thoai, 
    username, 
    trang_thai, 
    created_at, 
    created_by
) VALUES (
    'Quản trị viên', 
    '<EMAIL>', 
    '$2a$12$LQv3c1yqBw2YwjjMRRVAue8kHTXg5wHdHGbdJzjdHQyqd5iRw5AQC', -- password: admin123
    '0901234567', 
    'admin', 
    'active', 
    NOW(), 
    'system'
) ON CONFLICT (email) DO NOTHING;

-- User 2: Nh<PERSON> viên
INSERT INTO dm_nguoi_dung (
    ho_ten, 
    email, 
    mat_khau, 
    so_dien_thoai, 
    username, 
    trang_thai, 
    created_at, 
    created_by
) VALUES (
    '<PERSON><PERSON><PERSON><PERSON>', 
    '<EMAIL>', 
    '$2a$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: nhanvien123
    '0912345678', 
    'nhanvien', 
    'active', 
    NOW(), 
    'system'
) ON CONFLICT (email) DO NOTHING;

-- User 3: Khách hàng
INSERT INTO dm_nguoi_dung (
    ho_ten, 
    email, 
    mat_khau, 
    so_dien_thoai, 
    username, 
    trang_thai, 
    created_at, 
    created_by
) VALUES (
    'Trần Thị B', 
    '<EMAIL>', 
    '$2a$12$8.UnVuG9HHyaeqNw2BibBe6WqwNfXbM69roXBb3KuF.lSHbS2A4Em', -- password: khachhang123
    '0923456789', 
    'khachhang', 
    'active', 
    NOW(), 
    'system'
) ON CONFLICT (email) DO NOTHING;

-- User 4: Test user
INSERT INTO dm_nguoi_dung (
    ho_ten, 
    email, 
    mat_khau, 
    so_dien_thoai, 
    username, 
    trang_thai, 
    created_at, 
    created_by
) VALUES (
    'Test User', 
    '<EMAIL>', 
    '$2a$12$4.VV1vdancfgcXmCc6qGeOCkRx4wQdQaGfwMYdC8mEBVgNDN4FudO', -- password: test123
    '0934567890', 
    'testuser', 
    'active', 
    NOW(), 
    'system'
) ON CONFLICT (email) DO NOTHING;

-- Hiển thị thông tin các user đã tạo
SELECT 
    id,
    ho_ten,
    email,
    username,
    so_dien_thoai,
    trang_thai,
    created_at
FROM dm_nguoi_dung 
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>'
)
ORDER BY id;
