using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Domain.Events.NguoiDungVaiTro;
using HuyPhuc.Domain.Exceptions;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity đại diện cho mối quan hệ nhiều-nhiều giữa NguoiDung và VaiTro
/// </summary>
public class NguoiDungVaiTro : BaseEntity, IAuditableEntity
{
    public int NguoiDungId { get; private set; }
    public int VaiTroId { get; private set; }
    public DateTime NgayGan { get; private set; }
    public DateTime? NgayHetHan { get; private set; } // Null = không hết hạn
    public bool TrangThaiHoatDong { get; private set; }
    public string? GhiChu { get; private set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    // Navigation properties
    public virtual NguoiDung NguoiDung { get; private set; } = null!;
    public virtual VaiTro VaiTro { get; private set; } = null!;

    private NguoiDungVaiTro() { } // EF Core constructor

    /// <summary>
    /// Tạo mối quan hệ người dùng - vai trò mới
    /// </summary>
    public static NguoiDungVaiTro Tao(int nguoiDungId, int vaiTroId, DateTime? ngayHetHan = null, string? ghiChu = null)
    {
        if (nguoiDungId <= 0)
            throw new DomainException("ID người dùng không hợp lệ");

        if (vaiTroId <= 0)
            throw new DomainException("ID vai trò không hợp lệ");

        if (ngayHetHan.HasValue && ngayHetHan.Value <= DateTime.UtcNow)
            throw new DomainException("Ngày hết hạn phải lớn hơn thời gian hiện tại");

        var nguoiDungVaiTro = new NguoiDungVaiTro
        {
            NguoiDungId = nguoiDungId,
            VaiTroId = vaiTroId,
            NgayGan = DateTime.UtcNow,
            NgayHetHan = ngayHetHan,
            TrangThaiHoatDong = true,
            GhiChu = ghiChu?.Trim()
        };

        nguoiDungVaiTro.ThemSuKien(new NguoiDungVaiTroGanEvent(nguoiDungVaiTro));
        return nguoiDungVaiTro;
    }

    /// <summary>
    /// Cập nhật thông tin gán vai trò
    /// </summary>
    public void CapNhatThongTin(DateTime? ngayHetHan = null, string? ghiChu = null)
    {
        if (ngayHetHan.HasValue && ngayHetHan.Value <= DateTime.UtcNow)
            throw new DomainException("Ngày hết hạn phải lớn hơn thời gian hiện tại");

        NgayHetHan = ngayHetHan;
        GhiChu = ghiChu?.Trim();

        ThemSuKien(new NguoiDungVaiTroCapNhatEvent(this));
    }

    /// <summary>
    /// Kích hoạt vai trò cho người dùng
    /// </summary>
    public void KichHoat()
    {
        if (TrangThaiHoatDong)
            throw new BusinessRuleException("VAI_TRO_DA_KICH_HOAT", "Vai trò đã được kích hoạt cho người dùng này");

        if (NgayHetHan.HasValue && NgayHetHan.Value <= DateTime.UtcNow)
            throw new BusinessRuleException("VAI_TRO_DA_HET_HAN", "Vai trò đã hết hạn, không thể kích hoạt");

        TrangThaiHoatDong = true;
        ThemSuKien(new NguoiDungVaiTroKichHoatEvent(this));
    }

    /// <summary>
    /// Vô hiệu hóa vai trò cho người dùng
    /// </summary>
    public void VoHieuHoa()
    {
        if (!TrangThaiHoatDong)
            throw new BusinessRuleException("VAI_TRO_DA_VO_HIEU_HOA", "Vai trò đã bị vô hiệu hóa cho người dùng này");

        TrangThaiHoatDong = false;
        ThemSuKien(new NguoiDungVaiTroVoHieuHoaEvent(this));
    }

    /// <summary>
    /// Kiểm tra vai trò có còn hiệu lực không
    /// </summary>
    public bool ConHieuLuc()
    {
        return TrangThaiHoatDong && 
               (!NgayHetHan.HasValue || NgayHetHan.Value > DateTime.UtcNow);
    }

    /// <summary>
    /// Gia hạn vai trò
    /// </summary>
    public void GiaHan(DateTime ngayHetHanMoi)
    {
        if (ngayHetHanMoi <= DateTime.UtcNow)
            throw new DomainException("Ngày hết hạn mới phải lớn hơn thời gian hiện tại");

        if (NgayHetHan.HasValue && ngayHetHanMoi <= NgayHetHan.Value)
            throw new DomainException("Ngày hết hạn mới phải lớn hơn ngày hết hạn hiện tại");

        NgayHetHan = ngayHetHanMoi;
        ThemSuKien(new NguoiDungVaiTroGiaHanEvent(this));
    }
}
