{"ConnectionStrings": {"DefaultConnection": "Host=************;Port=5454;Database=ctyhuyphuc;Username=postgres;Password=************;SSL Mode=Prefer;Trust Server Certificate=true"}, "JwtSettings": {"SecretKey": "HuyPhuc-JWT-Secret-Key-2024-Development-Mode-Key", "Issuer": "HuyPhuc.Api", "Audience": "HuyPhuc.Client", "ExpiryInMinutes": 1440, "RefreshTokenExpiryInDays": 30}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}}