import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

import { Huyen, HuyenResponse, HuyenOption } from '../models';
import { environment } from '../../../../../environments/environment';

/**
 * Service để quản lý thông tin huyện/quận
 */
@Injectable({
  providedIn: 'root'
})
export class HuyenService {
  private readonly http = inject(HttpClient);
  private readonly apiUrl = `${environment.apiUrl}/huyen`;

  // Cache danh sách huyện theo tỉnh
  private huyenByTinhSubject = new BehaviorSubject<{ [maTinh: string]: Huyen[] }>({});
  public huyenByTinh$ = this.huyenByTinhSubject.asObservable();

  // Cache đã load hay chưa
  private loadedTinh = new Set<string>();

  /**
   * L<PERSON><PERSON> danh sách huyện theo mã tỉnh
   */
  getHuyenByTinh(maTinh: string): Observable<Huyen[]> {
    const currentCache = this.huyenByTinhSubject.value;

    if (this.loadedTinh.has(maTinh) && currentCache[maTinh]) {
      return of(currentCache[maTinh]);
    }

    return this.http.get<any>(`${this.apiUrl}?maTinh=${maTinh}`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.map((item: any) => ({
            id: item.id || 0,
            maHuyen: item.value || item.maHuyen || '',
            tenHuyen: item.ten || item.tenHuyen || '',
            textDisplay: item.text || item.textDisplay || '',
            maTinh: maTinh,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt
          }));
        }
        return [];
      }),
      tap(huyenList => {
        const updatedCache = { ...currentCache };
        updatedCache[maTinh] = huyenList;
        this.huyenByTinhSubject.next(updatedCache);
        this.loadedTinh.add(maTinh);
      }),
      catchError(error => {
        console.error('Lỗi khi lấy danh sách huyện:', error);
        // Fallback to mock data if API fails
        return this.getMockHuyenData(maTinh).pipe(
          tap(huyenList => {
            const updatedCache = { ...currentCache };
            updatedCache[maTinh] = huyenList;
            this.huyenByTinhSubject.next(updatedCache);
            this.loadedTinh.add(maTinh);
          })
        );
      })
    );
  }

  /**
   * Lấy danh sách huyện dưới dạng options cho dropdown
   */
  getHuyenOptions(maTinh: string): Observable<HuyenOption[]> {
    return this.getHuyenByTinh(maTinh).pipe(
      map(huyenList => 
        huyenList.map(huyen => ({
          value: huyen.maHuyen,
          text: huyen.textDisplay,
          ten: huyen.tenHuyen,
          maTinh: huyen.maTinh
        }))
      )
    );
  }

  /**
   * Tìm huyện theo mã huyện
   */
  getHuyenByMa(maHuyen: string): Observable<Huyen | null> {
    const currentCache = this.huyenByTinhSubject.value;
    
    // Tìm trong tất cả cache
    for (const maTinh in currentCache) {
      const huyen = currentCache[maTinh].find(h => h.maHuyen === maHuyen);
      if (huyen) {
        return of(huyen);
      }
    }
    
    return of(null);
  }

  /**
   * Lấy tên huyện theo mã huyện
   */
  getTenHuyenByMa(maHuyen: string): Observable<string> {
    return this.getHuyenByMa(maHuyen).pipe(
      map(huyen => huyen ? huyen.tenHuyen : maHuyen)
    );
  }

  /**
   * Lấy text display theo mã huyện
   */
  getTextDisplayByMa(maHuyen: string): Observable<string> {
    return this.getHuyenByMa(maHuyen).pipe(
      map(huyen => huyen ? huyen.textDisplay : maHuyen)
    );
  }

  /**
   * Convert mã huyện thành tên huyện (sync version cho pipe)
   */
  convertMaToTen(maHuyen: string): string {
    const currentCache = this.huyenByTinhSubject.value;
    
    for (const maTinh in currentCache) {
      const huyen = currentCache[maTinh].find(h => h.maHuyen === maHuyen);
      if (huyen) {
        return huyen.tenHuyen;
      }
    }
    
    return maHuyen;
  }

  /**
   * Convert mã huyện thành text display (sync version cho pipe)
   */
  convertMaToTextDisplay(maHuyen: string): string {
    const currentCache = this.huyenByTinhSubject.value;
    
    for (const maTinh in currentCache) {
      const huyen = currentCache[maTinh].find(h => h.maHuyen === maHuyen);
      if (huyen) {
        return huyen.textDisplay;
      }
    }
    
    return maHuyen;
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.huyenByTinhSubject.next({});
    this.loadedTinh.clear();
  }

  /**
   * Mock data cho testing (tạm thời)
   */
  private getMockHuyenData(maTinh: string): Observable<Huyen[]> {
    // Chỉ có data cho tỉnh An Giang (89)
    if (maTinh === '89') {
      const mockData: Huyen[] = [
        { id: 1, maHuyen: '883', tenHuyen: 'Thành phố Long Xuyên', textDisplay: '883 - Thành phố Long Xuyên', maTinh: '89' },
        { id: 2, maHuyen: '884', tenHuyen: 'Thành phố Châu Đốc', textDisplay: '884 - Thành phố Châu Đốc', maTinh: '89' },
        { id: 3, maHuyen: '886', tenHuyen: 'Huyện An Phú', textDisplay: '886 - Huyện An Phú', maTinh: '89' },
        { id: 4, maHuyen: '887', tenHuyen: 'Thị xã Tân Châu', textDisplay: '887 - Thị xã Tân Châu', maTinh: '89' },
        { id: 5, maHuyen: '888', tenHuyen: 'Huyện Phú Tân', textDisplay: '888 - Huyện Phú Tân', maTinh: '89' },
        { id: 6, maHuyen: '889', tenHuyen: 'Huyện Châu Phú', textDisplay: '889 - Huyện Châu Phú', maTinh: '89' },
        { id: 7, maHuyen: '890', tenHuyen: 'Thị xã Tịnh Biên', textDisplay: '890 - Thị xã Tịnh Biên', maTinh: '89' },
        { id: 8, maHuyen: '891', tenHuyen: 'Huyện Tri Tôn', textDisplay: '891 - Huyện Tri Tôn', maTinh: '89' },
        { id: 9, maHuyen: '892', tenHuyen: 'Huyện Châu Thành', textDisplay: '892 - Huyện Châu Thành', maTinh: '89' },
        { id: 10, maHuyen: '893', tenHuyen: 'Huyện Chợ Mới', textDisplay: '893 - Huyện Chợ Mới', maTinh: '89' },
        { id: 11, maHuyen: '894', tenHuyen: 'Huyện Thoại Sơn', textDisplay: '894 - Huyện Thoại Sơn', maTinh: '89' }
      ];
      return of(mockData);
    }
    
    // Trả về empty array cho các tỉnh khác
    return of([]);
  }
}
