import { patchState, signalStore, withMethods, withState, withComputed } from '@ngrx/signals';
import { computed, inject } from '@angular/core';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, switchMap, tap, catchError, of } from 'rxjs';

import { DonVi, DonViOption } from '../models';
import { DonViService } from '../services';

/**
 * State interface cho đơn vị store
 */
interface DonViState {
  danhSachDonVi: DonVi[];
  donViOptions: DonViOption[];
  donViDangChon: DonVi | null;
  daiLyIdHienTai: number | null;
  dangTai: boolean;
  loi: string | null;
}

/**
 * Initial state
 */
const initialState: DonViState = {
  danhSachDonVi: [],
  donViOptions: [],
  donViDangChon: null,
  daiLyIdHienTai: null,
  dangTai: false,
  loi: null
};

/**
 * Store quản lý state của đơn vị
 */
export const DonViStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withComputed((store) => ({
    // Computed signals
    coDonVi: computed(() => store.danhSachDonVi().length > 0),
    soDonVi: computed(() => store.danhSachDonVi().length),
    donViTheoDaiLy: computed(() => {
      const daiLyId = store.daiLyIdHienTai();
      return daiLyId ? store.danhSachDonVi().filter(dv => dv.daiLyId === daiLyId) : [];
    })
  })),
  withMethods((store, donViService = inject(DonViService)) => ({
    // Actions
    setDangTai: (dangTai: boolean) => patchState(store, { dangTai }),
    setLoi: (loi: string | null) => patchState(store, { loi }),
    setDonViDangChon: (donVi: DonVi | null) => patchState(store, { donViDangChon: donVi }),
    setDaiLyIdHienTai: (daiLyId: number | null) => {
      patchState(store, { 
        daiLyIdHienTai: daiLyId,
        donViDangChon: null, // Reset đơn vị đang chọn khi đổi đại lý
        danhSachDonVi: [],
        donViOptions: []
      });
    },
    
    // Reset state
    reset: () => patchState(store, initialState),
    
    // Load danh sách đơn vị theo đại lý
    taiDanhSachDonViTheoDaiLy: rxMethod<number>(
      pipe(
        tap((daiLyId) => patchState(store, { 
          dangTai: true, 
          loi: null,
          daiLyIdHienTai: daiLyId
        })),
        switchMap((daiLyId) =>
          donViService.layDanhSachDonViTheoDaiLy(daiLyId).pipe(
            tap((danhSach) => {
              patchState(store, {
                danhSachDonVi: danhSach,
                dangTai: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                loi: error.message || 'Có lỗi khi tải danh sách đơn vị',
                dangTai: false
              });
              return of([]);
            })
          )
        )
      )
    ),
    
    // Load danh sách đơn vị options theo đại lý
    taiDonViOptionsTheoDaiLy: rxMethod<number>(
      pipe(
        tap((daiLyId) => patchState(store, { 
          dangTai: true, 
          loi: null,
          daiLyIdHienTai: daiLyId
        })),
        switchMap((daiLyId) =>
          donViService.layDanhSachDonViOptionsTheoDaiLy(daiLyId).pipe(
            tap((options) => {
              patchState(store, {
                donViOptions: options,
                dangTai: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                loi: error.message || 'Có lỗi khi tải danh sách đơn vị',
                dangTai: false
              });
              return of([]);
            })
          )
        )
      )
    ),
    
    // Load đơn vị theo ID
    taiDonViTheoId: rxMethod<number>(
      pipe(
        tap(() => patchState(store, { dangTai: true, loi: null })),
        switchMap((id) =>
          donViService.layDonViTheoId(id).pipe(
            tap((donVi) => {
              patchState(store, {
                donViDangChon: donVi,
                dangTai: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                loi: error.message || 'Có lỗi khi tải thông tin đơn vị',
                dangTai: false
              });
              return of(null);
            })
          )
        )
      )
    )
  }))
);
