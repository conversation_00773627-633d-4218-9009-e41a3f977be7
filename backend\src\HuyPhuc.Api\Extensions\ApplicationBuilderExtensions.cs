using HuyPhuc.Api.Middleware;

namespace HuyPhuc.Api.Extensions;

public static class ApplicationBuilderExtensions
{
    public static IApplicationBuilder UseApiServices(this IApplicationBuilder app, IWebHostEnvironment env)
    {
        // Exception handling
        app.UseMiddleware<ExceptionHandlingMiddleware>();

        // Development specific middleware
        if (env.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "HuyPhuc API v1");
                c.RoutePrefix = string.Empty; // Set Swagger UI at app's root
            });
        }

        app.UseHttpsRedirection();

        app.UseCors("AllowAll");

        app.UseAuthentication();

        // Authorization logging middleware
        app.UseMiddleware<AuthorizationLoggingMiddleware>();

        app.UseAuthorization();

        return app;
    }
}
