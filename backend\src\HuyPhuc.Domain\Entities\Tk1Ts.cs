using HuyPhuc.Domain.Entities.Base;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity thông tin tĩnh lao động (bảng tk1_ts)
/// Ch<PERSON>a thông tin cá nhân cơ bản của lao động không thay đổi theo tờ khai
/// </summary>
public class Tk1Ts : BaseEntity, IAuditableEntity
{
    /// <summary>
    /// Mã số BHXH - Primary key business
    /// </summary>
    public string MaSoBHXH { get; set; } = string.Empty;

    /// <summary>
    /// Họ và tên
    /// </summary>
    public string HoTen { get; set; } = string.Empty;

    /// <summary>
    /// Căn cước công dân số (CCCD)
    /// </summary>
    public string? Ccns { get; set; }

    /// <summary>
    /// Ngày sinh (format: dd/MM/yyyy)
    /// </summary>
    public string NgaySinh { get; set; } = string.Empty;

    /// <summary>
    /// Giới tính (1: Nam, 2: Nữ)
    /// </summary>
    public int GioiTinh { get; set; }

    /// <summary>
    /// Quốc tịch (mặc định: VN)
    /// </summary>
    public string QuocTich { get; set; } = "VN";

    /// <summary>
    /// Dân tộc (mặc định: 01 - Kinh)
    /// </summary>
    public string DanToc { get; set; } = "01";

    /// <summary>
    /// Số CMND/CCCD
    /// </summary>
    public string Cmnd { get; set; } = string.Empty;

    /// <summary>
    /// Mã tỉnh thường trú
    /// </summary>
    public string? MaTinhKs { get; set; }

    /// <summary>
    /// Mã huyện thường trú
    /// </summary>
    public string? MaHuyenKs { get; set; }

    /// <summary>
    /// Mã xã thường trú
    /// </summary>
    public string? MaXaKs { get; set; }

    /// <summary>
    /// Điện thoại liên hệ
    /// </summary>
    public string? DienThoaiLh { get; set; }

    /// <summary>
    /// Mã hộ gia đình
    /// </summary>
    public string? MaHoGiaDinh { get; set; }

    /// <summary>
    /// Loại ID (mặc định: TM)
    /// </summary>
    public string TypeId { get; set; } = "TM";

    /// <summary>
    /// Có tham gia bảo hiểm bổ sung không
    /// </summary>
    public bool IsThamGiaBb { get; set; } = false;

    /// <summary>
    /// Có tạm hoãn hợp đồng không
    /// </summary>
    public bool IsTamHoanHd { get; set; } = false;



    // Navigation properties
    /// <summary>
    /// Danh sách chi tiết tờ khai 602 liên quan
    /// </summary>
    public virtual ICollection<LaoDongToKhai602> DanhSachChiTietToKhai { get; set; } = new List<LaoDongToKhai602>();

    // IAuditableEntity implementation (partial - only date fields)
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }

    // These properties don't exist in the tk1_ts table, so we implement them but they won't be persisted
    string? IAuditableEntity.NguoiTao { get; set; }
    string? IAuditableEntity.NguoiCapNhat { get; set; }

    private Tk1Ts() { } // EF Core constructor

    /// <summary>
    /// Tạo mới thông tin lao động tĩnh
    /// </summary>
    public static Tk1Ts Tao(string maSoBHXH, string hoTen, string ngaySinh, int gioiTinh, string cmnd,
        string? ccns = null, string? dienThoaiLh = null, string? maTinhKs = null, 
        string? maHuyenKs = null, string? maXaKs = null, string? maHoGiaDinh = null)
    {
        if (string.IsNullOrWhiteSpace(maSoBHXH))
            throw new ArgumentException("Mã số BHXH không được để trống", nameof(maSoBHXH));

        if (string.IsNullOrWhiteSpace(hoTen))
            throw new ArgumentException("Họ tên không được để trống", nameof(hoTen));

        if (string.IsNullOrWhiteSpace(ngaySinh))
            throw new ArgumentException("Ngày sinh không được để trống", nameof(ngaySinh));

        if (string.IsNullOrWhiteSpace(cmnd))
            throw new ArgumentException("Số CMND/CCCD không được để trống", nameof(cmnd));

        if (gioiTinh != 1 && gioiTinh != 2)
            throw new ArgumentException("Giới tính phải là 1 (Nam) hoặc 2 (Nữ)", nameof(gioiTinh));

        return new Tk1Ts
        {
            MaSoBHXH = maSoBHXH.Trim(),
            HoTen = hoTen.Trim(),
            NgaySinh = ngaySinh.Trim(),
            GioiTinh = gioiTinh,
            Cmnd = cmnd.Trim(),
            Ccns = ccns?.Trim(),
            DienThoaiLh = dienThoaiLh?.Trim(),
            MaTinhKs = maTinhKs?.Trim(),
            MaHuyenKs = maHuyenKs?.Trim(),
            MaXaKs = maXaKs?.Trim(),
            MaHoGiaDinh = maHoGiaDinh?.Trim(),
            NgayTao = DateTime.UtcNow,
            NgayCapNhat = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Cập nhật thông tin lao động
    /// </summary>
    public void CapNhatThongTin(string? hoTen = null, string? ngaySinh = null, int? gioiTinh = null,
        string? cmnd = null, string? ccns = null, string? dienThoaiLh = null,
        string? maTinhKs = null, string? maHuyenKs = null, string? maXaKs = null, string? maHoGiaDinh = null)
    {
        if (!string.IsNullOrWhiteSpace(hoTen))
            HoTen = hoTen.Trim();

        if (!string.IsNullOrWhiteSpace(ngaySinh))
            NgaySinh = ngaySinh.Trim();

        if (gioiTinh.HasValue && (gioiTinh == 1 || gioiTinh == 2))
            GioiTinh = gioiTinh.Value;

        if (!string.IsNullOrWhiteSpace(cmnd))
            Cmnd = cmnd.Trim();

        Ccns = ccns?.Trim();
        DienThoaiLh = dienThoaiLh?.Trim();
        MaTinhKs = maTinhKs?.Trim();
        MaHuyenKs = maHuyenKs?.Trim();
        MaXaKs = maXaKs?.Trim();
        MaHoGiaDinh = maHoGiaDinh?.Trim();

        NgayCapNhat = DateTime.UtcNow;
    }
}
