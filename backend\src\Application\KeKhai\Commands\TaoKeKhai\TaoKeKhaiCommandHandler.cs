using System.Text.Json;
using MediatR;
using Microsoft.EntityFrameworkCore;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;

namespace HuyPhuc.Application.KeKhai.Commands.TaoKeKhai;

/// <summary>
/// Handler tạo kê khai mới
/// </summary>
public class TaoKeKhaiCommandHandler : IRequestHandler<TaoKeKhaiCommand, TaoKeKhaiResponse>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public TaoKeKhaiCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<TaoKeKhaiResponse> Handle(TaoKeKhaiCommand command, CancellationToken cancellationToken)
    {
        var request = command.Request;
        var userIdString = _currentUserService.UserId;
        
        if (userIdString == null || !int.TryParse(userIdString, out var userId))
        {
            throw new UnauthorizedAccessException("Không thể xác định người dùng hiện tại");
        }

        // 1. Validate thủ tục
        var thuTuc = await _context.DanhMucThuTuc
            .FirstOrDefaultAsync(x => x.Id == request.ThuTucId && x.TrangThai == 1, cancellationToken);
        
        if (thuTuc == null)
            throw new ArgumentException($"Không tìm thấy thủ tục với ID: {request.ThuTucId}");

        // 2. Validate đại lý và đơn vị
        var daiLy = await _context.DaiLy
            .FirstOrDefaultAsync(x => x.Id == request.DaiLyId, cancellationToken);
        
        if (daiLy == null)
            throw new ArgumentException($"Không tìm thấy đại lý với ID: {request.DaiLyId}");

        var donVi = await _context.DonVi
            .FirstOrDefaultAsync(x => x.Id == request.DonViId && x.DaiLyId == request.DaiLyId, cancellationToken);
        
        if (donVi == null)
            throw new ArgumentException($"Không tìm thấy đơn vị với ID: {request.DonViId}");

        // 3. Tạo mã kê khai tự động
        var maKeKhai = await GenerateMaKeKhai(thuTuc.Ma, cancellationToken);

        // 4. Tạo entity trong bảng danh_sach_ke_khai
        var keKhai = new KeKhai
        {
            MaKeKhai = maKeKhai,
            ThuTucId = request.ThuTucId,
            DaiLyId = request.DaiLyId,
            DonViId = request.DonViId,
            SoSoBHXH = request.SoSoBHXH,
            ThongTinHeader = request.ThongTinHeader != null ? JsonSerializer.Serialize(request.ThongTinHeader) : null,
            TrangThai = (int)TrangThaiKeKhai.DangSoan,
            NguoiTaoId = userId,
            NgayTao = DateTime.UtcNow,
            GhiChu = request.GhiChu,
            Created = DateTime.UtcNow,
            CreatedBy = _currentUserService.UserName
        };

        _context.DanhSachKeKhai.Add(keKhai);
        await _context.SaveChangesAsync(cancellationToken);

        // 5. Tạo response
        return new TaoKeKhaiResponse
        {
            KeKhaiId = keKhai.Id,
            MaKeKhai = keKhai.MaKeKhai,
            NgayTao = keKhai.NgayTao,
            TrangThai = ((TrangThaiKeKhai)keKhai.TrangThai).ToString(),
            ThuTuc = new ThuTucDto
            {
                Id = thuTuc.Id,
                Ma = thuTuc.Ma,
                Ten = thuTuc.Ten,
                MoTa = thuTuc.MoTa
            },
            DaiLy = new DaiLyDto
            {
                Id = daiLy.Id,
                MaDaiLy = daiLy.MaDaiLy,
                TenDaiLy = daiLy.TenDaiLy
            },
            DonVi = new DonViDto
            {
                Id = donVi.Id,
                MaDonVi = donVi.MaDonVi,
                TenDonVi = donVi.TenDonVi
            }
        };
    }

    /// <summary>
    /// Tạo mã kê khai tự động theo format: TK{MaThuTuc}_{Nam}_{SoThuTu}
    /// </summary>
    private async Task<string> GenerateMaKeKhai(string maThuTuc, CancellationToken cancellationToken)
    {
        var nam = DateTime.Now.Year;
        var soThuTu = await _context.DanhSachKeKhai
            .Join(_context.DanhMucThuTuc, k => k.ThuTucId, t => t.Id, (k, t) => new { k, t })
            .Where(x => x.k.NgayTao.Year == nam && x.t.Ma == maThuTuc)
            .CountAsync(cancellationToken) + 1;

        return $"TK{maThuTuc}_{nam}_{soThuTu:D3}";
    }
}

/// <summary>
/// Enum trạng thái kê khai
/// </summary>
public enum TrangThaiKeKhai
{
    DangSoan = 0,
    DaGui = 1,
    DaDuyet = 2,
    BiTuChoi = 3
}
