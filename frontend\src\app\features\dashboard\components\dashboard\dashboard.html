<!-- Dashboard BHYT & BHXH -->
<div class="dashboard-bhyt-bhxh">
  <!-- <PERSON> Header -->
  <div class="page-header mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p class="mt-2 text-gray-600">Tổng quan hệ thống kê khai BHYT và BHXH tự nguyện</p>
      </div>
      <div class="flex space-x-3">
        <button class="nut-tao-moi bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          T<PERSON><PERSON> <PERSON><PERSON> sơ mới
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="dangTai" class="flex justify-center items-center h-64">
    <div class="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 border-t-blue-600"></div>
    <span class="ml-3 text-gray-600">Đang tải dữ liệu dashboard...</span>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!dangTai && thongKe" class="dashboard-content">
    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Tổng hồ sơ -->
      <div class="stat-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Tổng hồ sơ</p>
            <p class="text-3xl font-bold text-gray-900">{{ thongKe.tongHoSo | number }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Chờ xử lý -->
      <div class="stat-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Chờ xử lý</p>
            <p class="text-3xl font-bold text-orange-600">{{ thongKe.hoSoChoXuLy | number }}</p>
          </div>
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Đã duyệt -->
      <div class="stat-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Đã duyệt</p>
            <p class="text-3xl font-bold text-green-600">{{ thongKe.hoSoDaDuyet | number }}</p>
          </div>
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Doanh thu tháng -->
      <div class="stat-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Doanh thu tháng</p>
            <p class="text-3xl font-bold text-blue-600">{{ dinhDangTien(thongKe.doanhThuThang) }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- BHYT vs BHXH Stats -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- BHYT Stats -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Bảo hiểm Y tế</h3>
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
            </svg>
          </div>
        </div>
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Tổng số</span>
            <span class="font-semibold text-gray-900">{{ thongKe.thongKeBHYT.tongSo | number }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Chờ xử lý</span>
            <span class="font-semibold text-orange-600">{{ thongKe.thongKeBHYT.choXuLy | number }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Đã duyệt</span>
            <span class="font-semibold text-green-600">{{ thongKe.thongKeBHYT.daDuyet | number }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Từ chối</span>
            <span class="font-semibold text-red-600">{{ thongKe.thongKeBHYT.tuChoi | number }}</span>
          </div>
        </div>
      </div>

      <!-- BHXH Stats -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Bảo hiểm Xã hội</h3>
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
        </div>
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Tổng số</span>
            <span class="font-semibold text-gray-900">{{ thongKe.thongKeBHXH.tongSo | number }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Chờ xử lý</span>
            <span class="font-semibold text-orange-600">{{ thongKe.thongKeBHXH.choXuLy | number }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Đã duyệt</span>
            <span class="font-semibold text-green-600">{{ thongKe.thongKeBHXH.daDuyet | number }}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Từ chối</span>
            <span class="font-semibold text-red-600">{{ thongKe.thongKeBHXH.tuChoi | number }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activities & Notifications -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Activities -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Hoạt động gần đây</h3>
        <div class="space-y-4">
          <div *ngFor="let hoatDong of hoatDongGanDay" class="flex items-start space-x-3">
            <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
                 [class]="layMauHoatDong(hoatDong.loai) + ' bg-opacity-10'">
              <svg class="w-4 h-4" [class]="layMauHoatDong(hoatDong.loai)" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="layIconHoatDong(hoatDong.loai)"></path>
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900">{{ hoatDong.tieuDe }}</p>
              <p class="text-sm text-gray-600">{{ hoatDong.moTa }}</p>
              <div class="flex items-center mt-1 space-x-2">
                <span class="text-xs text-gray-500">{{ hoatDong.nguoiThucHien }}</span>
                <span class="text-xs text-gray-400">•</span>
                <span class="text-xs text-gray-500">{{ dinhDangThoiGian(hoatDong.thoiGian) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Notifications -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Thông báo hệ thống</h3>
        <div class="space-y-3">
          <div *ngFor="let thongBao of thongBaoHeThong"
               class="p-3 rounded-lg border"
               [class]="layMauThongBao(thongBao.loai)">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h4 class="text-sm font-medium text-gray-900">{{ thongBao.tieuDe }}</h4>
                <p class="text-sm text-gray-600 mt-1">{{ thongBao.noiDung }}</p>
                <span class="text-xs text-gray-500 mt-2 block">{{ dinhDangThoiGian(thongBao.thoiGian) }}</span>
              </div>
              <div *ngIf="!thongBao.daDoc" class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-1"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
