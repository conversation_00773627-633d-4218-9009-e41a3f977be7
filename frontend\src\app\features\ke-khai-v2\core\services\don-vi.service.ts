import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { DonVi, DonViOption } from '../models';
import { SimpleHttpClientService } from './simple-http-client.service';
import { MockDataService } from './mock-data.service';

/**
 * Service quản lý đơn vị
 */
@Injectable({
  providedIn: 'root'
})
export class DonViService {
  private readonly httpClient = inject(SimpleHttpClientService);
  private readonly mockDataService = inject(MockDataService);

  // Flag để chuyển đổi giữa mock và real API
  private readonly useMockData = false;

  /**
   * Lấy danh sách đơn vị theo đại lý
   */
  layDanhSachDonViTheoDaiLy(daiLyId: number): Observable<DonVi[]> {
    if (this.useMockData) {
      return this.mockDataService.layDanhSachDonVi(daiLyId);
    }

    // Backend trả về DonViSelectDto[], cần map sang DonVi[]
    return this.httpClient.get<any[]>(`don-vi/theo-dai-ly/${daiLyId}`).pipe(
      map(response => response.map(dto => ({
        id: dto.id,
        ma: dto.maDonVi,
        ten: dto.tenDonVi,
        daiLyId: dto.daiLyId,
        trangThai: dto.trangThaiHoatDong ? 1 : 0,
        // Backward compatibility
        maDonVi: dto.maDonVi,
        tenDonVi: dto.tenDonVi,
        trangThaiHoatDong: dto.trangThaiHoatDong
      } as DonVi)))
    );
  }

  /**
   * Lấy danh sách đơn vị cho dropdown theo đại lý
   */
  layDanhSachDonViOptionsTheoDaiLy(daiLyId: number): Observable<DonViOption[]> {
    return this.layDanhSachDonViTheoDaiLy(daiLyId).pipe(
      map(donViList => donViList
        .filter(dv => dv.trangThai === 1)
        .map(dv => ({
          id: dv.id,
          maDonVi: dv.ma,
          tenDonVi: dv.ten,
          tenHienThi: `${dv.ma} - ${dv.ten}`,
          daiLyId: dv.daiLyId
        }))
      )
    );
  }

  /**
   * Lấy thông tin đơn vị theo ID
   */
  layDonViTheoId(id: number): Observable<DonVi> {
    return this.httpClient.get<DonVi>(`don-vi/${id}`);
  }

  /**
   * Tạo đơn vị mới
   */
  taoDonVi(donVi: Omit<DonVi, 'id' | 'ngayTao' | 'ngayCapNhat'>): Observable<DonVi> {
    return this.httpClient.post<DonVi>('don-vi', donVi);
  }

  /**
   * Cập nhật đơn vị
   */
  capNhatDonVi(id: number, donVi: Partial<DonVi>): Observable<DonVi> {
    return this.httpClient.put<DonVi>(`don-vi/${id}`, donVi);
  }

  /**
   * Xóa đơn vị
   */
  xoaDonVi(id: number): Observable<void> {
    return this.httpClient.delete<void>(`don-vi/${id}`);
  }
}
