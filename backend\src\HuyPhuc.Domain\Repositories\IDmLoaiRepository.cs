using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories.Base;

namespace HuyPhuc.Domain.Repositories;

/// <summary>
/// Repository interface cho DmLoai
/// </summary>
public interface IDmLoaiRepository : IRepository<DmLoai>
{
    /// <summary>
    /// L<PERSON>y tất cả loại
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Danh sách loại</returns>
    Task<List<DmLoai>> GetAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy loại theo ID
    /// </summary>
    /// <param name="id">ID loại</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Thông tin loại</returns>
    Task<DmLoai?> GetByIdAsync(int id, CancellationToken cancellationToken = default);
}
