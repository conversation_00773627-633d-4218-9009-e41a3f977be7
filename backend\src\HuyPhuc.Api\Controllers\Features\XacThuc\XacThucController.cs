using HuyPhuc.Api.Controllers.Base;
using HuyPhuc.Application.Common.Models;
using HuyPhuc.Application.Features.XacThuc.Commands.DangNhap;
using HuyPhuc.Application.Features.XacThuc.Commands.DangXuat;
using HuyPhuc.Application.Features.XacThuc.Commands.LamMoiToken;
using HuyPhuc.Application.Features.XacThuc.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers.Features.XacThuc;

/// <summary>
/// Controller xử lý xác thực người dùng
/// </summary>
[Route("api/auth")]
public class XacThucController : BaseController
{
    /// <summary>
    /// Đăng nhập người dùng
    /// </summary>
    /// <param name="request">Thông tin đăng nhập</param>
    /// <returns>Thông tin người dùng và JWT tokens</returns>
    [HttpPost("dang-nhap")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<DangNhapResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ApiResponse<DangNhapResponse>>> DangNhap([FromBody] DangNhapRequest request)
    {
        var command = new DangNhapCommand(request.Username, request.MatKhau, request.GhiNhoDangNhap);
        var result = await Mediator.Send(command);

        if (result.IsSuccess)
        {
            return Ok(new ApiResponse<DangNhapResponse>
            {
                ThanhCong = true,
                ThongBao = "Đăng nhập thành công",
                DuLieu = result.Data
            });
        }

        return BadRequest(new ApiResponse
        {
            ThanhCong = false,
            ThongBao = result.Errors.FirstOrDefault() ?? "Đăng nhập thất bại",
            Loi = result.Errors
        });
    }

    /// <summary>
    /// Đăng xuất người dùng
    /// </summary>
    /// <param name="request">Refresh token</param>
    /// <returns>Kết quả đăng xuất</returns>
    [HttpPost("dang-xuat")]
    [Authorize]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<bool>>> DangXuat([FromBody] DangXuatRequest request)
    {
        var command = new DangXuatCommand(request.RefreshToken);
        var result = await Mediator.Send(command);

        if (result.IsSuccess)
        {
            return Ok(new ApiResponse<bool>
            {
                ThanhCong = true,
                ThongBao = "Đăng xuất thành công",
                DuLieu = result.Data
            });
        }

        return BadRequest(new ApiResponse
        {
            ThanhCong = false,
            ThongBao = result.Errors.FirstOrDefault() ?? "Đăng xuất thất bại",
            Loi = result.Errors
        });
    }

    /// <summary>
    /// Làm mới access token
    /// </summary>
    /// <param name="request">Refresh token</param>
    /// <returns>Access token và refresh token mới</returns>
    [HttpPost("lam-moi-token")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<LamMoiTokenResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<ApiResponse<LamMoiTokenResponse>>> LamMoiToken([FromBody] LamMoiTokenRequest request)
    {
        var command = new LamMoiTokenCommand(request.RefreshToken);
        var result = await Mediator.Send(command);

        if (result.IsSuccess)
        {
            return Ok(new ApiResponse<LamMoiTokenResponse>
            {
                ThanhCong = true,
                ThongBao = "Làm mới token thành công",
                DuLieu = result.Data
            });
        }

        return BadRequest(new ApiResponse
        {
            ThanhCong = false,
            ThongBao = result.Errors.FirstOrDefault() ?? "Làm mới token thất bại",
            Loi = result.Errors
        });
    }
}

/// <summary>
/// Request model cho đăng xuất
/// </summary>
public class DangXuatRequest
{
    public string RefreshToken { get; set; } = string.Empty;
}
