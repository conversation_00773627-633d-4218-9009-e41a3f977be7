import { Injectable, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

import { ToKhai602, ToKhai602Filter, ToKhai602Response } from '../models';
import { PaginationInfo, LoaiKeKhai, TrangThaiKeKhai } from '../../../core/models';
import { SimpleHttpClientService } from '../../../core/services/simple-http-client.service';
import { MockDataService } from '../../../core/services/mock-data.service';

/**
 * Interface cho response danh sách tờ khai 602
 */
interface DanhSachToKhai602ResponseInternal {
  danhSach: ToKhai602[];
  phanTrang: PaginationInfo;
}

/**
 * Service quản lý tờ khai 602
 */
@Injectable({
  providedIn: 'root'
})
export class ToKhai602Service {
  private readonly httpClient = inject(SimpleHttpClientService);
  private readonly mockDataService = inject(MockDataService);
  private readonly endpoint = 'to-khai-602';

  // Flag để chuyển đổi giữa mock và real API
  private readonly useMockData = false;

  /**
   * Lấy danh sách tờ khai 602 với filter và pagination
   */
  layDanhSachToKhai(filter?: ToKhai602Filter): Observable<DanhSachToKhai602ResponseInternal> {
    if (this.useMockData) {
      return this.mockDataService.layDanhSachToKhai602(filter).pipe(
        map(response => ({
          danhSach: response.data,
          phanTrang: {
            trang: response.trang,
            kichThuoc: response.kichThuoc,
            tongSo: response.tongSo,
            tongTrang: response.tongTrang
          }
        }))
      );
    }

    // Gọi API với query parameters
    const params = {
      trang: filter?.trang || 1,
      kichThuoc: filter?.kichThuoc || 10,
      ...(filter?.tuKhoa && { tuKhoa: filter.tuKhoa }),
      ...(filter?.daiLyId && { daiLyId: filter.daiLyId }),
      ...(filter?.donViId && { donViId: filter.donViId }),
      ...(filter?.trangThai && { trangThai: filter.trangThai }),
      ...(filter?.soSoBHXH && { soSoBHXH: filter.soSoBHXH }),
      ...(filter?.kyKeKhai && { kyKeKhai: filter.kyKeKhai }),
      ...(filter?.tuNgay && { tuNgay: filter.tuNgay }),
      ...(filter?.denNgay && { denNgay: filter.denNgay })
    };

    return this.httpClient.get<any>(this.endpoint, params).pipe(
      map(response => ({
        danhSach: response.data.danhSach,
        phanTrang: response.data.phanTrang
      }))
    );
  }

  /**
   * Lấy chi tiết tờ khai 602 theo ID
   */
  layChiTietToKhai(id: number): Observable<ToKhai602> {
    if (this.useMockData) {
      return this.mockDataService.layChiTietToKhai602(id);
    }
    return this.httpClient.get<any>(`${this.endpoint}/${id}`).pipe(
      map(response => response.data)
    );
  }

  /**
   * Tạo tờ khai 602 mới
   */
  taoToKhai(toKhai: Omit<ToKhai602, 'id' | 'ngayTao' | 'ngayCapNhat'>): Observable<ToKhai602> {
    if (this.useMockData) {
      return this.mockDataService.taoMoiToKhai602(toKhai);
    }

    // Map frontend model sang backend request format
    const request = {
      daiLyId: toKhai.daiLyId,
      donViId: toKhai.donViId,
      soSoBHXH: toKhai.thongTinKhac?.soSoBHXH || '',
      ghiChu: toKhai.ghiChu
    };

    // Sử dụng endpoint POST root để tạo tờ khai mới
    return this.httpClient.post<any>(this.endpoint, request).pipe(
      map(response => {
        const data = response.data || response;
        // Map backend response sang frontend model
        return {
          id: data.id,
          ma: data.maToKhai,
          daiLyId: data.daiLyId,
          donViId: data.donViId,
          loaiKeKhai: LoaiKeKhai.ToKhai602,
          trangThai: this.mapTrangThaiFromBackend(data.trangThai),
          ghiChu: data.ghiChu,
          ngayTao: new Date(data.ngayTao),
          ngayCapNhat: new Date(data.ngayTao),
          thongTinKhac: {
            soSoBHXH: data.soSoBHXH
          },
          danhSachLaoDong: []
        } as ToKhai602;
      })
    );
  }

  /**
   * Lấy tờ khai 602 theo ID
   */
  layToKhaiTheoId(id: number): Observable<ToKhai602> {
    if (this.useMockData) {
      return this.mockDataService.layChiTietToKhai602(id);
    }

    return this.httpClient.get<any>(`${this.endpoint}/${id}`).pipe(
      map(response => {
        const data = response.data || response;
        // Map backend response sang frontend model
        return {
          id: data.id,
          ma: data.maToKhai,
          daiLyId: data.daiLyId,
          donViId: data.donViId,
          loaiKeKhai: LoaiKeKhai.ToKhai602,
          trangThai: this.mapTrangThaiFromBackend(data.trangThai),
          ghiChu: data.ghiChu,
          ngayTao: new Date(data.ngayTao),
          ngayCapNhat: new Date(data.ngayTao),
          thongTinKhac: {
            soSoBHXH: data.soSoBHXH
          },
          danhSachLaoDong: []
        } as ToKhai602;
      })
    );
  }

  /**
   * Cập nhật tờ khai 602
   */
  capNhatToKhai(id: number, toKhai: Partial<ToKhai602>): Observable<ToKhai602> {
    if (this.useMockData) {
      return this.mockDataService.capNhatToKhai602(id, toKhai);
    }

    // Transform frontend model to backend request format (CapNhatToKhaiRequest)
    const request = {
      DaiLyId: toKhai.daiLyId,
      DonViId: toKhai.donViId,
      SoSoBHXH: toKhai.thongTinKhac?.soSoBHXH,
      GhiChu: toKhai.ghiChu
    };

    return this.httpClient.put<any>(`${this.endpoint}/${id}`, request).pipe(
      map(response => response.data)
    );
  }

  /**
   * Xóa tờ khai 602
   */
  xoaToKhai(id: number): Observable<void> {
    if (this.useMockData) {
      return this.mockDataService.xoaToKhai602(id).pipe(map(() => void 0));
    }
    return this.httpClient.delete<any>(`${this.endpoint}/${id}`).pipe(
      map(() => void 0)
    );
  }

  /**
   * Gửi tờ khai 602
   */
  guiToKhai(id: number): Observable<ToKhai602> {
    if (this.useMockData) {
      // Mock: Cập nhật trạng thái thành "Đã gửi"
      return this.mockDataService.capNhatToKhai602(id, {
        trangThai: 1 as any // TrangThaiKeKhai.DaGui
      });
    }
    return this.httpClient.post<ToKhai602>(`${this.endpoint}/${id}/gui`, {});
  }

  /**
   * Duyệt tờ khai 602
   */
  duyetToKhai(id: number, ghiChu?: string): Observable<ToKhai602> {
    if (this.useMockData) {
      // Mock: Cập nhật trạng thái thành "Đã duyệt"
      return this.mockDataService.capNhatToKhai602(id, {
        trangThai: 2 as any, // TrangThaiKeKhai.DaDuyet
        ghiChu
      });
    }
    return this.httpClient.post<ToKhai602>(`${this.endpoint}/${id}/duyet`, { ghiChu });
  }

  /**
   * Từ chối tờ khai 602
   */
  tuChoiToKhai(id: number, lyDo: string): Observable<ToKhai602> {
    if (this.useMockData) {
      // Mock: Cập nhật trạng thái thành "Bị từ chối"
      return this.mockDataService.capNhatToKhai602(id, {
        trangThai: 3 as any, // TrangThaiKeKhai.BiTuChoi
        ghiChu: lyDo
      });
    }
    return this.httpClient.post<ToKhai602>(`${this.endpoint}/${id}/tu-choi`, { lyDo });
  }

  /**
   * Tạo draft tờ khai 602 - sử dụng endpoint draft của backend
   */
  taoDraftToKhai(toKhai: Partial<ToKhai602>): Observable<ToKhai602> {
    if (this.useMockData) {
      return this.mockDataService.taoMoiToKhai602(toKhai);
    }
    // Sử dụng endpoint draft của backend
    return this.httpClient.post<any>(`${this.endpoint}/draft`, toKhai).pipe(
      map(response => response.data || response)
    );
  }

  /**
   * Validate tờ khai 602
   */
  validateToKhai(toKhai: ToKhai602): Observable<{ isValid: boolean; errors: string[] }> {
    if (this.useMockData) {
      // Mock validation - luôn trả về valid
      return new Observable(observer => {
        setTimeout(() => {
          observer.next({ isValid: true, errors: [] });
          observer.complete();
        }, 300);
      });
    }
    return this.httpClient.post<{ isValid: boolean; errors: string[] }>(`${this.endpoint}/validate`, toKhai);
  }

  /**
   * Xuất file Excel
   */
  xuatExcel(filter?: ToKhai602Filter): Observable<Blob> {
    // TODO: Implement export Excel
    throw new Error('Chức năng xuất Excel chưa được triển khai');
  }

  /**
   * Xuất file PDF
   */
  xuatPDF(id: number): Observable<Blob> {
    // TODO: Implement export PDF
    throw new Error('Chức năng xuất PDF chưa được triển khai');
  }

  /**
   * Map trạng thái từ backend (number) sang frontend enum
   */
  private mapTrangThaiFromBackend(trangThai: number): TrangThaiKeKhai {
    switch (trangThai) {
      case 0: return TrangThaiKeKhai.DangSoan;
      case 1: return TrangThaiKeKhai.DaGui;
      case 2: return TrangThaiKeKhai.DaDuyet;
      case 3: return TrangThaiKeKhai.BiTuChoi;
      default: return TrangThaiKeKhai.DangSoan;
    }
  }
}
