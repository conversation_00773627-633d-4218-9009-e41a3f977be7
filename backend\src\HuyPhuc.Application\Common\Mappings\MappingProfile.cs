using AutoMapper;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Application.Features.QuanLyNguoiDung.Queries.LayDanhSachNguoiDung;
using HuyPhuc.Application.Features.QuanLyNguoiDung.Queries.LayChiTietNguoiDung;

namespace HuyPhuc.Application.Common.Mappings;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        CreateMap<NguoiDung, NguoiDungDto>()
            .ForMember(d => d.Email, opt => opt.MapFrom(s => s.Email.Value))
            .ForMember(d => d.SoDienThoai, opt => opt.MapFrom(s => s.SoDienThoai != null ? s.SoDienThoai.Value : null))
            .ForMember(d => d.Dia<PERSON>hi, opt => opt.MapFrom(s => s.DiaChi));

        CreateMap<NguoiDung, ChiTietNguoiDungDto>()
            .ForMember(d => d.Email, opt => opt.MapFrom(s => s.Email.Value))
            .ForMember(d => d.SoDienThoai, opt => opt.MapFrom(s => s.SoDienThoai != null ? s.SoDienThoai.Value : null))
            .ForMember(d => d.DiaChiChiTiet, opt => opt.MapFrom(s => s.DiaChi))
            .ForMember(d => d.Phuong, opt => opt.Ignore())
            .ForMember(d => d.Quan, opt => opt.Ignore())
            .ForMember(d => d.ThanhPho, opt => opt.Ignore());
    }
}
