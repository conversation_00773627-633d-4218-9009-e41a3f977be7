import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

import { Loai, LoaiResponse, LoaiOption } from '../models';
import { environment } from '../../../../../environments/environment';

/**
 * Service để quản lý thông tin loại
 */
@Injectable({
  providedIn: 'root'
})
export class LoaiService {
  private readonly http = inject(HttpClient);
  private readonly apiUrl = `${environment.apiUrl}/loai`;

  // Cache danh sách loại
  private loaiListSubject = new BehaviorSubject<Loai[]>([]);
  public loaiList$ = this.loaiListSubject.asObservable();

  // Cache đã load hay chưa
  private isLoaded = false;

  /**
   * <PERSON><PERSON><PERSON> danh s<PERSON>ch tất cả loại
   */
  getAllLoai(): Observable<Loai[]> {
    if (this.isLoaded && this.loaiListSubject.value.length > 0) {
      return of(this.loaiListSubject.value);
    }

    return this.http.get<any>(this.apiUrl).pipe(
      map(response => {
        if (response.success && response.data && response.data.items) {
          return response.data.items.map((item: any) => ({
            id: item.id,
            tenLoai: item.tenLoai
          }));
        }
        return [];
      }),
      tap(loaiList => {
        this.loaiListSubject.next(loaiList);
        this.isLoaded = true;
      }),
      catchError(error => {
        console.error('Lỗi khi lấy danh sách loại:', error);
        // Fallback to mock data if API fails
        return this.getMockLoaiData().pipe(
          tap(loaiList => {
            this.loaiListSubject.next(loaiList);
            this.isLoaded = true;
          })
        );
      })
    );
  }

  /**
   * Lấy danh sách loại dưới dạng options cho dropdown
   */
  getLoaiOptions(): Observable<LoaiOption[]> {
    return this.getAllLoai().pipe(
      map(loaiList => 
        loaiList.map(loai => ({
          value: loai.id,
          text: loai.tenLoai
        }))
      )
    );
  }

  /**
   * Tìm loại theo ID
   */
  getLoaiById(id: number): Observable<Loai | null> {
    return this.getAllLoai().pipe(
      map(loaiList => loaiList.find(l => l.id === id) || null)
    );
  }

  /**
   * Lấy tên loại theo ID
   */
  getTenLoaiById(id: number): Observable<string> {
    return this.getLoaiById(id).pipe(
      map(loai => loai ? loai.tenLoai : id.toString())
    );
  }

  /**
   * Convert ID loại thành tên loại (sync version cho pipe)
   */
  convertIdToTen(id: number): string {
    const currentList = this.loaiListSubject.value;
    const loai = currentList.find(l => l.id === id);
    return loai ? loai.tenLoai : id.toString();
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.loaiListSubject.next([]);
    this.isLoaded = false;
  }

  /**
   * Mock data cho testing (tạm thời)
   */
  private getMockLoaiData(): Observable<Loai[]> {
    const mockData: Loai[] = [
      { id: 1, tenLoai: 'Tăng lao động' },
      { id: 2, tenLoai: 'Tăng mức lương' },
      { id: 3, tenLoai: 'Giảm lao động' },
      { id: 4, tenLoai: 'Giảm mức lương' },
      { id: 5, tenLoai: 'Khác' }
    ];
    return of(mockData);
  }
}
