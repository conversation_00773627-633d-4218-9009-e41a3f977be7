using HuyPhuc.Api.Controllers.Base;
using HuyPhuc.Application.Features.DiaChi.Queries.GetHuyenByTinh;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// API Controller cho quản lý huyện/quận
/// </summary>
[ApiController]
[Route("api/[controller]")]
// [Authorize] // Temporarily disabled for testing
public class HuyenController : BaseController
{
    /// <summary>
    /// L<PERSON>y danh sách huyện theo mã tỉnh
    /// </summary>
    /// <param name="maTinh">Mã tỉnh</param>
    /// <returns>Danh sách huyện thuộc tỉnh</returns>
    [HttpGet]
    public async Task<IActionResult> GetByTinh([FromQuery] string maTinh)
    {
        var result = await Mediator.Send(new GetHuyenByTinhQuery(maTinh));
        
        if (result.IsSuccess)
        {
            return Ok(new
            {
                data = result.Data,
                success = true,
                message = (string?)null,
                errors = (object?)null,
                status = 200,
                traceId = HttpContext.TraceIdentifier
            });
        }

        return BadRequest(new
        {
            data = (object?)null,
            success = false,
            message = string.Join(", ", result.Errors),
            errors = result.Errors,
            status = 400,
            traceId = HttpContext.TraceIdentifier
        });
    }
}
