using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Events;
using HuyPhuc.Domain.Exceptions;
using HuyPhuc.Domain.ValueObjects;

namespace HuyPhuc.Domain.Entities;

public class NguoiDung : BaseEntity, IAuditableEntity
{
    public string HoTen { get; private set; } = string.Empty;
    public Email Email { get; private set; } = null!;
    public string MatKhau { get; private set; } = string.Empty;
    public SoDienThoai? SoDienThoai { get; private set; }
    public string? DiaChi { get; private set; } // Simplified to string instead of value object
    public DateTime? NgaySinh { get; private set; }
    public string? GioiTinh { get; private set; }
    public string? AvatarUrl { get; private set; }
    public DateTime? LastLogin { get; private set; }
    public string? MaNhanVien { get; private set; }
    public string? Username { get; private set; }
    public TrangThaiNguoiDung TrangThai { get; private set; }
    public int? DaiLyId { get; private set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    // Navigation properties
    public virtual DaiLy? DaiLy { get; private set; }
    public virtual ICollection<DonHang> DanhSachDonHang { get; private set; } = new List<DonHang>();
    public virtual ICollection<NguoiDungVaiTro> DanhSachVaiTro { get; private set; } = new List<NguoiDungVaiTro>();

    private NguoiDung() { } // EF Core constructor

    public static NguoiDung Tao(string hoTen, string email, string matKhau, string? soDienThoai = null, string? username = null)
    {
        if (string.IsNullOrWhiteSpace(hoTen))
            throw new DomainException("Họ tên không được để trống");

        if (string.IsNullOrWhiteSpace(matKhau))
            throw new DomainException("Mật khẩu không được để trống");

        var nguoiDung = new NguoiDung
        {
            HoTen = hoTen.Trim(),
            Email = Email.Tao(email),
            MatKhau = matKhau, // Should be hashed before calling this method
            SoDienThoai = soDienThoai != null ? SoDienThoai.Tao(soDienThoai) : null,
            Username = username?.Trim(),
            TrangThai = TrangThaiNguoiDung.Active
        };

        nguoiDung.ThemSuKien(new NguoiDungDaTaoEvent(nguoiDung));
        return nguoiDung;
    }

    public void CapNhatThongTin(string hoTen, string? soDienThoai = null, string? diaChi = null,
        DateTime? ngaySinh = null, string? gioiTinh = null, string? username = null)
    {
        if (string.IsNullOrWhiteSpace(hoTen))
            throw new DomainException("Họ tên không được để trống");

        HoTen = hoTen.Trim();
        SoDienThoai = soDienThoai != null ? SoDienThoai.Tao(soDienThoai) : null;
        DiaChi = diaChi?.Trim();
        NgaySinh = ngaySinh;
        GioiTinh = gioiTinh?.Trim();
        Username = username?.Trim();
    }

    public void CapNhatAvatar(string avatarUrl)
    {
        AvatarUrl = avatarUrl?.Trim();
    }

    public void CapNhatMaNhanVien(string maNhanVien)
    {
        MaNhanVien = maNhanVien?.Trim();
    }

    public void GanDaiLy(int daiLyId)
    {
        DaiLyId = daiLyId;
    }

    public void XoaDaiLy()
    {
        DaiLyId = null;
    }

    public void CapNhatLastLogin()
    {
        LastLogin = DateTime.UtcNow;
    }

    public void DoiMatKhau(string matKhauMoi)
    {
        if (string.IsNullOrWhiteSpace(matKhauMoi))
            throw new DomainException("Mật khẩu mới không được để trống");

        MatKhau = matKhauMoi; // Should be hashed before calling this method
    }

    public void KichHoat()
    {
        if (TrangThai == TrangThaiNguoiDung.Active)
            throw new BusinessRuleException("NGUOI_DUNG_DA_KICH_HOAT", "Người dùng đã được kích hoạt");

        TrangThai = TrangThaiNguoiDung.Active;
    }

    public void VoHieuHoa()
    {
        if (TrangThai == TrangThaiNguoiDung.Inactive)
            throw new BusinessRuleException("NGUOI_DUNG_DA_VO_HIEU_HOA", "Người dùng đã bị vô hiệu hóa");

        TrangThai = TrangThaiNguoiDung.Inactive;
    }

    public bool CoTheThucHienGiaoDich()
    {
        return TrangThai == TrangThaiNguoiDung.Active;
    }

    /// <summary>
    /// Gán vai trò cho người dùng
    /// </summary>
    public void GanVaiTro(int vaiTroId, DateTime? ngayHetHan = null, string? ghiChu = null)
    {
        if (DanhSachVaiTro.Any(nv => nv.VaiTroId == vaiTroId && nv.ConHieuLuc()))
            throw new BusinessRuleException("VAI_TRO_DA_TON_TAI", "Người dùng đã có vai trò này");

        var nguoiDungVaiTro = NguoiDungVaiTro.Tao(Id, vaiTroId, ngayHetHan, ghiChu);
        DanhSachVaiTro.Add(nguoiDungVaiTro);
    }

    /// <summary>
    /// Xóa vai trò khỏi người dùng
    /// </summary>
    public void XoaVaiTro(int vaiTroId)
    {
        var nguoiDungVaiTro = DanhSachVaiTro.FirstOrDefault(nv => nv.VaiTroId == vaiTroId && nv.ConHieuLuc());
        if (nguoiDungVaiTro == null)
            throw new BusinessRuleException("VAI_TRO_KHONG_TON_TAI", "Người dùng không có vai trò này");

        nguoiDungVaiTro.VoHieuHoa();
    }

    /// <summary>
    /// Lấy danh sách vai trò hiệu lực của người dùng
    /// </summary>
    public IEnumerable<VaiTro> LayDanhSachVaiTroHieuLuc()
    {
        return DanhSachVaiTro
            .Where(nv => nv.ConHieuLuc())
            .Select(nv => nv.VaiTro)
            .Where(vt => vt.TrangThaiHoatDong);
    }

    /// <summary>
    /// Kiểm tra người dùng có vai trò cụ thể không
    /// </summary>
    public bool CoVaiTro(string tenVaiTro)
    {
        return LayDanhSachVaiTroHieuLuc().Any(vt => vt.TenVaiTro.Equals(tenVaiTro, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Lấy tất cả quyền của người dùng từ các vai trò
    /// </summary>
    public IEnumerable<string> LayTatCaQuyen()
    {
        return LayDanhSachVaiTroHieuLuc()
            .SelectMany(vt => vt.DanhSachQuyen)
            .Where(vq => vq.TrangThaiHoatDong && vq.Quyen.TrangThaiHoatDong)
            .Select(vq => vq.Quyen.MaQuyen)
            .Distinct();
    }

    /// <summary>
    /// Kiểm tra người dùng có quyền cụ thể không
    /// </summary>
    public bool CoQuyen(string maQuyen)
    {
        return LayTatCaQuyen().Contains(maQuyen, StringComparer.OrdinalIgnoreCase);
    }
}
