namespace HuyPhuc.Application.Features.BHXH.DTOs;

/// <summary>
/// DTO cho request đăng nhập VNPost
/// </summary>
public class VnPostLoginRequestDto
{
    public string GrantType { get; set; } = "password";
    public string UserName { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string Text { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public bool IsWeb { get; set; } = true;
}

/// <summary>
/// DTO cho response đăng nhập VNPost
/// </summary>
public class VnPostLoginResponseDto
{
    public string AccessToken { get; set; } = string.Empty;
    public string AsClientId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string MangLuoi { get; set; } = string.Empty;
    public string DonViCongTac { get; set; } = string.Empty;
    public string ChucDanh { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? SoDienThoai { get; set; }
    public bool IsSuperAdmin { get; set; }
    public string? Cap { get; set; }
    public int TypeMangLuoi { get; set; }
    public double UserId { get; set; }
    public int Status { get; set; }
    public long ExpiresAt { get; set; }
    public int ExpiresIn { get; set; }
    public string[] Roles { get; set; } = Array.Empty<string>();
}

/// <summary>
/// DTO cho captcha VNPost
/// </summary>
public class VnPostCaptchaResponseDto
{
    public VnPostCaptchaDataDto Data { get; set; } = new();
    public bool Success { get; set; }
    public string? Message { get; set; }
    public object? Errors { get; set; }
    public int Status { get; set; }
    public string? TraceId { get; set; }
}

/// <summary>
/// DTO cho dữ liệu captcha
/// </summary>
public class VnPostCaptchaDataDto
{
    public string Image { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
}

/// <summary>
/// DTO cho request refresh token từ frontend
/// </summary>
public class RefreshTokenRequestDto
{
    public string UserName { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string CaptchaText { get; set; } = string.Empty;
    public string CaptchaCode { get; set; } = string.Empty;
}
