import { patchState, signalStore, withMethods, withState, withComputed } from '@ngrx/signals';
import { computed, inject } from '@angular/core';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, switchMap, tap, catchError, of } from 'rxjs';

import { KeKhaiBase, BaseFilter, PaginationInfo, TrangThaiKeKhai } from '../models';
import { KeKhaiBaseService, DanhSachKeKhaiResponse } from '../services';

/**
 * State interface cho kê khai base store
 */
interface KeKhaiBaseState<T extends KeKhaiBase> {
  danhSachKeKhai: T[];
  keKhaiDangChon: T | null;
  phanTrang: PaginationInfo;
  filter: BaseFilter;
  dangTai: boolean;
  loi: string | null;
}

/**
 * Initial state
 */
const initialState: KeKhaiBaseState<KeKhaiBase> = {
  danhSachKeKhai: [],
  keKhaiDangChon: null,
  phanTrang: {
    trang: 1,
    kichThuoc: 10,
    tongSo: 0,
    tongTrang: 0
  },
  filter: {
    trang: 1,
    kichThuoc: 10
  },
  dangTai: false,
  loi: null
};

/**
 * Factory function để tạo store cho từng loại kê khai
 */
export function createKeKhaiStore<T extends KeKhaiBase>(endpoint: string) {
  return signalStore(
    { providedIn: 'root' },
    withState<KeKhaiBaseState<T>>(initialState as KeKhaiBaseState<T>),
    withComputed((store) => ({
      // Computed signals
      coKeKhai: computed(() => store.danhSachKeKhai().length > 0),
      soKeKhai: computed(() => store.danhSachKeKhai().length),
      keKhaiDangSoan: computed(() => 
        store.danhSachKeKhai().filter(kk => kk.trangThai === TrangThaiKeKhai.DangSoan)
      ),
      keKhaiDaGui: computed(() => 
        store.danhSachKeKhai().filter(kk => kk.trangThai === TrangThaiKeKhai.DaGui)
      ),
      keKhaiDaDuyet: computed(() => 
        store.danhSachKeKhai().filter(kk => kk.trangThai === TrangThaiKeKhai.DaDuyet)
      )
    })),
    withMethods((store, keKhaiService = inject(KeKhaiBaseService)) => ({
      // Actions
      setDangTai: (dangTai: boolean) => patchState(store, { dangTai }),
      setLoi: (loi: string | null) => patchState(store, { loi }),
      setKeKhaiDangChon: (keKhai: T | null) => patchState(store, { keKhaiDangChon: keKhai }),
      setFilter: (filter: Partial<BaseFilter>) => {
        patchState(store, { 
          filter: { ...store.filter(), ...filter }
        });
      },
      
      // Reset state
      reset: () => patchState(store, initialState as KeKhaiBaseState<T>),
      
      // Load danh sách kê khai
      taiDanhSachKeKhai: rxMethod<BaseFilter | void>(
        pipe(
          tap((filter) => {
            const newFilter = filter ? { ...store.filter(), ...filter } : store.filter();
            patchState(store, { 
              dangTai: true, 
              loi: null,
              filter: newFilter
            });
          }),
          switchMap((filter) => {
            const currentFilter = filter ? { ...store.filter(), ...filter } : store.filter();
            return keKhaiService.layDanhSachKeKhai<T>(endpoint, currentFilter).pipe(
              tap((response: DanhSachKeKhaiResponse<T>) => {
                patchState(store, {
                  danhSachKeKhai: response.danhSach,
                  phanTrang: response.phanTrang,
                  dangTai: false
                });
              }),
              catchError((error) => {
                patchState(store, {
                  loi: error.message || 'Có lỗi khi tải danh sách kê khai',
                  dangTai: false
                });
                return of({ danhSach: [], phanTrang: store.phanTrang() });
              })
            );
          })
        )
      ),
      
      // Load chi tiết kê khai
      taiChiTietKeKhai: rxMethod<number>(
        pipe(
          tap(() => patchState(store, { dangTai: true, loi: null })),
          switchMap((id) =>
            keKhaiService.layChiTietKeKhai<T>(endpoint, id).pipe(
              tap((keKhai) => {
                patchState(store, {
                  keKhaiDangChon: keKhai,
                  dangTai: false
                });
              }),
              catchError((error) => {
                patchState(store, {
                  loi: error.message || 'Có lỗi khi tải chi tiết kê khai',
                  dangTai: false
                });
                return of(null);
              })
            )
          )
        )
      ),
      
      // Tạo kê khai mới
      taoKeKhai: rxMethod<Omit<T, 'id' | 'ngayTao' | 'ngayCapNhat'>>(
        pipe(
          tap(() => patchState(store, { dangTai: true, loi: null })),
          switchMap((keKhai) =>
            keKhaiService.taoKeKhai<T>(endpoint, keKhai).pipe(
              tap((keKhaiMoi) => {
                const danhSachHienTai = store.danhSachKeKhai();
                patchState(store, {
                  danhSachKeKhai: [keKhaiMoi, ...danhSachHienTai],
                  keKhaiDangChon: keKhaiMoi,
                  dangTai: false
                });
              }),
              catchError((error) => {
                patchState(store, {
                  loi: error.message || 'Có lỗi khi tạo kê khai',
                  dangTai: false
                });
                return of(null);
              })
            )
          )
        )
      ),
      
      // Cập nhật kê khai
      capNhatKeKhai: rxMethod<{ id: number; data: Partial<T> }>(
        pipe(
          tap(() => patchState(store, { dangTai: true, loi: null })),
          switchMap(({ id, data }) =>
            keKhaiService.capNhatKeKhai<T>(endpoint, id, data).pipe(
              tap((keKhaiCapNhat) => {
                const danhSachHienTai = store.danhSachKeKhai();
                const index = danhSachHienTai.findIndex(kk => kk.id === id);
                if (index !== -1) {
                  const danhSachMoi = [...danhSachHienTai];
                  danhSachMoi[index] = keKhaiCapNhat;
                  patchState(store, {
                    danhSachKeKhai: danhSachMoi,
                    keKhaiDangChon: keKhaiCapNhat,
                    dangTai: false
                  });
                }
              }),
              catchError((error) => {
                patchState(store, {
                  loi: error.message || 'Có lỗi khi cập nhật kê khai',
                  dangTai: false
                });
                return of(null);
              })
            )
          )
        )
      )
    }))
  );
}
