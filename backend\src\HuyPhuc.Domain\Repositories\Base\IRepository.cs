using System.Linq.Expressions;
using HuyPhuc.Domain.Entities.Base;

namespace HuyPhuc.Domain.Repositories.Base;

public interface IRepository<T> where T : BaseEntity
{
    Task<T?> LayTheoIdAsync(int id, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> LayTatCaAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> TimKiemAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<T?> TimKiemDauTienAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<bool> TonTaiAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<int> DemAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default);
    Task ThemAsync(T entity, CancellationToken cancellationToken = default);
    Task ThemNhieuAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    void CapNhat(T entity);
    void CapNhatNhieu(IEnumerable<T> entities);
    void Xoa(T entity);
    void XoaNhieu(IEnumerable<T> entities);
    Task<IEnumerable<T>> LayPhanTrangAsync(
        int trang, 
        int kichThuocTrang, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool tangDan = true,
        CancellationToken cancellationToken = default);
}
