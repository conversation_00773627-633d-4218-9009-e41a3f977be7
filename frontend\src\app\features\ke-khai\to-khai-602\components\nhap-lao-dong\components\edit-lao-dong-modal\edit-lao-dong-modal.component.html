<!-- Modal Overlay -->
<div *ngIf="isModalOpen" 
     class="fixed inset-0 z-50 overflow-y-auto"
     (click)="onCloseModal()">
  
  <!-- Modal Background -->
  <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

    <!-- Modal Content -->
    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
         (click)="$event.stopPropagation()">
      
      <!-- Modal Header -->
      <div class="bg-white px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">
            <svg class="w-5 h-5 inline mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            {{ modalTitle }}
          </h3>
          <button
            type="button"
            (click)="onCloseModal()"
            [disabled]="dangLuu"
            class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition ease-in-out duration-150 disabled:opacity-50">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="bg-white px-6 py-4 max-h-96 overflow-y-auto">
        <form [formGroup]="editForm" class="space-y-6">

          <!-- Loading State -->
          <div *ngIf="dangTai" class="text-center py-8">
            <div class="inline-flex items-center">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
              <span class="text-gray-600">Đang tải dữ liệu...</span>
            </div>
          </div>

          <!-- Form Content -->
          <div *ngIf="!dangTai" class="space-y-6">

            <!-- Thông tin cá nhân -->
            <div class="form-section">
              <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                Thông tin cá nhân
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Mã số BHXH <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    formControlName="maSoBHXH"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    [class.border-red-500]="hasError('maSoBHXH')"
                    placeholder="Nhập mã số BHXH">
                  <div *ngIf="hasError('maSoBHXH')" class="mt-1 text-sm text-red-600">
                    {{ getErrorMessage('maSoBHXH') }}
                  </div>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Họ và tên <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    formControlName="hoTen"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    [class.border-red-500]="hasError('hoTen')"
                    placeholder="Nhập họ và tên">
                  <div *ngIf="hasError('hoTen')" class="mt-1 text-sm text-red-600">
                    {{ getErrorMessage('hoTen') }}
                  </div>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Số CCCD <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    formControlName="soCCCD"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    [class.border-red-500]="hasError('soCCCD')"
                    placeholder="Nhập số CCCD">
                  <div *ngIf="hasError('soCCCD')" class="mt-1 text-sm text-red-600">
                    {{ getErrorMessage('soCCCD') }}
                  </div>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Ngày sinh <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="date"
                    formControlName="ngaySinh"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    [class.border-red-500]="hasError('ngaySinh')">
                  <div *ngIf="hasError('ngaySinh')" class="mt-1 text-sm text-red-600">
                    {{ getErrorMessage('ngaySinh') }}
                  </div>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Giới tính <span class="text-red-500">*</span>
                  </label>
                  <select
                    formControlName="gioiTinh"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    [class.border-red-500]="hasError('gioiTinh')">
                    <option value="">-- Chọn giới tính --</option>
                    <option *ngFor="let item of danhSachGioiTinh" [value]="item.value">
                      {{ item.label }}
                    </option>
                  </select>
                  <div *ngIf="hasError('gioiTinh')" class="mt-1 text-sm text-red-600">
                    {{ getErrorMessage('gioiTinh') }}
                  </div>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Số điện thoại
                  </label>
                  <input
                    type="text"
                    formControlName="soDienThoai"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Nhập số điện thoại">
                </div>

              </div>
            </div>

            <!-- Thông tin BHXH -->
            <div class="form-section">
              <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Thông tin BHXH
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Phương án đóng <span class="text-red-500">*</span>
                  </label>
                  <select
                    formControlName="phuongAn"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option *ngFor="let item of danhSachPhuongAn" [value]="item.value">
                      {{ item.label }}
                    </option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Phương thức đóng <span class="text-red-500">*</span>
                  </label>
                  <select
                    formControlName="phuongThuc"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option *ngFor="let item of danhSachPhuongThuc" [value]="item.value">
                      {{ item.label }}
                    </option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Tháng bắt đầu <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    formControlName="thangBatDau"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    [class.border-red-500]="hasError('thangBatDau')"
                    placeholder="MM/yyyy">
                  <div *ngIf="hasError('thangBatDau')" class="mt-1 text-sm text-red-600">
                    {{ getErrorMessage('thangBatDau') }}
                  </div>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Mức thu nhập <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    formControlName="mucThuNhap"
                    (input)="onMucThuNhapChange()"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    [class.border-red-500]="hasError('mucThuNhap')"
                    placeholder="Nhập mức thu nhập">
                  <div *ngIf="hasError('mucThuNhap')" class="mt-1 text-sm text-red-600">
                    {{ getErrorMessage('mucThuNhap') }}
                  </div>
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Tỷ lệ đóng (%)
                  </label>
                  <input
                    type="number"
                    formControlName="tyLeDong"
                    (input)="onTyLeDongChange()"
                    min="0"
                    max="100"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="22">
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Số tháng
                  </label>
                  <input
                    type="number"
                    formControlName="soThang"
                    (input)="onSoThangChange()"
                    min="1"
                    max="12"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="1">
                </div>

              </div>
            </div>

            <!-- Thông tin tiền -->
            <div class="form-section">
              <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                Thông tin tiền
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Tiền lãi
                  </label>
                  <input
                    type="number"
                    formControlName="tienLai"
                    (input)="onTienChange()"
                    min="0"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="0">
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Tiền thừa
                  </label>
                  <input
                    type="number"
                    formControlName="tienThua"
                    (input)="onTienChange()"
                    min="0"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="0">
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Tiền tự đóng
                  </label>
                  <input
                    type="number"
                    formControlName="tienTuDong"
                    readonly
                    class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                    placeholder="Tự động tính">
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Tổng tiền
                  </label>
                  <input
                    type="number"
                    formControlName="tongTien"
                    readonly
                    class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 font-medium"
                    placeholder="Tự động tính">
                </div>

                <div class="form-group">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    NSNN hỗ trợ
                  </label>
                  <input
                    type="number"
                    formControlName="tienHoTro"
                    min="0"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="0">
                </div>

              </div>
            </div>

          </div>

        </form>
      </div>

      <!-- Modal Footer -->
      <div class="bg-gray-50 px-6 py-3 flex flex-col sm:flex-row gap-3 justify-end">
        <button
          type="button"
          (click)="onReset()"
          [disabled]="dangLuu"
          class="w-full sm:w-auto px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Đặt lại
        </button>
        
        <button
          type="button"
          (click)="onCloseModal()"
          [disabled]="dangLuu"
          class="w-full sm:w-auto px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          Hủy
        </button>
        
        <button
          type="button"
          (click)="onSave()"
          [disabled]="!canSave"
          class="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          <svg *ngIf="!dangLuu" class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <svg *ngIf="dangLuu" class="animate-spin w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ dangLuu ? 'Đang lưu...' : 'Lưu thay đổi' }}
        </button>
      </div>

    </div>
  </div>
</div>
