﻿// <auto-generated />
using System;
using HuyPhuc.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HuyPhuc.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250723062356_InitialMigrationFromSupabase")]
    partial class InitialMigrationFromSupabase
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("HuyPhuc.Domain.Entities.ChiTietDonHang", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("DonHangId")
                        .HasColumnType("integer");

                    b.Property<decimal>("GiaBan")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SanPhamId")
                        .HasColumnType("integer");

                    b.Property<int>("SoLuong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<decimal>("ThanhTien")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.HasKey("Id");

                    b.HasIndex("DonHangId")
                        .HasDatabaseName("IX_ChiTietDonHang_DonHangId");

                    b.HasIndex("SanPhamId")
                        .HasDatabaseName("IX_ChiTietDonHang_SanPhamId");

                    b.HasIndex("DonHangId", "SanPhamId")
                        .IsUnique()
                        .HasDatabaseName("IX_ChiTietDonHang_DonHangId_SanPhamId");

                    b.ToTable("ChiTietDonHang", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DonHang", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("MaDonHang")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("NgayDatHang")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("NgayGiaoHang")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("NguoiDungId")
                        .HasColumnType("integer");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("PhiVanChuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("TongGiaTri")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("TongThanhToan")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<int>("TrangThai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.HasKey("Id");

                    b.HasIndex("MaDonHang")
                        .IsUnique()
                        .HasDatabaseName("IX_DonHang_MaDonHang");

                    b.HasIndex("NgayDatHang")
                        .HasDatabaseName("IX_DonHang_NgayDatHang");

                    b.HasIndex("NguoiDungId")
                        .HasDatabaseName("IX_DonHang_NguoiDungId");

                    b.HasIndex("TrangThai")
                        .HasDatabaseName("IX_DonHang_TrangThai");

                    b.ToTable("DonHang", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.NguoiDung", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AvatarUrl")
                        .HasColumnType("text")
                        .HasColumnName("avatar_url");

                    b.Property<string>("DiaChi")
                        .HasColumnType("text")
                        .HasColumnName("dia_chi");

                    b.Property<string>("GioiTinh")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("gioi_tinh");

                    b.Property<string>("HoTen")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("ho_ten");

                    b.Property<DateTime?>("LastLogin")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_login");

                    b.Property<string>("MaNhanVien")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("ma_nhan_vien");

                    b.Property<string>("MatKhau")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("mat_khau");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<DateTime?>("NgaySinh")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_sinh");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("TrangThai")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasDefaultValue("active")
                        .HasColumnName("trang_thai");

                    b.Property<string>("Username")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("username");

                    b.HasKey("Id");

                    b.ToTable("dm_nguoi_dung", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.SanPham", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("DangKinhDoanh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<decimal>("GiaBan")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("GiaGoc")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("HinhAnh")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("LoaiSanPham")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(99);

                    b.Property<string>("MoTa")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("SoLuongTon")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("TenSanPham")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("DangKinhDoanh")
                        .HasDatabaseName("IX_SanPham_DangKinhDoanh");

                    b.HasIndex("LoaiSanPham")
                        .HasDatabaseName("IX_SanPham_LoaiSanPham");

                    b.HasIndex("TenSanPham")
                        .HasDatabaseName("IX_SanPham_TenSanPham");

                    b.ToTable("SanPham", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.ChiTietDonHang", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.DonHang", "DonHang")
                        .WithMany("DanhSachChiTiet")
                        .HasForeignKey("DonHangId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HuyPhuc.Domain.Entities.SanPham", "SanPham")
                        .WithMany("DanhSachChiTietDonHang")
                        .HasForeignKey("SanPhamId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DonHang");

                    b.Navigation("SanPham");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DonHang", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.NguoiDung", "NguoiDung")
                        .WithMany("DanhSachDonHang")
                        .HasForeignKey("NguoiDungId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("NguoiDung");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.NguoiDung", b =>
                {
                    b.OwnsOne("HuyPhuc.Domain.ValueObjects.Email", "Email", b1 =>
                        {
                            b1.Property<int>("NguoiDungId")
                                .HasColumnType("integer");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)")
                                .HasColumnName("email");

                            b1.HasKey("NguoiDungId");

                            b1.ToTable("dm_nguoi_dung");

                            b1.WithOwner()
                                .HasForeignKey("NguoiDungId");
                        });

                    b.OwnsOne("HuyPhuc.Domain.ValueObjects.SoDienThoai", "SoDienThoai", b1 =>
                        {
                            b1.Property<int>("NguoiDungId")
                                .HasColumnType("integer");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("so_dien_thoai");

                            b1.HasKey("NguoiDungId");

                            b1.ToTable("dm_nguoi_dung");

                            b1.WithOwner()
                                .HasForeignKey("NguoiDungId");
                        });

                    b.Navigation("Email")
                        .IsRequired();

                    b.Navigation("SoDienThoai");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DonHang", b =>
                {
                    b.Navigation("DanhSachChiTiet");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.NguoiDung", b =>
                {
                    b.Navigation("DanhSachDonHang");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.SanPham", b =>
                {
                    b.Navigation("DanhSachChiTietDonHang");
                });
#pragma warning restore 612, 618
        }
    }
}
