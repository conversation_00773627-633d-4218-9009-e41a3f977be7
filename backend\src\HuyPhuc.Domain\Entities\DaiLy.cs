using HuyPhuc.Domain.Entities.Base;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity đại diện cho đại lý
/// </summary>
public class DaiLy : BaseEntity, IAuditableEntity
{
    /// <summary>
    /// Mã đại lý (unique)
    /// </summary>
    public string MaDaiLy { get; private set; } = string.Empty;

    /// <summary>
    /// Tên đại lý
    /// </summary>
    public string TenDaiLy { get; private set; } = string.Empty;

    /// <summary>
    /// Địa chỉ đại lý
    /// </summary>
    public string? Dia<PERSON>hi { get; private set; }

    /// <summary>
    /// Số điện thoại
    /// </summary>
    public string? SoDienThoai { get; private set; }

    /// <summary>
    /// Email
    /// </summary>
    public string? Email { get; private set; }

    /// <summary>
    /// Mã số thuế
    /// </summary>
    public string? MaSoThue { get; private set; }

    /// <summary>
    /// Người đại diện
    /// </summary>
    public string? NguoiDaiDien { get; private set; }

    /// <summary>
    /// Trạng thái hoạt động
    /// </summary>
    public bool TrangThaiHoatDong { get; private set; } = true;

    /// <summary>
    /// Ghi chú
    /// </summary>
    public string? GhiChu { get; private set; }

    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    // Navigation properties
    /// <summary>
    /// Danh sách đơn vị thuộc đại lý
    /// </summary>
    public virtual ICollection<DonVi> DanhSachDonVi { get; private set; } = new List<DonVi>();

    /// <summary>
    /// Danh sách người dùng thuộc đại lý
    /// </summary>
    public virtual ICollection<NguoiDung> DanhSachNguoiDung { get; private set; } = new List<NguoiDung>();

    private DaiLy() { } // EF Core constructor

    public static DaiLy Tao(string maDaiLy, string tenDaiLy, string? diaChi = null, 
        string? soDienThoai = null, string? email = null, string? maSoThue = null, 
        string? nguoiDaiDien = null, string? ghiChu = null)
    {
        if (string.IsNullOrWhiteSpace(maDaiLy))
            throw new ArgumentException("Mã đại lý không được để trống", nameof(maDaiLy));

        if (string.IsNullOrWhiteSpace(tenDaiLy))
            throw new ArgumentException("Tên đại lý không được để trống", nameof(tenDaiLy));

        return new DaiLy
        {
            MaDaiLy = maDaiLy.Trim(),
            TenDaiLy = tenDaiLy.Trim(),
            DiaChi = diaChi?.Trim(),
            SoDienThoai = soDienThoai?.Trim(),
            Email = email?.Trim(),
            MaSoThue = maSoThue?.Trim(),
            NguoiDaiDien = nguoiDaiDien?.Trim(),
            GhiChu = ghiChu?.Trim(),
            TrangThaiHoatDong = true
        };
    }

    public void CapNhatThongTin(string tenDaiLy, string? diaChi = null, 
        string? soDienThoai = null, string? email = null, string? maSoThue = null, 
        string? nguoiDaiDien = null, string? ghiChu = null)
    {
        if (string.IsNullOrWhiteSpace(tenDaiLy))
            throw new ArgumentException("Tên đại lý không được để trống", nameof(tenDaiLy));

        TenDaiLy = tenDaiLy.Trim();
        DiaChi = diaChi?.Trim();
        SoDienThoai = soDienThoai?.Trim();
        Email = email?.Trim();
        MaSoThue = maSoThue?.Trim();
        NguoiDaiDien = nguoiDaiDien?.Trim();
        GhiChu = ghiChu?.Trim();
    }

    public void KichHoat()
    {
        TrangThaiHoatDong = true;
    }

    public void VoHieuHoa()
    {
        TrangThaiHoatDong = false;
    }

    public void ThemDonVi(DonVi donVi)
    {
        if (DanhSachDonVi.Any(dv => dv.MaDonVi == donVi.MaDonVi))
            throw new InvalidOperationException($"Đơn vị với mã {donVi.MaDonVi} đã tồn tại trong đại lý");

        DanhSachDonVi.Add(donVi);
    }

    public void XoaDonVi(int donViId)
    {
        var donVi = DanhSachDonVi.FirstOrDefault(dv => dv.Id == donViId);
        if (donVi != null)
        {
            DanhSachDonVi.Remove(donVi);
        }
    }

    public IEnumerable<DonVi> LayDanhSachDonViHoatDong()
    {
        return DanhSachDonVi.Where(dv => dv.TrangThaiHoatDong);
    }
}
