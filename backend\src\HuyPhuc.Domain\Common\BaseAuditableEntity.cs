using HuyPhuc.Domain.Entities.Base;

namespace HuyPhuc.Domain.Common;

/// <summary>
/// Base class cho các entity có audit fields
/// </summary>
public abstract class BaseAuditableEntity : BaseEntity, IAuditableEntity
{
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    // Additional audit fields for new system
    public DateTime Created { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime? LastModified { get; set; }
    public string? LastModifiedBy { get; set; }
}
