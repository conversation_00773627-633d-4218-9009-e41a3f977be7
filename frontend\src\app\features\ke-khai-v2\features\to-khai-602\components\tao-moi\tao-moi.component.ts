import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { ToKhai602Store } from '../../stores';
import { ToKhai602Service } from '../../services';
import { DaiLyStore, DonViStore } from '../../../../core/stores';
import { DaiLySelectComponent, DonViSelectComponent, LoadingSpinnerComponent } from '../../../../shared/components';
import { CommonValidators } from '../../../../shared/validators';
import { LoaiKeKhai, TrangThaiKeKhai } from '../../../../core/models';

/**
 * Component tạo mới tờ khai 602
 */
@Component({
  selector: 'app-tao-moi',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DaiLySelectComponent,
    DonViSelectComponent,
    LoadingSpinnerComponent
  ],
  template: `
    <div class="tao-moi-to-khai-602">
      <!-- Breadcrumb -->
      <nav class="flex mb-4" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
          <li class="inline-flex items-center">
            <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
              </svg>
              Trang chủ
            </a>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <a (click)="quayLai()" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 cursor-pointer md:ml-2">Kê khai</a>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
              <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Tạo mới</span>
            </div>
          </li>
        </ol>
      </nav>

      <!-- Header -->
      <div class="flex justify-between items-center mb-4 p-4 border-b border-gray-200 bg-white rounded-lg shadow-sm">
        <div>
          <h1 class="text-xl font-bold text-gray-900">Tạo mới Tờ khai 602</h1>
          <p class="text-gray-600 text-sm mt-1">Kê khai đóng BHXH cho lao động tự do</p>
        </div>
        <div class="flex space-x-3">
          <button
            type="button"
            (click)="quayLai()"
            class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium transition-colors duration-200"
          >
            Quay lại
          </button>
        </div>
      </div>

      <!-- Form -->
      <div class="space-y-4">
        <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-4">
          <!-- Thông tin cơ bản -->
          <div class="bg-white shadow-sm rounded-lg p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Thông tin cơ bản</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Đại lý -->
              <div>
                <app-dai-ly-select
                  label="Đại lý"
                  placeholder="Chọn đại lý"
                  [required]="true"
                  formControlName="daiLyId"
                  [errorMessage]="getFieldError('daiLyId')"
                  (daiLyChange)="onDaiLyChange($event)"
                ></app-dai-ly-select>
              </div>

              <!-- Đơn vị -->
              <div>
                <app-don-vi-select
                  label="Đơn vị"
                  placeholder="Chọn đơn vị"
                  [required]="true"
                  [daiLyId]="form.get('daiLyId')?.value"
                  formControlName="donViId"
                  [errorMessage]="getFieldError('donViId')"
                ></app-don-vi-select>
              </div>

              <!-- Số sổ BHXH -->
              <div>
                <label for="soSoBHXH" class="block text-sm font-medium text-gray-700 mb-1">
                  Số sổ BHXH <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="soSoBHXH"
                  formControlName="soSoBHXH"
                  placeholder="Nhập số sổ BHXH"
                  [class]="getInputClasses('soSoBHXH')"
                >
                <p *ngIf="getFieldError('soSoBHXH')" class="mt-1 text-sm text-red-600">
                  {{ getFieldError('soSoBHXH') }}
                </p>
              </div>

              <!-- Ghi chú -->
              <div>
                <label for="ghiChu" class="block text-sm font-medium text-gray-700 mb-1">
                  Ghi chú
                </label>
                <textarea
                  id="ghiChu"
                  formControlName="ghiChu"
                  rows="2"
                  placeholder="Nhập ghi chú (tùy chọn)"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Danh sách lao động -->
          <div class="bg-white shadow-sm rounded-lg p-4">
            <div class="flex justify-between items-center mb-3">
              <h3 class="text-lg font-medium text-gray-900">Danh sách lao động</h3>
              <button
                type="button"
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200"
                [disabled]="!form.get('daiLyId')?.value || !form.get('donViId')?.value"
              >
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Thêm lao động
              </button>
            </div>

            <!-- Empty state -->
            <div class="text-center py-6 border-2 border-dashed border-gray-300 rounded-lg">
              <svg class="mx-auto h-10 w-10 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
              <h4 class="text-base font-medium text-gray-900 mb-2">Chưa có lao động nào</h4>
              <p class="text-gray-500 text-sm">Vui lòng chọn đại lý và đơn vị trước khi thêm lao động.</p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 bg-white rounded-lg shadow-sm p-4">
            <button
              type="button"
              (click)="luuNhap()"
              [disabled]="toKhaiStore.dangLuu() || !form.valid"
              class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <app-loading-spinner 
                *ngIf="toKhaiStore.dangLuu()" 
                size="sm" 
                color="text-white"
                class="mr-2"
              ></app-loading-spinner>
              Lưu nháp
            </button>
            
            <button
              type="submit"
              [disabled]="toKhaiStore.dangLuu() || !form.valid"
              class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <app-loading-spinner 
                *ngIf="toKhaiStore.dangLuu()" 
                size="sm" 
                color="text-white"
                class="mr-2"
              ></app-loading-spinner>
              Tạo tờ khai
            </button>
          </div>
        </form>
      </div>
    </div>
  `,
  styles: [`
    .tao-moi-to-khai-602 {
      @apply min-h-full;
    }

    /* Tối ưu hóa spacing để giảm khoảng trống */
    .tao-moi-to-khai-602 .bg-white {
      @apply shadow-sm;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .tao-moi-to-khai-602 .grid {
        @apply grid-cols-1 gap-3;
      }

      .tao-moi-to-khai-602 .p-4 {
        @apply p-3;
      }

      .tao-moi-to-khai-602 .space-y-4 > * + * {
        @apply mt-3;
      }
    }

    /* Compact breadcrumb */
    .tao-moi-to-khai-602 nav {
      @apply text-sm;
    }
  `]
})
export class TaoMoiComponent implements OnInit {
  // Inject dependencies
  private readonly fb = inject(FormBuilder);
  private readonly router = inject(Router);

  // Inject stores and services
  readonly toKhaiStore = inject(ToKhai602Store);
  readonly toKhaiService = inject(ToKhai602Service);
  readonly daiLyStore = inject(DaiLyStore);
  readonly donViStore = inject(DonViStore);

  // Form
  form!: FormGroup;

  ngOnInit() {
    this.initForm();
    this.loadInitialData();
  }

  private initForm() {
    this.form = this.fb.group({
      daiLyId: [null, [Validators.required]],
      donViId: [null, [Validators.required]],
      soSoBHXH: ['', [Validators.required, Validators.minLength(1)]],
      ghiChu: ['']
    });
  }

  private loadInitialData() {
    this.daiLyStore.taiDaiLyOptions();
  }

  onDaiLyChange(daiLyId: number | null) {
    // Reset đơn vị khi đổi đại lý
    this.form.patchValue({ donViId: null });
    
    if (daiLyId) {
      this.donViStore.taiDonViOptionsTheoDaiLy(daiLyId);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field && field.invalid && (field.dirty || field.touched)) {
      if (field.errors?.['required']) {
        return 'Trường này là bắt buộc';
      }
      if (field.errors?.['minlength']) {
        return 'Giá trị quá ngắn';
      }
      // Add more validation messages as needed
    }
    return '';
  }

  getInputClasses(fieldName: string): string {
    const field = this.form.get(fieldName);
    const baseClasses = 'w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500';
    const errorClasses = field && field.invalid && (field.dirty || field.touched) 
      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
      : 'border-gray-300';
    
    return `${baseClasses} ${errorClasses}`;
  }

  luuNhap() {
    if (this.form.valid) {
      const formValue = this.form.value;
      const toKhai = {
        daiLyId: formValue.daiLyId,
        donViId: formValue.donViId,
        loaiKeKhai: LoaiKeKhai.ToKhai602 as const,
        trangThai: TrangThaiKeKhai.DangSoan,
        thongTinKhac: {
          soSoBHXH: formValue.soSoBHXH,
          ghiChu: formValue.ghiChu
        },
        danhSachLaoDong: []
      };

      // TODO: Implement save draft
      console.log('Lưu nháp:', toKhai);
    }
  }

  onSubmit() {
    if (this.form.valid) {
      const formValue = this.form.value;
      const toKhai = {
        daiLyId: formValue.daiLyId,
        donViId: formValue.donViId,
        loaiKeKhai: LoaiKeKhai.ToKhai602 as const,
        trangThai: TrangThaiKeKhai.DangSoan,
        thongTinKhac: {
          soSoBHXH: formValue.soSoBHXH,
          ghiChu: formValue.ghiChu
        },
        danhSachLaoDong: []
      };

      // Sử dụng service để tạo tờ khai và lấy kết quả
      this.toKhaiService.taoToKhai(toKhai).subscribe({
        next: (result) => {
          // Cập nhật store với tờ khai mới
          this.toKhaiStore.taoToKhai(toKhai);

          // Chuyển đến trang chi tiết để nhập thông tin lao động
          if (result && result.id) {
            this.router.navigate(['/ke-khai/to-khai-602/chi-tiet', result.id]);
          } else {
            // Fallback về trang quản lý nếu không có ID
            this.router.navigate(['/ke-khai/to-khai-602']);
          }
        },
        error: (error) => {
          console.error('Lỗi khi tạo tờ khai:', error);
          // Có thể hiển thị thông báo lỗi ở đây
        }
      });
    }
  }

  quayLai() {
    this.router.navigate(['/ke-khai/to-khai-602']);
  }
}
