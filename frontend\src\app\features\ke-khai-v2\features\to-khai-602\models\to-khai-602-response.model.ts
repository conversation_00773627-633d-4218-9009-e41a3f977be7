import { ToKhai602 } from './to-khai-602.model';
import { PaginationInfo } from '../../../core/models';

/**
 * Response models cho tờ khai 602
 */

/**
 * Response danh sách tờ khai 602
 */
export interface DanhSachToKhai602Response {
  danhSach: ToKhai602[];
  phanTrang: PaginationInfo;
}

/**
 * Response tạo tờ khai 602
 */
export interface TaoToKhai602Response {
  toKhai: ToKhai602;
  message: string;
}

/**
 * Response cập nhật tờ khai 602
 */
export interface CapNhatToKhai602Response {
  toKhai: ToKhai602;
  message: string;
}

/**
 * Response validate tờ khai 602
 */
export interface ValidateToKhai602Response {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

/**
 * Interface cho validation error
 */
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

/**
 * Interface cho validation warning
 */
export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
  value?: any;
}
