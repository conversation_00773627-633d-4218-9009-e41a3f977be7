using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories;
using HuyPhuc.Infrastructure.Repositories.Base;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Infrastructure.Repositories;

/// <summary>
/// Repository implementation cho DmTinh
/// </summary>
public class DmTinhRepository : Repository<DmTinh>, IDmTinhRepository
{
    public DmTinhRepository(IApplicationDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Lấy tất cả tỉnh/thành phố
    /// </summary>
    public async Task<List<DmTinh>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmTinh>()
            .OrderBy(t => t.MaTinh)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Lấy tỉnh theo mã tỉnh
    /// </summary>
    public async Task<DmTinh?> GetByMaTinhAsync(string maTinh, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmTinh>()
            .FirstOrDefaultAsync(t => t.MaTinh == maTinh, cancellationToken);
    }

    /// <summary>
    /// Kiểm tra mã tỉnh có tồn tại không
    /// </summary>
    public async Task<bool> ExistsByMaTinhAsync(string maTinh, CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<DmTinh>()
            .AnyAsync(t => t.MaTinh == maTinh, cancellationToken);
    }
}
