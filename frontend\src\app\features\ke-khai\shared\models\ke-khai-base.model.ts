/**
 * Base models cho tất cả các loại kê khai
 */

/**
 * Base interface cho tất cả các loại kê khai
 */
export interface KeKhaiBase {
  id?: number;
  daiLyId: number;
  donViId: number;
  loaiKeKhai: LoaiKeKhai;
  trangThai: TrangThaiKeKhai;
  ngayTao?: Date;
  ngayCapNhat?: Date;
  nguoiTao?: string;
  nguoiCapNhat?: string;
}

/**
 * Enum loại kê khai
 */
export enum LoaiKeKhai {
  ToKhai602 = 'to_khai_602',
  ToKhai603 = 'to_khai_603'
}

/**
 * Enum trạng thái kê khai
 */
export enum TrangThaiKeKhai {
  DangSoan = 'dang_soan',
  DaGui = 'da_gui',
  DaDuyet = 'da_duyet',
  BiTuChoi = 'bi_tu_choi'
}

/**
 * Interface cho thông tin đại lý
 */
export interface DaiLyInfo {
  id: number;
  maDaiLy: string;
  tenDaiLy: string;
  diaChi?: string;
  soDienThoai?: string;
  email?: string;
}

/**
 * Interface cho thông tin đơn vị
 */
export interface DonViInfo {
  id: number;
  maDonVi: string;
  tenDonVi: string;
  diaChi?: string;
  daiLyId: number;
}
