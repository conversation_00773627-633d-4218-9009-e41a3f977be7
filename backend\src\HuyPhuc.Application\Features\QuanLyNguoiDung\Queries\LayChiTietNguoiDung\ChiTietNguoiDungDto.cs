using HuyPhuc.Application.Common.Models;
using HuyPhuc.Domain.Enums;

namespace HuyPhuc.Application.Features.QuanLyNguoiDung.Queries.LayChiTietNguoiDung;

public class ChiTietNguoiDungDto : BaseDto
{
    public string HoTen { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? SoDienThoai { get; set; }
    public string? DiaChiChiTiet { get; set; }
    public string? Phuong { get; set; }
    public string? Quan { get; set; }
    public string? ThanhPho { get; set; }
    public TrangThaiNguoiDung TrangThai { get; set; }
    public string TenTrangThai => TrangThai switch
    {
        TrangThaiNguoiDung.Active => "Kích hoạt",
        TrangThaiNguoiDung.Inactive => "Vô hiệu hóa",
        _ => "Không xác định"
    };
    public int TongSoDonHang { get; set; }
    public decimal TongGiaTriMuaHang { get; set; }
}
