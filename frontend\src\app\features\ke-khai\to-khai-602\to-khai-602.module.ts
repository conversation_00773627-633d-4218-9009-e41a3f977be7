import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';

import { ToKhai602RoutingModule } from './to-khai-602-routing.module';

// Components
import { QuanLyToKhai602Component } from './components/quan-ly-to-khai-602/quan-ly-to-khai-602.component';
import { KhaiBaoToKhai602Component } from './components/khai-bao-to-khai-602/khai-bao-to-khai-602.component';
import { NhapLaoDongComponent } from './components/nhap-lao-dong/components/nhap-lao-dong';
import { DanhSachLaoDongComponent } from './components/danh-sach-lao-dong/danh-sach-lao-dong.component';

// Shared Components
import { TabsComponent } from '../../../shared/components/tabs/tabs.component';

// Services
import { DaiLyService, ToKhai602Service } from './services';

/**
 * Feature module cho tờ khai 602
 * Bao gồm khai báo, nhập thông tin lao động, quản lý tờ khai với tab navigation
 */
@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    HttpClientModule,
    ToKhai602RoutingModule,

    // Standalone components
    QuanLyToKhai602Component,
    KhaiBaoToKhai602Component,
    NhapLaoDongComponent,
    DanhSachLaoDongComponent,

    // Shared components
    TabsComponent
  ],
  providers: [
    DaiLyService,
    ToKhai602Service
  ]
})
export class ToKhai602Module { }
