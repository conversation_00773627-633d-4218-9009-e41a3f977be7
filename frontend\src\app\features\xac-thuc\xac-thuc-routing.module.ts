import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { DangNhapComponent } from './components/dang-nhap/dang-nhap.component';
import { ChuaDangNhapGuard } from './guards';

/**
 * Routing configuration cho feature x<PERSON>c thực
 * Bao gồm các routes cho đăng nhập, đăng ký, quên mật khẩu
 */
const routes: Routes = [
  {
    path: '',
    redirectTo: 'dang-nhap',
    pathMatch: 'full'
  },
  {
    path: 'dang-nhap',
    component: DangNhapComponent,
    canActivate: [ChuaDangNhapGuard],
    title: '<PERSON>ăng nhập - Hệ thống quản lý',
    data: {
      breadcrumb: 'Đăng nhập',
      description: 'Trang đăng nhập vào hệ thống'
    }
  }
  // TODO: Thêm các routes khác
  // {
  //   path: 'dang-ky',
  //   component: DangKyComponent,
  //   title: '<PERSON><PERSON><PERSON> ký - <PERSON><PERSON> thống quản lý'
  // },
  // {
  //   path: 'quen-mat-khau',
  //   component: QuenMatKhauComponent,
  //   title: 'Quên mật khẩu - Hệ thống quản lý'
  // },
  // {
  //   path: 'dat-lai-mat-khau',
  //   component: DatLaiMatKhauComponent,
  //   title: 'Đặt lại mật khẩu - Hệ thống quản lý'
  // }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class XacThucRoutingModule { }
