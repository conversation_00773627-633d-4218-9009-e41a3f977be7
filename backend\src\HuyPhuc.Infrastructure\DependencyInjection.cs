using HuyPhuc.Application.Common.Configurations;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Features.BHXH.Interfaces;
using HuyPhuc.Domain.Repositories;
using HuyPhuc.Domain.Services;
using HuyPhuc.Infrastructure.Data;
using HuyPhuc.Infrastructure.Data.Interceptors;
using HuyPhuc.Infrastructure.Repositories;
using HuyPhuc.Infrastructure.Services;
using HuyPhuc.Infrastructure.Settings;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace HuyPhuc.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Database
        var connectionString = configuration.GetConnectionString("DefaultConnection")
            ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseNpgsql(connectionString));

        services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Interceptors
        services.AddScoped<AuditableEntityInterceptor>();
        services.AddScoped<DomainEventInterceptor>();

        // JWT Settings
        services.Configure<JwtSettings>(configuration.GetSection(JwtSettings.SectionName));

        // VNPost API Configuration
        services.Configure<VnPostApiConfiguration>(configuration.GetSection(VnPostApiConfiguration.SectionName));

        // HTTP Clients
        services.AddHttpClient<ISupabaseApiClient, SupabaseApiClient>();
        services.AddHttpClient<IBhxhLookupService, BhxhLookupService>();
        services.AddHttpClient<IVnPostAuthService, VnPostAuthService>();

        // Repositories
        services.AddScoped<INguoiDungRepository, NguoiDungRepository>();
        services.AddScoped<IRefreshTokenRepository, RefreshTokenRepository>();
        services.AddScoped<IDanhMucThuTucRepository, DanhMucThuTucRepository>();

        // Địa chỉ repositories
        services.AddScoped<IDmTinhRepository, DmTinhRepository>();
        services.AddScoped<IDmHuyenRepository, DmHuyenRepository>();
        services.AddScoped<IDmXaRepository, DmXaRepository>();
        services.AddScoped<IDmLoaiRepository, DmLoaiRepository>();

        // Services
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        services.AddScoped<IEmailService, EmailService>();
        services.AddScoped<IDomainEventService, DomainEventService>();
        services.AddScoped<IJwtService, JwtService>();
        services.AddScoped<IPasswordHashingService, PasswordHashingService>();
        services.AddScoped<IBhxhLookupService, BhxhLookupService>();
        services.AddScoped<ITk1TsUpdateService, Tk1TsUpdateService>();
        services.AddScoped<IVnPostAuthService, VnPostAuthService>();

        // HTTP Context
        services.AddHttpContextAccessor();

        return services;
    }
}
