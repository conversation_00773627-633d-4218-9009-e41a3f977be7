import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

/**
 * Routing configuration cho feature kê khai
 * Quản lý routing cho tất cả các loại tờ khai
 */
const routes: Routes = [
  {
    path: '',
    redirectTo: 'to-khai-602',
    pathMatch: 'full'
  },
  // Tờ khai 602 - Đóng BHXH cho lao động tự do
  {
    path: 'to-khai-602',
    loadChildren: () => import('./to-khai-602/to-khai-602.module').then(m => m.ToKhai602Module),
    data: {
      breadcrumb: 'Tờ khai 602',
      description: 'Kê khai đóng BHXH cho lao động tự do'
    }
  }
  // TODO: Thêm các tờ khai khác
  // {
  //   path: 'to-khai-603',
  //   loadChildren: () => import('./to-khai-603/to-khai-603.module').then(m => m.ToKhai603Module),
  //   data: {
  //     breadcrumb: 'Tờ khai 603',
  //     description: 'Kê khai điều chỉnh BHXH'
  //   }
  // }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class KeKhaiRoutingModule { }
