import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { KeKhaiBase, DaiLyInfo, DonViInfo, ApiResponse } from '../models';
import { environment } from '../../../../../environments/environment';

/**
 * Base service cho tất cả các loại kê khai
 * Cung cấp các method chung như lấy danh sách đại lý, đơn vị, v.v.
 */
@Injectable({
  providedIn: 'root'
})
export class KeKhaiBaseService {
  private readonly apiUrl = `${environment.apiUrl}/api`;

  constructor(private http: HttpClient) {}

  /**
   * Lấy danh sách đại lý
   */
  layDanhSachDaiLy(): Observable<ApiResponse<DaiLyInfo[]>> {
    return this.http.get<ApiResponse<DaiLyInfo[]>>(`${this.apiUrl}/dai-ly`);
  }

  /**
   * L<PERSON>y danh sách đơn vị theo đại lý
   */
  layDanhSachDonViTheoDaiLy(daiLyId: number): Observable<ApiResponse<DonViInfo[]>> {
    return this.http.get<ApiResponse<DonViInfo[]>>(`${this.apiUrl}/dai-ly/${daiLyId}/don-vi`);
  }

  /**
   * Lấy thông tin đại lý theo ID
   */
  layThongTinDaiLy(daiLyId: number): Observable<ApiResponse<DaiLyInfo>> {
    return this.http.get<ApiResponse<DaiLyInfo>>(`${this.apiUrl}/dai-ly/${daiLyId}`);
  }

  /**
   * Lấy thông tin đơn vị theo ID
   */
  layThongTinDonVi(donViId: number): Observable<ApiResponse<DonViInfo>> {
    return this.http.get<ApiResponse<DonViInfo>>(`${this.apiUrl}/don-vi/${donViId}`);
  }
}
