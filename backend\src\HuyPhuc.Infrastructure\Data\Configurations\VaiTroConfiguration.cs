using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class VaiTroConfiguration : IEntityTypeConfiguration<VaiTro>
{
    public void Configure(EntityTypeBuilder<VaiTro> builder)
    {
        builder.ToTable("vai_tro");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).HasColumnName("id");

        builder.Property(x => x.TenVaiTro)
            .IsRequired()
            .HasColumnName("ten_vai_tro")
            .HasMaxLength(100);

        builder.Property(x => x.MoTa)
            .HasColumnName("mo_ta")
            .HasMaxLength(500);

        builder.Property(x => x.LaVaiTroHeThong)
            .IsRequired()
            .HasColumnName("la_vai_tro_he_thong")
            .HasDefaultValue(false);

        builder.Property(x => x.TrangThaiHoatDong)
            .IsRequired()
            .HasColumnName("trang_thai_hoat_dong")
            .HasDefaultValue(true);

        // Audit fields
        builder.Property(x => x.NgayTao)
            .IsRequired()
            .HasColumnName("ngay_tao");

        builder.Property(x => x.NgayCapNhat)
            .HasColumnName("ngay_cap_nhat");

        builder.Property(x => x.NguoiTao)
            .HasColumnName("nguoi_tao")
            .HasMaxLength(50);

        builder.Property(x => x.NguoiCapNhat)
            .HasColumnName("nguoi_cap_nhat")
            .HasMaxLength(50);

        // Indexes
        builder.HasIndex(x => x.TenVaiTro)
            .IsUnique()
            .HasDatabaseName("ix_vai_tro_ten_vai_tro");

        builder.HasIndex(x => x.TrangThaiHoatDong)
            .HasDatabaseName("ix_vai_tro_trang_thai_hoat_dong");

        // Relationships
        builder.HasMany(x => x.DanhSachNguoiDung)
            .WithOne(x => x.VaiTro)
            .HasForeignKey(x => x.VaiTroId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(x => x.DanhSachQuyen)
            .WithOne(x => x.VaiTro)
            .HasForeignKey(x => x.VaiTroId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(x => x.DomainEvents);
    }
}
