using HuyPhuc.Domain.Events.Base;
using HuyPhuc.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace HuyPhuc.Infrastructure.Services;

public class DomainEventService : IDomainEventService
{
    private readonly IMediator _mediator;
    private readonly ILogger<DomainEventService> _logger;

    public DomainEventService(IMediator mediator, ILogger<DomainEventService> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    public async Task PublishAsync(BaseEvent domainEvent, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Publishing domain event: {EventType} with ID: {EventId}", 
                domainEvent.GetType().Name, domainEvent.EventId);

            await _mediator.Publish(domainEvent, cancellationToken);

            _logger.LogInformation("Successfully published domain event: {EventType} with ID: {EventId}", 
                domainEvent.GetType().Name, domainEvent.EventId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing domain event: {EventType} with ID: {EventId}", 
                domainEvent.GetType().Name, domainEvent.EventId);
            throw;
        }
    }

    public async Task PublishAsync(IEnumerable<BaseEvent> domainEvents, CancellationToken cancellationToken = default)
    {
        var events = domainEvents.ToList();
        
        _logger.LogInformation("Publishing {EventCount} domain events", events.Count);

        var tasks = events.Select(domainEvent => PublishAsync(domainEvent, cancellationToken));
        await Task.WhenAll(tasks);

        _logger.LogInformation("Successfully published {EventCount} domain events", events.Count);
    }
}
