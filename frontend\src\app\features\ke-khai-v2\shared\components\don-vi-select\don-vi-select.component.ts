import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, inject, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';

import { DonViOption } from '../../../core/models';
import { DonViStore } from '../../../core/stores';

/**
 * Component select đơn vị với ControlValueAccessor
 */
@Component({
  selector: 'app-don-vi-select',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DonViSelectComponent),
      multi: true
    }
  ],
  template: `
    <div class="don-vi-select">
      <label *ngIf="label" [for]="inputId" class="block text-sm font-medium text-gray-700 mb-1">
        {{ label }}
        <span *ngIf="required" class="text-red-500">*</span>
      </label>
      
      <select
        [id]="inputId"
        [value]="value || ''"
        (change)="onSelectionChange($event)"
        (blur)="onTouched()"
        [disabled]="disabled || donViStore.dangTai() || !daiLyId"
        [class]="selectClasses"
      >
        <option value="">{{ getPlaceholder() }}</option>
        <option 
          *ngFor="let option of donViStore.donViOptions()" 
          [value]="option.id"
        >
          {{ option.tenHienThi }}
        </option>
      </select>
      
      <!-- Loading state -->
      <div *ngIf="donViStore.dangTai()" class="absolute right-3 top-1/2 transform -translate-y-1/2">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
      </div>
      
      <!-- Error message -->
      <p *ngIf="donViStore.loi()" class="mt-1 text-sm text-red-600">
        {{ donViStore.loi() }}
      </p>
      
      <!-- Validation error -->
      <p *ngIf="errorMessage" class="mt-1 text-sm text-red-600">
        {{ errorMessage }}
      </p>
    </div>
  `,
  styles: [`
    .don-vi-select {
      position: relative;
    }
    
    select {
      @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
    }
    
    select:disabled {
      @apply bg-gray-100 cursor-not-allowed;
    }
    
    select.error {
      @apply border-red-300 focus:ring-red-500 focus:border-red-500;
    }
  `]
})
export class DonViSelectComponent implements OnInit, OnChanges, ControlValueAccessor {
  @Input() label = '';
  @Input() placeholder = 'Chọn đơn vị';
  @Input() required = false;
  @Input() disabled = false;
  @Input() errorMessage = '';
  @Input() inputId = `don-vi-select-${Math.random().toString(36).substr(2, 9)}`;
  @Input() daiLyId: number | null = null; // ID đại lý để load đơn vị
  
  @Output() donViChange = new EventEmitter<number | null>();

  // Inject store
  readonly donViStore = inject(DonViStore);

  // ControlValueAccessor
  value: number | null = null;
  onChange = (value: number | null) => {};
  onTouched = () => {};

  ngOnInit() {
    // Load đơn vị nếu có daiLyId
    if (this.daiLyId) {
      this.loadDonViOptions();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    // Khi daiLyId thay đổi, load lại danh sách đơn vị
    if (changes['daiLyId'] && this.daiLyId) {
      this.loadDonViOptions();
      // Reset giá trị đã chọn
      this.value = null;
      this.onChange(null);
      this.donViChange.emit(null);
    }
  }

  get selectClasses(): string {
    const baseClasses = 'w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500';
    const errorClasses = this.errorMessage ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300';
    const disabledClasses = this.disabled || this.donViStore.dangTai() || !this.daiLyId ? 'bg-gray-100 cursor-not-allowed' : '';
    
    return `${baseClasses} ${errorClasses} ${disabledClasses}`.trim();
  }

  getPlaceholder(): string {
    if (!this.daiLyId) {
      return 'Vui lòng chọn đại lý trước';
    }
    return this.placeholder;
  }

  private loadDonViOptions(): void {
    if (this.daiLyId) {
      this.donViStore.taiDonViOptionsTheoDaiLy(this.daiLyId);
    }
  }

  onSelectionChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const selectedValue = target.value ? Number(target.value) : null;
    
    this.value = selectedValue;
    this.onChange(selectedValue);
    this.donViChange.emit(selectedValue);
  }

  // ControlValueAccessor implementation
  writeValue(value: number | null): void {
    this.value = value;
  }

  registerOnChange(fn: (value: number | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
