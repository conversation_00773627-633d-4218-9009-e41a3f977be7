using HuyPhuc.Application.Features.DanhMucThuTuc.Commands.CreateDanhMucThuTuc;
using HuyPhuc.Application.Features.DanhMucThuTuc.Common;
using HuyPhuc.Application.Features.DanhMucThuTuc.Queries.GetDanhMucThuTucById;
using HuyPhuc.Application.Features.DanhMucThuTuc.Queries.GetDanhMucThuTucList;
using HuyPhuc.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// Controller quản lý danh mục thủ tục hành chính
/// </summary>
[ApiController]
[Route("api/danh-muc-thu-tuc")]
// [Authorize] // Tạm thời comment để test
public class DanhMucThuTucController : ControllerBase
{
    private readonly IMediator _mediator;

    public DanhMucThuTucController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// L<PERSON>y danh sách danh mục thủ tục có phân trang
    /// </summary>
    /// <param name="page">Số trang (mặc định: 1)</param>
    /// <param name="pageSize">Kích thước trang (mặc định: 10, tối đa: 100)</param>
    /// <param name="tuKhoa">Từ khóa tìm kiếm</param>
    /// <param name="linhVuc">Lĩnh vực thủ tục</param>
    /// <param name="trangThai">Trạng thái thủ tục</param>
    /// <param name="chiLayThuTucHoatDong">Chỉ lấy thủ tục đang hoạt động</param>
    /// <param name="chiLayThuTucCoHieuLuc">Chỉ lấy thủ tục có hiệu lực</param>
    /// <returns>Danh sách thủ tục có phân trang</returns>
    [HttpGet]
    public async Task<ActionResult<DanhMucThuTucPaginatedResponse>> GetDanhSachThuTuc(
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? tuKhoa = null,
        [FromQuery] LinhVucThuTuc? linhVuc = null,
        [FromQuery] TrangThaiThuTuc? trangThai = null,
        [FromQuery] bool? chiLayThuTucHoatDong = null,
        [FromQuery] bool? chiLayThuTucCoHieuLuc = null)
    {
        var query = new GetDanhMucThuTucListQuery
        {
            Page = page,
            PageSize = pageSize,
            TuKhoa = tuKhoa,
            LinhVuc = linhVuc,
            TrangThai = trangThai,
            ChiLayThuTucHoatDong = chiLayThuTucHoatDong,
            ChiLayThuTucCoHieuLuc = chiLayThuTucCoHieuLuc
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Lấy chi tiết danh mục thủ tục theo ID
    /// </summary>
    /// <param name="id">ID của thủ tục</param>
    /// <returns>Chi tiết thủ tục</returns>
    [HttpGet("{id:int}")]
    public async Task<ActionResult<DanhMucThuTucDto>> GetThuTucById(int id)
    {
        var query = new GetDanhMucThuTucByIdQuery(id);
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Tạo danh mục thủ tục mới
    /// </summary>
    /// <param name="command">Thông tin thủ tục cần tạo</param>
    /// <returns>Thông tin thủ tục đã tạo</returns>
    [HttpPost]
    public async Task<ActionResult<DanhMucThuTucDto>> CreateThuTuc(CreateDanhMucThuTucCommand command)
    {
        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetThuTucById), new { id = result.Id }, result);
    }

    /// <summary>
    /// Lấy danh sách thủ tục theo lĩnh vực
    /// </summary>
    /// <param name="linhVuc">Lĩnh vực thủ tục</param>
    /// <param name="trangThai">Trạng thái thủ tục (tùy chọn)</param>
    /// <returns>Danh sách thủ tục theo lĩnh vực</returns>
    [HttpGet("linh-vuc/{linhVuc}")]
    public async Task<ActionResult<IEnumerable<DanhMucThuTucSummaryDto>>> GetThuTucTheoLinhVuc(
        LinhVucThuTuc linhVuc,
        [FromQuery] TrangThaiThuTuc? trangThai = null)
    {
        var query = new GetDanhMucThuTucListQuery
        {
            Page = 1,
            PageSize = 1000, // Get all for this specific query
            LinhVuc = linhVuc,
            TrangThai = trangThai
        };

        var result = await _mediator.Send(query);
        return Ok(result.Items);
    }

    /// <summary>
    /// Lấy danh sách thủ tục đang hoạt động
    /// </summary>
    /// <returns>Danh sách thủ tục đang hoạt động</returns>
    [HttpGet("hoat-dong")]
    public async Task<ActionResult<IEnumerable<DanhMucThuTucSummaryDto>>> GetThuTucHoatDong()
    {
        var query = new GetDanhMucThuTucListQuery
        {
            Page = 1,
            PageSize = 1000,
            ChiLayThuTucHoatDong = true
        };

        var result = await _mediator.Send(query);
        return Ok(result.Items);
    }

    /// <summary>
    /// Lấy danh sách thủ tục có hiệu lực
    /// </summary>
    /// <returns>Danh sách thủ tục có hiệu lực</returns>
    [HttpGet("co-hieu-luc")]
    public async Task<ActionResult<IEnumerable<DanhMucThuTucSummaryDto>>> GetThuTucCoHieuLuc()
    {
        var query = new GetDanhMucThuTucListQuery
        {
            Page = 1,
            PageSize = 1000,
            ChiLayThuTucCoHieuLuc = true
        };

        var result = await _mediator.Send(query);
        return Ok(result.Items);
    }

    /// <summary>
    /// Tìm kiếm thủ tục
    /// </summary>
    /// <param name="tuKhoa">Từ khóa tìm kiếm</param>
    /// <param name="linhVuc">Lĩnh vực (tùy chọn)</param>
    /// <param name="trangThai">Trạng thái (tùy chọn)</param>
    /// <returns>Danh sách thủ tục tìm được</returns>
    [HttpGet("tim-kiem")]
    public async Task<ActionResult<IEnumerable<DanhMucThuTucSummaryDto>>> TimKiemThuTuc(
        [FromQuery] string tuKhoa,
        [FromQuery] LinhVucThuTuc? linhVuc = null,
        [FromQuery] TrangThaiThuTuc? trangThai = null)
    {
        if (string.IsNullOrWhiteSpace(tuKhoa))
        {
            return BadRequest("Từ khóa tìm kiếm không được để trống");
        }

        var query = new GetDanhMucThuTucListQuery
        {
            Page = 1,
            PageSize = 100,
            TuKhoa = tuKhoa,
            LinhVuc = linhVuc,
            TrangThai = trangThai
        };

        var result = await _mediator.Send(query);
        return Ok(result.Items);
    }
}
