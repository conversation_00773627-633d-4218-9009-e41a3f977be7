using HuyPhuc.Api.Controllers.Base;
using HuyPhuc.Application.Features.DiaChi.Queries.GetAllTinh;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// API Controller cho quản lý tỉnh/thành phố
/// </summary>
[ApiController]
[Route("api/[controller]")]
// [Authorize] // Temporarily disabled for testing
public class TinhController : BaseController
{
    /// <summary>
    /// L<PERSON>y danh sách tất cả tỉnh/thành phố
    /// </summary>
    /// <returns>Danh sách tỉnh/thành phố</returns>
    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllTinhQuery());
        
        if (result.IsSuccess)
        {
            return Ok(new
            {
                data = result.Data,
                success = true,
                message = (string?)null,
                errors = (object?)null,
                status = 200,
                traceId = HttpContext.TraceIdentifier
            });
        }

        return BadRequest(new
        {
            data = (object?)null,
            success = false,
            message = string.Join(", ", result.Errors),
            errors = result.Errors,
            status = 400,
            traceId = HttpContext.TraceIdentifier
        });
    }
}
