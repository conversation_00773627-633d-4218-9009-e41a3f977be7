using HuyPhuc.Application.Common.Interfaces;
using BCrypt.Net;

namespace HuyPhuc.Infrastructure.Services;

/// <summary>
/// Service xử lý hash mật khẩu sử dụng BCrypt
/// </summary>
public class PasswordHashingService : IPasswordHashingService
{
    private const int WorkFactor = 12; // BCrypt work factor (độ khó)

    /// <summary>
    /// Hash mật khẩu sử dụng BCrypt
    /// </summary>
    /// <param name="matKhau">Mật khẩu gốc</param>
    /// <returns>Mật khẩu đã hash</returns>
    public string HashMatKhau(string matKhau)
    {
        if (string.IsNullOrWhiteSpace(matKhau))
            throw new ArgumentException("Mật khẩu không được để trống", nameof(matKhau));

        return BCrypt.Net.BCrypt.HashPassword(mat<PERSON><PERSON><PERSON>, WorkFactor);
    }

    /// <summary>
    /// <PERSON>á<PERSON> minh mật khẩu với hash
    /// </summary>
    /// <param name="matKhau">M<PERSON><PERSON> khẩu gốc</param>
    /// <param name="matKhauDaHash">Mật khẩu đã hash</param>
    /// <returns>True nếu mật khẩu đúng</returns>
    public bool XacMinhMatKhau(string matKhau, string matKhauDaHash)
    {
        if (string.IsNullOrWhiteSpace(matKhau) || string.IsNullOrWhiteSpace(matKhauDaHash))
            return false;

        try
        {
            return BCrypt.Net.BCrypt.Verify(matKhau, matKhauDaHash);
        }
        catch
        {
            // Nếu có lỗi trong quá trình verify (ví dụ: hash không hợp lệ)
            return false;
        }
    }

    /// <summary>
    /// Kiểm tra mật khẩu có cần hash lại không
    /// Điều này hữu ích khi muốn upgrade security (tăng work factor)
    /// </summary>
    /// <param name="matKhauDaHash">Mật khẩu đã hash</param>
    /// <returns>True nếu cần hash lại</returns>
    public bool CanHashLai(string matKhauDaHash)
    {
        if (string.IsNullOrWhiteSpace(matKhauDaHash))
            return true;

        try
        {
            // BCrypt.Net-Next không có GetWorkFactor method
            // Tạm thời return false, có thể implement logic khác nếu cần
            return false;
        }
        catch
        {
            // Nếu có lỗi, có thể hash không hợp lệ
            return true;
        }
    }
}
