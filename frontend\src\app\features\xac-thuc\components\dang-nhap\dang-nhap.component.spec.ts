import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { DangNhapComponent } from './dang-nhap.component';
import { XacThucService } from '../../services';

describe('DangNhapComponent', () => {
  let component: DangNhapComponent;
  let fixture: ComponentFixture<DangNhapComponent>;
  let mockXacThucService: jasmine.SpyObj<XacThucService>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const xacThucServiceSpy = jasmine.createSpyObj('XacThucService', ['dangNhap'], {
      daDangNhap: false
    });
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [DangNhapComponent, ReactiveFormsModule],
      providers: [
        { provide: XacThucService, useValue: xacThucServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DangNhapComponent);
    component = fixture.componentInstance;
    mockXacThucService = TestBed.inject(XacThucService) as jasmine.SpyObj<XacThucService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    
    fixture.detectChanges();
  });

  it('nen tao component thanh cong', () => {
    expect(component).toBeTruthy();
  });

  it('nen khoi tao form voi cac truong bat buoc', () => {
    expect(component.formDangNhap.get('email')).toBeTruthy();
    expect(component.formDangNhap.get('matKhau')).toBeTruthy();
    expect(component.formDangNhap.get('ghiNhoDangNhap')).toBeTruthy();
  });

  it('nen validate email bat buoc', () => {
    const emailControl = component.formDangNhap.get('email');
    emailControl?.setValue('');
    emailControl?.markAsTouched();
    
    expect(component.kiemTraLoiTruong('email')).toBeTruthy();
    expect(component.layThongBaoLoiTruong('email')).toBe('Email là bắt buộc');
  });

  it('nen validate dinh dang email', () => {
    const emailControl = component.formDangNhap.get('email');
    emailControl?.setValue('email-khong-hop-le');
    emailControl?.markAsTouched();
    
    expect(component.kiemTraLoiTruong('email')).toBeTruthy();
    expect(component.layThongBaoLoiTruong('email')).toBe('Email không đúng định dạng');
  });

  it('nen validate mat khau bat buoc', () => {
    const matKhauControl = component.formDangNhap.get('matKhau');
    matKhauControl?.setValue('');
    matKhauControl?.markAsTouched();
    
    expect(component.kiemTraLoiTruong('matKhau')).toBeTruthy();
    expect(component.layThongBaoLoiTruong('matKhau')).toBe('Mật khẩu là bắt buộc');
  });

  it('nen validate do dai toi thieu mat khau', () => {
    const matKhauControl = component.formDangNhap.get('matKhau');
    matKhauControl?.setValue('123');
    matKhauControl?.markAsTouched();
    
    expect(component.kiemTraLoiTruong('matKhau')).toBeTruthy();
    expect(component.layThongBaoLoiTruong('matKhau')).toBe('Mật khẩu phải có ít nhất 6 ký tự');
  });

  it('nen dang nhap thanh cong khi form hop le', () => {
    const ketQuaDangNhap = {
      thanhCong: true,
      thongBao: 'Đăng nhập thành công',
      duLieu: {
        nguoiDung: { id: 1, hoTen: 'Test User', email: '<EMAIL>' },
        accessToken: 'token',
        refreshToken: 'refresh-token',
        thoiGianHetHan: Date.now() + 3600000
      }
    };

    mockXacThucService.dangNhap.and.returnValue(of(ketQuaDangNhap));

    component.formDangNhap.patchValue({
      email: '<EMAIL>',
      matKhau: 'password123'
    });

    component.onDangNhap();

    expect(mockXacThucService.dangNhap).toHaveBeenCalledWith({
      email: '<EMAIL>',
      matKhau: 'password123',
      ghiNhoDangNhap: false
    });
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/']);
  });

  it('nen hien thi loi khi dang nhap that bai', () => {
    const ketQuaDangNhap = {
      thanhCong: false,
      thongBao: 'Email hoặc mật khẩu không đúng'
    };

    mockXacThucService.dangNhap.and.returnValue(of(ketQuaDangNhap));

    component.formDangNhap.patchValue({
      email: '<EMAIL>',
      matKhau: 'wrong-password'
    });

    component.onDangNhap();

    expect(component.thongBaoLoi).toBe('Email hoặc mật khẩu không đúng');
    expect(mockRouter.navigate).not.toHaveBeenCalled();
  });

  it('nen xu ly loi API', () => {
    mockXacThucService.dangNhap.and.returnValue(throwError('API Error'));

    component.formDangNhap.patchValue({
      email: '<EMAIL>',
      matKhau: 'password123'
    });

    component.onDangNhap();

    expect(component.thongBaoLoi).toBe('Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.');
  });

  it('nen toggle an hien mat khau', () => {
    expect(component.anMatKhau).toBeTruthy();
    
    component.toggleAnHienMatKhau();
    expect(component.anMatKhau).toBeFalsy();
    
    component.toggleAnHienMatKhau();
    expect(component.anMatKhau).toBeTruthy();
  });

  it('khong nen submit form khi khong hop le', () => {
    component.formDangNhap.patchValue({
      email: '',
      matKhau: ''
    });

    component.onDangNhap();

    expect(mockXacThucService.dangNhap).not.toHaveBeenCalled();
  });

  it('nen chuyen huong neu da dang nhap', () => {
    // Simulate đã đăng nhập
    Object.defineProperty(mockXacThucService, 'daDangNhap', {
      get: () => true
    });

    component.ngOnInit();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/']);
  });
});
