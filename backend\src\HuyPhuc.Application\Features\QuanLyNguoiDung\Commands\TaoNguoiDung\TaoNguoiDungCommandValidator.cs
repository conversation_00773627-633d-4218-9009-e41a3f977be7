using FluentValidation;

namespace HuyPhuc.Application.Features.QuanLyNguoiDung.Commands.TaoNguoiDung;

public class TaoNguoiDungCommandValidator : AbstractValidator<TaoNguoiDungCommand>
{
    public TaoNguoiDungCommandValidator()
    {
        RuleFor(x => x.<PERSON><PERSON>)
            .NotEmpty().WithMessage("Họ tên không được để trống")
            .MaximumLength(100).WithMessage("Họ tên không được vượt quá 100 ký tự");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email không được để trống")
            .EmailAddress().WithMessage("Định dạng email không hợp lệ")
            .MaximumLength(255).WithMessage("Email không được vượt quá 255 ký tự");

        RuleFor(x => x.<PERSON>)
            .NotEmpty().WithMessage("Mật khẩu không được để trống")
            .MinimumLength(6).WithMessage("Mật khẩu phải có ít nhất 6 ký tự");

        RuleFor(x => x.SoDienThoai)
            .Matches(@"^(\+84|84|0)(3[2-9]|5[6|8|9]|7[0|6-9]|8[1-6|8|9]|9[0-4|6-9])[0-9]{7}$")
            .WithMessage("Định dạng số điện thoại không hợp lệ")
            .When(x => !string.IsNullOrEmpty(x.SoDienThoai));

        RuleFor(x => x.Username)
            .MaximumLength(100).WithMessage("Username không được vượt quá 100 ký tự")
            .When(x => !string.IsNullOrEmpty(x.Username));
    }
}
