using HuyPhuc.Domain.Entities.Base;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity cho refresh token
/// </summary>
public class RefreshToken : BaseEntity, IAuditableEntity
{
    /// <summary>
    /// Token string
    /// </summary>
    public string Token { get; private set; } = string.Empty;

    /// <summary>
    /// ID của người dùng sở hữu token
    /// </summary>
    public int NguoiDungId { get; private set; }

    /// <summary>
    /// Thời gian hết hạn
    /// </summary>
    public DateTime ThoiGianHetHan { get; private set; }

    /// <summary>
    /// Token có bị thu hồi không
    /// </summary>
    public bool DaThuHoi { get; private set; }

    /// <summary>
    /// Lý do thu hồi token
    /// </summary>
    public string? LyDoThuHoi { get; private set; }

    /// <summary>
    /// IP address tạo token
    /// </summary>
    public string? IpAddress { get; private set; }

    /// <summary>
    /// User agent tạo token
    /// </summary>
    public string? UserAgent { get; private set; }

    /// <summary>
    /// Navigation property đến người dùng
    /// </summary>
    public virtual NguoiDung NguoiDung { get; set; } = null!;

    // IAuditableEntity properties
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    /// <summary>
    /// Constructor mặc định cho EF Core
    /// </summary>
    private RefreshToken() { }

    /// <summary>
    /// Tạo refresh token mới
    /// </summary>
    public static RefreshToken TaoMoi(
        string token,
        int nguoiDungId,
        DateTime thoiGianHetHan,
        string? ipAddress = null,
        string? userAgent = null)
    {
        return new RefreshToken
        {
            Token = token,
            NguoiDungId = nguoiDungId,
            ThoiGianHetHan = thoiGianHetHan,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            DaThuHoi = false
        };
    }

    /// <summary>
    /// Thu hồi token
    /// </summary>
    public void ThuHoi(string lyDo)
    {
        DaThuHoi = true;
        LyDoThuHoi = lyDo;
    }

    /// <summary>
    /// Kiểm tra token có hợp lệ không
    /// </summary>
    public bool CoHopLe => !DaThuHoi && ThoiGianHetHan > DateTime.UtcNow;

    /// <summary>
    /// Kiểm tra token có hết hạn không
    /// </summary>
    public bool DaHetHan => ThoiGianHetHan <= DateTime.UtcNow;
}
