<div class="nhap-lao-dong-container max-w-full mx-auto p-6">
  <!-- Header -->
  <div class="header-section mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
          {{ isEditMode ? 'Chỉnh sửa thông tin lao động' : 'Nhập thông tin lao động' }}
        </h1>
        <p class="text-gray-600">
          {{ isEditMode ? 'Cập nhật thông tin chi tiết cho lao động' : 'Nhập thông tin chi tiết cho từng lao động trong tờ khai 602' }}
        </p>
        <div *ngIf="isEditMode && laoDongDangChinhSua" class="mt-2 text-sm text-blue-600">
          <span class="font-medium">Đang chỉnh sửa:</span> {{ laoDongDangChinhSua.hoTen || 'Lao động #' + editLaoDongId }}
        </div>
      </div>
      
      <!-- Thông tin đại lý/đơn vị -->
      <div *ngIf="formState && formState.daiLyDaChon && formState.donViDaChon"
           class="info-card bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h3 class="font-medium text-blue-900 mb-2">Thông tin đã chọn:</h3>
        <div class="text-sm space-y-1">
          <div><span class="font-medium">Đại lý:</span> {{ formState.daiLyDaChon.tenDaiLy }}</div>
          <div><span class="font-medium">Đơn vị:</span> {{ formState.donViDaChon.tenDonVi }}</div>
          <div><span class="font-medium">Số sổ BHXH:</span> {{ formState.thongTinKhac.soSoBHXH }}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Form Container -->
  <div class="form-container bg-white rounded-lg shadow-md">

    <!-- Flow mới: Sử dụng API với keKhaiId -->
    <div *ngIf="keKhaiId; else noKeKhaiTemplate">

      <!-- Edit Mode: Form chỉnh sửa lao động -->
      <div *ngIf="isEditMode" class="edit-mode-container">

        <!-- Header edit mode -->
        <div class="form-header p-6 border-b border-gray-200 bg-blue-50">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-blue-800">
              <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Chỉnh sửa thông tin lao động
            </h2>
            <div class="flex space-x-3">
              <button
                type="button"
                (click)="onHuyChinhSua()"
                class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Hủy
              </button>
              <button
                type="button"
                (click)="onLuuThayDoi()"
                [disabled]="!laoDongForm.valid || dangLuu"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                <svg *ngIf="!dangLuu" class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <svg *ngIf="dangLuu" class="animate-spin w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ dangLuu ? 'Đang lưu...' : 'Lưu thay đổi' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Form chỉnh sửa -->
        <div class="form-edit-worker p-6">
          <form [formGroup]="laoDongForm" class="space-y-6">

            <!-- Loading state -->
            <div *ngIf="dangTai" class="text-center py-8">
              <div class="inline-flex items-center">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
                <span class="text-gray-600">Đang tải thông tin lao động...</span>
              </div>
            </div>

            <!-- Form content -->
            <div *ngIf="!dangTai" class="space-y-6">

              <!-- Thông tin cá nhân -->
              <div class="form-section">
                <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">
                  <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                  Thông tin cá nhân
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Mã số BHXH <span class="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      formControlName="maSoBHXH"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      [class.border-red-500]="laoDongForm.get('maSoBHXH')?.invalid && laoDongForm.get('maSoBHXH')?.touched"
                      placeholder="Nhập mã số BHXH">
                    <div *ngIf="laoDongForm.get('maSoBHXH')?.invalid && laoDongForm.get('maSoBHXH')?.touched" class="mt-1 text-sm text-red-600">
                      {{ getFieldErrorMessage('maSoBHXH') }}
                    </div>
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Họ và tên <span class="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      formControlName="hoTen"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      [class.border-red-500]="laoDongForm.get('hoTen')?.invalid && laoDongForm.get('hoTen')?.touched"
                      placeholder="Nhập họ và tên">
                    <div *ngIf="laoDongForm.get('hoTen')?.invalid && laoDongForm.get('hoTen')?.touched" class="mt-1 text-sm text-red-600">
                      {{ getFieldErrorMessage('hoTen') }}
                    </div>
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Số CCCD <span class="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      formControlName="cmnd"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      [class.border-red-500]="laoDongForm.get('cmnd')?.invalid && laoDongForm.get('cmnd')?.touched"
                      placeholder="Nhập số CCCD">
                    <div *ngIf="laoDongForm.get('cmnd')?.invalid && laoDongForm.get('cmnd')?.touched" class="mt-1 text-sm text-red-600">
                      {{ getFieldErrorMessage('cmnd') }}
                    </div>
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Giới tính <span class="text-red-500">*</span>
                    </label>
                    <select
                      formControlName="gioiTinh"
                      class="form-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      [class.border-red-500]="laoDongForm.get('gioiTinh')?.invalid && laoDongForm.get('gioiTinh')?.touched">
                      <option value="">-- Chọn giới tính --</option>
                      <option value="Nam">Nam</option>
                      <option value="Nữ">Nữ</option>
                    </select>
                    <div *ngIf="laoDongForm.get('gioiTinh')?.invalid && laoDongForm.get('gioiTinh')?.touched" class="mt-1 text-sm text-red-600">
                      {{ getFieldErrorMessage('gioiTinh') }}
                    </div>
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Ngày sinh <span class="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      formControlName="ngaySinh"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      [class.border-red-500]="laoDongForm.get('ngaySinh')?.invalid && laoDongForm.get('ngaySinh')?.touched">
                    <div *ngIf="laoDongForm.get('ngaySinh')?.invalid && laoDongForm.get('ngaySinh')?.touched" class="mt-1 text-sm text-red-600">
                      {{ getFieldErrorMessage('ngaySinh') }}
                    </div>
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Số điện thoại
                    </label>
                    <input
                      type="text"
                      formControlName="dienThoaiLh"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Nhập số điện thoại">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Quốc tịch
                    </label>
                    <input
                      type="text"
                      formControlName="quocTich"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="VN">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Dân tộc
                    </label>
                    <input
                      type="text"
                      formControlName="danToc"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="01">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Mã tỉnh KS
                    </label>
                    <input
                      type="text"
                      formControlName="maTinhKs"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="89">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Mã huyện KS
                    </label>
                    <input
                      type="text"
                      formControlName="maHuyenKs"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="890">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Mã xã KS
                    </label>
                    <input
                      type="text"
                      formControlName="maXaKs"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="30538">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Mã hộ gia đình
                    </label>
                    <input
                      type="text"
                      formControlName="maHoGiaDinh"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Nhập mã hộ gia đình">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      CCNS
                    </label>
                    <input
                      type="text"
                      formControlName="ccns"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="0">
                  </div>

                </div>
              </div>

              <!-- Thông tin BHXH -->
              <div class="form-section">
                <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">
                  <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Thông tin BHXH
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Phương án đóng <span class="text-red-500">*</span>
                    </label>
                    <select
                      formControlName="phuongAn"
                      class="form-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="DB">Đóng bù</option>
                      <option value="DT">Đóng tiếp</option>
                      <option value="TN">Tự nguyện</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Tháng bắt đầu <span class="text-red-500">*</span>
                    </label>
                    <input
                      type="month"
                      formControlName="thangBatDau"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Mức thu nhập <span class="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      formControlName="mucThuNhap"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      [class.border-red-500]="laoDongForm.get('mucThuNhap')?.invalid && laoDongForm.get('mucThuNhap')?.touched"
                      placeholder="Nhập mức thu nhập">
                    <div *ngIf="laoDongForm.get('mucThuNhap')?.invalid && laoDongForm.get('mucThuNhap')?.touched" class="mt-1 text-sm text-red-600">
                      {{ getFieldErrorMessage('mucThuNhap') }}
                    </div>
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Tổng tiền
                    </label>
                    <input
                      type="number"
                      formControlName="tongTien"
                      readonly
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                      placeholder="Tự động tính">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Tiền tự đóng
                    </label>
                    <input
                      type="number"
                      formControlName="tienTuDong"
                      readonly
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                      placeholder="Tự động tính">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      NSNN hỗ trợ
                    </label>
                    <input
                      type="number"
                      formControlName="tienHoTro"
                      readonly
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                      placeholder="Tự động tính">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Tiền lãi
                    </label>
                    <input
                      type="number"
                      formControlName="tienLai"
                      readonly
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                      placeholder="Tự động tính">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Tiền thừa
                    </label>
                    <input
                      type="number"
                      formControlName="tienThua"
                      readonly
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                      placeholder="Tự động tính">
                  </div>

                </div>
              </div>

              <!-- Thông tin bổ sung -->
              <div class="form-section">
                <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">
                  <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Thông tin bổ sung
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Type ID
                    </label>
                    <input
                      type="text"
                      formControlName="typeId"
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="TM">
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Tham gia BB
                    </label>
                    <select
                      formControlName="isThamGiaBb"
                      class="form-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="false">Không</option>
                      <option value="true">Có</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Tạm hoãn HĐ
                    </label>
                    <select
                      formControlName="isTamHoanHD"
                      class="form-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option value="false">Không</option>
                      <option value="true">Có</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Mã lỗi
                    </label>
                    <input
                      type="text"
                      formControlName="maLoi"
                      readonly
                      class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                      placeholder="Tự động từ API">
                  </div>

                  <div class="form-group md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      Mô tả lỗi
                    </label>
                    <textarea
                      formControlName="moTaLoi"
                      readonly
                      rows="2"
                      class="form-textarea w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                      placeholder="Tự động từ API"></textarea>
                  </div>

                </div>
              </div>

            </div>

          </form>
        </div>

      </div>

      <!-- Normal Mode: Header với nút thêm -->
      <div *ngIf="!isEditMode" class="form-header p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-gray-800">
            Danh sách lao động ({{ danhSachLaoDong.length }})
          </h2>
          <button
            type="button"
            (click)="onThemLaoDongMoi()"
            [disabled]="dangLuu"
            class="btn-add px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            <svg *ngIf="!dangLuu" class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <svg *ngIf="dangLuu" class="animate-spin w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ dangLuu ? 'Đang thêm...' : 'Thêm lao động' }}
          </button>
        </div>
      </div>

      <!-- Form thêm lao động mới -->
      <div *ngIf="!isEditMode" class="form-add-worker p-6 border-b border-gray-200 bg-gray-50">
        <form [formGroup]="laoDongForm" class="space-y-6">

          <!-- Thông tin cá nhân -->
          <div class="form-section">
            <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">
              <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              Thông tin cá nhân
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Mã số BHXH <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  formControlName="maSoBHXH"
                  (blur)="laoDongForm.get('maSoBHXH')?.markAsTouched()"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  [class.border-red-500]="laoDongForm.get('maSoBHXH')?.invalid && laoDongForm.get('maSoBHXH')?.touched"
                  placeholder="Nhập mã số BHXH">
                <div *ngIf="laoDongForm.get('maSoBHXH')?.invalid && laoDongForm.get('maSoBHXH')?.touched" class="mt-1 text-sm text-red-600">
                  {{ getFieldErrorMessage('maSoBHXH') }}
                </div>
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Họ và tên <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  formControlName="hoTen"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  [class.border-red-500]="laoDongForm.get('hoTen')?.invalid && laoDongForm.get('hoTen')?.touched"
                  placeholder="Nhập họ và tên">
                <div *ngIf="laoDongForm.get('hoTen')?.invalid && laoDongForm.get('hoTen')?.touched" class="mt-1 text-sm text-red-600">
                  {{ getFieldErrorMessage('hoTen') }}
                </div>
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Số CCCD <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  formControlName="cmnd"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  [class.border-red-500]="laoDongForm.get('cmnd')?.invalid && laoDongForm.get('cmnd')?.touched"
                  placeholder="Nhập số CCCD">
                <div *ngIf="laoDongForm.get('cmnd')?.invalid && laoDongForm.get('cmnd')?.touched" class="mt-1 text-sm text-red-600">
                  {{ getFieldErrorMessage('cmnd') }}
                </div>
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Ngày sinh <span class="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  formControlName="ngaySinh"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  [class.border-red-500]="laoDongForm.get('ngaySinh')?.invalid && laoDongForm.get('ngaySinh')?.touched">
                <div *ngIf="laoDongForm.get('ngaySinh')?.invalid && laoDongForm.get('ngaySinh')?.touched" class="mt-1 text-sm text-red-600">
                  {{ getFieldErrorMessage('ngaySinh') }}
                </div>
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Giới tính <span class="text-red-500">*</span>
                </label>
                <select
                  formControlName="gioiTinh"
                  class="form-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  [class.border-red-500]="laoDongForm.get('gioiTinh')?.invalid && laoDongForm.get('gioiTinh')?.touched">
                  <option value="">-- Chọn giới tính --</option>
                  <option value="Nam">Nam</option>
                  <option value="Nữ">Nữ</option>
                </select>
                <div *ngIf="laoDongForm.get('gioiTinh')?.invalid && laoDongForm.get('gioiTinh')?.touched" class="mt-1 text-sm text-red-600">
                  {{ getFieldErrorMessage('gioiTinh') }}
                </div>
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Số điện thoại
                </label>
                <input
                  type="text"
                  formControlName="dienThoaiLh"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Nhập số điện thoại">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Quốc tịch
                </label>
                <input
                  type="text"
                  formControlName="quocTich"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="VN">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Dân tộc
                </label>
                <input
                  type="text"
                  formControlName="danToc"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="01">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Mã tỉnh KS
                </label>
                <input
                  type="text"
                  formControlName="maTinhKs"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="89">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Mã huyện KS
                </label>
                <input
                  type="text"
                  formControlName="maHuyenKs"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="890">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Mã xã KS
                </label>
                <input
                  type="text"
                  formControlName="maXaKs"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="30538">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Mã hộ gia đình
                </label>
                <input
                  type="text"
                  formControlName="maHoGiaDinh"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Nhập mã hộ gia đình">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  CCNS
                </label>
                <input
                  type="text"
                  formControlName="ccns"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0">
              </div>

            </div>
          </div>

          <!-- Thông tin đóng BHXH -->
          <div class="form-section">
            <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">
              <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
              Thông tin đóng BHXH
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Phương án đóng <span class="text-red-500">*</span>
                </label>
                <select
                  formControlName="phuongAn"
                  class="form-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="">-- Chọn phương án --</option>
                  <option value="DB">Đóng bù</option>
                  <option value="DT">Đóng tiếp</option>
                  <option value="TN">Tự nguyện</option>
                </select>
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Phương thức đóng <span class="text-red-500">*</span>
                </label>
                <select
                  formControlName="phuongThuc"
                  class="form-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="">-- Chọn phương thức --</option>
                  <option value="1">Phương thức 1</option>
                  <option value="2">Phương thức 2</option>
                  <option value="3">Phương thức 3</option>
                </select>
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Tháng bắt đầu <span class="text-red-500">*</span>
                </label>
                <input
                  type="month"
                  formControlName="thangBatDau"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Mức thu nhập  <span class="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  formControlName="mucThuNhap"
                  min="0"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  [class.border-red-500]="laoDongForm.get('mucThuNhap')?.invalid && laoDongForm.get('mucThuNhap')?.touched"
                  placeholder="Nhập mức thu nhập">
                <div *ngIf="laoDongForm.get('mucThuNhap')?.invalid && laoDongForm.get('mucThuNhap')?.touched" class="mt-1 text-sm text-red-600">
                  {{ getFieldErrorMessage('mucThuNhap') }}
                </div>
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Tổng tiền
                </label>
                <input
                  type="number"
                  formControlName="tongTien"
                  readonly
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                  placeholder="Tự động tính">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Tiền tự đóng
                </label>
                <input
                  type="number"
                  formControlName="tienTuDong"
                  readonly
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                  placeholder="Tự động tính">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  NSNN hỗ trợ
                </label>
                <input
                  type="number"
                  formControlName="tienHoTro"
                  readonly
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                  placeholder="Tự động tính">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Tiền lãi
                </label>
                <input
                  type="number"
                  formControlName="tienLai"
                  readonly
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                  placeholder="Tự động tính">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Tiền thừa
                </label>
                <input
                  type="number"
                  formControlName="tienThua"
                  readonly
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                  placeholder="Tự động tính">
              </div>

            </div>
          </div>

          <!-- Thông tin bổ sung -->
          <div class="form-section">
            <h4 class="text-lg font-medium text-gray-800 mb-4 border-b border-gray-200 pb-2">
              <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Thông tin bổ sung
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Type ID
                </label>
                <input
                  type="text"
                  formControlName="typeId"
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="TM">
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Tham gia BB
                </label>
                <select
                  formControlName="isThamGiaBb"
                  class="form-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="false">Không</option>
                  <option value="true">Có</option>
                </select>
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Tạm hoãn HĐ
                </label>
                <select
                  formControlName="isTamHoanHD"
                  class="form-select w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="false">Không</option>
                  <option value="true">Có</option>
                </select>
              </div>

              <div class="form-group">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Mã lỗi
                </label>
                <input
                  type="text"
                  formControlName="maLoi"
                  readonly
                  class="form-input w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                  placeholder="Tự động từ API">
              </div>

              <div class="form-group md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Mô tả lỗi
                </label>
                <textarea
                  formControlName="moTaLoi"
                  readonly
                  rows="2"
                  class="form-textarea w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                  placeholder="Tự động từ API"></textarea>
              </div>

            </div>
          </div>



        </form>
      </div>



      <!-- Action Buttons cho flow mới -->
      <div class="action-buttons p-6 border-t border-gray-200 flex flex-col sm:flex-row gap-4 justify-between">
        <button
          type="button"
          (click)="onQuayLai()"
          class="btn-back px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Quay lại
        </button>

        <div class="flex gap-4">
          <button
            type="button"
            (click)="onGuiKeKhaiMoi()"
            [disabled]="danhSachLaoDong.length === 0"
            class="btn-submit px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
            </svg>
            Gửi kê khai
          </button>
        </div>
      </div>

    </div>

    <!-- Template fallback khi không có keKhaiId -->
    <ng-template #noKeKhaiTemplate>
      <div class="p-6 text-center">
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <svg class="w-12 h-12 mx-auto mb-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          <h3 class="text-lg font-medium text-yellow-800 mb-2">Không tìm thấy thông tin kê khai</h3>
          <p class="text-yellow-700 mb-4">Vui lòng chọn đại lý và đơn vị trước khi nhập thông tin lao động.</p>
          <button
            type="button"
            (click)="onQuayLai()"
            class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 transition-colors">
            Quay lại chọn đại lý
          </button>
        </div>
      </div>
    </ng-template>

  </div>
</div>
