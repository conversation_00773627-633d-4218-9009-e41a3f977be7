import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { delay, switchMap } from 'rxjs/operators';

import { 
  NguoiDung, 
  VaiTro, 
  ThongTinDangNhap, 
  KetQuaDangNhap,
  ApiResponse 
} from '../models';

/**
 * Mock API Service để test chức năng đăng nhập
 * Giả lập các API calls với dữ liệu demo
 */
@Injectable({
  providedIn: 'root'
})
export class MockApiService {
  
  // Danh sách tài khoản demo
  private readonly taiKhoanDemo: Array<{username: string, matKhau: string, nguoiDung: NguoiDung}> = [
    {
      username: 'admin',
      matKhau: 'Admin@123',
      nguoiDung: {
        id: 1,
        hoTen: 'Quản trị viên',
        email: '<EMAIL>',
        username: 'admin',
        soDienThoai: '0901234567',
        avatar: 'https://ui-avatars.com/api/?name=Admin&background=3b82f6&color=fff',
        vaiTro: VaiTro.Admin,
        trangThaiHoatDong: true,
        ngayTao: new Date('2024-01-01'),
        ngayCapNhat: new Date()
      }
    },
    {
      username: 'manager',
      matKhau: 'Manager@123',
      nguoiDung: {
        id: 2,
        hoTen: 'Trần Thị Manager',
        email: '<EMAIL>',
        username: 'manager',
        soDienThoai: '0987654321',
        avatar: 'https://ui-avatars.com/api/?name=Manager&background=10b981&color=fff',
        vaiTro: VaiTro.NhanVien,
        trangThaiHoatDong: true,
        ngayTao: new Date('2024-01-15'),
        ngayCapNhat: new Date()
      }
    },
    {
      username: 'user',
      matKhau: 'User@123',
      nguoiDung: {
        id: 3,
        hoTen: 'Lê Văn User',
        email: '<EMAIL>',
        username: 'user',
        soDienThoai: '0369852147',
        avatar: 'https://ui-avatars.com/api/?name=User&background=f59e0b&color=fff',
        vaiTro: VaiTro.KhachHang,
        trangThaiHoatDong: true,
        ngayTao: new Date('2024-02-01'),
        ngayCapNhat: new Date()
      }
    }
  ];

  /**
   * Mock API đăng nhập
   */
  dangNhap(thongTinDangNhap: ThongTinDangNhap): Observable<KetQuaDangNhap> {
    // Giả lập delay API call
    return of(null).pipe(
      delay(1500), // Delay 1.5s để test loading state
      switchMap(() => {
        const taiKhoan = this.taiKhoanDemo.find(tk =>
          tk.username === thongTinDangNhap.username &&
          tk.matKhau === thongTinDangNhap.matKhau
        );

        if (taiKhoan) {
          // Đăng nhập thành công
          const ketQua: KetQuaDangNhap = {
            thanhCong: true,
            thongBao: 'Đăng nhập thành công',
            duLieu: {
              nguoiDung: taiKhoan.nguoiDung,
              accessToken: this.taoMockToken(taiKhoan.nguoiDung),
              refreshToken: this.taoMockRefreshToken(),
              thoiGianHetHan: Date.now() + (24 * 60 * 60 * 1000) // 24 giờ
            }
          };
          return of(ketQua);
        } else {
          // Đăng nhập thất bại
          const ketQua: KetQuaDangNhap = {
            thanhCong: false,
            thongBao: 'Username hoặc mật khẩu không đúng'
          };
          return of(ketQua);
        }
      })
    );
  }

  /**
   * Mock API đăng xuất
   */
  dangXuat(): Observable<ApiResponse> {
    return of({
      thanhCong: true,
      thongBao: 'Đăng xuất thành công'
    }).pipe(delay(500));
  }

  /**
   * Mock API làm mới token
   */
  lamMoiToken(refreshToken: string): Observable<ApiResponse<any>> {
    return of({
      thanhCong: true,
      thongBao: 'Làm mới token thành công',
      duLieu: {
        accessToken: this.taoMockToken(),
        refreshToken: this.taoMockRefreshToken()
      }
    }).pipe(delay(1000));
  }

  /**
   * Tạo mock access token
   */
  private taoMockToken(nguoiDung?: NguoiDung): string {
    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
    const payload = btoa(JSON.stringify({
      sub: nguoiDung?.id || 1,
      username: nguoiDung?.username || 'demo',
      email: nguoiDung?.email || '<EMAIL>',
      vaiTro: nguoiDung?.vaiTro || VaiTro.KhachHang,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 giờ
    }));
    const signature = btoa('mock-signature');
    
    return `${header}.${payload}.${signature}`;
  }

  /**
   * Tạo mock refresh token
   */
  private taoMockRefreshToken(): string {
    return btoa(`refresh-token-${Date.now()}-${Math.random()}`);
  }

  /**
   * Lấy danh sách tài khoản demo (chỉ để hiển thị)
   */
  layDanhSachTaiKhoanDemo(): Array<{username: string, matKhau: string, vaiTro: string}> {
    return this.taiKhoanDemo.map(tk => ({
      username: tk.username,
      matKhau: tk.matKhau,
      vaiTro: tk.nguoiDung.vaiTro
    }));
  }
}
