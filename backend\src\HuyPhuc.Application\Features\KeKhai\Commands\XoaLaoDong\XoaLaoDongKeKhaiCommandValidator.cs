using FluentValidation;

namespace HuyPhuc.Application.Features.KeKhai.Commands.XoaLaoDong;

/// <summary>
/// Validator cho XoaLaoDongKeKhaiCommand
/// </summary>
public class XoaLaoDongKeKhaiCommandValidator : AbstractValidator<XoaLaoDongKeKhaiCommand>
{
    public XoaLaoDongKeKhaiCommandValidator()
    {
        RuleFor(x => x.Request.Id)
            .GreaterThan(0)
            .WithMessage("ID lao động phải lớn hơn 0");

        RuleFor(x => x.Request.KeKhaiId)
            .GreaterThan(0)
            .WithMessage("ID kê khai phải lớn hơn 0");
    }
}
