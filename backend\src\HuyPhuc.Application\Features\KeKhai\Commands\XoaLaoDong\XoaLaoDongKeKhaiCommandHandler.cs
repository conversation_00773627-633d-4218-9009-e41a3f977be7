using HuyPhuc.Application.Common.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.KeKhai.Commands.XoaLaoDong;

/// <summary>
/// Handler xóa lao động khỏi kê khai
/// </summary>
public class XoaLaoDongKeKhaiCommandHandler : IRequestHandler<XoaLaoDongKeKhaiCommand, XoaLaoDongResponse>
{
    private readonly IApplicationDbContext _context;

    public XoaLaoDongKeKhaiCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<XoaLaoDongResponse> Handle(XoaLaoDongKeKhaiCommand command, CancellationToken cancellationToken)
    {
        var request = command.Request;

        try
        {
            // 1. Validate kê khai tồn tại và đang ở trạng thái có thể chỉnh sửa
            var keKhai = await _context.DanhSachKeKhai
                .FirstOrDefaultAsync(x => x.Id == request.KeKhaiId, cancellationToken);

            if (keKhai == null)
            {
                return new XoaLaoDongResponse
                {
                    Success = false,
                    Message = $"Không tìm thấy kê khai với ID: {request.KeKhaiId}"
                };
            }

            if (keKhai.TrangThai != 0) // Chỉ cho phép chỉnh sửa khi đang soạn
            {
                return new XoaLaoDongResponse
                {
                    Success = false,
                    Message = "Không thể xóa lao động trong kê khai đã gửi"
                };
            }

            // 2. Tìm lao động cần xóa
            var laoDongCanXoa = await _context.ChiTietLaoDongKeKhaiV2
                .FirstOrDefaultAsync(x => x.Id == request.Id && x.KeKhaiId == request.KeKhaiId, cancellationToken);

            if (laoDongCanXoa == null)
            {
                return new XoaLaoDongResponse
                {
                    Success = false,
                    Message = $"Không tìm thấy lao động với ID: {request.Id}"
                };
            }

            // 3. Xóa lao động
            _context.ChiTietLaoDongKeKhaiV2.Remove(laoDongCanXoa);

            // 4. Cập nhật lại STT cho các lao động còn lại
            var danhSachLaoDongConLai = await _context.ChiTietLaoDongKeKhaiV2
                .Where(x => x.KeKhaiId == request.KeKhaiId && x.Stt > laoDongCanXoa.Stt)
                .OrderBy(x => x.Stt)
                .ToListAsync(cancellationToken);

            foreach (var laoDong in danhSachLaoDongConLai)
            {
                laoDong.Stt -= 1; // Giảm STT đi 1
                laoDong.LastModified = DateTime.UtcNow;
            }

            // 5. Lưu thay đổi
            await _context.SaveChangesAsync(cancellationToken);

            return new XoaLaoDongResponse
            {
                Success = true,
                Message = "Xóa lao động thành công"
            };
        }
        catch (Exception ex)
        {
            return new XoaLaoDongResponse
            {
                Success = false,
                Message = $"Có lỗi xảy ra khi xóa lao động: {ex.Message}"
            };
        }
    }
}
