import { patchState, signalStore, withMethods, withState, withComputed } from '@ngrx/signals';
import { computed, inject } from '@angular/core';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { pipe, switchMap, tap, catchError, of } from 'rxjs';

import { DaiLy, DaiLyOption } from '../models';
import { DaiLyService } from '../services';

/**
 * State interface cho đại lý store
 */
interface DaiLyState {
  danhSachDaiLy: DaiLy[];
  daiLyOptions: DaiLyOption[];
  daiLyDangChon: DaiLy | null;
  dangTai: boolean;
  loi: string | null;
}

/**
 * Initial state
 */
const initialState: DaiLyState = {
  danhSachDaiLy: [],
  daiLyOptions: [],
  daiLyDangChon: null,
  dangTai: false,
  loi: null
};

/**
 * Store quản lý state của đại lý
 */
export const DaiLyStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withComputed((store) => ({
    // Computed signals
    coDaiLy: computed(() => store.danhSachDaiLy().length > 0),
    soDaiLy: computed(() => store.danhSachDaiLy().length)
  })),
  withMethods((store, daiLyService = inject(DaiLyService)) => ({
    // Actions
    setDangTai: (dangTai: boolean) => patchState(store, { dangTai }),
    setLoi: (loi: string | null) => patchState(store, { loi }),
    setDaiLyDangChon: (daiLy: DaiLy | null) => patchState(store, { daiLyDangChon: daiLy }),
    
    // Reset state
    reset: () => patchState(store, initialState),
    
    // Load danh sách đại lý
    taiDanhSachDaiLy: rxMethod<void>(
      pipe(
        tap(() => patchState(store, { dangTai: true, loi: null })),
        switchMap(() =>
          daiLyService.layDanhSachDaiLy().pipe(
            tap((danhSach) => {
              patchState(store, {
                danhSachDaiLy: danhSach,
                dangTai: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                loi: error.message || 'Có lỗi khi tải danh sách đại lý',
                dangTai: false
              });
              return of([]);
            })
          )
        )
      )
    ),
    
    // Load danh sách đại lý options
    taiDaiLyOptions: rxMethod<void>(
      pipe(
        tap(() => patchState(store, { dangTai: true, loi: null })),
        switchMap(() =>
          daiLyService.layDanhSachDaiLyOptions().pipe(
            tap((options) => {
              patchState(store, {
                daiLyOptions: options,
                dangTai: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                loi: error.message || 'Có lỗi khi tải danh sách đại lý',
                dangTai: false
              });
              return of([]);
            })
          )
        )
      )
    ),
    
    // Load đại lý theo ID
    taiDaiLyTheoId: rxMethod<number>(
      pipe(
        tap(() => patchState(store, { dangTai: true, loi: null })),
        switchMap((id) =>
          daiLyService.layDaiLyTheoId(id).pipe(
            tap((daiLy) => {
              patchState(store, {
                daiLyDangChon: daiLy,
                dangTai: false
              });
            }),
            catchError((error) => {
              patchState(store, {
                loi: error.message || 'Có lỗi khi tải thông tin đại lý',
                dangTai: false
              });
              return of(null);
            })
          )
        )
      )
    )
  }))
);
