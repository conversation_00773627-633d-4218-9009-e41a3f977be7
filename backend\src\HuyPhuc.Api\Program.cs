using HuyPhuc.Api.Extensions;
using HuyPhuc.Application;
using HuyPhuc.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddApiServices(builder.Configuration);
builder.Services.AddApplicationServices();
builder.Services.AddInfrastructureServices(builder.Configuration);

var app = builder.Build();

// Configure the HTTP request pipeline
app.UseApiServices(app.Environment);

app.MapControllers();

app.Run();
