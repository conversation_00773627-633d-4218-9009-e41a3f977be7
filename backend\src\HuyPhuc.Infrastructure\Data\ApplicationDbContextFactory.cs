using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace HuyPhuc.Infrastructure.Data;

public class ApplicationDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
{
    public ApplicationDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();

        // Use connection string directly for design time
        var connectionString = "Host=************;Port=5454;Database=ctyhuyphuc;Username=postgres;Password=************;SSL Mode=Prefer;Trust Server Certificate=true";

        optionsBuilder.UseNpgsql(connectionString);

        // Create a simple context without interceptors for design time
        return new ApplicationDbContext(optionsBuilder.Options);
    }
}
