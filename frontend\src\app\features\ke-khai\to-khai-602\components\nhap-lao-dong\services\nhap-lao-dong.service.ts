import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { KeKhaiChiTietDto, LaoDongKeKhaiDto } from '../../../../../../shared/models/ke-khai.model';
import { LaoDong, ValidationError } from '../../../models';
import { NhapLaoDongApiService } from './nhap-lao-dong-api.service';
import { NhapLaoDongFormService } from './nhap-lao-dong-form.service';
import { NhapLaoDongHelperService } from './nhap-lao-dong-helper.service';

/**
 * Service tổng hợp cho component nhập lao động
 * Refactored để tuân thủ quy tắc 400 dòng - sử dụng composition pattern
 */
@Injectable({
  providedIn: 'root'
})
export class NhapLaoDongService {

  constructor(
    private fb: FormBuilder,
    private apiService: NhapLaoDongApiService,
    private formService: NhapLaoDongFormService,
    private helperService: NhapLaoDongHelperService
  ) {}

  // ==================== API METHODS - Delegate to ApiService ====================

  /**
   * Load thông tin kê khai từ bảng danh_sach_ke_khai
   */
  async loadKeKhaiInfo(keKhaiId: number): Promise<KeKhaiChiTietDto | undefined> {
    return this.apiService.loadKeKhaiInfo(keKhaiId);
  }

  /**
   * Load danh sách lao động từ bảng chi_tiet_lao_dong_ke_khai_v2
   */
  async loadDanhSachLaoDong(keKhaiId: number): Promise<LaoDongKeKhaiDto[]> {
    return this.apiService.loadDanhSachLaoDong(keKhaiId);
  }

  /**
   * Thêm lao động mới - sử dụng API mới
   */
  async themLaoDongMoi(keKhaiId: number, formValue: any): Promise<boolean> {
    return this.apiService.themLaoDongMoi(keKhaiId, formValue);
  }

  /**
   * Xóa lao động
   */
  async xoaLaoDong(keKhaiId: number, laoDongId: number): Promise<boolean> {
    return this.apiService.xoaLaoDong(keKhaiId, laoDongId);
  }

  /**
   * Gửi kê khai - sử dụng API mới
   */
  async guiKeKhai(keKhaiId: number): Promise<boolean> {
    return this.apiService.guiKeKhai(keKhaiId);
  }

  /**
   * Hiển thị confirmation dialog cho xóa lao động
   */
  xacNhanXoaLaoDong(): boolean {
    return this.apiService.xacNhanXoaLaoDong();
  }

  /**
   * Kiểm tra danh sách lao động có rỗng không
   */
  kiemTraDanhSachRong(danhSach: any[]): boolean {
    return this.apiService.kiemTraDanhSachRong(danhSach);
  }

  // ==================== FORM METHODS - Delegate to FormService ====================

  /**
   * Tạo form group chính
   */
  taoForm(): FormGroup {
    return this.formService.taoForm();
  }

  /**
   * Tạo form cho flow mới (kê khai)
   */
  taoFormChoFlowMoi(): FormGroup {
    return this.formService.taoFormChoFlowMoi();
  }

  /**
   * Tạo form group cho một lao động
   */
  taoLaoDongFormGroup(laoDong: Partial<LaoDong>): FormGroup {
    return this.formService.taoLaoDongFormGroup(laoDong);
  }

  // ==================== UTILITY METHODS - Delegate to HelperService ====================

  /**
   * Format currency theo định dạng Việt Nam
   */
  formatCurrency(value: number): string {
    return this.helperService.formatCurrency(value);
  }

  /**
   * Validate mã số BHXH
   */
  validateMaSoBHXH(maSo: string): boolean {
    return this.helperService.validateMaSoBHXH(maSo);
  }

  /**
   * Validate số CCCD
   */
  validateSoCCCD(soCCCD: string): boolean {
    return this.helperService.validateSoCCCD(soCCCD);
  }

  /**
   * Validate số điện thoại
   */
  validateSoDienThoai(soDienThoai: string): boolean {
    return this.helperService.validateSoDienThoai(soDienThoai);
  }

  /**
   * Validate email
   */
  validateEmail(email: string): boolean {
    return this.helperService.validateEmail(email);
  }

  // ==================== FORM ARRAY METHODS - Delegate to FormService ====================

  /**
   * Cập nhật form array với danh sách lao động
   */
  capNhatFormArray(form: FormGroup, danhSachLaoDong: Partial<LaoDong>[]): void {
    return this.formService.capNhatFormArray(form, danhSachLaoDong);
  }

  /**
   * Thêm lao động mới vào form array
   */
  themLaoDongVaoForm(form: FormGroup): void {
    return this.formService.themLaoDongVaoForm(form);
  }

  /**
   * Xóa lao động khỏi form array
   */
  xoaLaoDongKhoiForm(form: FormGroup, index: number): void {
    return this.formService.xoaLaoDongKhoiForm(form, index);
  }

  /**
   * Validate form và trả về danh sách lỗi
   */
  validateForm(form: FormGroup): ValidationError[] {
    return this.formService.validateForm(form);
  }

  /**
   * Kiểm tra field có lỗi không
   */
  hasError(form: FormGroup, index: number, fieldName: string): boolean {
    return this.formService.hasError(form, index, fieldName);
  }

  /**
   * Lấy thông báo lỗi cho field
   */
  getErrorMessage(fieldName: string, errorType: string, errorValue?: any): string {
    return this.formService.getErrorMessage(fieldName, errorType, errorValue);
  }
}
