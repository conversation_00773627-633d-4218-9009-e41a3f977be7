using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Domain.Events.Quyen;
using HuyPhuc.Domain.Exceptions;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity đại diện cho quyền trong hệ thống
/// </summary>
public class Quyen : BaseEntity, IAuditableEntity
{
    public string TenQuyen { get; private set; } = string.Empty;
    public string MaQuyen { get; private set; } = string.Empty; // Unique code for permission
    public string? MoTa { get; private set; }
    public string? NhomQuyen { get; private set; } // Nhóm quyền (VD: NGUOI_DUNG, SAN_PHAM, DON_HANG)
    public bool LaQuyenHeThong { get; private set; } // Không thể xóa các quyền hệ thống
    public bool TrangThaiHoatDong { get; private set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    // Navigation properties
    public virtual ICollection<VaiTroQuyen> DanhSachVaiTro { get; private set; } = new List<VaiTroQuyen>();

    private Quyen() { } // EF Core constructor

    /// <summary>
    /// Tạo quyền mới
    /// </summary>
    public static Quyen Tao(string tenQuyen, string maQuyen, string? moTa = null, 
        string? nhomQuyen = null, bool laQuyenHeThong = false)
    {
        if (string.IsNullOrWhiteSpace(tenQuyen))
            throw new DomainException("Tên quyền không được để trống");

        if (string.IsNullOrWhiteSpace(maQuyen))
            throw new DomainException("Mã quyền không được để trống");

        if (tenQuyen.Length > 100)
            throw new DomainException("Tên quyền không được vượt quá 100 ký tự");

        if (maQuyen.Length > 50)
            throw new DomainException("Mã quyền không được vượt quá 50 ký tự");

        // Validate mã quyền format (chỉ chứa chữ cái, số và dấu gạch dưới)
        if (!System.Text.RegularExpressions.Regex.IsMatch(maQuyen, @"^[A-Z0-9_]+$"))
            throw new DomainException("Mã quyền chỉ được chứa chữ cái in hoa, số và dấu gạch dưới");

        var quyen = new Quyen
        {
            TenQuyen = tenQuyen.Trim(),
            MaQuyen = maQuyen.Trim().ToUpper(),
            MoTa = moTa?.Trim(),
            NhomQuyen = nhomQuyen?.Trim(),
            LaQuyenHeThong = laQuyenHeThong,
            TrangThaiHoatDong = true
        };

        quyen.ThemSuKien(new QuyenDaTaoEvent(quyen));
        return quyen;
    }

    /// <summary>
    /// Cập nhật thông tin quyền
    /// </summary>
    public void CapNhatThongTin(string tenQuyen, string? moTa = null, string? nhomQuyen = null)
    {
        if (string.IsNullOrWhiteSpace(tenQuyen))
            throw new DomainException("Tên quyền không được để trống");

        if (tenQuyen.Length > 100)
            throw new DomainException("Tên quyền không được vượt quá 100 ký tự");

        TenQuyen = tenQuyen.Trim();
        MoTa = moTa?.Trim();
        NhomQuyen = nhomQuyen?.Trim();

        ThemSuKien(new QuyenCapNhatEvent(this));
    }

    /// <summary>
    /// Kích hoạt quyền
    /// </summary>
    public void KichHoat()
    {
        if (TrangThaiHoatDong)
            throw new BusinessRuleException("QUYEN_DA_KICH_HOAT", "Quyền đã được kích hoạt");

        TrangThaiHoatDong = true;
        ThemSuKien(new QuyenKichHoatEvent(this));
    }

    /// <summary>
    /// Vô hiệu hóa quyền
    /// </summary>
    public void VoHieuHoa()
    {
        if (LaQuyenHeThong)
            throw new BusinessRuleException("KHONG_THE_VO_HIEU_HOA_QUYEN_HE_THONG", 
                "Không thể vô hiệu hóa quyền hệ thống");

        if (!TrangThaiHoatDong)
            throw new BusinessRuleException("QUYEN_DA_VO_HIEU_HOA", "Quyền đã bị vô hiệu hóa");

        TrangThaiHoatDong = false;
        ThemSuKien(new QuyenVoHieuHoaEvent(this));
    }

    /// <summary>
    /// Kiểm tra có thể xóa quyền không
    /// </summary>
    public bool CoTheXoa()
    {
        return !LaQuyenHeThong && !DanhSachVaiTro.Any();
    }
}
