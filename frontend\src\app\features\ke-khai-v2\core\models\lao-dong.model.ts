import { GioiTinh, PhuongAnDongBHXH, PhuongThucDong } from './enums';

/**
 * Interface cho thông tin lao động cơ bản
 */
export interface LaoDongBase {
  id?: number | string; // ID có thể là number (từ backend) hoặc string (temporary ID)
  stt: number;
  
  // Thông tin cá nhân
  maSoBHXH: string;
  hoTen: string;
  ccns: string;
  ngaySinh: string; // Format: dd/MM/yyyy
  gioiTinh: GioiTinh;
  quocTich: string;
  danToc: string;
  cmnd: string; // Số CCCD/CMND
  
  // Địa chỉ thường trú
  maTinhKs: string;
  maHuyenKs: string;
  maXaKs: string;
  
  // Thông tin liên hệ
  dienThoaiLh: string;
  maHoGiaDinh: string;

  // Loại (tăng/giảm lao động, lương) - đổi tên từ typeId
  loai: number;

  // Loại NSNN
  loaiNsnn?: string;

  // Tỷ lệ NSNN hỗ trợ (%)
  tyLeNsnnHoTro?: number;

  // Ngày biên lai
  ngayBienLai?: string;

  // Số biên lai
  soBienLai?: string;

  // Hệ số đóng
  heSoDong?: number;
}

/**
 * Interface cho thông tin đóng BHXH của lao động
 */
export interface ThongTinDongBHXH {
  phuongAn: PhuongAnDongBHXH;
  phuongThuc: PhuongThucDong;
  thangBatDau: string; // Format: MM/yyyy
  
  // Thông tin tiền
  tienLai: number;
  tienThua: number;
  tienTuDong: number;
  tongTien: number;
  tienHoTro: number;
  mucThuNhap: number;
}

/**
 * Interface đầy đủ cho lao động
 */
export interface LaoDong extends LaoDongBase, ThongTinDongBHXH {
  // Có thể thêm các trường khác nếu cần
  ghiChu?: string;
  ngayTao?: Date;
  ngayCapNhat?: Date;
}

/**
 * Interface cho validation errors của lao động
 */
export interface LaoDongValidationError {
  field: keyof LaoDong;
  message: string;
  value?: any;
}
