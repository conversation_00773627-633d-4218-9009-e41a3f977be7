namespace HuyPhuc.Application.DTOs.ToKhai602;

/// <summary>
/// Response DTO cho việc tạo draft tờ khai 602
/// </summary>
public class TaoDraftToKhaiResponse
{
    /// <summary>
    /// ID của draft tờ khai vừa tạo
    /// </summary>
    public int DraftId { get; set; }

    /// <summary>
    /// Mã tờ khai (tự động generate)
    /// </summary>
    public string MaToKhai { get; set; } = string.Empty;

    /// <summary>
    /// Thông tin đại lý
    /// </summary>
    public DaiLyInfo DaiLy { get; set; } = new();

    /// <summary>
    /// Thông tin đơn vị
    /// </summary>
    public DonViInfo DonVi { get; set; } = new();

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime NgayTao { get; set; }

    /// <summary>
    /// Trạng thái
    /// </summary>
    public string TrangThai { get; set; } = "DRAFT";
}

/// <summary>
/// Thông tin đại lý trong response
/// </summary>
public class DaiLyInfo
{
    public int Id { get; set; }
    public string MaDaiLy { get; set; } = string.Empty;
    public string TenDaiLy { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
    public string SoDienThoai { get; set; } = string.Empty;
}

/// <summary>
/// Thông tin đơn vị trong response
/// </summary>
public class DonViInfo
{
    public int Id { get; set; }
    public string MaDonVi { get; set; } = string.Empty;
    public string TenDonVi { get; set; } = string.Empty;
    public string DiaChi { get; set; } = string.Empty;
}
