using System.ComponentModel.DataAnnotations;

namespace HuyPhuc.Application.DTOs.ToKhai602;

/// <summary>
/// Request DTO cho việc lấy danh sách tờ khai 602
/// </summary>
public class LayDanhSachToKhaiRequest
{
    /// <summary>
    /// Trang hiện tại (bắt đầu từ 1)
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "Trang phải lớn hơn 0")]
    public int Trang { get; set; } = 1;

    /// <summary>
    /// Kích thước trang
    /// </summary>
    [Range(1, 100, ErrorMessage = "Kích thước trang phải từ 1 đến 100")]
    public int KichThuoc { get; set; } = 10;

    /// <summary>
    /// Từ khóa tìm kiếm
    /// </summary>
    public string? TuKhoa { get; set; }

    /// <summary>
    /// ID đại lý
    /// </summary>
    public int? DaiLyId { get; set; }

    /// <summary>
    /// ID đơn vị
    /// </summary>
    public int? DonViId { get; set; }

    /// <summary>
    /// Trạng thái tờ khai
    /// </summary>
    public string? TrangThai { get; set; }

    /// <summary>
    /// Từ ngày
    /// </summary>
    public DateTime? TuNgay { get; set; }

    /// <summary>
    /// Đến ngày
    /// </summary>
    public DateTime? DenNgay { get; set; }

    /// <summary>
    /// Số sổ BHXH
    /// </summary>
    public string? SoSoBHXH { get; set; }

    /// <summary>
    /// Kỳ kê khai
    /// </summary>
    public string? KyKeKhai { get; set; }
}
