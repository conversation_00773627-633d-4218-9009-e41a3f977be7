import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { KeKhaiChiTietDto, LaoDongKeKhaiDto } from '../../../../../../shared/models/ke-khai.model';
import { ToKhaiFormState } from '../../../models';

/**
 * Service quản lý state cho component nhập lao động
 * Tách từ NhapLaoDongComponent để tuân thủ quy tắc 400 dòng
 */
@Injectable({
  providedIn: 'root'
})
export class NhapLaoDongStateService {
  
  // State subjects
  private _keKhaiInfo$ = new BehaviorSubject<KeKhaiChiTietDto | undefined>(undefined);
  private _danhSachLaoDong$ = new BehaviorSubject<LaoDongKeKhaiDto[]>([]);
  private _formState$ = new BehaviorSubject<ToKhaiFormState | null>(null);
  private _dangTai$ = new BehaviorSubject<boolean>(false);
  private _dangLuu$ = new BehaviorSubject<boolean>(false);
  private _dangGhiNhan$ = new BehaviorSubject<boolean>(false);
  private _isEditMode$ = new BehaviorSubject<boolean>(false);
  private _editLaoDongId$ = new BehaviorSubject<number | null>(null);
  private _laoDongDangChinhSua$ = new BehaviorSubject<LaoDongKeKhaiDto | null>(null);

  constructor() {}

  // ==================== OBSERVABLES ====================

  /**
   * Observable cho thông tin kê khai
   */
  get keKhaiInfo$(): Observable<KeKhaiChiTietDto | undefined> {
    return this._keKhaiInfo$.asObservable();
  }

  /**
   * Observable cho danh sách lao động
   */
  get danhSachLaoDong$(): Observable<LaoDongKeKhaiDto[]> {
    return this._danhSachLaoDong$.asObservable();
  }

  /**
   * Observable cho form state
   */
  get formState$(): Observable<ToKhaiFormState | null> {
    return this._formState$.asObservable();
  }

  /**
   * Observable cho trạng thái đang tải
   */
  get dangTai$(): Observable<boolean> {
    return this._dangTai$.asObservable();
  }

  /**
   * Observable cho trạng thái đang lưu
   */
  get dangLuu$(): Observable<boolean> {
    return this._dangLuu$.asObservable();
  }

  /**
   * Observable cho trạng thái đang ghi nhận
   */
  get dangGhiNhan$(): Observable<boolean> {
    return this._dangGhiNhan$.asObservable();
  }

  /**
   * Observable cho edit mode
   */
  get isEditMode$(): Observable<boolean> {
    return this._isEditMode$.asObservable();
  }

  /**
   * Observable cho edit lao dong ID
   */
  get editLaoDongId$(): Observable<number | null> {
    return this._editLaoDongId$.asObservable();
  }

  /**
   * Observable cho lao động đang chỉnh sửa
   */
  get laoDongDangChinhSua$(): Observable<LaoDongKeKhaiDto | null> {
    return this._laoDongDangChinhSua$.asObservable();
  }

  // ==================== GETTERS ====================

  /**
   * Lấy giá trị hiện tại của thông tin kê khai
   */
  get keKhaiInfo(): KeKhaiChiTietDto | undefined {
    return this._keKhaiInfo$.value;
  }

  /**
   * Lấy giá trị hiện tại của danh sách lao động
   */
  get danhSachLaoDong(): LaoDongKeKhaiDto[] {
    return this._danhSachLaoDong$.value;
  }

  /**
   * Lấy giá trị hiện tại của form state
   */
  get formState(): ToKhaiFormState | null {
    return this._formState$.value;
  }

  /**
   * Lấy giá trị hiện tại của trạng thái đang tải
   */
  get dangTai(): boolean {
    return this._dangTai$.value;
  }

  /**
   * Lấy giá trị hiện tại của trạng thái đang lưu
   */
  get dangLuu(): boolean {
    return this._dangLuu$.value;
  }

  /**
   * Lấy giá trị hiện tại của trạng thái đang ghi nhận
   */
  get dangGhiNhan(): boolean {
    return this._dangGhiNhan$.value;
  }

  /**
   * Lấy giá trị hiện tại của edit mode
   */
  get isEditMode(): boolean {
    return this._isEditMode$.value;
  }

  /**
   * Lấy giá trị hiện tại của edit lao dong ID
   */
  get editLaoDongId(): number | null {
    return this._editLaoDongId$.value;
  }

  /**
   * Lấy giá trị hiện tại của lao động đang chỉnh sửa
   */
  get laoDongDangChinhSua(): LaoDongKeKhaiDto | null {
    return this._laoDongDangChinhSua$.value;
  }

  // ==================== SETTERS ====================

  /**
   * Cập nhật thông tin kê khai
   */
  setKeKhaiInfo(keKhaiInfo: KeKhaiChiTietDto | undefined): void {
    this._keKhaiInfo$.next(keKhaiInfo);
  }

  /**
   * Cập nhật danh sách lao động
   */
  setDanhSachLaoDong(danhSach: LaoDongKeKhaiDto[]): void {
    this._danhSachLaoDong$.next(danhSach);
  }

  /**
   * Cập nhật form state
   */
  setFormState(formState: ToKhaiFormState | null): void {
    this._formState$.next(formState);
  }

  /**
   * Cập nhật trạng thái đang tải
   */
  setDangTai(dangTai: boolean): void {
    this._dangTai$.next(dangTai);
  }

  /**
   * Cập nhật trạng thái đang lưu
   */
  setDangLuu(dangLuu: boolean): void {
    this._dangLuu$.next(dangLuu);
  }

  /**
   * Cập nhật trạng thái đang ghi nhận
   */
  setDangGhiNhan(dangGhiNhan: boolean): void {
    this._dangGhiNhan$.next(dangGhiNhan);
  }

  /**
   * Cập nhật edit mode
   */
  setIsEditMode(isEditMode: boolean): void {
    this._isEditMode$.next(isEditMode);
  }

  /**
   * Cập nhật edit lao dong ID
   */
  setEditLaoDongId(editLaoDongId: number | null): void {
    this._editLaoDongId$.next(editLaoDongId);
  }

  /**
   * Cập nhật lao động đang chỉnh sửa
   */
  setLaoDongDangChinhSua(laoDong: LaoDongKeKhaiDto | null): void {
    this._laoDongDangChinhSua$.next(laoDong);
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Reset tất cả state về giá trị mặc định
   */
  resetState(): void {
    this._keKhaiInfo$.next(undefined);
    this._danhSachLaoDong$.next([]);
    this._formState$.next(null);
    this._dangTai$.next(false);
    this._dangLuu$.next(false);
    this._dangGhiNhan$.next(false);
    this._isEditMode$.next(false);
    this._editLaoDongId$.next(null);
    this._laoDongDangChinhSua$.next(null);
  }

  /**
   * Reset edit state
   */
  resetEditState(): void {
    this._isEditMode$.next(false);
    this._editLaoDongId$.next(null);
    this._laoDongDangChinhSua$.next(null);
  }

  /**
   * Kiểm tra có đang trong quá trình xử lý không
   */
  isDangXuLy(): boolean {
    return this.dangTai || this.dangLuu || this.dangGhiNhan;
  }

  /**
   * Cleanup khi component destroy
   */
  cleanup(): void {
    // Không complete subjects vì service là singleton
    // Chỉ reset state
    this.resetState();
  }
}
