/**
 * Test file để kiểm tra mapping dữ liệu từ API
 * Mô phỏng response từ API và test logic mapping
 */

// Mock API Response giống như dữ liệu thực tế
const mockApiResponse = {
    "data": {
        "maSoBHXH": "8923390541",
        "hoTen": "Nguy<PERSON><PERSON>",
        "ccns": "0",
        "ngaySinh": "08/08/1965",
        "gioiTinh": 1,
        "quocTich": "VN",
        "danToc": "01",
        "cmnd": "083065001366",
        "maTinhKs": "89",
        "maHuyenKs": "890",
        "maXaKs": "30538",
        "dienThoaiLh": "0975076009",
        "maHoGiaDinh": "8999429075",
        "phuongAn": "DB",
        "phuongThuc": "3",  // API trả về "3"
        "thangBatDau": "04/2025",
        "tien<PERSON>ai": 3188.0,
        "tienThua": 0.0,
        "tienTu<PERSON><PERSON>": 993188.0,
        "tongTien": 894188.0,
        "tienHoTro": 99000.0,
        "mucThuNhap": 1500000.0,
        "typeId": null,
        "isThamGiaBb": false,
        "isTamHoanHD": false,
        "message": "Tra cứu thông tin thanh toán BHXH tự nguyện thành công",
        "isError": false,
        "maLoi": "25",
        "moTaLoi": "Tra cứu thông tin thanh toán BHXH tự nguyện thành công"
    },
    "success": true,
    "message": null,
    "errors": null,
    "status": 200,
    "traceId": "00-e9497425-4f8d-4587-bd32-62affc7c6cc7-00"
};

// Test mapping functions
class ApiMappingTest {
    /**
     * Mapping phương thức đóng từ API sang frontend format
     */
    private mapPhuongThucFromApi(apiPhuongThuc: string): string {
        const mapping: { [key: string]: string } = {
            '1': '1-thang',
            '3': '3-thang', 
            '6': '6-thang',
            '12': '12-thang',
            'nam-sau': 'nam-sau',
            'nam-thieu': 'nam-thieu'
        };
        
        console.log(`🔄 Mapping phương thức từ API: "${apiPhuongThuc}" -> "${mapping[apiPhuongThuc] || apiPhuongThuc}"`);
        return mapping[apiPhuongThuc] || apiPhuongThuc;
    }

    /**
     * Tự động set số tháng dựa trên phương thức đóng từ API
     */
    private autoSetSoThangFromApi(phuongThuc: string): number | null {
        const soThangMapping: { [key: string]: number } = {
            '1-thang': 1,
            '3-thang': 3,
            '6-thang': 6,
            '12-thang': 12
        };

        if (soThangMapping[phuongThuc]) {
            console.log(`🔢 Tự động set số tháng: ${soThangMapping[phuongThuc]} cho phương thức: ${phuongThuc}`);
            return soThangMapping[phuongThuc];
        }
        
        console.log(`⚠️ Không tự động set số tháng cho phương thức: ${phuongThuc} (cần chọn thủ công)`);
        return null;
    }

    /**
     * Test mapping với dữ liệu thực tế
     */
    testMapping() {
        console.log('🧪 === TEST API MAPPING ===');
        console.log('📥 API Response:', mockApiResponse.data);
        
        const apiData = mockApiResponse.data;
        
        // Test mapping phương thức
        const originalPhuongThuc = apiData.phuongThuc;
        const mappedPhuongThuc = this.mapPhuongThucFromApi(originalPhuongThuc);
        
        console.log(`\n🔄 PHƯƠNG THỨC MAPPING:`);
        console.log(`   API trả về: "${originalPhuongThuc}"`);
        console.log(`   Frontend sử dụng: "${mappedPhuongThuc}"`);
        
        // Test auto set số tháng
        const autoSoThang = this.autoSetSoThangFromApi(mappedPhuongThuc);
        
        console.log(`\n🔢 SỐ THÁNG AUTO SET:`);
        console.log(`   Phương thức: "${mappedPhuongThuc}"`);
        console.log(`   Số tháng tự động: ${autoSoThang}`);
        
        // Test với các trường hợp khác
        console.log(`\n🧪 TEST CÁC TRƯỜNG HỢP KHÁC:`);
        
        const testCases = ['1', '6', '12', 'nam-sau', 'nam-thieu', 'unknown'];
        testCases.forEach(testCase => {
            const mapped = this.mapPhuongThucFromApi(testCase);
            const soThang = this.autoSetSoThangFromApi(mapped);
            console.log(`   "${testCase}" -> "${mapped}" -> số tháng: ${soThang || 'null'}`);
        });
        
        // Kết quả cuối cùng cho form
        console.log(`\n✅ KẾT QUẢ CUỐI CÙNG CHO FORM:`);
        console.log(`   phuongThuc: "${mappedPhuongThuc}"`);
        console.log(`   soThang: ${autoSoThang}`);
        console.log(`   thangBatDau: "${apiData.thangBatDau}" -> cần convert sang yyyy-MM`);
        console.log(`   mucThuNhap: ${apiData.mucThuNhap}`);
        
        return {
            phuongThuc: mappedPhuongThuc,
            soThang: autoSoThang,
            originalData: apiData
        };
    }
}

// Chạy test
const tester = new ApiMappingTest();
const result = tester.testMapping();

console.log('\n🎯 === SUMMARY ===');
console.log('Khi API trả về phuongThuc: "3"');
console.log('Frontend sẽ:');
console.log('1. Map thành "3-thang" cho dropdown phương thức');
console.log('2. Tự động set soThang = 3');
console.log('3. Hiển thị dropdown số tháng với 1 option: "3 tháng"');
console.log('4. User không cần chọn thủ công số tháng');

export { ApiMappingTest, mockApiResponse };
