﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HuyPhuc.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddDmDanTocTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NgayCapNhat",
                table: "dm_xa");

            migrationBuilder.DropColumn(
                name: "<PERSON>ay<PERSON><PERSON>",
                table: "dm_xa");

            migrationBuilder.DropColumn(
                name: "NguoiCapNhat",
                table: "dm_xa");

            migrationBuilder.DropColumn(
                name: "<PERSON>uo<PERSON><PERSON><PERSON>",
                table: "dm_xa");

            migrationBuilder.DropColumn(
                name: "created_by",
                table: "dm_xa");

            migrationBuilder.DropColumn(
                name: "updated_by",
                table: "dm_xa");

            migrationBuilder.DropColumn(
                name: "NgayCapNhat",
                table: "dm_tinh");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON><PERSON>",
                table: "dm_tinh");

            migrationBuilder.DropColumn(
                name: "NguoiCapNhat",
                table: "dm_tinh");

            migrationBuilder.DropColumn(
                name: "NguoiTao",
                table: "dm_tinh");

            migrationBuilder.DropColumn(
                name: "created_by",
                table: "dm_tinh");

            migrationBuilder.DropColumn(
                name: "updated_by",
                table: "dm_tinh");

            migrationBuilder.DropColumn(
                name: "NgayCapNhat",
                table: "dm_huyen");

            migrationBuilder.DropColumn(
                name: "NgayTao",
                table: "dm_huyen");

            migrationBuilder.DropColumn(
                name: "NguoiCapNhat",
                table: "dm_huyen");

            migrationBuilder.DropColumn(
                name: "NguoiTao",
                table: "dm_huyen");

            migrationBuilder.DropColumn(
                name: "created_by",
                table: "dm_huyen");

            migrationBuilder.DropColumn(
                name: "updated_by",
                table: "dm_huyen");

            migrationBuilder.AddColumn<string>(
                name: "loai_nsnn",
                table: "chi_tiet_to_khai_602",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "he_so_dong",
                table: "chi_tiet_lao_dong_ke_khai_v2",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "loai_nsnn",
                table: "chi_tiet_lao_dong_ke_khai_v2",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ty_le_nsnn_ho_tro",
                table: "chi_tiet_lao_dong_ke_khai_v2",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "dm_dan_toc",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ma = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    ten = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ma_va_ten = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false),
                    can_cu = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    rownum = table.Column<decimal>(type: "numeric(10,1)", nullable: true),
                    NgayTao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    NgayCapNhat = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NguoiTao = table.Column<string>(type: "text", nullable: true),
                    NguoiCapNhat = table.Column<string>(type: "text", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    created_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dm_dan_toc", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "dm_loai",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ten_loai = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dm_loai", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_dm_dan_toc_ma",
                table: "dm_dan_toc",
                column: "ma",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_dm_dan_toc_rownum",
                table: "dm_dan_toc",
                column: "rownum");

            migrationBuilder.CreateIndex(
                name: "idx_dm_loai_ten_loai",
                table: "dm_loai",
                column: "ten_loai");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "dm_dan_toc");

            migrationBuilder.DropTable(
                name: "dm_loai");

            migrationBuilder.DropColumn(
                name: "loai_nsnn",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "he_so_dong",
                table: "chi_tiet_lao_dong_ke_khai_v2");

            migrationBuilder.DropColumn(
                name: "loai_nsnn",
                table: "chi_tiet_lao_dong_ke_khai_v2");

            migrationBuilder.DropColumn(
                name: "ty_le_nsnn_ho_tro",
                table: "chi_tiet_lao_dong_ke_khai_v2");

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "dm_xa",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayTao",
                table: "dm_xa",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "NguoiCapNhat",
                table: "dm_xa",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NguoiTao",
                table: "dm_xa",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "created_by",
                table: "dm_xa",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "updated_by",
                table: "dm_xa",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "dm_tinh",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayTao",
                table: "dm_tinh",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "NguoiCapNhat",
                table: "dm_tinh",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NguoiTao",
                table: "dm_tinh",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "created_by",
                table: "dm_tinh",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "updated_by",
                table: "dm_tinh",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayCapNhat",
                table: "dm_huyen",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NgayTao",
                table: "dm_huyen",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "NguoiCapNhat",
                table: "dm_huyen",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NguoiTao",
                table: "dm_huyen",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "created_by",
                table: "dm_huyen",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "updated_by",
                table: "dm_huyen",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);
        }
    }
}
