using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HuyPhuc.Domain.Entities;

namespace HuyPhuc.Infrastructure.Data.Configurations;

/// <summary>
/// Configuration cho entity KeKhai (bảng danh_sach_ke_khai)
/// </summary>
public class KeKhaiConfiguration : IEntityTypeConfiguration<KeKhai>
{
    public void Configure(EntityTypeBuilder<KeKhai> builder)
    {
        // Table name
        builder.ToTable("danh_sach_ke_khai");

        // Primary key
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(e => e.MaKeKhai)
            .HasColumnName("ma_ke_khai")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.ThuTucId)
            .HasColumnName("thu_tuc_id")
            .IsRequired();

        builder.Property(e => e.DaiLyId)
            .HasColumnName("dai_ly_id")
            .IsRequired();

        builder.Property(e => e.DonViId)
            .HasColumnName("don_vi_id")
            .IsRequired();

        builder.Property(e => e.SoSoBHXH)
            .HasColumnName("so_so_bhxh")
            .HasMaxLength(50)
            .IsRequired()
            .HasDefaultValue("1");

        builder.Property(e => e.ThongTinHeader)
            .HasColumnName("thong_tin_header")
            .HasColumnType("jsonb");

        builder.Property(e => e.TrangThai)
            .HasColumnName("trang_thai")
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(e => e.NguoiTaoId)
            .HasColumnName("nguoi_tao_id")
            .IsRequired();

        builder.Property(e => e.NgayTao)
            .HasColumnName("ngay_tao")
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(e => e.NguoiPheDuyetId)
            .HasColumnName("nguoi_phe_duyet_id");

        builder.Property(e => e.NgayPheDuyet)
            .HasColumnName("ngay_phe_duyet");

        builder.Property(e => e.LyDoTuChoi)
            .HasColumnName("ly_do_tu_choi")
            .HasColumnType("text");

        builder.Property(e => e.FileXmlPath)
            .HasColumnName("file_xml_path")
            .HasMaxLength(500);

        builder.Property(e => e.ChuKySo)
            .HasColumnName("chu_ky_so")
            .HasColumnType("text");

        builder.Property(e => e.DigestValue)
            .HasColumnName("digest_value")
            .HasMaxLength(255);

        builder.Property(e => e.SignatureValue)
            .HasColumnName("signature_value")
            .HasColumnType("text");

        builder.Property(e => e.GhiChu)
            .HasColumnName("ghi_chu")
            .HasColumnType("text");

        // Audit fields - New system (BaseAuditableEntity)
        builder.Property(e => e.Created)
            .HasColumnName("created")
            .IsRequired()
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(e => e.CreatedBy)
            .HasColumnName("created_by")
            .HasMaxLength(100);

        builder.Property(e => e.LastModified)
            .HasColumnName("last_modified");

        builder.Property(e => e.LastModifiedBy)
            .HasColumnName("last_modified_by")
            .HasMaxLength(100);

        // Audit fields - Old system (IAuditableEntity) - ignore to avoid conflicts
        builder.Ignore(e => e.NgayCapNhat);
        builder.Ignore(e => e.NguoiTao);
        builder.Ignore(e => e.NguoiCapNhat);

        // Indexes
        builder.HasIndex(e => e.MaKeKhai)
            .IsUnique()
            .HasDatabaseName("idx_ke_khai_ma_ke_khai");

        builder.HasIndex(e => new { e.ThuTucId, e.TrangThai })
            .HasDatabaseName("idx_ke_khai_thu_tuc_trang_thai");

        builder.HasIndex(e => new { e.DaiLyId, e.DonViId })
            .HasDatabaseName("idx_ke_khai_dai_ly_don_vi");

        builder.HasIndex(e => e.NgayTao)
            .HasDatabaseName("idx_ke_khai_ngay_tao");

        builder.HasIndex(e => e.NguoiTaoId)
            .HasDatabaseName("idx_ke_khai_nguoi_tao");

        // GIN index for JSONB
        builder.HasIndex(e => e.ThongTinHeader)
            .HasDatabaseName("idx_ke_khai_header_gin")
            .HasMethod("gin");

        // Foreign key relationships
        builder.HasOne(e => e.ThuTuc)
            .WithMany()
            .HasForeignKey(e => e.ThuTucId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.DaiLy)
            .WithMany()
            .HasForeignKey(e => e.DaiLyId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.DonVi)
            .WithMany()
            .HasForeignKey(e => e.DonViId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.NguoiTao)
            .WithMany()
            .HasForeignKey(e => e.NguoiTaoId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.NguoiPheDuyet)
            .WithMany()
            .HasForeignKey(e => e.NguoiPheDuyetId)
            .OnDelete(DeleteBehavior.SetNull);

        // One-to-many relationship with ChiTietLaoDongKeKhaiV2
        builder.HasMany(e => e.DanhSachLaoDong)
            .WithOne(e => e.KeKhai)
            .HasForeignKey(e => e.KeKhaiId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
