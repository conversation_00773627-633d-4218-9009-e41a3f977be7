import { NgModule, Optional, SkipSelf } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';

// Services
import { ApiService } from './services/api.service';
import { AuthService } from './services/auth.service';
import { LoggingService } from './services/logging.service';
import { XacThucService } from '../features/xac-thuc/services/xac-thuc.service';
import { MockApiService } from '../features/xac-thuc/services/mock-api.service';
import { BhxhLookupService } from '../features/ke-khai-v2/core/services/bhxh-lookup.service';
import { VnPostAuthService } from '../features/ke-khai-v2/core/services/vnpost-auth.service';

// Guards
import { AuthGuard } from './guards/auth.guard';

// Interceptors
import { HttpErrorInterceptor } from './interceptors/http-error.interceptor';
import { AuthInterceptor } from './interceptors/auth.interceptor';

@NgModule({
  imports: [
    CommonModule,
    HttpClientModule
  ],
  providers: [
    // Services
    ApiService,
    AuthService,
    LoggingService,
    XacThucService,
    MockApiService,
    BhxhLookupService,
    VnPostAuthService,

    // Guards
    AuthGuard,
    
    // HTTP Interceptors
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpErrorInterceptor,
      multi: true
    }
  ]
})
export class CoreModule {
  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {
    if (parentModule) {
      throw new Error('CoreModule is already loaded. Import it in the AppModule only.');
    }
  }
}
