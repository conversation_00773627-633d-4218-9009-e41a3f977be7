using HuyPhuc.Application.Common.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Mail;

namespace HuyPhuc.Infrastructure.Services;

public class EmailService : IEmailService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<EmailService> _logger;

    public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public async Task GuiEmailAsync(string toEmail, string subject, string body, CancellationToken cancellationToken = default)
    {
        try
        {
            var smtpSettings = _configuration.GetSection("EmailSettings");
            var smtpHost = smtpSettings["SmtpHost"];
            var smtpPort = int.Parse(smtpSettings["SmtpPort"] ?? "587");
            var smtpUsername = smtpSettings["SmtpUsername"];
            var smtpPassword = smtpSettings["SmtpPassword"];
            var fromEmail = smtpSettings["FromEmail"];
            var fromName = smtpSettings["FromName"];

            using var client = new SmtpClient(smtpHost, smtpPort)
            {
                Credentials = new NetworkCredential(smtpUsername, smtpPassword),
                EnableSsl = true
            };

            var mailMessage = new MailMessage
            {
                From = new MailAddress(fromEmail!, fromName),
                Subject = subject,
                Body = body,
                IsBodyHtml = true
            };

            mailMessage.To.Add(toEmail);

            await client.SendMailAsync(mailMessage, cancellationToken);
            
            _logger.LogInformation("Email đã được gửi thành công đến {ToEmail}", toEmail);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi gửi email đến {ToEmail}", toEmail);
            throw;
        }
    }

    public async Task GuiEmailNhieuNguoiAsync(IEnumerable<string> toEmails, string subject, string body, CancellationToken cancellationToken = default)
    {
        var tasks = toEmails.Select(email => GuiEmailAsync(email, subject, body, cancellationToken));
        await Task.WhenAll(tasks);
    }

    public async Task GuiEmailVoiTemplateAsync(string toEmail, string templateName, object model, CancellationToken cancellationToken = default)
    {
        // Implement template rendering logic here
        // For now, just send a simple email
        var subject = $"Thông báo từ hệ thống - {templateName}";
        var body = $"<p>Xin chào,</p><p>Đây là email được gửi từ template {templateName}.</p>";
        
        await GuiEmailAsync(toEmail, subject, body, cancellationToken);
    }
}
