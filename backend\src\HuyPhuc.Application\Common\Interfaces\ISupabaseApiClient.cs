using HuyPhuc.Domain.Entities;

namespace HuyPhuc.Application.Common.Interfaces;

public interface ISupabaseApiClient
{
    Task<IEnumerable<NguoiDung>> LayDanhSachNguoiDungAsync(int offset = 0, int limit = 10);
    Task<NguoiDung?> LayNguoiDungTheoIdAsync(int id);
    Task<NguoiDung> TaoNguoiDungAsync(NguoiDung nguoiDung);
    Task<NguoiDung> CapNhatNguoiDungAsync(NguoiDung nguoiDung);
    Task<bool> XoaNguoiDungAsync(int id);
}
