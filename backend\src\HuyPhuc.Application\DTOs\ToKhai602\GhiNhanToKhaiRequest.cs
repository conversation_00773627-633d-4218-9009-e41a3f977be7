using System.ComponentModel.DataAnnotations;

namespace HuyPhuc.Application.DTOs.ToKhai602;

/// <summary>
/// Request DTO cho việc ghi nhận tờ khai 602
/// </summary>
public class GhiNhanToKhaiRequest
{
    /// <summary>
    /// ID của draft tờ khai
    /// </summary>
    [Required(ErrorMessage = "Vui lòng cung cấp ID tờ khai")]
    public int DraftId { get; set; }

    /// <summary>
    /// Danh sách lao động
    /// </summary>
    [Required(ErrorMessage = "Vui lòng cung cấp danh sách lao động")]
    public List<LaoDongToKhaiDto> DanhSachLaoDong { get; set; } = new();
}


