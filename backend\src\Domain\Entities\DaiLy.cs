using HuyPhuc.Domain.Common;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity đại diện cho đại lý
/// </summary>
public class DaiLy : BaseAuditableEntity
{
    /// <summary>
    /// Mã đại lý (unique)
    /// </summary>
    public string MaDaiLy { get; set; } = string.Empty;

    /// <summary>
    /// Tên đại lý
    /// </summary>
    public string TenDaiLy { get; set; } = string.Empty;

    /// <summary>
    /// Địa chỉ đại lý
    /// </summary>
    public string? DiaChi { get; set; }

    /// <summary>
    /// Số điện thoại
    /// </summary>
    public string? SoDienThoai { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Mã số thuế
    /// </summary>
    public string? MaSoThue { get; set; }

    /// <summary>
    /// Người đại diện
    /// </summary>
    public string? NguoiDaiDien { get; set; }

    /// <summary>
    /// Trạng thái hoạt động
    /// </summary>
    public bool TrangThaiHoatDong { get; set; } = true;

    /// <summary>
    /// Ghi chú
    /// </summary>
    public string? GhiChu { get; set; }

    // Navigation properties
    /// <summary>
    /// Danh sách đơn vị thuộc đại lý
    /// </summary>
    public virtual ICollection<DonVi> DanhSachDonVi { get; set; } = new List<DonVi>();

    /// <summary>
    /// Danh sách người dùng thuộc đại lý
    /// </summary>
    public virtual ICollection<NguoiDung> DanhSachNguoiDung { get; set; } = new List<NguoiDung>();
}
