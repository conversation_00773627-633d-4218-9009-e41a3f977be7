import { Pipe, PipeTransform, inject } from '@angular/core';
import { HuyenService } from '../../core/services';

/**
 * <PERSON><PERSON> để convert mã huyện thành tên huyện
 * Usage: {{ maHuyen | huyenName }}
 * Usage: {{ maHuyen | huyenName:'display' }} // để hiển thị text display
 */
@Pipe({
  name: 'huyenName',
  standalone: true
})
export class HuyenNamePipe implements PipeTransform {
  private readonly huyenService = inject(HuyenService);

  transform(maHuyen: string, type: 'name' | 'display' = 'name'): string {
    if (!maHuyen) {
      return '';
    }

    if (type === 'display') {
      return this.huyenService.convertMaToTextDisplay(maHuyen);
    } else {
      return this.huyenService.convertMaToTen(maHuyen);
    }
  }
}
