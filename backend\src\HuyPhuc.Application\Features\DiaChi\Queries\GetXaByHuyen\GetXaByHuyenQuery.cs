using HuyPhuc.Application.Common.Models;
using HuyPhuc.Application.DTOs.DiaChi;
using HuyPhuc.Domain.Repositories;
using MediatR;

namespace HuyPhuc.Application.Features.DiaChi.Queries.GetXaByHuyen;

/// <summary>
/// Query để lấy danh sách xã theo mã huyện
/// </summary>
public record GetXaByHuyenQuery(string MaHuyen) : IRequest<Result<List<XaOptionDto>>>;

/// <summary>
/// Handler cho GetXaByHuyenQuery
/// </summary>
public class GetXaByHuyenQueryHandler : IRequestHandler<GetXaByHuyenQuery, Result<List<XaOptionDto>>>
{
    private readonly IDmXaRepository _xaRepository;

    public GetXaByHuyenQueryHandler(IDmXaRepository xaRepository)
    {
        _xaRepository = xaRepository;
    }

    public async Task<Result<List<XaOptionDto>>> Handle(GetXaByHuyenQuery request, CancellationToken cancellationToken)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.MaHuyen))
            {
                return Result<List<XaOptionDto>>.Failure("Mã huyện không được để trống");
            }

            var xaList = await _xaRepository.GetByMaHuyenAsync(request.MaHuyen, cancellationToken);

            var result = xaList.Select(x => new XaOptionDto
            {
                Value = x.MaXa,
                Text = x.TextDisplay,
                Ten = x.TenXa,
                Ma = null
            }).ToList();

            return Result<List<XaOptionDto>>.Success(result);
        }
        catch (Exception ex)
        {
            return Result<List<XaOptionDto>>.Failure($"Lỗi khi lấy danh sách xã: {ex.Message}");
        }
    }
}
