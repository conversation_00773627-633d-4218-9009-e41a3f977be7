using MediatR;

namespace HuyPhuc.Application.Features.KeKhai.Commands.XoaLaoDong;

/// <summary>
/// Command xóa lao động khỏi kê khai
/// </summary>
public record XoaLaoDongKeKhaiCommand(XoaLaoDongKeKhaiRequest Request) : IRequest<XoaLaoDongResponse>;

/// <summary>
/// Request xóa lao động khỏi kê khai
/// </summary>
public class XoaLaoDongKeKhaiRequest
{
    /// <summary>
    /// ID lao động cần xóa
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// ID kê khai
    /// </summary>
    public int KeKhaiId { get; set; }
}

/// <summary>
/// Response xóa lao động
/// </summary>
public class XoaLaoDongResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
}
