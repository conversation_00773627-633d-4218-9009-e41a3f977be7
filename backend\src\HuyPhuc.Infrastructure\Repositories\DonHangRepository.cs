using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Repositories;
using HuyPhuc.Infrastructure.Repositories.Base;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Infrastructure.Repositories;

public class DonHangRepository : Repository<DonHang>, IDonHangRepository
{
    public DonHangRepository(IApplicationDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<DonHang>> LayTheoNguoiDungAsync(int nguoiDungId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.NguoiDungId == nguoiDungId)
            .Include(x => x.DanhSachChiTiet)
                .ThenInclude(x => x.SanPham)
            .OrderByDescending(x => x.NgayDatHang)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<DonHang>> LayTheoTrangThaiAsync(TrangThaiDonHang trangThai, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.TrangThai == trangThai)
            .Include(x => x.NguoiDung)
            .Include(x => x.DanhSachChiTiet)
                .ThenInclude(x => x.SanPham)
            .OrderByDescending(x => x.NgayDatHang)
            .ToListAsync(cancellationToken);
    }

    public async Task<DonHang?> LayTheoMaDonHangAsync(string maDonHang, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(x => x.NguoiDung)
            .Include(x => x.DanhSachChiTiet)
                .ThenInclude(x => x.SanPham)
            .FirstOrDefaultAsync(x => x.MaDonHang == maDonHang, cancellationToken);
    }

    public async Task<IEnumerable<DonHang>> LayDonHangTrongKhoangThoiGianAsync(
        DateTime tuNgay, 
        DateTime denNgay, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(x => x.NgayDatHang >= tuNgay && x.NgayDatHang <= denNgay)
            .Include(x => x.NguoiDung)
            .Include(x => x.DanhSachChiTiet)
                .ThenInclude(x => x.SanPham)
            .OrderByDescending(x => x.NgayDatHang)
            .ToListAsync(cancellationToken);
    }

    public async Task<decimal> TinhTongDoanhThuAsync(
        DateTime? tuNgay = null, 
        DateTime? denNgay = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(x => x.TrangThai == TrangThaiDonHang.DaGiao);

        if (tuNgay.HasValue)
            query = query.Where(x => x.NgayGiaoHang >= tuNgay.Value);

        if (denNgay.HasValue)
            query = query.Where(x => x.NgayGiaoHang <= denNgay.Value);

        return await query.SumAsync(x => x.TongThanhToan, cancellationToken);
    }

    public async Task<int> DemDonHangTheoTrangThaiAsync(TrangThaiDonHang trangThai, CancellationToken cancellationToken = default)
    {
        return await _dbSet.CountAsync(x => x.TrangThai == trangThai, cancellationToken);
    }
}
