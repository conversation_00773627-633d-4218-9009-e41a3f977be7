import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, tap, delay } from 'rxjs/operators';

import { DanToc, DanTocResponse, DanTocOption } from '../models';
import { environment } from '../../../../../environments/environment';

/**
 * Service để quản lý thông tin dân tộc
 */
@Injectable({
  providedIn: 'root'
})
export class DanTocService {
  private readonly http = inject(HttpClient);
  private readonly apiUrl = `${environment.apiUrl}/dan-toc`;

  // Cache danh sách dân tộc
  private danTocListSubject = new BehaviorSubject<DanToc[]>([]);
  public danTocList$ = this.danTocListSubject.asObservable();

  // Cache đã load hay chưa
  private isLoaded = false;

  /**
   * <PERSON><PERSON><PERSON> danh sách tất cả dân tộc
   */
  getAllDanToc(): Observable<DanToc[]> {
    if (this.isLoaded && this.danTocListSubject.value.length > 0) {
      return of(this.danTocListSubject.value);
    }

    return this.http.get<any>(this.apiUrl).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data.map((item: any) => ({
            id: item.id || 0,
            ma: item.ma || '',
            ten: item.ten || '',
            maVaTen: item.maVaTen || item.ma_va_ten || '',
            canCu: item.canCu || item.can_cu || '',
            rownum: item.rownum || 0,
            createdAt: item.createdAt || item.created_at,
            updatedAt: item.updatedAt || item.updated_at
          }));
        }
        return [];
      }),
      tap(danTocList => {
        this.danTocListSubject.next(danTocList);
        this.isLoaded = true;
        console.log('📋 Loaded dân tộc list:', danTocList.length);
      }),
      catchError(error => {
        console.error('🔴 Lỗi khi lấy danh sách dân tộc:', error);
        // Fallback to mock data if API fails
        return this.getMockDanTocData().pipe(
          tap(danTocList => {
            this.danTocListSubject.next(danTocList);
            this.isLoaded = true;
          })
        );
      })
    );
  }

  /**
   * Lấy danh sách dân tộc dưới dạng options cho dropdown
   */
  getDanTocOptions(): Observable<DanTocOption[]> {
    return this.getAllDanToc().pipe(
      map(danTocList => 
        danTocList
          .sort((a, b) => (a.rownum || 0) - (b.rownum || 0)) // Sắp xếp theo rownum
          .map(danToc => ({
            value: danToc.ma,
            text: danToc.maVaTen,
            ten: danToc.ten,
            ma: danToc.ma
          }))
      )
    );
  }

  /**
   * Tìm dân tộc theo mã
   */
  getDanTocByMa(maDanToc: string): Observable<DanToc | null> {
    return this.getAllDanToc().pipe(
      map(danTocList => {
        const danToc = danTocList.find(dt => dt.ma === maDanToc);
        return danToc || null;
      })
    );
  }

  /**
   * Lấy tên dân tộc theo mã
   */
  getTenDanTocByMa(maDanToc: string): Observable<string> {
    return this.getDanTocByMa(maDanToc).pipe(
      map(danToc => danToc ? danToc.ten : maDanToc)
    );
  }

  /**
   * Lấy mã và tên dân tộc theo mã
   */
  getMaVaTenByMa(maDanToc: string): Observable<string> {
    return this.getDanTocByMa(maDanToc).pipe(
      map(danToc => danToc ? danToc.maVaTen : maDanToc)
    );
  }

  /**
   * Convert mã dân tộc thành tên dân tộc (sync version cho pipe)
   */
  convertMaToTen(maDanToc: string): string {
    const danTocList = this.danTocListSubject.value;
    const danToc = danTocList.find(dt => dt.ma === maDanToc);
    return danToc ? danToc.ten : maDanToc;
  }

  /**
   * Convert mã dân tộc thành mã và tên (sync version cho pipe)
   */
  convertMaToMaVaTen(maDanToc: string): string {
    const danTocList = this.danTocListSubject.value;
    const danToc = danTocList.find(dt => dt.ma === maDanToc);
    return danToc ? danToc.maVaTen : maDanToc;
  }

  /**
   * Refresh cache - force reload từ API
   */
  refreshCache(): Observable<DanToc[]> {
    this.isLoaded = false;
    this.danTocListSubject.next([]);
    return this.getAllDanToc();
  }

  /**
   * Mock data cho testing (tạm thời)
   */
  private getMockDanTocData(): Observable<DanToc[]> {
    const mockData: DanToc[] = [
      {
        id: 1,
        ma: '01',
        ten: 'Kinh',
        maVaTen: '01 - Kinh',
        canCu: ' ',
        rownum: 1.0
      },
      {
        id: 2,
        ma: '02',
        ten: 'Tày',
        maVaTen: '02 - Tày',
        canCu: ' ',
        rownum: 2.0
      },
      {
        id: 3,
        ma: '03',
        ten: 'Thái',
        maVaTen: '03 - Thái',
        canCu: ' ',
        rownum: 3.0
      },
      {
        id: 4,
        ma: '04',
        ten: 'Hoa',
        maVaTen: '04 - Hoa',
        canCu: ' ',
        rownum: 4.0
      },
      {
        id: 5,
        ma: '05',
        ten: 'Khơ-me',
        maVaTen: '05 - Khơ-me',
        canCu: ' ',
        rownum: 5.0
      },
      {
        id: 56,
        ma: '99',
        ten: 'Người nước ngoài',
        maVaTen: '99 - Người nước ngoài',
        canCu: null,
        rownum: 56.0
      }
    ];

    console.log('🧪 Using mock dân tộc data');
    return of(mockData);
  }
}
