using AutoMapper;
using HuyPhuc.Domain.Entities;

namespace HuyPhuc.Application.Features.DanhMucThuTuc.Common;

/// <summary>
/// AutoMapper profile cho danh mục thủ tục
/// </summary>
public class DanhMucThuTucMappingProfile : Profile
{
    public DanhMucThuTucMappingProfile()
    {
        // Entity to DTO mappings
        CreateMap<Domain.Entities.DanhMucThuTuc, DanhMucThuTucDto>()
            .ForMember(dest => dest.Created, opt => opt.MapFrom(src => src.NgayTao))
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.NguoiTao))
            .ForMember(dest => dest.LastModified, opt => opt.MapFrom(src => src.NgayCapNhat))
            .ForMember(dest => dest.LastModifiedBy, opt => opt.MapFrom(src => src.NguoiCapNhat))
            .ForMember(dest => dest.DangHoatDong, opt => opt.MapFrom(src => src.DangHoatDong))
            .ForMember(dest => dest.CoHieuLuc, opt => opt.MapFrom(src => src.CoHieuLuc));

        CreateMap<Domain.Entities.DanhMucThuTuc, DanhMucThuTucSummaryDto>()
            .ForMember(dest => dest.DangHoatDong, opt => opt.MapFrom(src => src.DangHoatDong))
            .ForMember(dest => dest.CoHieuLuc, opt => opt.MapFrom(src => src.CoHieuLuc));

        // DTO to Entity mappings (for create/update)
        CreateMap<CreateUpdateDanhMucThuTucDto, Domain.Entities.DanhMucThuTuc>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.TrangThai, opt => opt.Ignore())
            .ForMember(dest => dest.NgayTao, opt => opt.Ignore())
            .ForMember(dest => dest.NguoiTao, opt => opt.Ignore())
            .ForMember(dest => dest.NgayCapNhat, opt => opt.Ignore())
            .ForMember(dest => dest.NguoiCapNhat, opt => opt.Ignore())
            .ForMember(dest => dest.DomainEvents, opt => opt.Ignore());
    }
}
