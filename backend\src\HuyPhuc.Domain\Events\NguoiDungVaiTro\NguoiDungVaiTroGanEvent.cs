using MediatR;

namespace HuyPhuc.Domain.Events.NguoiDungVaiTro;

public record NguoiDungVaiTroGanEvent(Entities.NguoiDungVaiTro NguoiDungVaiTro) : INotification;
public record NguoiDungVaiTroCapNhatEvent(Entities.NguoiDungVaiTro NguoiDungVaiTro) : INotification;
public record NguoiDungVaiTroKichHoatEvent(Entities.NguoiDungVaiTro NguoiDungVaiTro) : INotification;
public record NguoiDungVaiTroVoHieuHoaEvent(Entities.NguoiDungVaiTro NguoiDungVaiTro) : INotification;
public record NguoiDungVaiTroGiaHanEvent(Entities.NguoiDungVaiTro NguoiDungVaiTro) : INotification;
