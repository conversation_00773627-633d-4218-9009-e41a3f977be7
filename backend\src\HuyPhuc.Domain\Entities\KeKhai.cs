using HuyPhuc.Domain.Common;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity cho bảng danh_sach_ke_khai
/// </summary>
public class KeKhai : BaseAuditableEntity
{
    public int Id { get; set; }
    public string <PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; } = string.Empty;
    public int ThuTucId { get; set; }
    public int DaiLyId { get; set; }
    public int DonViId { get; set; }
    public string SoSoBHXH { get; set; } = "1";
    public string? ThongTinHeader { get; set; } // JSON
    public int TrangThai { get; set; } = 0; // 0: <PERSON><PERSON> so<PERSON>, 1: <PERSON><PERSON>, 2: <PERSON><PERSON>, 3: Bị từ chối
    public int NguoiTaoId { get; set; }
    public DateTime NgayTao { get; set; }
    public int? NguoiPheDuyetId { get; set; }
    public DateTime? NgayPheDuyet { get; set; }
    public string? LyDoTuChoi { get; set; }
    public string? FileXmlPath { get; set; }
    public string? ChuKySo { get; set; }
    public string? DigestValue { get; set; }
    public string? SignatureValue { get; set; }
    public string? GhiChu { get; set; }

    // Navigation properties
    public virtual DanhMucThuTuc ThuTuc { get; set; } = null!;
    public virtual DaiLy DaiLy { get; set; } = null!;
    public virtual DonVi DonVi { get; set; } = null!;
    public virtual NguoiDung NguoiTao { get; set; } = null!;
    public virtual NguoiDung? NguoiPheDuyet { get; set; }
    public virtual ICollection<ChiTietLaoDongKeKhaiV2> DanhSachLaoDong { get; set; } = new List<ChiTietLaoDongKeKhaiV2>();
}
