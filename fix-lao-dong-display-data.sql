-- Sc<PERSON>t để sửa dữ liệu hiển thị lao động
-- Vấn đề: họ tên và CMND hiển thị không đúng

-- 1. <PERSON><PERSON><PERSON> tra dữ liệu hiện tại
SELECT 
    'tk1_ts' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN ho_ten LIKE 'Lao động %' THEN 1 END) as invalid_ho_ten_pattern,
    COUNT(CASE WHEN ho_ten = '[Cần cập nhật họ tên]' THEN 1 END) as placeholder_ho_ten,
    COUNT(CASE WHEN cmnd = '000000000000' THEN 1 END) as placeholder_cmnd,
    COUNT(CASE WHEN cmnd = ma_so_bhxh THEN 1 END) as cmnd_equals_bhxh
FROM tk1_ts

UNION ALL

SELECT 
    'chi_tiet_lao_dong_ke_khai_v2' as table_name,
    COUNT(*) as total_records,
    0 as invalid_ho_ten_pattern,
    0 as placeholder_ho_ten,
    0 as placeholder_cmnd,
    0 as cmnd_equals_bhxh
FROM chi_tiet_lao_dong_ke_khai_v2;

-- 2. Xem chi tiết các bản ghi có vấn đề
SELECT 
    'Problematic records in tk1_ts' as description,
    ma_so_bhxh, 
    ho_ten, 
    cmnd, 
    ngay_sinh, 
    gioi_tinh,
    last_updated
FROM tk1_ts 
WHERE ho_ten LIKE 'Lao động %' 
   OR ho_ten = '[Cần cập nhật họ tên]'
   OR cmnd = '000000000000'
   OR cmnd = ma_so_bhxh
ORDER BY last_updated DESC
LIMIT 10;

-- 3. Cập nhật dữ liệu để đảm bảo tính nhất quán

-- Cập nhật họ tên: nếu là pattern "Lao động XXXX" thì chuyển thành placeholder
UPDATE tk1_ts 
SET ho_ten = '[Cần cập nhật họ tên]',
    last_updated = CURRENT_TIMESTAMP
WHERE ho_ten LIKE 'Lao động %'
  AND ho_ten != '[Cần cập nhật họ tên]';

-- Cập nhật CMND: nếu bằng mã số BHXH thì chuyển thành placeholder
UPDATE tk1_ts 
SET cmnd = '000000000000',
    last_updated = CURRENT_TIMESTAMP
WHERE cmnd = ma_so_bhxh
  AND cmnd != '000000000000';

-- 4. Kiểm tra kết quả sau khi cập nhật
SELECT 
    'After update - tk1_ts' as description,
    COUNT(*) as total_records,
    COUNT(CASE WHEN ho_ten LIKE 'Lao động %' THEN 1 END) as remaining_invalid_ho_ten,
    COUNT(CASE WHEN ho_ten = '[Cần cập nhật họ tên]' THEN 1 END) as placeholder_ho_ten,
    COUNT(CASE WHEN cmnd = '000000000000' THEN 1 END) as placeholder_cmnd,
    COUNT(CASE WHEN cmnd = ma_so_bhxh THEN 1 END) as remaining_cmnd_equals_bhxh
FROM tk1_ts;

-- 5. Xem một số bản ghi mẫu sau khi cập nhật
SELECT 
    'Sample records after update' as description,
    ma_so_bhxh, 
    ho_ten, 
    cmnd, 
    ngay_sinh,
    CASE 
        WHEN gioi_tinh = 1 THEN 'Nam'
        WHEN gioi_tinh = 2 THEN 'Nữ'
        ELSE 'Khác'
    END as gioi_tinh_display,
    last_updated
FROM tk1_ts 
ORDER BY last_updated DESC
LIMIT 5;

-- 6. Kiểm tra dữ liệu JOIN giữa hai bảng
SELECT 
    'JOIN result sample' as description,
    c.ma_so_bhxh,
    c.stt,
    COALESCE(t.ho_ten, '[Chưa có thông tin]') as ho_ten_display,
    COALESCE(t.cmnd, '000000000000') as cmnd_display,
    t.ngay_sinh,
    t.gioi_tinh,
    c.phuong_an,
    c.muc_thu_nhap
FROM chi_tiet_lao_dong_ke_khai_v2 c
LEFT JOIN tk1_ts t ON c.ma_so_bhxh = t.ma_so_bhxh
ORDER BY c.id
LIMIT 5;

-- 7. Thống kê tổng quan
SELECT 
    'Summary statistics' as description,
    (SELECT COUNT(*) FROM tk1_ts) as total_tk1_ts,
    (SELECT COUNT(*) FROM chi_tiet_lao_dong_ke_khai_v2) as total_chi_tiet,
    (SELECT COUNT(*) FROM chi_tiet_lao_dong_ke_khai_v2 c 
     LEFT JOIN tk1_ts t ON c.ma_so_bhxh = t.ma_so_bhxh 
     WHERE t.ma_so_bhxh IS NOT NULL) as records_with_tk1_data,
    (SELECT COUNT(*) FROM chi_tiet_lao_dong_ke_khai_v2 c 
     LEFT JOIN tk1_ts t ON c.ma_so_bhxh = t.ma_so_bhxh 
     WHERE t.ma_so_bhxh IS NULL) as records_without_tk1_data;
