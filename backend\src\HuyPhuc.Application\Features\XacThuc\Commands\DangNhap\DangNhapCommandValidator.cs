using FluentValidation;

namespace HuyPhuc.Application.Features.XacThuc.Commands.DangNhap;

public class DangNhapCommandValidator : AbstractValidator<DangNhapCommand>
{
    public DangNhapCommandValidator()
    {
        RuleFor(x => x.Username)
            .NotEmpty().WithMessage("Username không được để trống")
            .MinimumLength(3).WithMessage("Username phải có ít nhất 3 ký tự")
            .MaximumLength(50).WithMessage("Username không được vượt quá 50 ký tự");

        RuleFor(x => x.Mat<PERSON>hau)
            .NotEmpty().WithMessage("Mật khẩu không được để trống")
            .MinimumLength(6).WithMessage("Mật khẩu phải có ít nhất 6 ký tự");
    }
}
