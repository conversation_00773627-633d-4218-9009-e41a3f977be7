import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>e<PERSON><PERSON> } from '../../../core/models';

/**
 * Interface cho thông tin khác trong tờ khai 602
 */
export interface ThongTinKhac602 {
  soSoBHXH: string;
  ngayTao?: Date;
  nguoiTao?: string;
  ghiChu?: string;
}

/**
 * Interface chính cho Tờ khai 602
 */
export interface ToKhai602 extends KeKhaiBase {
  loaiKeKhai: LoaiKeKhai.ToKhai602;
  thongTinKhac: ThongTinKhac602;
  danhSachLaoDong: LaoDong[];
  
  // Thông tin tính toán
  tongSoLaoDong?: number;
  tongTienDong?: number;
  
  // Thông tin xử lý
  fileXmlPath?: string;
  chuKySo?: string;
  digestValue?: string;
  signatureValue?: string;
}

/**
 * Interface cho form state của tờ khai 602
 */
export interface ToKhai602FormState {
  // Thông tin cơ bản
  daiLyId: number | null;
  donViId: number | null;
  soSoBHXH: string;
  ghiChu: string;
  
  // Danh sách lao động
  danhSachLaoDong: LaoDong[];
  
  // Trạng thái form
  isValid: boolean;
  isDirty: boolean;
  errors: Record<string, string>;
}

/**
 * Interface cho filter tờ khai 602
 */
export interface ToKhai602Filter {
  trang?: number;
  kichThuoc?: number;
  tuKhoa?: string;
  daiLyId?: number | null;
  donViId?: number | null;
  trangThai?: string;
  tuNgay?: Date;
  denNgay?: Date;
  soSoBHXH?: string;
  kyKeKhai?: string;
}

/**
 * Interface cho response danh sách tờ khai 602
 */
export interface ToKhai602Response {
  data: ToKhai602[];
  tongSo: number;
  trang: number;
  kichThuoc: number;
  tongTrang: number;
}
