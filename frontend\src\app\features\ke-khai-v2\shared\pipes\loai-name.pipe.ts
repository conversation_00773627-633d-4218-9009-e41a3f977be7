import { Pipe, PipeTransform, inject } from '@angular/core';
import { LoaiService } from '../../core/services';

/**
 * <PERSON><PERSON> để convert ID loại thành tên loại
 * Usage: {{ idLoai | loaiName }}
 */
@Pipe({
  name: 'loaiName',
  standalone: true
})
export class LoaiNamePipe implements PipeTransform {
  private readonly loaiService = inject(LoaiService);

  transform(id: number): string {
    if (!id) {
      return '';
    }

    return this.loaiService.convertIdToTen(id);
  }
}
