import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { QuanLyToKhai602Component } from './components/quan-ly-to-khai-602/quan-ly-to-khai-602.component';
import { KhaiBaoToKhai602Component } from './components/khai-bao-to-khai-602/khai-bao-to-khai-602.component';
import { NhapLaoDongComponent } from './components/nhap-lao-dong/components/nhap-lao-dong';
import { DanhSachLaoDongComponent } from './components/danh-sach-lao-dong/danh-sach-lao-dong.component';

/**
 * Routing configuration cho feature tờ khai 602
 * - Trang khai báo độc lập
 * - Trang quản lý với tabs cho nhập và hiển thị lao động
 */
const routes: Routes = [
  {
    path: '',
    redirectTo: 'khai-bao',
    pathMatch: 'full'
  },
  // Trang khai báo tờ khai - độc lập
  {
    path: 'khai-bao',
    component: KhaiBaoToKhai602Component,
    title: 'Khai báo Tờ khai 602 - Hệ thống quản lý',
    data: {
      breadcrumb: 'Khai báo tờ khai 602',
      description: 'Tạo tờ khai đóng bảo hiểm xã hội cho lao động tự do'
    }
  },
  // Trang quản lý tờ khai với tabs (sau khi đã tạo)
  {
    path: 'quan-ly/:id',
    component: QuanLyToKhai602Component,
    children: [
      {
        path: '',
        redirectTo: 'nhap-lao-dong',
        pathMatch: 'full'
      },
      {
        path: 'nhap-lao-dong',
        component: NhapLaoDongComponent,
        title: 'Nhập thông tin lao động - Tờ khai 602',
        data: {
          breadcrumb: 'Nhập thông tin lao động',
          description: 'Nhập thông tin chi tiết cho từng lao động'
        }
      },
      {
        path: 'danh-sach-lao-dong',
        component: DanhSachLaoDongComponent,
        title: 'Danh sách người lao động - Tờ khai 602',
        data: {
          breadcrumb: 'Danh sách người lao động',
          description: 'Xem và quản lý danh sách người lao động đã khai báo'
        }
      }
    ]
  }
  // TODO: Thêm các routes khác như danh sách, chi tiết, chỉnh sửa
  // {
  //   path: 'danh-sach',
  //   component: DanhSachToKhaiComponent,
  //   title: 'Danh sách Tờ khai 602',
  //   data: {
  //     breadcrumb: 'Danh sách tờ khai',
  //     description: 'Quản lý danh sách tờ khai 602'
  //   }
  // },
  // {
  //   path: 'chi-tiet/:id',
  //   component: ChiTietToKhaiComponent,
  //   title: 'Chi tiết Tờ khai 602',
  //   data: {
  //     breadcrumb: 'Chi tiết tờ khai',
  //     description: 'Xem chi tiết tờ khai 602'
  //   }
  // }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ToKhai602RoutingModule { }
