import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { LaoDong } from '../../../../models/to-khai-602.model';
import { LaoDongKeKhaiDto } from '../../../../../../../shared/models/ke-khai.model';
import { EditLaoDongService } from '../../services/edit-lao-dong.service';

/**
 * Component modal chỉnh sửa thông tin lao động
 * Hỗ trợ cả flow cũ (LaoDong) và flow mới (LaoDongKeKhaiDto)
 */
@Component({
  selector: 'app-edit-lao-dong-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './edit-lao-dong-modal.component.html',
  styleUrls: ['./edit-lao-dong-modal.component.scss']
})
export class EditLaoDongModalComponent implements OnInit, OnDestroy {
  @Input() isOpen = false;
  @Input() laoDong: LaoDong | LaoDongKeKhaiDto | null = null;
  @Input() keKhaiId: number | null = null; // Cho flow mới
  @Input() isFlowMoi = false; // Phân biệt flow cũ và mới

  @Output() closeModal = new EventEmitter<void>();
  @Output() saveSuccess = new EventEmitter<LaoDong | LaoDongKeKhaiDto>();

  editForm!: FormGroup;
  dangTai = false;
  dangLuu = false;

  private destroy$ = new Subject<void>();

  constructor(
    private editLaoDongService: EditLaoDongService
  ) {}

  ngOnInit(): void {
    this.khoiTaoForm();
    this.subscribeToStates();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Khởi tạo form chỉnh sửa
   */
  private khoiTaoForm(): void {
    this.editForm = this.editLaoDongService.taoFormChinhSua(this.laoDong || undefined);
  }

  /**
   * Subscribe to service states
   */
  private subscribeToStates(): void {
    this.editLaoDongService.dangTai$
      .pipe(takeUntil(this.destroy$))
      .subscribe(dangTai => this.dangTai = dangTai);

    this.editLaoDongService.dangLuu$
      .pipe(takeUntil(this.destroy$))
      .subscribe(dangLuu => this.dangLuu = dangLuu);
  }

  /**
   * Đóng modal
   */
  onCloseModal(): void {
    if (this.dangLuu) return; // Không cho đóng khi đang lưu
    this.closeModal.emit();
  }

  /**
   * Lưu thông tin lao động
   */
  async onSave(): Promise<void> {
    if (!this.editLaoDongService.validateForm(this.editForm)) {
      return;
    }

    if (this.isFlowMoi && this.keKhaiId) {
      // Flow mới: Gọi API cập nhật
      this.editLaoDongService.capNhatLaoDong(this.keKhaiId, this.editForm.value)
        .pipe(takeUntil(this.destroy$))
        .subscribe(success => {
          if (success) {
            this.saveSuccess.emit(this.editForm.value);
            this.onCloseModal();
          }
        });
    } else {
      // Flow cũ: Emit dữ liệu để parent component xử lý
      const updatedLaoDong = { ...this.laoDong, ...this.editForm.value };
      this.saveSuccess.emit(updatedLaoDong);
      this.onCloseModal();
    }
  }

  /**
   * Reset form về giá trị ban đầu
   */
  onReset(): void {
    this.editForm.reset();
    this.khoiTaoForm();
  }

  /**
   * Kiểm tra field có lỗi không
   */
  hasError(fieldName: string): boolean {
    const field = this.editForm.get(fieldName);
    return !!(field?.errors && field.touched);
  }

  /**
   * Lấy thông báo lỗi cho field
   */
  getErrorMessage(fieldName: string): string {
    return this.editLaoDongService.getFieldErrorMessage(this.editForm, fieldName);
  }

  /**
   * Format tiền tệ
   */
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(value);
  }

  /**
   * Xử lý thay đổi mức thu nhập để tính toán các giá trị khác
   */
  onMucThuNhapChange(): void {
    const mucThuNhap = this.editForm.get('mucThuNhap')?.value || 0;
    const tyLeDong = this.editForm.get('tyLeDong')?.value || 22;
    const soThang = this.editForm.get('soThang')?.value || 1;
    
    // Tính toán tự động
    const tienTuDong = Math.round(mucThuNhap * (tyLeDong / 100) * soThang);
    
    this.editForm.patchValue({
      tienTuDong: tienTuDong,
      tongTien: tienTuDong + (this.editForm.get('tienLai')?.value || 0) - (this.editForm.get('tienThua')?.value || 0)
    });
  }

  /**
   * Xử lý thay đổi tỷ lệ đóng
   */
  onTyLeDongChange(): void {
    this.onMucThuNhapChange(); // Tính lại khi tỷ lệ thay đổi
  }

  /**
   * Xử lý thay đổi số tháng
   */
  onSoThangChange(): void {
    this.onMucThuNhapChange(); // Tính lại khi số tháng thay đổi
  }

  /**
   * Xử lý thay đổi tiền lãi/thừa
   */
  onTienChange(): void {
    const tienTuDong = this.editForm.get('tienTuDong')?.value || 0;
    const tienLai = this.editForm.get('tienLai')?.value || 0;
    const tienThua = this.editForm.get('tienThua')?.value || 0;
    
    const tongTien = tienTuDong + tienLai - tienThua;
    
    this.editForm.patchValue({
      tongTien: tongTien
    });
  }

  /**
   * Lấy danh sách phương án đóng
   */
  get danhSachPhuongAn(): Array<{value: string, label: string}> {
    return [
      { value: 'DB', label: 'Đóng bù' },
      { value: 'DT', label: 'Đóng tiếp' },
      { value: 'TN', label: 'Tự nguyện' }
    ];
  }

  /**
   * Lấy danh sách phương thức đóng
   */
  get danhSachPhuongThuc(): Array<{value: string, label: string}> {
    return [
      { value: '1', label: 'Phương thức 1' },
      { value: '2', label: 'Phương thức 2' },
      { value: '3', label: 'Phương thức 3' }
    ];
  }

  /**
   * Lấy danh sách giới tính
   */
  get danhSachGioiTinh(): Array<{value: string, label: string}> {
    return [
      { value: 'Nam', label: 'Nam' },
      { value: 'Nữ', label: 'Nữ' }
    ];
  }

  /**
   * Kiểm tra modal có đang mở không
   */
  get isModalOpen(): boolean {
    return this.isOpen && !!this.laoDong;
  }

  /**
   * Lấy tiêu đề modal
   */
  get modalTitle(): string {
    if (this.laoDong) {
      const hoTen = this.laoDong.hoTen || 'Lao động';
      return `Chỉnh sửa thông tin: ${hoTen}`;
    }
    return 'Chỉnh sửa thông tin lao động';
  }

  /**
   * Kiểm tra có thể lưu không
   */
  get canSave(): boolean {
    return this.editForm.valid && !this.dangLuu;
  }
}
