using HuyPhuc.Domain.Common;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity cho danh mục dân tộc
/// </summary>
public class DmDanToc : BaseAuditableEntity
{
    /// <summary>
    /// Mã dân tộc (2 ký tự)
    /// </summary>
    public string Ma { get; set; } = string.Empty;

    /// <summary>
    /// Tên dân tộc
    /// </summary>
    public string Ten { get; set; } = string.Empty;

    /// <summary>
    /// Mã và tên dân tộc (mã - tên)
    /// </summary>
    public string MaVaTen { get; set; } = string.Empty;

    /// <summary>
    /// <PERSON><PERSON>n cứ pháp lý
    /// </summary>
    public string? CanCu { get; set; }

    /// <summary>
    /// Số thứ tự
    /// </summary>
    public decimal? Rownum { get; set; }
}
