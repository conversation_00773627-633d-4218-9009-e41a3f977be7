import { Routes } from '@angular/router';
import { XacThucGuard } from './features/xac-thuc/guards';

export const routes: Routes = [
  // Redirect root to dashboard
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },

  // Auth routes - không cần guard
  {
    path: 'auth',
    loadChildren: () => import('./features/xac-thuc/xac-thuc.module').then(m => m.XacThucModule)
  },

  // Shortcut cho đăng nhập
  {
    path: 'dang-nhap',
    redirectTo: '/auth/dang-nhap',
    pathMatch: 'full'
  },

  // Protected routes với layout - cần xác thực
  {
    path: '',
    loadComponent: () => import('./shared/components/layout/main-layout.component').then(m => m.MainLayoutComponent),
    canActivate: [XacThucGuard],
    children: [
      {
        path: 'dashboard',
        loadComponent: () => import('./features/dashboard/components/dashboard/dashboard').then(m => m.DashboardComponent)
      },
      {
        path: 'danh-muc-thu-tuc',
        loadChildren: () => import('./features/danh-muc-thu-tuc/danh-muc-thu-tuc.module').then(m => m.DanhMucThuTucModule),
        data: {
          title: 'Danh mục thủ tục',
          breadcrumb: 'Danh mục thủ tục'
        }
      },
      // Ke-khai module - Now using v2 as main
      {
        path: 'ke-khai',
        loadChildren: () => import('./features/ke-khai-v2/ke-khai.routes').then(m => m.KE_KHAI_ROUTES),
        data: {
          title: 'Kê khai',
          breadcrumb: 'Kê khai'
        }
      },
      // Legacy ke-khai v1 module (backup)
      {
        path: 'ke-khai-legacy',
        loadChildren: () => import('./features/ke-khai/ke-khai.module').then(m => m.KeKhaiModule),
        data: {
          title: 'Kê khai (Legacy)',
          breadcrumb: 'Kê khai (Legacy)'
        }
      },
      // Backward compatibility - redirect old to-khai-602 routes to v2
      {
        path: 'to-khai-602',
        redirectTo: '/ke-khai/to-khai-602',
        pathMatch: 'prefix'
      },
      // Redirect ke-khai-v2 to main ke-khai (now v2)
      {
        path: 'ke-khai-v2',
        redirectTo: '/ke-khai',
        pathMatch: 'prefix'
      }
      // TODO: Thêm các routes khác sau khi tạo modules
      // {
      //   path: 'ho-so-bhyt',
      //   loadChildren: () => import('./features/ho-so-bhyt/ho-so-bhyt.module').then(m => m.HoSoBHYTModule)
      // },
      // {
      //   path: 'ho-so-bhxh',
      //   loadChildren: () => import('./features/ho-so-bhxh/ho-so-bhxh.module').then(m => m.HoSoBHXHModule)
      // }
    ]
  },

  // Wildcard route - phải để cuối cùng
  {
    path: '**',
    redirectTo: '/dang-nhap'
  }
];
