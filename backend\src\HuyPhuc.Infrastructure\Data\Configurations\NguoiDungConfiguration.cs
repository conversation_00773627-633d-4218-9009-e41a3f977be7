using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class NguoiDungConfiguration : IEntityTypeConfiguration<NguoiDung>
{
    public void Configure(EntityTypeBuilder<NguoiDung> builder)
    {
        builder.ToTable("nguoi_dung");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).HasColumnName("id");

        builder.Property(x => x.HoTen)
            .IsRequired()
            .HasColumnName("ho_ten")
            .HasMaxLength(255);

        // Configure Email value object
        builder.OwnsOne(x => x.Email, email =>
        {
            email.Property(e => e.Value)
                .HasColumnName("email")
                .IsRequired()
                .HasMaxLength(255);
        });

        builder.Property(x => x.MatKhau)
            .IsRequired()
            .HasColumnName("mat_khau")
            .HasMaxLength(255);

        // Configure SoDienThoai value object
        builder.OwnsOne(x => x.SoDienThoai, sdt =>
        {
            sdt.Property(s => s.Value)
                .HasColumnName("so_dien_thoai")
                .HasMaxLength(20);
        });

        builder.Property(x => x.DiaChi)
            .HasColumnName("dia_chi");

        builder.Property(x => x.NgaySinh)
            .HasColumnName("ngay_sinh");

        builder.Property(x => x.GioiTinh)
            .HasColumnName("gioi_tinh")
            .HasMaxLength(10);

        builder.Property(x => x.AvatarUrl)
            .HasColumnName("avatar_url");

        builder.Property(x => x.LastLogin)
            .HasColumnName("last_login");

        builder.Property(x => x.MaNhanVien)
            .HasColumnName("ma_nhan_vien")
            .HasMaxLength(50);

        builder.Property(x => x.Username)
            .HasColumnName("username")
            .HasMaxLength(100);

        builder.Property(x => x.TrangThai)
            .IsRequired()
            .HasColumnName("trang_thai")
            .HasConversion(
                v => v.ToDbString(),
                v => TrangThaiNguoiDungExtensions.FromDbString(v))
            .HasDefaultValue(TrangThaiNguoiDung.Active);

        builder.Property(x => x.NgayTao)
            .IsRequired()
            .HasColumnName("created_at");

        builder.Property(x => x.NgayCapNhat)
            .HasColumnName("updated_at");

        builder.Property(x => x.NguoiTao)
            .HasColumnName("created_by")
            .HasMaxLength(50);

        builder.Property(x => x.NguoiCapNhat)
            .HasColumnName("updated_by")
            .HasMaxLength(50);

        builder.Property(x => x.DaiLyId)
            .HasColumnName("dai_ly_id");

        // Configure relationships
        builder.HasMany(x => x.DanhSachDonHang)
            .WithOne(x => x.NguoiDung)
            .HasForeignKey(x => x.NguoiDungId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(x => x.DaiLy)
            .WithMany(x => x.DanhSachNguoiDung)
            .HasForeignKey(x => x.DaiLyId)
            .OnDelete(DeleteBehavior.SetNull);

        // Ignore domain events
        builder.Ignore(x => x.DomainEvents);
    }
}
