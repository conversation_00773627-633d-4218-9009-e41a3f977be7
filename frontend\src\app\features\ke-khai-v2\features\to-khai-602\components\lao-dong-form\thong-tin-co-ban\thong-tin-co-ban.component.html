<div class="thong-tin-co-ban bg-white border border-gray-200 rounded-lg p-6" [formGroup]="parentForm">
  <h4 class="text-lg font-semibold text-gray-900 mb-6">Thông tin cơ bản</h4>

  <!-- Thông báo tra cứu BHXH - hiển thị toàn bộ chiều rộng -->
  <div *ngIf="thongBaoTraCuu" class="text-green-600 text-sm mb-4 p-3 bg-green-50 rounded border border-green-200">
    {{ thongBaoTraCuu }}
  </div>

  <!-- Lỗi tra cứu BHXH - hiển thị toàn bộ chiều rộng -->
  <div *ngIf="loiTraCuu" class="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded border border-red-200">
    {{ loiTraCuu }}
  </div>

  <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
    <!-- Mã số BHXH -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Mã số BHXH <span class="text-red-500">*</span>
      </label>
      <div class="relative">
        <input
          type="text"
          formControlName="maSoBHXH"
          (keydown)="onBhxhKeyDown($event)"
          [disabled]="disabled || dangTraCuu"
          class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
          placeholder="Nhập mã số BHXH và nhấn Enter để tra cứu"
        />
        <button
          type="button"
          (click)="traCuuThongTinBhxh()"
          [disabled]="disabled || dangTraCuu || !parentForm.get('maSoBHXH')?.value"
          class="absolute right-1 top-1 bottom-1 w-8 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
          title="Tra cứu thông tin BHXH"
        >
          <svg *ngIf="!dangTraCuu" class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <svg *ngIf="dangTraCuu" class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </button>
      </div>

      <!-- Validation error chỉ hiển thị trong cột -->
      <div *ngIf="hasError('maSoBHXH') && !loiTraCuu" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('maSoBHXH') }}
      </div>
    </div>

    <!-- Thông tin cá nhân -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Họ và tên <span class="text-red-500">*</span>
      </label>
      <input
        type="text"
        formControlName="hoTen"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        placeholder="Nhập họ và tên"
      />
      <div *ngIf="hasError('hoTen')" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('hoTen') }}
      </div>
    </div>

    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Số CCCD/CMND <span class="text-red-500">*</span>
      </label>
      <input
        type="text"
        formControlName="cmnd"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        placeholder="Nhập số CCCD/CMND"
      />
      <div *ngIf="hasError('cmnd')" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('cmnd') }}
      </div>
    </div>

    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Ngày sinh <span class="text-red-500">*</span>
      </label>
      <input
        type="date"
        formControlName="ngaySinh"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      />
      <div *ngIf="hasError('ngaySinh')" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('ngaySinh') }}
      </div>
    </div>

    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Giới tính <span class="text-red-500">*</span>
      </label>
      <select
        formControlName="gioiTinh"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      >
        <option value="">Chọn giới tính</option>
        <option value="Nam">Nam</option>
        <option value="Nữ">Nữ</option>
      </select>
      <div *ngIf="hasError('gioiTinh')" class="text-red-500 text-sm mt-1">
        {{ getErrorMessage('gioiTinh') }}
      </div>
    </div>

    <!-- CCNS - Hidden field -->
    <input type="hidden" formControlName="ccns" value="0">

    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Quốc tịch
      </label>
      <input
        type="text"
        formControlName="quocTich"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        placeholder="Nhập quốc tịch"
      />
    </div>

    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Dân tộc
      </label>
      <select
        formControlName="danToc"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      >
        <option value="">Chọn dân tộc</option>
        <option *ngFor="let danToc of danTocOptions" [value]="danToc.value">
          {{ danToc.text }}
        </option>
      </select>
    </div>

    <!-- Địa chỉ thường trú -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Tỉnh/Thành phố
      </label>
      <select
        formControlName="maTinhKs"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      >
        <option value="">Chọn tỉnh/thành phố</option>
        <option *ngFor="let tinh of tinhOptions" [value]="tinh.value">
          {{ tinh.text }}
        </option>
      </select>
    </div>

    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Quận/Huyện
      </label>
      <select
        formControlName="maHuyenKs"
        [disabled]="disabled || !parentForm.get('maTinhKs')?.value"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      >
        <option value="">{{ !parentForm.get('maTinhKs')?.value ? 'Chọn tỉnh trước' : 'Chọn quận/huyện' }}</option>
        <option *ngFor="let huyen of huyenOptions" [value]="huyen.value">
          {{ huyen.text }}
        </option>
      </select>
    </div>

    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Phường/Xã
      </label>
      <select
        formControlName="maXaKs"
        [disabled]="disabled || !parentForm.get('maHuyenKs')?.value"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
      >
        <option value="">{{ !parentForm.get('maHuyenKs')?.value ? 'Chọn huyện trước' : 'Chọn phường/xã' }}</option>
        <option *ngFor="let xa of xaOptions" [value]="xa.value">
          {{ xa.text }}
        </option>
      </select>
    </div>

    <!-- Thông tin liên hệ -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Số điện thoại liên hệ
      </label>
      <input
        type="text"
        formControlName="dienThoaiLh"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        placeholder="Nhập số điện thoại"
      />
    </div>

    <div>
      <label class="block text-sm font-medium text-gray-700 mb-1">
        Mã hộ gia đình
      </label>
      <input
        type="text"
        formControlName="maHoGiaDinh"
        [disabled]="disabled"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
        placeholder="Nhập mã hộ gia đình"
      />
    </div>
  </div>
</div>

<!-- VNPost Login Modal is now handled globally at app level -->
