import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';

import { XacThucRoutingModule } from './xac-thuc-routing.module';

// Components
import { DangNhapComponent } from './components/dang-nhap/dang-nhap.component';

// Services
import { XacThucService } from './services';

/**
 * Feature module cho xác thực người dùng
 * Bao gồm đăng nhập, đăng ký, quên mật khẩu
 */
@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    HttpClientModule,
    XacThucRoutingModule,
    
    // Standalone components
    DangNhapComponent
  ],
  providers: [
    XacThucService
  ]
})
export class XacThucModule { }
