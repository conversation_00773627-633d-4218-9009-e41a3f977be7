/**
 * Model cho thông tin xã/phường
 */
export interface Xa {
  id: number;
  maXa: string;
  tenXa: string;
  textDisplay: string;
  maHuyen: string;
  maTinh: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Response từ API khi lấy danh sách xã
 */
export interface XaResponse {
  data: Xa[];
  success: boolean;
  message?: string;
  errors?: any;
  status: number;
  traceId?: string;
}

/**
 * Option cho dropdown xã
 */
export interface XaOption {
  value: string;
  text: string;
  ten: string;
  maHuyen: string;
  maTinh: string;
}
