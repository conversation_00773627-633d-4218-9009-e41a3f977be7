/**
 * Models cho hệ thống bảo hiểm y tế và xã hội tự nguyện
 */

// Enum trạng thái hồ sơ
export enum TrangThaiHoSo {
  ChoDuyet = 'cho_duyet',
  DaDuyet = 'da_duyet',
  TuChoi = 'tu_choi',
  CanBoSung = 'can_bo_sung',
  DaHoanThanh = 'da_hoan_thanh'
}

// Enum loại bảo hiểm
export enum LoaiBaoHiem {
  BHYT = 'bhyt',
  BHXH = 'bhxh',
  BHTN = 'bhtn'
}

// Enum mức lương
export enum MucLuong {
  ToiThieu = 'toi_thieu',
  Muc1 = 'muc_1',
  Muc2 = 'muc_2',
  Muc3 = 'muc_3',
  Muc4 = 'muc_4',
  Muc5 = 'muc_5'
}

/**
 * Interface thông tin người tham gia bảo hiểm
 */
export interface NguoiThamGia {
  id: number;
  hoTen: string;
  ngaySinh: Date;
  gioiTinh: 'nam' | 'nu';
  soCCCD: string;
  soDienThoai: string;
  email: string;
  diaChi: string;
  tinhThanh: string;
  quanHuyen: string;
  xaPhuong: string;
  ngheNghiep: string;
  noiLamViec?: string;
}

/**
 * Interface hồ sơ BHYT tự nguyện
 */
export interface HoSoBHYT {
  id: number;
  nguoiThamGia: NguoiThamGia;
  maHoSo: string;
  ngayNop: Date;
  trangThai: TrangThaiHoSo;
  mucDong: number; // Số tiền đóng
  tuThang: Date;
  denThang: Date;
  ghiChu?: string;
  nguoiXuLy?: string;
  ngayXuLy?: Date;
  lyDoTuChoi?: string;
}

/**
 * Interface hồ sơ BHXH tự nguyện
 */
export interface HoSoBHXH {
  id: number;
  nguoiThamGia: NguoiThamGia;
  maHoSo: string;
  ngayNop: Date;
  trangThai: TrangThaiHoSo;
  mucLuong: MucLuong;
  mucDong: number;
  tuThang: Date;
  denThang: Date;
  ghiChu?: string;
  nguoiXuLy?: string;
  ngayXuLy?: Date;
  lyDoTuChoi?: string;
}

/**
 * Interface thống kê dashboard
 */
export interface ThongKeDashboard {
  tongHoSo: number;
  hoSoChoXuLy: number;
  hoSoDaDuyet: number;
  hoSoTuChoi: number;
  
  // Thống kê theo loại bảo hiểm
  thongKeBHYT: {
    tongSo: number;
    choXuLy: number;
    daDuyet: number;
    tuChoi: number;
  };
  
  thongKeBHXH: {
    tongSo: number;
    choXuLy: number;
    daDuyet: number;
    tuChoi: number;
  };
  
  // Thống kê theo thời gian
  thongKeTheoThang: Array<{
    thang: string;
    soLuong: number;
    loai: LoaiBaoHiem;
  }>;
  
  // Doanh thu
  tongDoanhThu: number;
  doanhThuThang: number;
}

/**
 * Interface bộ lọc tìm kiếm
 */
export interface BoLocHoSo {
  tuNgay?: Date;
  denNgay?: Date;
  trangThai?: TrangThaiHoSo;
  loaiBaoHiem?: LoaiBaoHiem;
  tuKhoa?: string; // Tìm theo tên, CCCD, mã hồ sơ
  tinhThanh?: string;
}

/**
 * Interface phân trang
 */
export interface PhanTrang {
  trang: number;
  kichThuoc: number;
  tongSo: number;
  tongTrang: number;
}

/**
 * Interface response API danh sách hồ sơ
 */
export interface DanhSachHoSoResponse {
  danhSach: (HoSoBHYT | HoSoBHXH)[];
  phanTrang: PhanTrang;
}

/**
 * Interface hoạt động gần đây
 */
export interface HoatDongGanDay {
  id: number;
  loai: 'tao_moi' | 'duyet' | 'tu_choi' | 'cap_nhat';
  tieuDe: string;
  moTa: string;
  thoiGian: Date;
  nguoiThucHien: string;
  maHoSo?: string;
}

/**
 * Interface thông báo hệ thống
 */
export interface ThongBaoHeThong {
  id: number;
  tieuDe: string;
  noiDung: string;
  loai: 'thong_tin' | 'canh_bao' | 'loi';
  thoiGian: Date;
  daDoc: boolean;
}
