namespace HuyPhuc.Application.DTOs.ToKhai602;

/// <summary>
/// Response DTO cho việc ghi nhận tờ khai 602
/// </summary>
public class GhiNhanToKhaiResponse
{
    /// <summary>
    /// ID tờ khai đã ghi nhận
    /// </summary>
    public int ToKhaiId { get; set; }

    /// <summary>
    /// Mã tờ khai
    /// </summary>
    public string MaToKhai { get; set; } = string.Empty;

    /// <summary>
    /// Số lao động đã lưu thành công
    /// </summary>
    public int SoLaoDongDaLuu { get; set; }

    /// <summary>
    /// Số lao động bị lỗi
    /// </summary>
    public int SoLaoDongLoi { get; set; }

    /// <summary>
    /// Danh sách lỗi (nếu có)
    /// </summary>
    public List<string> DanhSachLoi { get; set; } = new();

    /// <summary>
    /// Thông tin chi tiết các lao động đã lưu
    /// </summary>
    public List<LaoDongDaLuuDto> DanhSachLaoDongDaLuu { get; set; } = new();
}

/// <summary>
/// DTO cho thông tin lao động đã được lưu
/// </summary>
public class LaoDongDaLuuDto
{
    /// <summary>
    /// ID của bản ghi lao động
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Số thứ tự
    /// </summary>
    public int Stt { get; set; }

    /// <summary>
    /// Mã số BHXH
    /// </summary>
    public string MaSoBHXH { get; set; } = string.Empty;

    /// <summary>
    /// Họ và tên
    /// </summary>
    public string HoTen { get; set; } = string.Empty;

    /// <summary>
    /// Trạng thái lưu (thành công/lỗi)
    /// </summary>
    public bool ThanhCong { get; set; }

    /// <summary>
    /// Thông báo lỗi (nếu có)
    /// </summary>
    public string? ThongBaoLoi { get; set; }
}
