namespace HuyPhuc.Application.DTOs.DiaChi;

/// <summary>
/// DTO cho thông tin tỉnh/thành phố
/// </summary>
public class TinhDto
{
    /// <summary>
    /// ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Mã tỉnh
    /// </summary>
    public string MaTinh { get; set; } = string.Empty;

    /// <summary>
    /// Tên tỉnh/thành phố
    /// </summary>
    public string TenTinh { get; set; } = string.Empty;

    /// <summary>
    /// Text hiển thị (mã - tên)
    /// </summary>
    public string TextDisplay { get; set; } = string.Empty;

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Ngày cập nhật
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// DTO cho option dropdown tỉnh
/// </summary>
public class TinhOptionDto
{
    /// <summary>
    /// Gi<PERSON> trị (mã tỉnh)
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Text hiển thị
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Tên tỉnh
    /// </summary>
    public string Ten { get; set; } = string.Empty;

    /// <summary>
    /// Mã (null cho tương thích với frontend)
    /// </summary>
    public string? Ma { get; set; } = null;
}
