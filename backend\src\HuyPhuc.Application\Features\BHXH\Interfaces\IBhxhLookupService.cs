using HuyPhuc.Application.Features.BHXH.DTOs;

namespace HuyPhuc.Application.Features.BHXH.Interfaces;

/// <summary>
/// Interface cho service tra cứu thông tin BHXH
/// </summary>
public interface IBhxhLookupService
{
    /// <summary>
    /// Tra cứu thông tin BHXH theo mã số BHXH
    /// </summary>
    /// <param name="maSoBHXH">Mã số BHXH cần tra cứu</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Thông tin BHXH</returns>
    Task<BhxhLookupResponseDto> LookupBhxhInfoAsync(string maSoBHXH, CancellationToken cancellationToken = default);

    /// <summary>
    /// Kiểm tra tính hợp lệ của mã số BHXH
    /// </summary>
    /// <param name="maSoBHXH">Mã số BHXH cần kiểm tra</param>
    /// <returns>True nếu hợp lệ, False nếu không hợp lệ</returns>
    bool IsValidBhxhCode(string maSoBHXH);
}
