﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HuyPhuc.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialMigrationFromSupabase : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "dm_nguoi_dung",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ho_ten = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    email = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    mat_khau = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    so_dien_thoai = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    dia_chi = table.Column<string>(type: "text", nullable: true),
                    ngay_sinh = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    gioi_tinh = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    avatar_url = table.Column<string>(type: "text", nullable: true),
                    last_login = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ma_nhan_vien = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    username = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    trang_thai = table.Column<string>(type: "text", nullable: false, defaultValue: "active"),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    updated_by = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dm_nguoi_dung", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "SanPham",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    TenSanPham = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    MoTa = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    GiaBan = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    GiaGoc = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    SoLuongTon = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    LoaiSanPham = table.Column<int>(type: "integer", nullable: false, defaultValue: 99),
                    HinhAnh = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DangKinhDoanh = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    NgayTao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    NgayCapNhat = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NguoiTao = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    NguoiCapNhat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SanPham", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DonHang",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MaDonHang = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    NguoiDungId = table.Column<int>(type: "integer", nullable: false),
                    TongGiaTri = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    PhiVanChuyen = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TongThanhToan = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TrangThai = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    GhiChu = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    NgayDatHang = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    NgayGiaoHang = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NgayTao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    NgayCapNhat = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NguoiTao = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    NguoiCapNhat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DonHang", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DonHang_dm_nguoi_dung_NguoiDungId",
                        column: x => x.NguoiDungId,
                        principalTable: "dm_nguoi_dung",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ChiTietDonHang",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    DonHangId = table.Column<int>(type: "integer", nullable: false),
                    SanPhamId = table.Column<int>(type: "integer", nullable: false),
                    SoLuong = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    GiaBan = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    ThanhTien = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChiTietDonHang", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ChiTietDonHang_DonHang_DonHangId",
                        column: x => x.DonHangId,
                        principalTable: "DonHang",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ChiTietDonHang_SanPham_SanPhamId",
                        column: x => x.SanPhamId,
                        principalTable: "SanPham",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ChiTietDonHang_DonHangId",
                table: "ChiTietDonHang",
                column: "DonHangId");

            migrationBuilder.CreateIndex(
                name: "IX_ChiTietDonHang_DonHangId_SanPhamId",
                table: "ChiTietDonHang",
                columns: new[] { "DonHangId", "SanPhamId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ChiTietDonHang_SanPhamId",
                table: "ChiTietDonHang",
                column: "SanPhamId");

            migrationBuilder.CreateIndex(
                name: "IX_DonHang_MaDonHang",
                table: "DonHang",
                column: "MaDonHang",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DonHang_NgayDatHang",
                table: "DonHang",
                column: "NgayDatHang");

            migrationBuilder.CreateIndex(
                name: "IX_DonHang_NguoiDungId",
                table: "DonHang",
                column: "NguoiDungId");

            migrationBuilder.CreateIndex(
                name: "IX_DonHang_TrangThai",
                table: "DonHang",
                column: "TrangThai");

            migrationBuilder.CreateIndex(
                name: "IX_SanPham_DangKinhDoanh",
                table: "SanPham",
                column: "DangKinhDoanh");

            migrationBuilder.CreateIndex(
                name: "IX_SanPham_LoaiSanPham",
                table: "SanPham",
                column: "LoaiSanPham");

            migrationBuilder.CreateIndex(
                name: "IX_SanPham_TenSanPham",
                table: "SanPham",
                column: "TenSanPham");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ChiTietDonHang");

            migrationBuilder.DropTable(
                name: "DonHang");

            migrationBuilder.DropTable(
                name: "SanPham");

            migrationBuilder.DropTable(
                name: "dm_nguoi_dung");
        }
    }
}
