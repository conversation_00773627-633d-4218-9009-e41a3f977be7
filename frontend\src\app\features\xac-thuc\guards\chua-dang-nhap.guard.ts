import { Injectable } from '@angular/core';
import { CanActivate, Router, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';

import { XacThucService } from '../services';

/**
 * Guard chỉ cho phép truy cập khi chưa đăng nhập
 * Chuyển hướng đến dashboard nếu đã đăng nhập
 */
@Injectable({
  providedIn: 'root'
})
export class ChuaDangNhapGuard implements CanActivate {

  constructor(
    private xacThucService: XacThucService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean | UrlTree> {
    return this.xacThucService.daDangNhap$.pipe(
      take(1),
      map(daDangNhap => {
        if (!daDangNhap) {
          return true;
        } else {
          // Đã đăng nhập, chuyển hướng đến dashboard
          return this.router.createUrlTree(['/dashboard']);
        }
      })
    );
  }
}
