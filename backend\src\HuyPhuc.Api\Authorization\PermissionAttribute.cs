using Microsoft.AspNetCore.Authorization;

namespace HuyPhuc.Api.Authorization;

/// <summary>
/// Attribute để kiểm tra quyền truy cập dựa trên permission code
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public class PermissionAttribute : AuthorizeAttribute
{
    public string Permission { get; }

    public PermissionAttribute(string permission) : base("PermissionPolicy")
    {
        Permission = permission;
    }
}
