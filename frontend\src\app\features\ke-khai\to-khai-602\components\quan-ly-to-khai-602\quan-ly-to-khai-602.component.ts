import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { TabsComponent, TabItem } from '../../../../../shared/components/tabs/tabs.component';
import { ToKhai602Service } from '../../services/to-khai-602.service';

/**
 * Container component cho quản lý tờ khai 602 với tab navigation
 * Bao gồm các tabs: <PERSON><PERSON> báo, <PERSON><PERSON><PERSON><PERSON> lao động, <PERSON>h sách lao động
 */
@Component({
  selector: 'app-quan-ly-to-khai-602',
  standalone: true,
  imports: [CommonModule, RouterOutlet, TabsComponent],
  template: `
    <div class="quan-ly-to-khai-container min-h-screen bg-gray-50">

      <!-- Header space above tab navigation -->
      <div class="header-space py-4"></div>

      <!-- Tab Navigation và Content -->
      <div class="tab-container bg-white">
        <app-tabs [tabs]="tabs">
          <!-- Tab content sẽ được render qua router-outlet -->
          <div class="tab-content-wrapper">
            <router-outlet></router-outlet>
          </div>
        </app-tabs>
      </div>

      <!-- Loading Overlay -->
      <div *ngIf="dangTai" class="loading-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="loading-content bg-white rounded-lg p-6 flex items-center space-x-3">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span class="text-gray-700">Đang xử lý...</span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .quan-ly-to-khai-container {
      min-height: 100vh;
    }

    .header-space {
      min-height: 2rem;
    }

    .tab-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }

    .tab-content-wrapper {
      flex: 1;
      min-height: 0;
      overflow: auto;
    }

    .loading-overlay {
      backdrop-filter: blur(2px);
    }

    .loading-content {
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .animate-spin {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }


  `]
})
export class QuanLyToKhai602Component implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // Tab configuration - chỉ có 2 tabs cho quản lý
  tabs: TabItem[] = [
    {
      id: 'nhap-lao-dong',
      nhan: 'Nhập lao động',
      duongDan: './nhap-lao-dong',
      icon: 'M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z'
    },
    {
      id: 'danh-sach-lao-dong',
      nhan: 'Danh sách lao động',
      duongDan: './danh-sach-lao-dong',
      icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'
    }
  ];

  // State
  dangTai = false;
  keKhaiId?: number;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private toKhai602Service: ToKhai602Service
  ) {}

  ngOnInit(): void {
    console.log('🏗️ QuanLyToKhai602Component ngOnInit');
    this.khoiTaoSubscriptions();
    this.layKeKhaiId();
    this.kiemTraRouteHienTai();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Khởi tạo subscriptions
   */
  private khoiTaoSubscriptions(): void {
    // Subscribe to loading state
    this.toKhai602Service.dangTai$
      .pipe(takeUntil(this.destroy$))
      .subscribe(dangTai => {
        this.dangTai = dangTai;
      });

    // Subscribe to route params để lấy keKhaiId nếu có
    this.route.params
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        if (params['id']) {
          this.keKhaiId = +params['id'];
          this.capNhatTabsTheoKeKhai();
        }
      });
  }

  /**
   * Lấy keKhaiId từ route params
   */
  private layKeKhaiId(): void {
    const id = this.route.snapshot.params['id'];
    console.log('🆔 Route params id:', id);
    if (id) {
      this.keKhaiId = +id;
      console.log('✅ KeKhaiId set to:', this.keKhaiId);
      this.capNhatTabsTheoKeKhai();
    }
  }

  /**
   * Kiểm tra route hiện tại và redirect nếu cần
   */
  private kiemTraRouteHienTai(): void {
    const currentUrl = this.router.url;

    // Nếu đang ở route quản lý mà chưa có tab cụ thể, redirect đến tab đầu tiên
    if (currentUrl.includes('/quan-ly/') && currentUrl.endsWith(this.keKhaiId?.toString() || '')) {
      this.router.navigate(['./nhap-lao-dong'], { relativeTo: this.route, replaceUrl: true });
    }
  }

  /**
   * Cập nhật tabs configuration dựa trên keKhaiId
   */
  private capNhatTabsTheoKeKhai(): void {
    // Trong trang quản lý, tất cả tabs đều được enable vì đã có keKhaiId
    this.tabs = this.tabs.map(tab => ({
      ...tab,
      disabled: false
    }));
  }

  /**
   * Xử lý khi tạo kê khai thành công (được gọi từ component khác)
   */
  onKeKhaiTaoThanhCong(keKhaiId: number): void {
    this.keKhaiId = keKhaiId;
    this.capNhatTabsTheoKeKhai();

    // Navigate đến trang quản lý với keKhaiId
    this.router.navigate(['/ke-khai/to-khai-602/quan-ly', keKhaiId]);
  }

  /**
   * Xử lý navigation giữa các tabs
   */
  onTabChange(tabId: string): void {
    console.log('Chuyển đến tab:', tabId);
    
    // Có thể thêm logic validation trước khi chuyển tab
    // Ví dụ: kiểm tra form đã lưu chưa, etc.
  }
}
