using HuyPhuc.Domain.Enums;

namespace HuyPhuc.Application.Features.DanhMucThuTuc.Common;

/// <summary>
/// DTO cho danh mục thủ tục
/// </summary>
public class DanhMucThuTucDto
{
    public int Id { get; set; }
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
    public LinhVucThuTuc LinhVuc { get; set; }
    public string TenLinhVuc { get; set; } = string.Empty;
    public DateTime? NgayApDung { get; set; }
    public TrangThaiThuTuc TrangThai { get; set; }
    public string? MoTa { get; set; }
    public int? ThoiGianXuLy { get; set; }
    public decimal? PhiThucHien { get; set; }
    public string? CoQuanThucHien { get; set; }
    public string? CanCuPhapLy { get; set; }
    public DateTime Created { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime? LastModified { get; set; }
    public string? LastModifiedBy { get; set; }
    public bool DangHoatDong { get; set; }
    public bool CoHieuLuc { get; set; }
}

/// <summary>
/// DTO tóm tắt cho danh mục thủ tục (dùng cho danh sách)
/// </summary>
public class DanhMucThuTucSummaryDto
{
    public int Id { get; set; }
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
    public LinhVucThuTuc LinhVuc { get; set; }
    public string TenLinhVuc { get; set; } = string.Empty;
    public TrangThaiThuTuc TrangThai { get; set; }
    public DateTime? NgayApDung { get; set; }
    public int? ThoiGianXuLy { get; set; }
    public decimal? PhiThucHien { get; set; }
    public bool DangHoatDong { get; set; }
    public bool CoHieuLuc { get; set; }
}

/// <summary>
/// Response cho danh sách có phân trang
/// </summary>
public class DanhMucThuTucPaginatedResponse
{
    public IEnumerable<DanhMucThuTucSummaryDto> Items { get; set; } = new List<DanhMucThuTucSummaryDto>();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasPreviousPage => Page > 1;
    public bool HasNextPage => Page < TotalPages;
}

/// <summary>
/// DTO cho tạo/cập nhật danh mục thủ tục
/// </summary>
public class CreateUpdateDanhMucThuTucDto
{
    public string Ma { get; set; } = string.Empty;
    public string Ten { get; set; } = string.Empty;
    public LinhVucThuTuc LinhVuc { get; set; }
    public string TenLinhVuc { get; set; } = string.Empty;
    public DateTime? NgayApDung { get; set; }
    public string? MoTa { get; set; }
    public int? ThoiGianXuLy { get; set; }
    public decimal? PhiThucHien { get; set; }
    public string? CoQuanThucHien { get; set; }
    public string? CanCuPhapLy { get; set; }
}

/// <summary>
/// DTO cho thống kê danh mục thủ tục
/// </summary>
public class DanhMucThuTucStatisticsDto
{
    public int TongSoThuTuc { get; set; }
    public int SoThuTucHoatDong { get; set; }
    public int SoThuTucTamNgung { get; set; }
    public int SoThuTucNgungHoatDong { get; set; }
    public Dictionary<LinhVucThuTuc, int> ThongKeLinhVuc { get; set; } = new();
    public Dictionary<TrangThaiThuTuc, int> ThongKeTrangThai { get; set; } = new();
}

/// <summary>
/// DTO cho filter/search
/// </summary>
public class DanhMucThuTucFilterDto
{
    public string? TuKhoa { get; set; }
    public LinhVucThuTuc? LinhVuc { get; set; }
    public TrangThaiThuTuc? TrangThai { get; set; }
    public DateTime? NgayApDungTu { get; set; }
    public DateTime? NgayApDungDen { get; set; }
    public bool? ChiLayThuTucHoatDong { get; set; }
    public bool? ChiLayThuTucCoHieuLuc { get; set; }
}
