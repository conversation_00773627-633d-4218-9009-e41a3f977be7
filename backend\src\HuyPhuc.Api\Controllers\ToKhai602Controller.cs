using HuyPhuc.Application.DTOs.ToKhai602;
using HuyPhuc.Application.Features.ToKhai602.Commands.TaoToKhai;
using HuyPhuc.Application.Features.ToKhai602.Commands.TaoDraftToKhai;
using HuyPhuc.Application.Features.ToKhai602.Commands.GhiNhanToKhai;
using HuyPhuc.Application.Features.ToKhai602.Commands.CapNhatToKhai;
using HuyPhuc.Application.Features.ToKhai602.Commands.XoaToKhai;
using HuyPhuc.Application.Features.ToKhai602.Queries.LayDanhSachToKhai;
using HuyPhuc.Application.Features.ToKhai602.Queries.LayChiTietToKhai;
using HuyPhuc.Api.Controllers.Base;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// Controller quản lý tờ khai 602
/// </summary>
[ApiController]
[Route("api/to-khai-602")]
[Authorize]
public class ToKhai602Controller : BaseController
{
    /// <summary>
    /// L<PERSON>y danh sách tờ khai 602 với filter và pagination
    /// </summary>
    /// <param name="request">Thông tin filter và pagination</param>
    /// <returns>Danh sách tờ khai</returns>
    [HttpGet]
    public async Task<ActionResult<LayDanhSachToKhaiResponse>> LayDanhSachToKhai([FromQuery] LayDanhSachToKhaiRequest request)
    {
        try
        {
            var query = new LayDanhSachToKhaiQuery(request);
            var result = await Mediator.Send(query);

            return Ok(new
            {
                success = true,
                data = result,
                message = "Lấy danh sách tờ khai thành công"
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                success = false,
                message = "Có lỗi xảy ra khi lấy danh sách tờ khai",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Lấy chi tiết tờ khai 602 theo ID
    /// </summary>
    /// <param name="id">ID của tờ khai</param>
    /// <returns>Chi tiết tờ khai</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ToKhai602ChiTietDto>> LayChiTietToKhai(int id)
    {
        try
        {
            var query = new LayChiTietToKhaiQuery(id);
            var result = await Mediator.Send(query);

            return Ok(new
            {
                success = true,
                data = result,
                message = "Lấy chi tiết tờ khai thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                success = false,
                message = "Có lỗi xảy ra khi lấy chi tiết tờ khai",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Cập nhật tờ khai 602
    /// </summary>
    /// <param name="id">ID của tờ khai</param>
    /// <param name="request">Thông tin cập nhật</param>
    /// <returns>Thông tin tờ khai đã cập nhật</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ToKhai602ChiTietDto>> CapNhatToKhai(int id, [FromBody] CapNhatToKhaiRequest request)
    {
        try
        {
            var command = new CapNhatToKhaiCommand(id, request);
            var result = await Mediator.Send(command);

            return Ok(new
            {
                success = true,
                data = result,
                message = "Cập nhật tờ khai thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                success = false,
                message = "Có lỗi xảy ra khi cập nhật tờ khai",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Xóa tờ khai 602
    /// </summary>
    /// <param name="id">ID của tờ khai</param>
    /// <returns>Kết quả xóa</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult> XoaToKhai(int id)
    {
        try
        {
            var command = new XoaToKhaiCommand(id);
            var result = await Mediator.Send(command);

            return Ok(new
            {
                success = true,
                data = result,
                message = "Xóa tờ khai thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                success = false,
                message = "Có lỗi xảy ra khi xóa tờ khai",
                error = ex.Message
            });
        }
    }



    /// <summary>
    /// Tạo tờ khai 602 mới
    /// </summary>
    /// <param name="request">Thông tin tạo tờ khai</param>
    /// <returns>Thông tin tờ khai đã tạo</returns>
    [HttpPost]
    public async Task<ActionResult<TaoToKhaiResponse>> TaoToKhai([FromBody] TaoDraftToKhaiRequest request)
    {
        try
        {
            var command = new TaoToKhaiCommand(request);
            var result = await Mediator.Send(command);

            return Ok(new
            {
                success = true,
                data = result,
                message = "Tạo tờ khai thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                success = false,
                message = "Có lỗi xảy ra khi tạo tờ khai",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Tạo draft tờ khai 602
    /// </summary>
    /// <param name="request">Thông tin tạo draft</param>
    /// <returns>Thông tin draft đã tạo</returns>
    [HttpPost("draft")]
    public async Task<ActionResult<TaoDraftToKhaiResponse>> TaoDraftToKhai([FromBody] TaoDraftToKhaiRequest request)
    {
        try
        {
            var command = new TaoDraftToKhaiCommand(request);
            var result = await Mediator.Send(command);
            
            return Ok(new
            {
                success = true,
                data = result,
                message = "Tạo draft tờ khai thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                success = false,
                message = "Có lỗi xảy ra khi tạo draft tờ khai",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Lấy thông tin draft tờ khai theo ID
    /// </summary>
    /// <param name="draftId">ID của draft</param>
    /// <returns>Thông tin draft</returns>
    [HttpGet("draft/{draftId}")]
    public async Task<ActionResult<TaoDraftToKhaiResponse>> LayDraftToKhai(int draftId)
    {
        try
        {
            // TODO: Implement LayDraftToKhaiQuery
            return Ok(new
            {
                success = true,
                message = "Lấy thông tin draft thành công"
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                success = false,
                message = "Có lỗi xảy ra khi lấy thông tin draft",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Cập nhật draft tờ khai
    /// </summary>
    /// <param name="draftId">ID của draft</param>
    /// <param name="request">Thông tin cập nhật</param>
    /// <returns>Kết quả cập nhật</returns>
    [HttpPut("draft/{draftId}")]
    public async Task<ActionResult> CapNhatDraftToKhai(int draftId, [FromBody] TaoDraftToKhaiRequest request)
    {
        try
        {
            // TODO: Implement CapNhatDraftToKhaiCommand
            return Ok(new
            {
                success = true,
                message = "Cập nhật draft thành công"
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                success = false,
                message = "Có lỗi xảy ra khi cập nhật draft",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Xóa draft tờ khai
    /// </summary>
    /// <param name="draftId">ID của draft</param>
    /// <returns>Kết quả xóa</returns>
    [HttpDelete("draft/{draftId}")]
    public async Task<ActionResult> XoaDraftToKhai(int draftId)
    {
        try
        {
            // TODO: Implement XoaDraftToKhaiCommand
            return Ok(new
            {
                success = true,
                message = "Xóa draft thành công"
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                success = false,
                message = "Có lỗi xảy ra khi xóa draft",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Ghi nhận tờ khai 602 vào database
    /// </summary>
    /// <param name="request">Thông tin ghi nhận</param>
    /// <returns>Kết quả ghi nhận</returns>
    [HttpPost("ghi-nhan")]
    public async Task<ActionResult<GhiNhanToKhaiResponse>> GhiNhanToKhai([FromBody] GhiNhanToKhaiRequest request)
    {
        try
        {
            var command = new GhiNhanToKhaiCommand(request);
            var result = await Mediator.Send(command);

            return Ok(new
            {
                success = true,
                data = result,
                message = "Ghi nhận tờ khai thành công"
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(new
            {
                success = false,
                message = ex.Message
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new
            {
                success = false,
                message = "Có lỗi xảy ra khi ghi nhận tờ khai",
                error = ex.Message
            });
        }
    }
}
