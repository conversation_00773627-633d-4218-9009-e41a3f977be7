using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Domain.Exceptions;

namespace HuyPhuc.Domain.Entities;

public class ChiTietDonHang : BaseEntity
{
    public int DonHangId { get; private set; }
    public int SanPhamId { get; private set; }
    public int SoLuong { get; private set; }
    public decimal GiaBan { get; private set; }
    public decimal ThanhTien { get; private set; }

    // Navigation properties
    public virtual DonHang DonHang { get; private set; } = null!;
    public virtual SanPham SanPham { get; private set; } = null!;

    private ChiTietDonHang() { } // EF Core constructor

    public static ChiTietDonHang Tao(int donHangId, int sanPhamId, int soLuong, decimal giaBan)
    {
        if (soLuong <= 0)
            throw new DomainException("Số lượng phải lớn hơn 0");

        if (giaBan <= 0)
            throw new DomainException("Giá bán phải lớn hơn 0");

        var chiTiet = new ChiTietDonHang
        {
            DonHangId = donHangId,
            SanPhamId = sanPhamId,
            SoLuong = soLuong,
            GiaBan = giaBan
        };

        chiTiet.TinhThanhTien();
        return chiTiet;
    }

    public void CapNhatSoLuong(int soLuongMoi)
    {
        if (soLuongMoi <= 0)
            throw new DomainException("Số lượng phải lớn hơn 0");

        SoLuong = soLuongMoi;
        TinhThanhTien();
    }

    public void CapNhatGiaBan(decimal giaBanMoi)
    {
        if (giaBanMoi <= 0)
            throw new DomainException("Giá bán phải lớn hơn 0");

        GiaBan = giaBanMoi;
        TinhThanhTien();
    }

    private void TinhThanhTien()
    {
        ThanhTien = SoLuong * GiaBan;
    }
}
