using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace HuyPhuc.Api.Authorization;

/// <summary>
/// Handler để xử lý authorization dựa trên permission
/// </summary>
public class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
{
    private readonly ILogger<PermissionAuthorizationHandler> _logger;

    public PermissionAuthorizationHandler(ILogger<PermissionAuthorizationHandler> logger)
    {
        _logger = logger;
    }

    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        PermissionRequirement requirement)
    {
        // Kiểm tra user đã authenticated chưa
        if (!context.User.Identity?.IsAuthenticated ?? true)
        {
            _logger.LogWarning("Authorization failed: User not authenticated");
            context.Fail();
            return Task.CompletedTask;
        }

        var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var userName = context.User.FindFirst(ClaimTypes.Name)?.Value;

        // L<PERSON>y danh sách permissions từ claims
        var userPermissions = context.User.Claims
            .Where(c => c.Type == "permission")
            .Select(c => c.Value)
            .ToList();

        // Kiểm tra user có permission yêu cầu không
        if (userPermissions.Contains(requirement.Permission, StringComparer.OrdinalIgnoreCase))
        {
            _logger.LogInformation(
                "Authorization succeeded: User {UserId} ({UserName}) has permission {Permission}",
                userId, userName, requirement.Permission);
            context.Succeed(requirement);
        }
        else
        {
            _logger.LogWarning(
                "Authorization failed: User {UserId} ({UserName}) lacks permission {Permission}. User permissions: [{UserPermissions}]",
                userId, userName, requirement.Permission, string.Join(", ", userPermissions));
            context.Fail();
        }

        return Task.CompletedTask;
    }
}

/// <summary>
/// Requirement cho permission-based authorization
/// </summary>
public class PermissionRequirement : IAuthorizationRequirement
{
    public string Permission { get; }

    public PermissionRequirement(string permission)
    {
        Permission = permission;
    }
}
