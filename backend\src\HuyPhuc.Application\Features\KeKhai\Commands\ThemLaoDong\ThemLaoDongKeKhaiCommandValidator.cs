using FluentValidation;

namespace HuyPhuc.Application.Features.KeKhai.Commands.ThemLaoDong;

/// <summary>
/// Validator cho ThemLaoDongKeKhaiCommand
/// </summary>
public class ThemLaoDongKeKhaiCommandValidator : AbstractValidator<ThemLaoDongKeKhaiCommand>
{
    public ThemLaoDongKeKhaiCommandValidator()
    {
        RuleFor(x => x.Request.KeKhaiId)
            .GreaterThan(0)
            .WithMessage("ID kê khai phải lớn hơn 0");

        RuleFor(x => x.Request.MaSoBHXH)
            .NotEmpty()
            .WithMessage("Mã số BHXH không được để trống")
            .MinimumLength(3)
            .WithMessage("Mã số BHXH phải có ít nhất 3 ký tự")
            .MaximumLength(20)
            .WithMessage("Mã số BHXH không được vượt quá 20 ký tự");

        // Validate thông tin cá nhân (optional - sẽ lấy từ tk1_ts nếu không có)
        RuleFor(x => x.Request.HoTen)
            .MaximumLength(100)
            .WithMessage("Họ tên không được vượt quá 100 ký tự")
            .When(x => !string.IsNullOrWhiteSpace(x.Request.HoTen));

        RuleFor(x => x.Request.Cmnd)
            .MaximumLength(20)
            .WithMessage("CMND/CCCD không được vượt quá 20 ký tự")
            .When(x => !string.IsNullOrWhiteSpace(x.Request.Cmnd));

        RuleFor(x => x.Request.GioiTinh)
            .InclusiveBetween(1, 2)
            .WithMessage("Giới tính phải là 1 (Nam) hoặc 2 (Nữ)")
            .When(x => x.Request.GioiTinh.HasValue);

        RuleFor(x => x.Request.DienThoai)
            .MaximumLength(15)
            .WithMessage("Số điện thoại không được vượt quá 15 ký tự")
            .When(x => !string.IsNullOrWhiteSpace(x.Request.DienThoai));

        // Validate thông tin BHXH
        RuleFor(x => x.Request.PhuongAn)
            .MaximumLength(10)
            .WithMessage("Phương án đóng không được vượt quá 10 ký tự")
            .Must(x => string.IsNullOrWhiteSpace(x) || new[] { "DB", "DT", "TN" }.Contains(x))
            .WithMessage("Phương án đóng phải là DB, DT hoặc TN")
            .When(x => !string.IsNullOrWhiteSpace(x.Request.PhuongAn));

        RuleFor(x => x.Request.PhuongThuc)
            .MaximumLength(10)
            .WithMessage("Phương thức đóng không được vượt quá 10 ký tự")
            .Must(x => string.IsNullOrWhiteSpace(x) || new[] { "1", "2", "3", "6", "12", "nam-sau", "nam-thieu" }.Contains(x))
            .WithMessage("Phương thức đóng phải là 1, 2, 3, 6, 12, nam-sau hoặc nam-thieu")
            .When(x => !string.IsNullOrWhiteSpace(x.Request.PhuongThuc));

        RuleFor(x => x.Request.MucThuNhap)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Mức thu nhập phải lớn hơn hoặc bằng 0")
            .When(x => x.Request.MucThuNhap.HasValue);

        RuleFor(x => x.Request.TyLe)
            .InclusiveBetween(0, 100)
            .WithMessage("Tỷ lệ đóng phải từ 0 đến 100")
            .When(x => x.Request.TyLe.HasValue);

        RuleFor(x => x.Request.SoThang)
            .InclusiveBetween(1, 12)
            .WithMessage("Số tháng phải từ 1 đến 12")
            .When(x => x.Request.SoThang.HasValue);

        // Validate thông tin tiền
        RuleFor(x => x.Request.TienLai)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Tiền lãi phải lớn hơn hoặc bằng 0")
            .When(x => x.Request.TienLai.HasValue);

        RuleFor(x => x.Request.TienThua)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Tiền thừa phải lớn hơn hoặc bằng 0")
            .When(x => x.Request.TienThua.HasValue);

        RuleFor(x => x.Request.TienHoTro)
            .GreaterThanOrEqualTo(0)
            .WithMessage("NSNN hỗ trợ phải lớn hơn hoặc bằng 0")
            .When(x => x.Request.TienHoTro.HasValue);

        RuleFor(x => x.Request.TongTien)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Tổng tiền phải lớn hơn hoặc bằng 0")
            .When(x => x.Request.TongTien.HasValue);

        RuleFor(x => x.Request.TienTuDong)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Tiền tự đóng phải lớn hơn hoặc bằng 0")
            .When(x => x.Request.TienTuDong.HasValue);

        // Validate địa chỉ
        RuleFor(x => x.Request.MaTinhKs)
            .MaximumLength(10)
            .WithMessage("Mã tỉnh không được vượt quá 10 ký tự")
            .When(x => !string.IsNullOrWhiteSpace(x.Request.MaTinhKs));

        RuleFor(x => x.Request.MaHuyenKs)
            .MaximumLength(10)
            .WithMessage("Mã huyện không được vượt quá 10 ký tự")
            .When(x => !string.IsNullOrWhiteSpace(x.Request.MaHuyenKs));

        RuleFor(x => x.Request.MaXaKs)
            .MaximumLength(10)
            .WithMessage("Mã xã không được vượt quá 10 ký tự")
            .When(x => !string.IsNullOrWhiteSpace(x.Request.MaXaKs));
    }
}
