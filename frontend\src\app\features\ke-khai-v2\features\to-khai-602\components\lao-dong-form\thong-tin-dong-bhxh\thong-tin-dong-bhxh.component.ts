import { Component, Input, Output, EventEmitter, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup } from '@angular/forms';
import { LoaiService } from '../../../../../core/services';
import { LoaiOption, LoaiNsnnOption } from '../../../../../core/models';

/**
 * Component form thông tin đóng BHXH của lao động
 */
@Component({
  selector: 'app-thong-tin-dong-bhxh',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  templateUrl: './thong-tin-dong-bhxh.component.html',
  styleUrls: ['./thong-tin-dong-bhxh.component.scss']
})
export class ThongTinDongBhxhComponent implements OnInit {
  @Input() parentForm!: FormGroup;
  @Input() disabled = false;
  @Output() formChange = new EventEmitter<any>();

  private readonly loaiService = inject(LoaiService);

  // Danh sách loại cho dropdown
  loaiOptions: LoaiOption[] = [];

  // Danh sách loại NSNN với tỷ lệ hỗ trợ
  loaiNsnnOptions: LoaiNsnnOption[] = [
    { value: 'ho-ngheo-xa-dao', text: 'Hộ nghèo, người đang sinh sống tại xã đảo, đặc khu (50%)' },
    { value: 'ho-can-ngheo', text: 'Hộ cận nghèo (40%)' },
    { value: 'dan-toc-thieu-so', text: 'Dân tộc thiểu số (30%)' },
    { value: 'khac', text: 'Khác (20%)' }
  ];

  // Mapping tỷ lệ NSNN hỗ trợ theo loại
  tyLeNsnnHoTroMapping: { [key: string]: number } = {
    'ho-ngheo-xa-dao': 50,
    'ho-can-ngheo': 40,
    'dan-toc-thieu-so': 30,
    'khac': 20
  };

  // Danh sách phương thức đóng
  phuongThucOptions: { value: string; text: string }[] = [
    { value: '1-thang', text: 'Đóng 1 tháng' },
    { value: '3-thang', text: 'Đóng 3 tháng' },
    { value: '6-thang', text: 'Đóng 6 tháng' },
    { value: '12-thang', text: 'Đóng 12 tháng' },
    { value: 'nam-sau', text: 'Đóng cho những năm về sau' },
    { value: 'nam-thieu', text: 'Đóng cho những năm còn thiếu' }
  ];

  // Danh sách số tháng theo phương thức đóng
  soThangOptions: { value: number; text: string }[] = [];

  // Mapping số tháng theo phương thức
  private soThangMapping: { [key: string]: { value: number; text: string }[] } = {
    '1-thang': [{ value: 1, text: '1 tháng' }],
    '3-thang': [{ value: 3, text: '3 tháng' }],
    '6-thang': [{ value: 6, text: '6 tháng' }],
    '12-thang': [{ value: 12, text: '12 tháng' }],
    'nam-sau': [
      { value: 24, text: '24 tháng' },
      { value: 36, text: '36 tháng' },
      { value: 48, text: '48 tháng' },
      { value: 60, text: '60 tháng' }
    ],
    'nam-thieu': Array.from({ length: 60 }, (_, i) => ({
      value: i + 1,
      text: `${i + 1} tháng`
    }))
  };

  ngOnInit() {
    // Load danh sách loại
    this.loadLoaiOptions();

    // Set ngày biên lai mặc định là ngày hiện tại nếu chưa có giá trị
    this.setDefaultNgayBienLai();

    // Set loại NSNN mặc định là "khác" nếu chưa có giá trị
    this.setDefaultLoaiNsnn();

    // Set tỷ lệ NLD đóng mặc định là 22% nếu chưa có giá trị
    this.setDefaultTyLeNldDong();

    // Lắng nghe thay đổi loại NSNN để cập nhật tỷ lệ hỗ trợ
    this.parentForm.get('loaiNsnn')?.valueChanges.subscribe(loaiNsnn => {
      this.onLoaiNsnnChange(loaiNsnn);
    });

    // Lắng nghe thay đổi mức thu nhập để cập nhật hệ số đóng
    this.parentForm.get('mucThuNhap')?.valueChanges.subscribe(mucThuNhap => {
      this.onMucThuNhapChange(mucThuNhap);
    });

    // Lắng nghe thay đổi phương thức đóng để cập nhật số tháng
    this.parentForm.get('phuongThuc')?.valueChanges.subscribe(phuongThuc => {
      this.onPhuongThucChange(phuongThuc);
    });

    // Lắng nghe thay đổi số tháng để cập nhật số tháng NSNN hỗ trợ
    this.parentForm.get('soThang')?.valueChanges.subscribe(soThang => {
      this.onSoThangChange(soThang);
    });

    // Lắng nghe thay đổi form và emit lên parent
    this.parentForm.valueChanges.subscribe(value => {
      this.formChange.emit(value);
    });
  }

  /**
   * Kiểm tra field có lỗi không
   */
  hasError(fieldName: string): boolean {
    const field = this.parentForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  /**
   * Lấy thông báo lỗi cho field
   */
  getErrorMessage(fieldName: string): string {
    const field = this.parentForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return this.getRequiredMessage(fieldName);
      }
      if (field.errors['min']) {
        return 'Giá trị phải lớn hơn hoặc bằng 0';
      }
    }
    return '';
  }

  /**
   * Lấy thông báo lỗi required cho từng field
   */
  private getRequiredMessage(fieldName: string): string {
    const messages: { [key: string]: string } = {
      'mucThuNhap': 'Vui lòng nhập mức thu nhập',
      'thangBatDau': 'Vui lòng chọn tháng bắt đầu',
      'phuongAn': 'Vui lòng chọn phương án',
      'phuongThuc': 'Vui lòng chọn phương thức',
      'soThang': 'Vui lòng chọn số tháng',
      'loaiNsnn': 'Vui lòng chọn loại NSNN'
    };
    return messages[fieldName] || 'Vui lòng nhập thông tin';
  }

  /**
   * Set ngày biên lai mặc định là ngày hiện tại
   */
  private setDefaultNgayBienLai() {
    const ngayBienLaiControl = this.parentForm.get('ngayBienLai');
    if (ngayBienLaiControl && !ngayBienLaiControl.value) {
      const today = new Date();
      const todayString = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD
      ngayBienLaiControl.setValue(todayString);
      console.log(`📅 Set ngày biên lai mặc định: ${todayString}`);
    }
  }

  /**
   * Set loại NSNN mặc định là "khác"
   */
  private setDefaultLoaiNsnn() {
    const loaiNsnnControl = this.parentForm.get('loaiNsnn');
    if (loaiNsnnControl && !loaiNsnnControl.value) {
      loaiNsnnControl.setValue('khac');
      console.log(`🎯 Set loại NSNN mặc định: khác (20%)`);
    }
  }

  /**
   * Set tỷ lệ NLD đóng mặc định là 22%
   */
  private setDefaultTyLeNldDong() {
    const tyLeNldDongControl = this.parentForm.get('tyLeNldDong');
    if (tyLeNldDongControl && !tyLeNldDongControl.value) {
      tyLeNldDongControl.setValue(22);
      console.log(`💰 Set tỷ lệ NLD đóng mặc định: 22%`);
    }
  }

  /**
   * Xử lý thay đổi loại NSNN và cập nhật tỷ lệ hỗ trợ
   */
  onLoaiNsnnChange(loaiNsnn: string) {
    if (loaiNsnn && this.tyLeNsnnHoTroMapping[loaiNsnn]) {
      const tyLeHoTro = this.tyLeNsnnHoTroMapping[loaiNsnn];
      this.parentForm.get('tyLeNsnnHoTro')?.setValue(tyLeHoTro);
      console.log(`🎯 Cập nhật tỷ lệ NSNN hỗ trợ: ${tyLeHoTro}% cho loại ${loaiNsnn}`);
    } else {
      this.parentForm.get('tyLeNsnnHoTro')?.setValue(0);
    }
  }

  /**
   * Xử lý thay đổi phương thức đóng và cập nhật danh sách số tháng
   */
  onPhuongThucChange(phuongThuc: string) {
    if (phuongThuc && this.soThangMapping[phuongThuc]) {
      this.soThangOptions = this.soThangMapping[phuongThuc];

      // Reset số tháng khi thay đổi phương thức
      this.parentForm.get('soThang')?.setValue('');
      this.parentForm.get('soThangNsnnHoTro')?.setValue(0);

      // Tự động set số tháng cho các phương thức có số tháng cố định
      if (phuongThuc === '1-thang') {
        this.parentForm.get('soThang')?.setValue(1);
      } else if (phuongThuc === '3-thang') {
        this.parentForm.get('soThang')?.setValue(3);
      } else if (phuongThuc === '6-thang') {
        this.parentForm.get('soThang')?.setValue(6);
      } else if (phuongThuc === '12-thang') {
        this.parentForm.get('soThang')?.setValue(12);
      }

      console.log(`📅 Cập nhật danh sách số tháng cho phương thức: ${phuongThuc}`, this.soThangOptions);
    } else {
      this.soThangOptions = [];
      this.parentForm.get('soThang')?.setValue('');
      this.parentForm.get('soThangNsnnHoTro')?.setValue(0);
    }
  }

  /**
   * Xử lý thay đổi số tháng và cập nhật số tháng NSNN hỗ trợ
   * Số tháng NSNN hỗ trợ = Số tháng (copy giá trị)
   */
  onSoThangChange(soThang: number) {
    if (soThang && soThang > 0) {
      this.parentForm.get('soThangNsnnHoTro')?.setValue(soThang);
      console.log(`📅 Cập nhật số tháng NSNN hỗ trợ: ${soThang} tháng`);
    } else {
      this.parentForm.get('soThangNsnnHoTro')?.setValue(0);
    }
  }

  /**
   * Xử lý thay đổi mức thu nhập và tính hệ số đóng
   * Công thức: Hệ số = (Mức thu nhập - 1.500.000) / 50.000
   */
  onMucThuNhapChange(mucThuNhap: number) {
    if (mucThuNhap && mucThuNhap >= 1500000) {
      const heSoDong = Math.floor((mucThuNhap - 1500000) / 50000);
      this.parentForm.get('heSoDong')?.setValue(heSoDong);
      console.log(`💰 Cập nhật hệ số đóng: ${heSoDong} cho mức thu nhập ${mucThuNhap.toLocaleString('vi-VN')} VNĐ`);
    } else {
      this.parentForm.get('heSoDong')?.setValue(0);
    }
  }

  /**
   * Format số tiền với dấu phân cách
   */
  formatCurrency(value: number): string {
    if (!value && value !== 0) return '';
    return value.toLocaleString('vi-VN');
  }

  /**
   * Lấy giá trị tiền đã format để hiển thị
   */
  getFormattedValue(fieldName: string): string {
    const value = this.parentForm.get(fieldName)?.value;
    return this.formatCurrency(value);
  }

  /**
   * Lấy giá trị mức thu nhập đã format để hiển thị
   */
  getFormattedMucThuNhap(): string {
    const value = this.parentForm.get('mucThuNhap')?.value;
    return this.formatCurrency(value);
  }

  /**
   * Xử lý input mức thu nhập với format tiền
   */
  onMucThuNhapInput(event: any) {
    const inputValue = event.target.value;
    // Loại bỏ tất cả ký tự không phải số
    const numericValue = inputValue.replace(/[^\d]/g, '');

    if (numericValue) {
      const numberValue = parseInt(numericValue, 10);
      // Kiểm tra giá trị tối thiểu
      if (numberValue >= 1500000) {
        this.parentForm.get('mucThuNhap')?.setValue(numberValue);
      } else {
        // Nếu nhỏ hơn mức tối thiểu, set về mức tối thiểu
        this.parentForm.get('mucThuNhap')?.setValue(1500000);
      }
    } else {
      this.parentForm.get('mucThuNhap')?.setValue(null);
    }
  }

  /**
   * Load danh sách loại cho dropdown
   */
  private loadLoaiOptions() {
    this.loaiService.getLoaiOptions().subscribe({
      next: (options) => {
        this.loaiOptions = options;
        console.log('📋 Loaded loại options:', this.loaiOptions.length);
      },
      error: (error) => {
        console.error('🔴 Lỗi khi load danh sách loại:', error);
        this.loaiOptions = [];
      }
    });
  }
}
