import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

/**
 * Component loading spinner t<PERSON>i sử dụng
 */
@Component({
  selector: 'app-loading-spinner',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div [class]="containerClasses">
      <div [class]="spinnerClasses"></div>
      <span *ngIf="message" [class]="messageClasses">{{ message }}</span>
    </div>
  `,
  styles: [`
    .spinner-container {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .spinner-container.inline {
      display: inline-flex;
    }

    .spinner-container.block {
      display: flex;
    }

    .spinner-container.overlay {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 50;
    }

    .spinner {
      animation: spin 1s linear infinite;
      border-radius: 9999px;
      border-style: solid;
      border-color: currentColor;
      border-right-color: transparent;
    }

    .spinner-sm {
      width: 1rem;
      height: 1rem;
      border-width: 2px;
    }

    .spinner-md {
      width: 1.5rem;
      height: 1.5rem;
      border-width: 2px;
    }

    .spinner-lg {
      width: 2rem;
      height: 2rem;
      border-width: 2px;
    }

    .spinner-xl {
      width: 3rem;
      height: 3rem;
      border-width: 4px;
    }

    .message {
      margin-left: 0.5rem;
      font-size: 0.875rem;
    }

    .message-sm {
      font-size: 0.75rem;
    }

    .message-lg {
      font-size: 1rem;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `]
})
export class LoadingSpinnerComponent {
  @Input() size: 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() color = 'text-blue-600';
  @Input() message = '';
  @Input() type: 'inline' | 'block' | 'overlay' = 'inline';

  get containerClasses(): string {
    const baseClasses = 'spinner-container flex items-center justify-center';
    const typeClasses = this.getTypeClasses();
    
    return `${baseClasses} ${typeClasses}`.trim();
  }

  get spinnerClasses(): string {
    const baseClasses = 'spinner animate-spin rounded-full border-solid border-current border-r-transparent';
    const sizeClasses = this.getSizeClasses();
    const colorClasses = this.color;
    
    return `${baseClasses} ${sizeClasses} ${colorClasses}`.trim();
  }

  get messageClasses(): string {
    const baseClasses = 'message ml-2';
    const sizeClasses = this.getMessageSizeClasses();
    const colorClasses = this.color;
    
    return `${baseClasses} ${sizeClasses} ${colorClasses}`.trim();
  }

  private getTypeClasses(): string {
    switch (this.type) {
      case 'inline':
        return 'inline-flex';
      case 'block':
        return 'flex';
      case 'overlay':
        return 'fixed inset-0 bg-black bg-opacity-50 z-50';
      default:
        return 'inline-flex';
    }
  }

  private getSizeClasses(): string {
    switch (this.size) {
      case 'sm':
        return 'spinner-sm w-4 h-4 border-2';
      case 'md':
        return 'spinner-md w-6 h-6 border-2';
      case 'lg':
        return 'spinner-lg w-8 h-8 border-2';
      case 'xl':
        return 'spinner-xl w-12 h-12 border-4';
      default:
        return 'spinner-md w-6 h-6 border-2';
    }
  }

  private getMessageSizeClasses(): string {
    switch (this.size) {
      case 'sm':
        return 'message-sm text-xs';
      case 'md':
        return 'text-sm';
      case 'lg':
      case 'xl':
        return 'message-lg text-base';
      default:
        return 'text-sm';
    }
  }
}
