﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HuyPhuc.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateLaoDongToKhai602Schema : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_lao_dong_to_khai_602",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropPrimaryKey(
                name: "PK_lao_dong_to_khai_602",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropIndex(
                name: "idx_lao_dong_to_khai_602_ngay_bat_dau",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropColumn(
                name: "dia_chi",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropColumn(
                name: "email",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropColumn(
                name: "ghi_chu",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropColumn(
                name: "gioi_tinh",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropColumn(
                name: "ho_ten",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropColumn(
                name: "muc_luong",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropColumn(
                name: "ngay_bat_dau",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropColumn(
                name: "nghe_nghiep",
                table: "lao_dong_to_khai_602");

            migrationBuilder.DropColumn(
                name: "so_dien_thoai",
                table: "lao_dong_to_khai_602");

            migrationBuilder.RenameTable(
                name: "lao_dong_to_khai_602",
                newName: "chi_tiet_to_khai_602");

            migrationBuilder.RenameColumn(
                name: "Id",
                table: "to_khai_602",
                newName: "id");

            migrationBuilder.RenameColumn(
                name: "updated_at",
                table: "to_khai_602",
                newName: "last_modified");

            migrationBuilder.RenameColumn(
                name: "so_cccd",
                table: "chi_tiet_to_khai_602",
                newName: "ma_so_bhxh");

            migrationBuilder.RenameColumn(
                name: "ngay_sinh",
                table: "chi_tiet_to_khai_602",
                newName: "created");

            migrationBuilder.RenameColumn(
                name: "ngay_ket_thuc",
                table: "chi_tiet_to_khai_602",
                newName: "last_modified");

            migrationBuilder.RenameIndex(
                name: "idx_lao_dong_to_khai_602_to_khai_id",
                table: "chi_tiet_to_khai_602",
                newName: "idx_chi_tiet_to_khai_602_to_khai_id");

            migrationBuilder.RenameIndex(
                name: "idx_lao_dong_to_khai_602_so_cccd",
                table: "chi_tiet_to_khai_602",
                newName: "idx_chi_tiet_to_khai_602_ma_so_bhxh");

            migrationBuilder.AddColumn<string>(
                name: "created_by",
                table: "to_khai_602",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "last_modified_by",
                table: "to_khai_602",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "created_by",
                table: "chi_tiet_to_khai_602",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "is_error",
                table: "chi_tiet_to_khai_602",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "last_modified_by",
                table: "chi_tiet_to_khai_602",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ma_loi",
                table: "chi_tiet_to_khai_602",
                type: "character varying(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "message",
                table: "chi_tiet_to_khai_602",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "mo_ta_loi",
                table: "chi_tiet_to_khai_602",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "muc_thu_nhap",
                table: "chi_tiet_to_khai_602",
                type: "numeric(15,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "phuong_an",
                table: "chi_tiet_to_khai_602",
                type: "character varying(10)",
                maxLength: 10,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "phuong_thuc",
                table: "chi_tiet_to_khai_602",
                type: "character varying(10)",
                maxLength: 10,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "stt",
                table: "chi_tiet_to_khai_602",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "thang_bat_dau",
                table: "chi_tiet_to_khai_602",
                type: "character varying(10)",
                maxLength: 10,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "tien_ho_tro",
                table: "chi_tiet_to_khai_602",
                type: "numeric(15,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "tien_lai",
                table: "chi_tiet_to_khai_602",
                type: "numeric(15,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "tien_thua",
                table: "chi_tiet_to_khai_602",
                type: "numeric(15,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "tien_tu_dong",
                table: "chi_tiet_to_khai_602",
                type: "numeric(15,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "tong_tien",
                table: "chi_tiet_to_khai_602",
                type: "numeric(15,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddPrimaryKey(
                name: "PK_chi_tiet_to_khai_602",
                table: "chi_tiet_to_khai_602",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "ix_chi_tiet_to_khai_602_unique",
                table: "chi_tiet_to_khai_602",
                columns: new[] { "to_khai_602_id", "ma_so_bhxh" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "fk_chi_tiet_to_khai_602",
                table: "chi_tiet_to_khai_602",
                column: "to_khai_602_id",
                principalTable: "to_khai_602",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_chi_tiet_to_khai_602",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropPrimaryKey(
                name: "PK_chi_tiet_to_khai_602",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropIndex(
                name: "ix_chi_tiet_to_khai_602_unique",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "created_by",
                table: "to_khai_602");

            migrationBuilder.DropColumn(
                name: "last_modified_by",
                table: "to_khai_602");

            migrationBuilder.DropColumn(
                name: "created_by",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "is_error",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "last_modified_by",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "ma_loi",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "message",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "mo_ta_loi",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "muc_thu_nhap",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "phuong_an",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "phuong_thuc",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "stt",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "thang_bat_dau",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "tien_ho_tro",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "tien_lai",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "tien_thua",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "tien_tu_dong",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropColumn(
                name: "tong_tien",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.RenameTable(
                name: "chi_tiet_to_khai_602",
                newName: "lao_dong_to_khai_602");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "to_khai_602",
                newName: "Id");

            migrationBuilder.RenameColumn(
                name: "last_modified",
                table: "to_khai_602",
                newName: "updated_at");

            migrationBuilder.RenameColumn(
                name: "ma_so_bhxh",
                table: "lao_dong_to_khai_602",
                newName: "so_cccd");

            migrationBuilder.RenameColumn(
                name: "last_modified",
                table: "lao_dong_to_khai_602",
                newName: "ngay_ket_thuc");

            migrationBuilder.RenameColumn(
                name: "created",
                table: "lao_dong_to_khai_602",
                newName: "ngay_sinh");

            migrationBuilder.RenameIndex(
                name: "idx_chi_tiet_to_khai_602_to_khai_id",
                table: "lao_dong_to_khai_602",
                newName: "idx_lao_dong_to_khai_602_to_khai_id");

            migrationBuilder.RenameIndex(
                name: "idx_chi_tiet_to_khai_602_ma_so_bhxh",
                table: "lao_dong_to_khai_602",
                newName: "idx_lao_dong_to_khai_602_so_cccd");

            migrationBuilder.AddColumn<string>(
                name: "dia_chi",
                table: "lao_dong_to_khai_602",
                type: "character varying(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "email",
                table: "lao_dong_to_khai_602",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ghi_chu",
                table: "lao_dong_to_khai_602",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "gioi_tinh",
                table: "lao_dong_to_khai_602",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "ho_ten",
                table: "lao_dong_to_khai_602",
                type: "character varying(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "muc_luong",
                table: "lao_dong_to_khai_602",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<DateTime>(
                name: "ngay_bat_dau",
                table: "lao_dong_to_khai_602",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "nghe_nghiep",
                table: "lao_dong_to_khai_602",
                type: "character varying(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "so_dien_thoai",
                table: "lao_dong_to_khai_602",
                type: "character varying(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_lao_dong_to_khai_602",
                table: "lao_dong_to_khai_602",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "idx_lao_dong_to_khai_602_ngay_bat_dau",
                table: "lao_dong_to_khai_602",
                column: "ngay_bat_dau");

            migrationBuilder.AddForeignKey(
                name: "fk_lao_dong_to_khai_602",
                table: "lao_dong_to_khai_602",
                column: "to_khai_602_id",
                principalTable: "to_khai_602",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
