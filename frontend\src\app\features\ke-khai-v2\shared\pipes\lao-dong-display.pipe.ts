import { Pipe, PipeTransform } from '@angular/core';

/**
 * Pipe để format hiển thị thông tin lao động
 * Xử lý các trường hợp dữ liệu placeholder hoặc không hợp lệ
 */
@Pipe({
  name: 'laoDongDisplay',
  standalone: true
})
export class LaoDongDisplayPipe implements PipeTransform {

  /**
   * Transform dữ liệu hiển thị cho lao động
   * @param value Giá trị cần transform
   * @param type Loại dữ liệu: 'hoTen' | 'cmnd' | 'soBHXH'
   * @param maSoBHXH Mã số BHXH (dùng cho fallback)
   * @returns Giá trị đã được format
   */
  transform(value: any, type: 'hoTen' | 'cmnd' | 'soBHXH', maSoBHXH?: string): string {
    if (!value) {
      return this.getDefaultValue(type, maSoBHXH);
    }

    const stringValue = String(value).trim();

    switch (type) {
      case 'hoTen':
        return this.formatHoTen(stringValue, maSoBHXH);
      
      case 'cmnd':
        return this.formatCmnd(stringValue, maSoBHXH);
      
      case 'soBHXH':
        return this.formatSoBHXH(stringValue);
      
      default:
        return stringValue;
    }
  }

  /**
   * Format họ tên
   */
  private formatHoTen(hoTen: string, maSoBHXH?: string): string {
    // Kiểm tra các trường hợp placeholder
    if (hoTen === '[Cần cập nhật họ tên]' ||
        hoTen === '[Chưa có thông tin]' ||
        hoTen.startsWith('Lao động ')) {
      // Tùy chọn: hiển thị "Lao động + mã số" hoặc placeholder
      return maSoBHXH ? `Lao động ${maSoBHXH}` : 'Chưa có thông tin';
      // Hoặc: return '[Cần cập nhật họ tên]'; // để hiển thị placeholder
    }

    // Trả về họ tên bình thường
    return hoTen;
  }

  /**
   * Format CMND/CCCD
   */
  private formatCmnd(cmnd: string, maSoBHXH?: string): string {
    // Kiểm tra các trường hợp không hợp lệ
    if (cmnd === '000000000000' || 
        cmnd === maSoBHXH ||
        cmnd.length < 9) {
      return 'Chưa cập nhật';
    }

    // Trả về CMND bình thường
    return cmnd;
  }

  /**
   * Format số BHXH
   */
  private formatSoBHXH(soBHXH: string): string {
    // Kiểm tra độ dài hợp lệ
    if (soBHXH.length !== 10) {
      return soBHXH;
    }

    // Format: XXXX-XXX-XXX
    return `${soBHXH.substring(0, 4)}-${soBHXH.substring(4, 7)}-${soBHXH.substring(7, 10)}`;
  }

  /**
   * Lấy giá trị mặc định khi không có dữ liệu
   */
  private getDefaultValue(type: 'hoTen' | 'cmnd' | 'soBHXH', maSoBHXH?: string): string {
    switch (type) {
      case 'hoTen':
        return maSoBHXH ? `Lao động ${maSoBHXH}` : 'Chưa có thông tin';
      
      case 'cmnd':
        return 'Chưa cập nhật';
      
      case 'soBHXH':
        return 'Chưa có';
      
      default:
        return '';
    }
  }
}
