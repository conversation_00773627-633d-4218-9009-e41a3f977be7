using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Common.Models;
using HuyPhuc.Application.Features.XacThuc.Models;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace HuyPhuc.Application.Features.XacThuc.Commands.LamMoiToken;

/// <summary>
/// Handler xử lý làm mới access token
/// </summary>
public class LamMoiTokenCommandHandler : IRequestHandler<LamMoiTokenCommand, Result<LamMoiTokenResponse>>
{
    private readonly INguoiDungRepository _nguoiDungRepository;
    private readonly IRefreshTokenRepository _refreshTokenRepository;
    private readonly IJwtService _jwtService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<LamMoiTokenCommandHandler> _logger;

    public LamMoiTokenCommandHandler(
        INguoiDungRepository nguoiDungRepository,
        IRefreshTokenRepository refreshTokenRepository,
        IJwtService jwtService,
        IUnitOfWork unitOfWork,
        ILogger<LamMoiTokenCommandHandler> logger)
    {
        _nguoiDungRepository = nguoiDungRepository;
        _refreshTokenRepository = refreshTokenRepository;
        _jwtService = jwtService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<LamMoiTokenResponse>> Handle(LamMoiTokenCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Bắt đầu xử lý làm mới token");

            if (string.IsNullOrWhiteSpace(request.RefreshToken))
            {
                return Result<LamMoiTokenResponse>.Failure("Refresh token không hợp lệ");
            }

            // Tìm refresh token trong database
            var refreshTokenEntity = await _refreshTokenRepository.LayTheoTokenAsync(request.RefreshToken, cancellationToken);
            if (refreshTokenEntity == null)
            {
                _logger.LogWarning("Refresh token không tồn tại: {Token}", request.RefreshToken[..10] + "...");
                return Result<LamMoiTokenResponse>.Failure("Refresh token không hợp lệ");
            }

            // Kiểm tra refresh token có hợp lệ không
            if (!refreshTokenEntity.CoHopLe)
            {
                _logger.LogWarning("Refresh token không hợp lệ hoặc đã bị thu hồi cho người dùng: {UserId}", refreshTokenEntity.NguoiDungId);
                return Result<LamMoiTokenResponse>.Failure("Refresh token đã hết hạn hoặc bị thu hồi");
            }

            // Lấy thông tin người dùng với vai trò
            var nguoiDung = await _nguoiDungRepository.LayTheoIdVoiVaiTroAsync(refreshTokenEntity.NguoiDungId, cancellationToken);
            if (nguoiDung == null)
            {
                _logger.LogWarning("Không tìm thấy người dùng với ID: {UserId}", refreshTokenEntity.NguoiDungId);
                return Result<LamMoiTokenResponse>.Failure("Người dùng không tồn tại");
            }

            // Thu hồi refresh token cũ
            refreshTokenEntity.ThuHoi("Đã sử dụng để làm mới token");
            _refreshTokenRepository.CapNhat(refreshTokenEntity);

            // Tạo tokens mới
            var newAccessToken = _jwtService.TaoAccessToken(nguoiDung);
            var newRefreshTokenString = _jwtService.TaoRefreshToken();
            var thoiGianHetHan = _jwtService.LayThoiGianHetHan(newAccessToken) ?? DateTime.UtcNow.AddHours(1);

            // Lưu refresh token mới
            var newRefreshTokenEntity = RefreshToken.TaoMoi(
                token: newRefreshTokenString,
                nguoiDungId: nguoiDung.Id,
                thoiGianHetHan: DateTime.UtcNow.AddDays(7),
                ipAddress: refreshTokenEntity.IpAddress,
                userAgent: refreshTokenEntity.UserAgent
            );

            await _refreshTokenRepository.ThemAsync(newRefreshTokenEntity, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Làm mới token thành công cho người dùng: {UserId}", nguoiDung.Id);

            var response = new LamMoiTokenResponse
            {
                AccessToken = newAccessToken,
                RefreshToken = newRefreshTokenString,
                ThoiGianHetHan = thoiGianHetHan
            };

            return Result<LamMoiTokenResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi xử lý làm mới token");
            return Result<LamMoiTokenResponse>.Failure("Có lỗi xảy ra khi làm mới token");
        }
    }
}
