using System.ComponentModel.DataAnnotations;

namespace HuyPhuc.Application.Features.BHXH.DTOs;

/// <summary>
/// DTO cho request tra cứu thông tin BHXH
/// </summary>
public class BhxhLookupRequestDto
{
    [Required(ErrorMessage = "Mã số BHXH là bắt buộc")]
    [StringLength(10, MinimumLength = 10, ErrorMessage = "Mã số BHXH phải có đúng 10 chữ số")]
    [RegularExpression(@"^\d{10}$", ErrorMessage = "Mã số BHXH chỉ được chứa số")]
    public string MaSoBHXH { get; set; } = string.Empty;
}

/// <summary>
/// DTO cho response tra cứu thông tin BHXH từ VNPost API
/// </summary>
public class BhxhLookupResponseDto
{
    public BhxhDataDto Data { get; set; } = new();
    public bool Success { get; set; }
    public string? Message { get; set; }
    public object? Errors { get; set; }
    public int Status { get; set; }
    public string? TraceId { get; set; }
}

/// <summary>
/// DTO chứa thông tin chi tiết BHXH
/// </summary>
public class BhxhDataDto
{
    public string MaSoBHXH { get; set; } = string.Empty;
    public string HoTen { get; set; } = string.Empty;
    public string CCNS { get; set; } = string.Empty;
    public string NgaySinh { get; set; } = string.Empty;
    public int GioiTinh { get; set; }
    public string QuocTich { get; set; } = string.Empty;
    public string DanToc { get; set; } = string.Empty;
    public string CMND { get; set; } = string.Empty;
    public string MaTinhKs { get; set; } = string.Empty;
    public string MaHuyenKs { get; set; } = string.Empty;
    public string MaXaKs { get; set; } = string.Empty;
    public string DienThoaiLh { get; set; } = string.Empty;
    public string MaHoGiaDinh { get; set; } = string.Empty;
    public string PhuongAn { get; set; } = string.Empty;
    public string PhuongThuc { get; set; } = string.Empty;
    public string ThangBatDau { get; set; } = string.Empty;
    public decimal TienLai { get; set; }
    public decimal TienThua { get; set; }
    public decimal TienTuDong { get; set; }
    public decimal TongTien { get; set; }
    public decimal TienHoTro { get; set; }
    public decimal MucThuNhap { get; set; }
    public string TypeId { get; set; } = string.Empty;
    public bool IsThamGiaBb { get; set; }
    public bool IsTamHoanHD { get; set; }
    public string Message { get; set; } = string.Empty;
    public bool IsError { get; set; }
    public string MaLoi { get; set; } = string.Empty;
    public string MoTaLoi { get; set; } = string.Empty;
}

/// <summary>
/// DTO cho request gửi đến VNPost API
/// </summary>
public class VnPostBhxhRequestDto
{
    public string Code { get; set; } = string.Empty;
    public string Text { get; set; } = string.Empty;
    public string MaSoBHXH { get; set; } = string.Empty;
}
