using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class QuyenConfiguration : IEntityTypeConfiguration<Quyen>
{
    public void Configure(EntityTypeBuilder<Quyen> builder)
    {
        builder.ToTable("quyen");

        builder.<PERSON><PERSON>ey(x => x.Id);
        builder.Property(x => x.Id).HasColumnName("id");

        builder.Property(x => x.TenQuyen)
            .IsRequired()
            .HasColumnName("ten_quyen")
            .HasMaxLength(100);

        builder.Property(x => x.<PERSON>Quy<PERSON>)
            .IsRequired()
            .HasColumnName("ma_quyen")
            .HasMaxLength(50);

        builder.Property(x => x.MoTa)
            .HasColumnName("mo_ta")
            .HasMaxLength(500);

        builder.Property(x => x.NhomQuyen)
            .HasColumnName("nhom_quyen")
            .HasMaxLength(50);

        builder.Property(x => x.<PERSON><PERSON><PERSON><PERSON>eThong)
            .IsRequired()
            .HasColumnName("la_quyen_he_thong")
            .HasDefaultValue(false);

        builder.Property(x => x.TrangThaiHoatDong)
            .IsRequired()
            .HasColumnName("trang_thai_hoat_dong")
            .HasDefaultValue(true);

        // Audit fields
        builder.Property(x => x.NgayTao)
            .IsRequired()
            .HasColumnName("ngay_tao");

        builder.Property(x => x.NgayCapNhat)
            .HasColumnName("ngay_cap_nhat");

        builder.Property(x => x.NguoiTao)
            .HasColumnName("nguoi_tao")
            .HasMaxLength(50);

        builder.Property(x => x.NguoiCapNhat)
            .HasColumnName("nguoi_cap_nhat")
            .HasMaxLength(50);

        // Indexes
        builder.HasIndex(x => x.MaQuyen)
            .IsUnique()
            .HasDatabaseName("ix_quyen_ma_quyen");

        builder.HasIndex(x => x.TenQuyen)
            .HasDatabaseName("ix_quyen_ten_quyen");

        builder.HasIndex(x => x.NhomQuyen)
            .HasDatabaseName("ix_quyen_nhom_quyen");

        builder.HasIndex(x => x.TrangThaiHoatDong)
            .HasDatabaseName("ix_quyen_trang_thai_hoat_dong");

        // Relationships
        builder.HasMany(x => x.DanhSachVaiTro)
            .WithOne(x => x.Quyen)
            .HasForeignKey(x => x.QuyenId)
            .OnDelete(DeleteBehavior.Cascade);

        // Ignore domain events
        builder.Ignore(x => x.DomainEvents);
    }
}
