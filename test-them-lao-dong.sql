-- Script test thêm lao động để debug lỗi validation

-- 1. <PERSON><PERSON><PERSON> tra các kê khai hiện có
SELECT 
    id, ma_ke_khai, trang_thai, 
    CASE 
        WHEN trang_thai = 0 THEN 'Đang soạn'
        WHEN trang_thai = 1 THEN 'Đã gửi'
        ELSE 'Khác'
    END as trang_thai_text,
    created, created_by
FROM danh_sach_ke_khai 
ORDER BY id DESC 
LIMIT 5;

-- 2. Kiểm tra lao động trong kê khai gần nhất
WITH latest_ke_khai AS (
    SELECT id FROM danh_sach_ke_khai WHERE trang_thai = 0 ORDER BY id DESC LIMIT 1
)
SELECT 
    c.id, c.ke_khai_id, c.ma_so_bhxh, c.stt,
    t.ho_ten, t.cmnd, t.ngay_sinh
FROM chi_tiet_lao_dong_ke_khai_v2 c
LEFT JOIN tk1_ts t ON c.ma_so_bhxh = t.ma_so_bhxh
WHERE c.ke_khai_id = (SELECT id FROM latest_ke_khai)
ORDER BY c.stt;

-- 3. Kiểm tra mã số BHXH có sẵn trong tk1_ts
SELECT 
    ma_so_bhxh, ho_ten, cmnd, ngay_sinh, gioi_tinh,
    CASE 
        WHEN ho_ten = '[Cần cập nhật họ tên]' THEN 'Placeholder'
        ELSE 'Có dữ liệu'
    END as trang_thai_ho_ten,
    CASE 
        WHEN cmnd = '000000000000' THEN 'Placeholder'
        ELSE 'Có dữ liệu'
    END as trang_thai_cmnd
FROM tk1_ts 
WHERE ma_so_bhxh NOT IN (
    SELECT DISTINCT ma_so_bhxh 
    FROM chi_tiet_lao_dong_ke_khai_v2 
    WHERE ke_khai_id = (SELECT id FROM danh_sach_ke_khai WHERE trang_thai = 0 ORDER BY id DESC LIMIT 1)
)
ORDER BY id 
LIMIT 5;

-- 4. Kiểm tra unique constraints
SELECT 
    'Unique constraint check' as description,
    COUNT(*) as total_records,
    COUNT(DISTINCT CONCAT(ke_khai_id, '-', ma_so_bhxh)) as unique_combinations,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT CONCAT(ke_khai_id, '-', ma_so_bhxh)) THEN 'OK'
        ELSE 'VIOLATION'
    END as constraint_status
FROM chi_tiet_lao_dong_ke_khai_v2;

-- 5. Tìm các bản ghi vi phạm unique constraint (nếu có)
SELECT 
    ke_khai_id, ma_so_bhxh, COUNT(*) as count
FROM chi_tiet_lao_dong_ke_khai_v2
GROUP BY ke_khai_id, ma_so_bhxh
HAVING COUNT(*) > 1;

-- 6. Kiểm tra STT trong kê khai
WITH latest_ke_khai AS (
    SELECT id FROM danh_sach_ke_khai WHERE trang_thai = 0 ORDER BY id DESC LIMIT 1
)
SELECT 
    'STT check for latest ke_khai' as description,
    ke_khai_id,
    COUNT(*) as total_records,
    MIN(stt) as min_stt,
    MAX(stt) as max_stt,
    COUNT(DISTINCT stt) as unique_stt_count,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT stt) THEN 'OK'
        ELSE 'DUPLICATE STT'
    END as stt_status
FROM chi_tiet_lao_dong_ke_khai_v2
WHERE ke_khai_id = (SELECT id FROM latest_ke_khai)
GROUP BY ke_khai_id;

-- 7. Gợi ý mã số BHXH để test
SELECT 
    'Suggested BHXH codes for testing' as description,
    ma_so_bhxh, ho_ten, cmnd
FROM tk1_ts 
WHERE ma_so_bhxh NOT IN (
    SELECT DISTINCT ma_so_bhxh 
    FROM chi_tiet_lao_dong_ke_khai_v2
)
ORDER BY id 
LIMIT 3;
