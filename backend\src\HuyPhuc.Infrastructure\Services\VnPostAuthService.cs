using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using HuyPhuc.Application.Common.Configurations;
using HuyPhuc.Application.Features.BHXH.DTOs;
using HuyPhuc.Application.Features.BHXH.Interfaces;

namespace HuyPhuc.Infrastructure.Services;

/// <summary>
/// Service để xử lý authentication với VNPost API
/// </summary>
public class VnPostAuthService : IVnPostAuthService
{
    private readonly HttpClient _httpClient;
    private readonly VnPostApiConfiguration _config;
    private readonly ILogger<VnPostAuthService> _logger;
    
    private string _currentAccessToken;
    private DateTime _tokenExpiryTime;
    private readonly object _tokenLock = new();

    public VnPostAuthService(
        HttpClient httpClient,
        IOptions<VnPostApiConfiguration> config,
        ILogger<VnPostAuthService> logger)
    {
        _httpClient = httpClient;
        _config = config.Value;
        _logger = logger;
        _currentAccessToken = _config.AccessToken;
        
        // Giả sử token hiện tại hết hạn sau 5 giờ (18000 seconds)
        _tokenExpiryTime = DateTime.UtcNow.AddSeconds(18000);
    }

    public async Task<VnPostCaptchaResponseDto> GetCaptchaAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_config.BaseUrl}/oauth2/Captcha");
            
            // Add headers
            request.Headers.Add("Accept", "application/json, text/plain, */*");
            request.Headers.Add("Accept-Language", "en-US,en;q=0.9");
            request.Headers.Add("Referer", "https://ssm.vnpost.vn/account/login");
            request.Headers.Add("User-Agent", _config.UserAgent);

            var response = await _httpClient.SendAsync(request, cancellationToken);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var captchaResponse = JsonSerializer.Deserialize<VnPostCaptchaResponseDto>(content, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            _logger.LogInformation("Successfully retrieved captcha from VNPost");
            return captchaResponse ?? new VnPostCaptchaResponseDto();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting captcha from VNPost");
            throw;
        }
    }

    public async Task<VnPostLoginResponseDto> LoginAsync(VnPostLoginRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var formData = new List<KeyValuePair<string, string>>
            {
                new("grant_type", request.GrantType),
                new("userName", request.UserName),
                new("password", request.Password),
                new("text", request.Text),
                new("code", request.Code),
                new("clientId", request.ClientId),
                new("isWeb", request.IsWeb.ToString().ToLower())
            };

            var httpRequest = new HttpRequestMessage(HttpMethod.Post, $"{_config.BaseUrl}/oauth2/Authenticate")
            {
                Content = new FormUrlEncodedContent(formData)
            };

            // Add headers
            httpRequest.Headers.Add("Accept", "application/json, text/plain, */*");
            httpRequest.Headers.Add("Accept-Language", "en-US,en;q=0.9");
            httpRequest.Headers.Add("not_auth_token", "false");
            httpRequest.Headers.Add("Origin", "https://ssm.vnpost.vn");
            httpRequest.Headers.Add("Referer", "https://ssm.vnpost.vn/account/login");
            httpRequest.Headers.Add("User-Agent", _config.UserAgent);

            var response = await _httpClient.SendAsync(httpRequest, cancellationToken);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var loginResponse = JsonSerializer.Deserialize<VnPostLoginResponseDto>(content, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            if (loginResponse != null && !string.IsNullOrEmpty(loginResponse.AccessToken))
            {
                lock (_tokenLock)
                {
                    _currentAccessToken = loginResponse.AccessToken;
                    _tokenExpiryTime = DateTime.UtcNow.AddSeconds(loginResponse.ExpiresIn);

                    // Also update the configuration for other services to use
                    _config.AccessToken = loginResponse.AccessToken;
                }

                _logger.LogInformation("Successfully logged in to VNPost and updated access token. New token expires at: {ExpiryTime}", _tokenExpiryTime);
            }

            return loginResponse ?? new VnPostLoginResponseDto();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging in to VNPost");
            throw;
        }
    }

    public async Task<bool> RefreshTokenAsync(RefreshTokenRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            var loginRequest = new VnPostLoginRequestDto
            {
                UserName = request.UserName,
                Password = request.Password,
                Text = request.CaptchaText,
                Code = request.CaptchaCode,
                ClientId = "Yjg2NWUwMWEtNDVmZS00MTdhLTg5M2MtNzdkZmE3NjU4NzUz" // Default client ID
            };

            var loginResponse = await LoginAsync(loginRequest, cancellationToken);
            return !string.IsNullOrEmpty(loginResponse.AccessToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing VNPost token");
            return false;
        }
    }

    public string GetCurrentAccessToken()
    {
        lock (_tokenLock)
        {
            return _currentAccessToken;
        }
    }

    public bool IsTokenExpired()
    {
        lock (_tokenLock)
        {
            var now = DateTime.UtcNow;
            var isExpired = now >= _tokenExpiryTime || string.IsNullOrEmpty(_currentAccessToken);

            _logger.LogInformation("🔍 VNPost Token validation: Now={Now}, ExpiryTime={ExpiryTime}, IsExpired={IsExpired}, HasToken={HasToken}",
                now, _tokenExpiryTime, isExpired, !string.IsNullOrEmpty(_currentAccessToken));

            return isExpired;
        }
    }

    public void ForceTokenExpired()
    {
        lock (_tokenLock)
        {
            _logger.LogWarning("🔴 Force expiring VNPost token for testing");
            _currentAccessToken = string.Empty;
            _tokenExpiryTime = DateTime.UtcNow.AddMinutes(-1); // Set to past time
        }
    }
}
