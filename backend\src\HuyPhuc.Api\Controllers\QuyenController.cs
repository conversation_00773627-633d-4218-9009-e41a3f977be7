using HuyPhuc.Api.Authorization;
using HuyPhuc.Application.Features.Quyen.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// Controller quản lý quyền
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class QuyenController : ControllerBase
{
    private readonly IMediator _mediator;

    public QuyenController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// L<PERSON>y danh sách quyền
    /// </summary>
    [HttpGet]
    [Permission("QUYEN_QUAN_LY")]
    public async Task<IActionResult> LayDanhSachQuyen([FromQuery] LayDanhSachQuyenQuery query)
    {
        var result = await _mediator.Send(query);
        
        if (result.IsSuccess)
        {
            return Ok(new
            {
                success = true,
                data = result.Data,
                message = "<PERSON><PERSON><PERSON> danh sách quyền thành công"
            });
        }

        return BadRequest(new
        {
            success = false,
            message = result.Errors.FirstOrDefault() ?? "Có lỗi xảy ra"
        });
    }

    /// <summary>
    /// Lấy danh sách nhóm quyền
    /// </summary>
    [HttpGet("nhom")]
    [Permission("QUYEN_QUAN_LY")]
    public async Task<IActionResult> LayDanhSachNhomQuyen()
    {
        var query = new LayDanhSachQuyenQuery { TrangThaiHoatDong = true };
        var result = await _mediator.Send(query);
        
        if (result.IsSuccess)
        {
            var nhomQuyenList = result.Data
                .Where(q => !string.IsNullOrEmpty(q.NhomQuyen))
                .GroupBy(q => q.NhomQuyen)
                .Select(g => new
                {
                    NhomQuyen = g.Key,
                    SoLuongQuyen = g.Count(),
                    DanhSachQuyen = g.Select(q => new { q.MaQuyen, q.TenQuyen }).ToList()
                })
                .OrderBy(g => g.NhomQuyen)
                .ToList();

            return Ok(new
            {
                success = true,
                data = nhomQuyenList,
                message = "Lấy danh sách nhóm quyền thành công"
            });
        }

        return BadRequest(new
        {
            success = false,
            message = result.Errors.FirstOrDefault() ?? "Có lỗi xảy ra"
        });
    }
}
