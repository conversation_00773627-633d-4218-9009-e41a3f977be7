using HuyPhuc.Domain.Common;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity đại diện cho đơn vị thuộc đại lý
/// </summary>
public class DonVi : BaseAuditableEntity
{
    /// <summary>
    /// Mã đơn vị
    /// </summary>
    public string MaDonVi { get; set; } = string.Empty;

    /// <summary>
    /// Tên đơn vị
    /// </summary>
    public string TenDonVi { get; set; } = string.Empty;

    /// <summary>
    /// Địa chỉ đơn vị
    /// </summary>
    public string? DiaChi { get; set; }

    /// <summary>
    /// Số điện thoại
    /// </summary>
    public string? SoDienThoai { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// ID đại lý
    /// </summary>
    public int DaiLyId { get; set; }

    /// <summary>
    /// Trạng thái hoạt động
    /// </summary>
    public bool TrangThaiHoatDong { get; set; } = true;

    /// <summary>
    /// Ghi chú
    /// </summary>
    public string? GhiChu { get; set; }

    // Navigation properties
    /// <summary>
    /// Đại lý mà đơn vị này thuộc về
    /// </summary>
    public virtual DaiLy DaiLy { get; set; } = null!;
}
