using HuyPhuc.Domain.Events.Base;
using HuyPhuc.Domain.Entities;

namespace HuyPhuc.Domain.Events;

/// <summary>
/// Event khi danh mục thủ tục được tạo
/// </summary>
public class DanhMucThuTucDaTaoEvent : BaseEvent
{
    public DanhMucThuTuc DanhMucThuTuc { get; }

    public DanhMucThuTucDaTaoEvent(DanhMucThuTuc danhMucThuTuc)
    {
        DanhMucThuTuc = danhMucThuTuc;
    }
}

/// <summary>
/// Event khi danh mục thủ tục được cập nhật
/// </summary>
public class DanhMucThuTucDaCapNhatEvent : BaseEvent
{
    public DanhMucThuTuc DanhMucThuTuc { get; }

    public DanhMucThuTucDaCapNhatEvent(DanhMucThuTuc danhMucThuTuc)
    {
        DanhMucThuTuc = danhMucThuTuc;
    }
}

/// <summary>
/// Event khi danh mục thủ tục được kích hoạt
/// </summary>
public class DanhMucThuTucDaKichHoatEvent : BaseEvent
{
    public DanhMucThuTuc DanhMucThuTuc { get; }

    public DanhMucThuTucDaKichHoatEvent(DanhMucThuTuc danhMucThuTuc)
    {
        DanhMucThuTuc = danhMucThuTuc;
    }
}

/// <summary>
/// Event khi danh mục thủ tục được tạm ngưng
/// </summary>
public class DanhMucThuTucDaTamNgungEvent : BaseEvent
{
    public DanhMucThuTuc DanhMucThuTuc { get; }

    public DanhMucThuTucDaTamNgungEvent(DanhMucThuTuc danhMucThuTuc)
    {
        DanhMucThuTuc = danhMucThuTuc;
    }
}

/// <summary>
/// Event khi danh mục thủ tục ngừng hoạt động
/// </summary>
public class DanhMucThuTucDaNgungHoatDongEvent : BaseEvent
{
    public DanhMucThuTuc DanhMucThuTuc { get; }

    public DanhMucThuTucDaNgungHoatDongEvent(DanhMucThuTuc danhMucThuTuc)
    {
        DanhMucThuTuc = danhMucThuTuc;
    }
}

/// <summary>
/// Event khi danh mục thủ tục được xóa
/// </summary>
public class DanhMucThuTucDaXoaEvent : BaseEvent
{
    public int DanhMucThuTucId { get; }
    public string Ma { get; }
    public string Ten { get; }

    public DanhMucThuTucDaXoaEvent(int danhMucThuTucId, string ma, string ten)
    {
        DanhMucThuTucId = danhMucThuTucId;
        Ma = ma;
        Ten = ten;
    }
}
