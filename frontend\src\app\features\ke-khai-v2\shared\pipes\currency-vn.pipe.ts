import { Pipe, PipeTransform } from '@angular/core';

/**
 * Pipe format tiền tệ Việt Nam
 */
@Pipe({
  name: 'currencyVN',
  standalone: true
})
export class CurrencyVNPipe implements PipeTransform {
  transform(value: number | null | undefined, showSymbol = true): string {
    if (value === null || value === undefined || isNaN(value)) {
      return showSymbol ? '0 ₫' : '0';
    }

    // Format number với dấu phẩy phân cách hàng nghìn
    const formatted = value.toLocaleString('vi-VN');
    
    return showSymbol ? `${formatted} ₫` : formatted;
  }
}
