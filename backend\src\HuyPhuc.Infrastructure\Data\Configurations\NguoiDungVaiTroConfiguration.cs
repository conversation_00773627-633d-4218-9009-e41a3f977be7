using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class NguoiDungVaiTroConfiguration : IEntityTypeConfiguration<NguoiDungVaiTro>
{
    public void Configure(EntityTypeBuilder<NguoiDungVaiTro> builder)
    {
        builder.ToTable("nguoi_dung_vai_tro");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).HasColumnName("id");

        builder.Property(x => x.NguoiDungId)
            .IsRequired()
            .HasColumnName("nguoi_dung_id");

        builder.Property(x => x.VaiTroId)
            .IsRequired()
            .HasColumnName("vai_tro_id");

        builder.Property(x => x.NgayGan)
            .IsRequired()
            .HasColumnName("ngay_gan");

        builder.Property(x => x.NgayHetHan)
            .HasColumnName("ngay_het_han");

        builder.Property(x => x.TrangThaiHoatDong)
            .IsRequired()
            .HasColumnName("trang_thai_hoat_dong")
            .HasDefaultValue(true);

        builder.Property(x => x.GhiChu)
            .HasColumnName("ghi_chu")
            .HasMaxLength(500);

        // Audit fields
        builder.Property(x => x.NgayTao)
            .IsRequired()
            .HasColumnName("ngay_tao");

        builder.Property(x => x.NgayCapNhat)
            .HasColumnName("ngay_cap_nhat");

        builder.Property(x => x.NguoiTao)
            .HasColumnName("nguoi_tao")
            .HasMaxLength(50);

        builder.Property(x => x.NguoiCapNhat)
            .HasColumnName("nguoi_cap_nhat")
            .HasMaxLength(50);

        // Indexes
        builder.HasIndex(x => new { x.NguoiDungId, x.VaiTroId })
            .HasDatabaseName("ix_nguoi_dung_vai_tro_composite");

        builder.HasIndex(x => x.NgayHetHan)
            .HasDatabaseName("ix_nguoi_dung_vai_tro_ngay_het_han");

        builder.HasIndex(x => x.TrangThaiHoatDong)
            .HasDatabaseName("ix_nguoi_dung_vai_tro_trang_thai_hoat_dong");

        // Relationships
        builder.HasOne(x => x.NguoiDung)
            .WithMany(x => x.DanhSachVaiTro)
            .HasForeignKey(x => x.NguoiDungId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(x => x.VaiTro)
            .WithMany(x => x.DanhSachNguoiDung)
            .HasForeignKey(x => x.VaiTroId)
            .OnDelete(DeleteBehavior.Restrict);

        // Ignore domain events
        builder.Ignore(x => x.DomainEvents);
    }
}
