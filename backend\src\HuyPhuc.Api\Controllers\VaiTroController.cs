using HuyPhuc.Api.Authorization;
using HuyPhuc.Application.Features.VaiTro.Commands;
using HuyPhuc.Application.Features.VaiTro.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// Controller quản lý vai trò
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class VaiTroController : ControllerBase
{
    private readonly IMediator _mediator;

    public VaiTroController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Lấy danh sách vai trò
    /// </summary>
    [HttpGet]
    [Permission("VAI_TRO_QUAN_LY")]
    public async Task<IActionResult> LayDanhSachVaiTro([FromQuery] LayDanhSachVaiTroQuery query)
    {
        var result = await _mediator.Send(query);
        
        if (result.IsSuccess)
        {
            return Ok(new
            {
                success = true,
                data = result.Data,
                message = "L<PERSON>y danh sách vai trò thành công"
            });
        }

        return BadRequest(new
        {
            success = false,
            message = result.Errors.FirstOrDefault() ?? "Có lỗi xảy ra"
        });
    }

    /// <summary>
    /// Tạo vai trò mới
    /// </summary>
    [HttpPost]
    [Permission("VAI_TRO_QUAN_LY")]
    public async Task<IActionResult> TaoVaiTro([FromBody] TaoVaiTroCommand command)
    {
        var result = await _mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return CreatedAtAction(
                nameof(LayDanhSachVaiTro),
                new { id = result.Data },
                new
                {
                    success = true,
                    data = new { id = result.Data },
                    message = "Tạo vai trò thành công"
                });
        }

        return BadRequest(new
        {
            success = false,
            message = result.Errors.FirstOrDefault() ?? "Có lỗi xảy ra"
        });
    }

    /// <summary>
    /// Gán quyền cho vai trò
    /// </summary>
    [HttpPost("{id}/quyen")]
    [Permission("VAI_TRO_QUAN_LY")]
    public async Task<IActionResult> GanQuyenChoVaiTro(int id, [FromBody] List<int> danhSachQuyenId)
    {
        var command = new GanQuyenChoVaiTroCommand
        {
            VaiTroId = id,
            DanhSachQuyenId = danhSachQuyenId
        };

        var result = await _mediator.Send(command);
        
        if (result.IsSuccess)
        {
            return Ok(new
            {
                success = true,
                message = "Gán quyền cho vai trò thành công"
            });
        }

        return BadRequest(new
        {
            success = false,
            message = result.Errors.FirstOrDefault() ?? "Có lỗi xảy ra"
        });
    }
}
