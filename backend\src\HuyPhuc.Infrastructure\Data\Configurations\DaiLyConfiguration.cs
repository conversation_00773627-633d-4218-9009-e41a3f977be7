using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class DaiLyConfiguration : IEntityTypeConfiguration<DaiLy>
{
    public void Configure(EntityTypeBuilder<DaiLy> builder)
    {
        builder.ToTable("dai_ly");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        builder.Property(x => x.MaDaiLy)
            .HasColumnName("ma_dai_ly")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.TenDaiLy)
            .HasColumnName("ten_dai_ly")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(x => x.DiaChi)
            .HasColumnName("dia_chi")
            .HasColumnType("text");

        builder.Property(x => x.SoDienThoai)
            .HasColumnName("so_dien_thoai")
            .HasMaxLength(20);

        builder.Property(x => x.Email)
            .HasColumnName("email")
            .HasMaxLength(255);

        builder.Property(x => x.MaSoThue)
            .HasColumnName("ma_so_thue")
            .HasMaxLength(50);

        builder.Property(x => x.NguoiDaiDien)
            .HasColumnName("nguoi_dai_dien")
            .HasMaxLength(255);

        builder.Property(x => x.TrangThaiHoatDong)
            .HasColumnName("trang_thai_hoat_dong")
            .HasDefaultValue(true);

        builder.Property(x => x.GhiChu)
            .HasColumnName("ghi_chu")
            .HasColumnType("text");

        // Audit fields
        builder.Property(x => x.NgayTao)
            .HasColumnName("created_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(x => x.NgayCapNhat)
            .HasColumnName("updated_at");

        builder.Property(x => x.NguoiTao)
            .HasColumnName("created_by")
            .HasMaxLength(255);

        builder.Property(x => x.NguoiCapNhat)
            .HasColumnName("updated_by")
            .HasMaxLength(255);

        // Indexes
        builder.HasIndex(x => x.MaDaiLy)
            .IsUnique()
            .HasDatabaseName("idx_dai_ly_ma_dai_ly");

        builder.HasIndex(x => x.TrangThaiHoatDong)
            .HasDatabaseName("idx_dai_ly_trang_thai");

        // Relationships
        builder.HasMany(x => x.DanhSachDonVi)
            .WithOne(x => x.DaiLy)
            .HasForeignKey(x => x.DaiLyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(x => x.DanhSachNguoiDung)
            .WithOne(x => x.DaiLy)
            .HasForeignKey(x => x.DaiLyId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}
