.chi-tiet-to-khai-602 {
  // Component styles có thể được thêm vào đây nếu cần
  
  // Tab navigation styles
  .tab-navigation {
    .tab-button {
      @apply transition-colors duration-200;
      
      &.active {
        @apply border-blue-500 text-blue-600;
      }
      
      &:not(.active) {
        @apply border-transparent text-gray-500;
        
        &:hover {
          @apply text-gray-700 border-gray-300;
        }
      }
    }
  }
  
  // Custom styles cho các trạng thái
  .trang-thai-badge {
    &.dang-soan {
      @apply bg-yellow-100 text-yellow-800;
    }
    
    &.da-gui {
      @apply bg-blue-100 text-blue-800;
    }
    
    &.da-duyet {
      @apply bg-green-100 text-green-800;
    }
    
    &.bi-tu-choi {
      @apply bg-red-100 text-red-800;
    }
  }
  
  // Loading spinner container
  .loading-container {
    @apply flex justify-center items-center py-12;
  }
  
  // Error state styles
  .error-container {
    .error-content {
      @apply bg-red-50 border border-red-200 rounded-md p-4;
      
      .error-icon {
        @apply h-5 w-5 text-red-400;
      }
      
      .error-title {
        @apply text-sm font-medium text-red-800;
      }
      
      .error-message {
        @apply mt-1 text-sm text-red-700;
      }
    }
  }
  
  // Header styles
  .page-header {
    @apply flex justify-between items-center mb-6 pb-4 border-b border-gray-200;
    
    .header-title {
      h1 {
        @apply text-2xl font-bold text-gray-900;
      }
      
      p {
        @apply text-gray-600 mt-1;

        span {
          @apply font-medium;
        }
      }
    }
    
    .header-actions {
      @apply flex space-x-3;
      
      button {
        @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
        
        &.btn-secondary {
          @apply bg-gray-600 hover:bg-gray-700 text-white;
        }
        
        &.btn-primary {
          @apply bg-blue-600 hover:bg-blue-700 text-white;
          
          &:disabled {
            @apply opacity-50 cursor-not-allowed;
          }
        }
      }
    }
  }
  
  // Card styles
  .info-card {
    @apply bg-white border border-gray-200 rounded-lg p-6 mb-6;
    
    .card-title {
      @apply text-lg font-semibold text-gray-900 mb-4;
    }
    
    .info-grid {
      @apply grid grid-cols-1 md:grid-cols-2 gap-4;
      
      .info-item {
        label {
          @apply block text-sm font-medium text-gray-700 mb-1;
        }
        
        p {
          @apply text-gray-900;
        }
        
        &.full-width {
          @apply md:col-span-2;
        }
      }
    }
  }
  
  // Empty state styles
  .empty-state {
    @apply text-center py-12;
    
    svg {
      @apply mx-auto h-12 w-12 text-gray-400;
    }
    
    h3 {
      @apply mt-2 text-sm font-medium text-gray-900;
    }
    
    p {
      @apply mt-1 text-sm text-gray-500;
    }
    
    .empty-actions {
      @apply mt-6;
      
      button {
        @apply bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200;
      }
    }
  }
  
  // Table actions
  .table-actions {
    @apply flex justify-between items-center mb-4;
    
    .table-info {
      @apply text-sm text-gray-600;

      span {
        @apply font-medium;
      }
    }
    
    .add-button {
      @apply bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200;
    }
  }
}
