using AutoMapper;
using HuyPhuc.Application.Common.Models;
using HuyPhuc.Domain.Repositories;
using MediatR;
using System.Linq.Expressions;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;

namespace HuyPhuc.Application.Features.QuanLyNguoiDung.Queries.LayDanhSachNguoiDung;

public class LayDanhSachNguoiDungQueryHandler : IRequestHandler<LayDanhSachNguoiDungQuery, PaginatedList<NguoiDungDto>>
{
    private readonly INguoiDungRepository _nguoiDungRepository;
    private readonly IMapper _mapper;

    public LayDanhSachNguoiDungQueryHandler(INguoiDungRepository nguoiDungRepository, IMapper mapper)
    {
        _nguoiDungRepository = nguoiDungRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedList<NguoiDungDto>> Handle(LayDanhSachNguoiDungQuery request, CancellationToken cancellationToken)
    {
        // Xây dựng điều kiện lọc
        Expression<Func<Domain.Entities.NguoiDung, bool>>? predicate = null;

        if (!string.IsNullOrWhiteSpace(request.TuKhoa))
        {
            var tuKhoa = request.TuKhoa.Trim().ToLower();
            predicate = x => x.HoTen.ToLower().Contains(tuKhoa) || 
                           x.Email.Value.ToLower().Contains(tuKhoa);
        }

        if (request.TrangThai.HasValue)
        {
            var trangThai = (TrangThaiNguoiDung)request.TrangThai.Value;
            if (predicate == null)
                predicate = x => x.TrangThai == trangThai;
            else
            {
                var existingPredicate = predicate;
                predicate = x => existingPredicate.Compile()(x) && x.TrangThai == trangThai;
            }
        }

        // Lấy dữ liệu phân trang
        var nguoiDungList = await _nguoiDungRepository.LayPhanTrangAsync(
            request.Trang,
            request.KichThuocTrang,
            predicate,
            x => x.NgayTao,
            false, // Sắp xếp giảm dần theo ngày tạo
            cancellationToken);

        // Đếm tổng số bản ghi
        var tongSoMuc = await _nguoiDungRepository.DemAsync(predicate, cancellationToken);

        // Map sang DTO
        var nguoiDungDtos = _mapper.Map<IReadOnlyCollection<NguoiDungDto>>(nguoiDungList);

        return PaginatedList<NguoiDungDto>.Tao(nguoiDungDtos, tongSoMuc, request.Trang, request.KichThuocTrang);
    }
}
