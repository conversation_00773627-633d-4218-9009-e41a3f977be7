import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';

import { KeKhaiBase, BaseFilter, PaginationInfo } from '../models';
import { HttpClientService } from './http-client.service';

/**
 * Interface cho response danh sách kê khai
 */
export interface DanhSachKeKhaiResponse<T extends KeKhaiBase> {
  danhSach: T[];
  phanTrang: PaginationInfo;
}

/**
 * Base service cho tất cả các loại kê khai
 */
@Injectable({
  providedIn: 'root'
})
export class KeKhaiBaseService {
  private readonly httpClient = inject(HttpClientService);

  /**
   * L<PERSON>y danh sách kê khai với filter và pagination
   */
  layDanhSachKeKhai<T extends KeKhaiBase>(
    endpoint: string, 
    filter?: BaseFilter
  ): Observable<DanhSachKeKhaiResponse<T>> {
    return this.httpClient.get<DanhSachKeKhaiResponse<T>>(endpoint, filter);
  }

  /**
   * <PERSON><PERSON><PERSON> chi tiết kê khai theo ID
   */
  layChiTietKeKhai<T extends KeKhaiBase>(endpoint: string, id: number): Observable<T> {
    return this.httpClient.get<T>(`${endpoint}/${id}`);
  }

  /**
   * Tạo kê khai mới
   */
  taoKeKhai<T extends KeKhaiBase>(endpoint: string, keKhai: Omit<T, 'id' | 'ngayTao' | 'ngayCapNhat'>): Observable<T> {
    return this.httpClient.post<T>(endpoint, keKhai);
  }

  /**
   * Cập nhật kê khai
   */
  capNhatKeKhai<T extends KeKhaiBase>(endpoint: string, id: number, keKhai: Partial<T>): Observable<T> {
    return this.httpClient.put<T>(`${endpoint}/${id}`, keKhai);
  }

  /**
   * Xóa kê khai
   */
  xoaKeKhai(endpoint: string, id: number): Observable<void> {
    return this.httpClient.delete<void>(`${endpoint}/${id}`);
  }

  /**
   * Gửi kê khai
   */
  guiKeKhai<T extends KeKhaiBase>(endpoint: string, id: number): Observable<T> {
    return this.httpClient.post<T>(`${endpoint}/${id}/gui`, {});
  }

  /**
   * Duyệt kê khai
   */
  duyetKeKhai<T extends KeKhaiBase>(endpoint: string, id: number, ghiChu?: string): Observable<T> {
    return this.httpClient.post<T>(`${endpoint}/${id}/duyet`, { ghiChu });
  }

  /**
   * Từ chối kê khai
   */
  tuChoiKeKhai<T extends KeKhaiBase>(endpoint: string, id: number, lyDo: string): Observable<T> {
    return this.httpClient.post<T>(`${endpoint}/${id}/tu-choi`, { lyDo });
  }
}
