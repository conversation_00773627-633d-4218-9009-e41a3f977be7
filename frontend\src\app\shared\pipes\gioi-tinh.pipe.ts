import { Pipe, PipeTransform } from '@angular/core';

/**
 * <PERSON><PERSON> để convert giới tính từ số hoặc string thành text hiển thị
 * Usage: {{ gioiTinh | gioiTinh }}
 * 
 * Input có thể là:
 * - 1 hoặc "1" -> "Nam"
 * - 2 hoặc "2" -> "Nữ"  
 * - "Nam" -> "Nam"
 * - "Nữ" -> "Nữ"
 * - null/undefined/empty -> ""
 */
@Pipe({
  name: 'gioiTinh',
  standalone: true
})
export class GioiTinhPipe implements PipeTransform {

  transform(value: any): string {
    if (value === null || value === undefined || value === '') {
      return '';
    }

    // Nếu đã là string và hợp lệ, trả về luôn
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase().trim();
      if (lowerValue === 'nam' || lowerValue === 'male') {
        return 'Nam';
      }
      if (lowerValue === 'nữ' || lowerValue === 'nu' || lowerValue === 'female') {
        return 'Nữ';
      }
      // Nếu là string số
      if (lowerValue === '1') {
        return 'Nam';
      }
      if (lowerValue === '2') {
        return 'Nữ';
      }
    }

    // Nếu là số
    if (typeof value === 'number') {
      if (value === 1) {
        return 'Nam';
      }
      if (value === 2 || value === 0) {
        // Cả 2 và 0 đều map thành Nữ
        return 'Nữ';
      }
    }

    // Trường hợp không xác định
    return '';
  }
}
