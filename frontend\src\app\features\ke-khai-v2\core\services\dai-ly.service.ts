import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { DaiLy, DaiLyOption } from '../models';
import { SimpleHttpClientService } from './simple-http-client.service';
import { MockDataService } from './mock-data.service';

/**
 * Service quản lý đại lý
 */
@Injectable({
  providedIn: 'root'
})
export class DaiLyService {
  private readonly httpClient = inject(SimpleHttpClientService);
  private readonly mockDataService = inject(MockDataService);

  // Flag để chuyển đổi giữa mock và real API
  private readonly useMockData = false;

  /**
   * L<PERSON>y danh sách tất cả đại lý của người dùng hiện tại
   */
  layDanhSachDaiLy(): Observable<DaiLy[]> {
    if (this.useMockData) {
      return this.mockDataService.layDanhSachDaiLy();
    }

    // Backend trả về DaiLySelectDto[], cần map sang DaiLy[]
    return this.httpClient.get<any[]>('dai-ly/cua-nguoi-dung').pipe(
      map(response => response.map(dto => ({
        id: dto.id,
        ma: dto.maDaiLy,
        ten: dto.tenDaiLy,
        diaChi: dto.diaChi,
        soDienThoai: dto.soDienThoai,
        email: dto.email,
        trangThai: dto.trangThaiHoatDong ? 1 : 0,
        // Backward compatibility
        maDaiLy: dto.maDaiLy,
        tenDaiLy: dto.tenDaiLy,
        trangThaiHoatDong: dto.trangThaiHoatDong
      } as DaiLy)))
    );
  }

  /**
   * Lấy danh sách đại lý cho dropdown
   */
  layDanhSachDaiLyOptions(): Observable<DaiLyOption[]> {
    return this.layDanhSachDaiLy().pipe(
      map(daiLyList => daiLyList
        .filter(dl => dl.trangThai === 1)
        .map(dl => ({
          id: dl.id,
          maDaiLy: dl.ma,
          tenDaiLy: dl.ten,
          tenHienThi: `${dl.ma} - ${dl.ten}`
        }))
      )
    );
  }

  /**
   * Lấy thông tin đại lý theo ID
   */
  layDaiLyTheoId(id: number): Observable<DaiLy> {
    return this.httpClient.get<DaiLy>(`dai-ly/${id}`);
  }

  /**
   * Tạo đại lý mới
   */
  taoDaiLy(daiLy: Omit<DaiLy, 'id' | 'ngayTao' | 'ngayCapNhat'>): Observable<DaiLy> {
    return this.httpClient.post<DaiLy>('dai-ly', daiLy);
  }

  /**
   * Cập nhật đại lý
   */
  capNhatDaiLy(id: number, daiLy: Partial<DaiLy>): Observable<DaiLy> {
    return this.httpClient.put<DaiLy>(`dai-ly/${id}`, daiLy);
  }

  /**
   * Xóa đại lý
   */
  xoaDaiLy(id: number): Observable<void> {
    return this.httpClient.delete<void>(`dai-ly/${id}`);
  }
}
