using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class DonHangConfiguration : IEntityTypeConfiguration<DonHang>
{
    public void Configure(EntityTypeBuilder<DonHang> builder)
    {
        builder.ToTable("DonHang");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.MaDonHang)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.NguoiDungId)
            .IsRequired();

        builder.Property(x => x.TongGiaTri)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(0);

        builder.Property(x => x.PhiVanChuyen)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(0);

        builder.Property(x => x.TongThanhToan)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(0);

        builder.Property(x => x.TrangThai)
            .IsRequired()
            .HasConversion<int>()
            .HasDefaultValue(TrangThaiDonHang.ChoDuyet);

        builder.Property(x => x.GhiChu)
            .HasMaxLength(500);

        builder.Property(x => x.NgayDatHang)
            .IsRequired();

        builder.Property(x => x.NgayGiaoHang);

        builder.Property(x => x.NgayTao)
            .IsRequired();

        builder.Property(x => x.NgayCapNhat);

        builder.Property(x => x.NguoiTao)
            .HasMaxLength(50);

        builder.Property(x => x.NguoiCapNhat)
            .HasMaxLength(50);

        // Configure relationships
        builder.HasOne(x => x.NguoiDung)
            .WithMany(x => x.DanhSachDonHang)
            .HasForeignKey(x => x.NguoiDungId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(x => x.DanhSachChiTiet)
            .WithOne(x => x.DonHang)
            .HasForeignKey(x => x.DonHangId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(x => x.MaDonHang)
            .IsUnique()
            .HasDatabaseName("IX_DonHang_MaDonHang");

        builder.HasIndex(x => x.NguoiDungId)
            .HasDatabaseName("IX_DonHang_NguoiDungId");

        builder.HasIndex(x => x.TrangThai)
            .HasDatabaseName("IX_DonHang_TrangThai");

        builder.HasIndex(x => x.NgayDatHang)
            .HasDatabaseName("IX_DonHang_NgayDatHang");

        // Ignore domain events
        builder.Ignore(x => x.DomainEvents);
    }
}
