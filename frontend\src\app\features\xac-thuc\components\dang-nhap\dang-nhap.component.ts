import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil, finalize } from 'rxjs/operators';
import { CommonModule } from '@angular/common';

import { XacThucService } from '../../services';
import { ThongTinDangNhap } from '../../models';
import { LogoComponent } from '../../../../shared/components/logo/logo.component';

/**
 * Component trang đăng nhập
 * Xử lý form đăng nhập với validation và gọi API
 */
@Component({
  selector: 'app-dang-nhap',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, LogoComponent],
  templateUrl: './dang-nhap.component.html',
  styleUrls: ['./dang-nhap.component.scss']
})
export class DangNhapComponent implements OnInit, OnDestroy {
  formDangNhap!: FormGroup;
  dangXuLy = false;
  thongBaoLoi = '';
  anMatKhau = true;
  danhSachTaiKhoanDemo: Array<{username: string, matKhau: string, vaiTro: string}> = [];

  private readonly destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private xacThucService: XacThucService,
    private router: Router
  ) {
    this.khoiTaoForm();
  }

  ngOnInit(): void {
    // Kiểm tra nếu đã đăng nhập thì chuyển hướng
    if (this.xacThucService.daDangNhap) {
      this.router.navigate(['/dashboard']);
    }

    // Lấy danh sách tài khoản demo
    this.danhSachTaiKhoanDemo = this.xacThucService.layDanhSachTaiKhoanDemo();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Khởi tạo reactive form với validation
   */
  private khoiTaoForm(): void {
    this.formDangNhap = this.fb.group({
      username: ['', [
        Validators.required,
        Validators.minLength(3),
        Validators.maxLength(50)
      ]],
      matKhau: ['', [
        Validators.required,
        Validators.minLength(6)
      ]],
      ghiNhoDangNhap: [false]
    });
  }

  /**
   * Xử lý submit form đăng nhập
   */
  onDangNhap(): void {
    if (this.formDangNhap.invalid) {
      this.danhDauTatCaTruongLaDoiTuong();
      return;
    }

    this.dangXuLy = true;
    this.thongBaoLoi = '';

    const thongTinDangNhap: ThongTinDangNhap = this.formDangNhap.value;

    this.xacThucService.dangNhap(thongTinDangNhap)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.dangXuLy = false)
      )
      .subscribe({
        next: (ketQua) => {
          if (ketQua.thanhCong) {
            // Đăng nhập thành công, đợi một chút để authentication state được cập nhật
            setTimeout(() => {
              this.router.navigate(['/dashboard']);
            }, 100);
          } else {
            this.thongBaoLoi = ketQua.thongBao;
          }
        },
        error: (error) => {
          console.error('Lỗi đăng nhập:', error);
          this.thongBaoLoi = 'Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.';
        }
      });
  }

  /**
   * Đánh dấu tất cả các trường là đã được tương tác
   */
  private danhDauTatCaTruongLaDoiTuong(): void {
    Object.keys(this.formDangNhap.controls).forEach(key => {
      const control = this.formDangNhap.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  /**
   * Kiểm tra trường có lỗi và đã được tương tác
   */
  kiemTraLoiTruong(tenTruong: string): boolean {
    const control = this.formDangNhap.get(tenTruong);
    return !!(control && control.invalid && (control.dirty || control.touched));
  }

  /**
   * Lấy thông báo lỗi cho trường
   */
  layThongBaoLoiTruong(tenTruong: string): string {
    const control = this.formDangNhap.get(tenTruong);
    
    if (!control || !control.errors) {
      return '';
    }

    if (control.errors['required']) {
      return this.layThongBaoLoiBatBuoc(tenTruong);
    }

    if (control.errors['maxlength']) {
      const maxLength = control.errors['maxlength'].requiredLength;
      return `${this.layTenTruongHienThi(tenTruong)} không được vượt quá ${maxLength} ký tự`;
    }

    if (control.errors['minlength']) {
      const minLength = control.errors['minlength'].requiredLength;
      return `${this.layTenTruongHienThi(tenTruong)} phải có ít nhất ${minLength} ký tự`;
    }

    return 'Trường này không hợp lệ';
  }

  /**
   * Lấy thông báo lỗi bắt buộc theo tên trường
   */
  private layThongBaoLoiBatBuoc(tenTruong: string): string {
    const tenHienThi = this.layTenTruongHienThi(tenTruong);
    return `${tenHienThi} là bắt buộc`;
  }

  /**
   * Lấy tên hiển thị của trường
   */
  private layTenTruongHienThi(tenTruong: string): string {
    const danhSachTen: { [key: string]: string } = {
      'username': 'Username',
      'matKhau': 'Mật khẩu'
    };
    return danhSachTen[tenTruong] || tenTruong;
  }

  /**
   * Toggle hiển thị/ẩn mật khẩu
   */
  toggleAnHienMatKhau(): void {
    this.anMatKhau = !this.anMatKhau;
  }

  /**
   * Xử lý quên mật khẩu
   */
  onQuenMatKhau(): void {
    // TODO: Implement quên mật khẩu
    console.log('Chức năng quên mật khẩu sẽ được triển khai');
  }

  /**
   * Chuyển đến trang đăng ký
   */
  onChuyenDenDangKy(): void {
    // TODO: Implement chuyển đến trang đăng ký
    console.log('Chức năng đăng ký sẽ được triển khai');
  }

  /**
   * Điền nhanh thông tin tài khoản demo
   */
  dienNhanhTaiKhoan(username: string, matKhau: string): void {
    this.formDangNhap.patchValue({
      username: username,
      matKhau: matKhau
    });
    this.thongBaoLoi = '';
  }
}
