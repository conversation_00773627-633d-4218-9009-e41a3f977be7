/**
 * Models cho đại lý
 */

/**
 * Interface cho thông tin đại lý
 */
export interface DaiLy {
  id: number;
  ma: string;
  ten: string;
  diaChi?: string;
  soDienThoai?: string;
  email?: string;
  trangThai: number;
  ngayTao?: Date;
  ngayCapNhat?: Date;

  // Backward compatibility
  maDaiLy?: string;
  tenDaiLy?: string;
  trangThaiHoatDong?: boolean;
}

/**
 * Interface cho đại lý trong dropdown/select
 */
export interface DaiLyOption {
  id: number;
  maDaiLy: string;
  tenDaiLy: string;
  tenHienThi: string; // Kết hợp mã + tên để hiển thị
}
