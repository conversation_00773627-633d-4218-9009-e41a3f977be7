using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Features.KeKhai.Commands.ThemLaoDong;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.KeKhai.Queries.LayDanhSachLaoDong;

/// <summary>
/// Handler lấy danh sách lao động trong kê khai
/// </summary>
public class LayDanhSachLaoDongKeKhaiQueryHandler : IRequestHandler<LayDanhSachLaoDongKeKhaiQuery, List<LaoDongKeKhaiDto>>
{
    private readonly IApplicationDbContext _context;

    public LayDanhSachLaoDongKeKhaiQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<LaoDongKeKhaiDto>> Handle(LayDanhSachLaoDongKeKhaiQuery request, CancellationToken cancellationToken)
    {
        // Lấy danh sách lao động từ bảng chi_tiet_lao_dong_ke_khai_v2
        // Sử dụng LEFT JOIN với tk1_ts để lấy thông tin chi tiết
        var query = from c in _context.ChiTietLaoDongKeKhaiV2
                    join t in _context.Tk1Ts on c.MaSoBhxh equals t.MaSoBHXH into tk1Group
                    from t in tk1Group.DefaultIfEmpty()
                    where c.KeKhaiId == request.KeKhaiId
                    orderby c.Stt
                    select new { ChiTiet = c, ThongTin = t };

        var danhSachLaoDong = await query.ToListAsync(cancellationToken);

        // Map sang DTO
        var result = danhSachLaoDong?.Select(x => new LaoDongKeKhaiDto
        {
            Id = x.ChiTiet.Id,
            KeKhaiId = x.ChiTiet.KeKhaiId,
            MaSoBHXH = x.ChiTiet.MaSoBhxh,
            Stt = x.ChiTiet.Stt,

            // Thông tin từ bảng tk1_ts (nếu có)
            HoTen = x.ThongTin?.HoTen ?? $"Lao động {x.ChiTiet.MaSoBhxh}",
            Ccns = x.ThongTin?.Ccns,
            Cmnd = x.ThongTin?.Cmnd ?? "000000000000",
            NgaySinh = x.ThongTin?.NgaySinh,
            GioiTinh = x.ThongTin?.GioiTinh,
            QuocTich = x.ThongTin?.QuocTich,
            DanToc = x.ThongTin?.DanToc,
            MaTinhKs = x.ThongTin?.MaTinhKs,
            MaHuyenKs = x.ThongTin?.MaHuyenKs,
            MaXaKs = x.ThongTin?.MaXaKs,
            DienThoaiLh = x.ThongTin?.DienThoaiLh,
            MaHoGiaDinh = x.ThongTin?.MaHoGiaDinh,
            
            // Thông tin đặc thù cho 602
            PhuongAn = x.ChiTiet.PhuongAn,
            PhuongThuc = x.ChiTiet.PhuongThuc,
            ThangBatDau = x.ChiTiet.ThangBatDau,
            MucThuNhap = x.ChiTiet.MucThuNhap,
            TienLai = x.ChiTiet.TienLai,
            TienThua = x.ChiTiet.TienThua,
            TienTuDong = x.ChiTiet.TienTuDong,
            TongTien = x.ChiTiet.TongTien,
            TienHoTro = x.ChiTiet.TienHoTro,
            TyLe = x.ChiTiet.TyLe,
            SoThang = x.ChiTiet.SoThang,

            // Thông tin đặc thù cho 603
            NgayBienLai = x.ChiTiet.NgayBienLai,
            SoBienLai = x.ChiTiet.SoBienLai,
            GhiChuBienLai = x.ChiTiet.GhiChuBienLai,

            // Thông tin bổ sung
            LoaiNsnn = x.ChiTiet.LoaiNsnn,
            TyLeNsnnHoTro = x.ChiTiet.TyLeNsnnHoTro,
            HeSoDong = x.ChiTiet.HeSoDong
        }).ToList() ?? new List<LaoDongKeKhaiDto>();

        return result;
    }
}
