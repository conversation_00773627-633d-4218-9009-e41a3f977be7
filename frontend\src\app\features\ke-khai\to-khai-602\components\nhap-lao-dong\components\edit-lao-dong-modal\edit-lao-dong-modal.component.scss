/* Modal styles */
.modal-overlay {
  backdrop-filter: blur(2px);
}

.form-section {
  @apply bg-white border border-gray-100 rounded-lg p-4;
}

.form-section h4 {
  @apply text-gray-800 font-medium mb-3 pb-2 border-b border-gray-200;
}

.form-group {
  @apply mb-4;
}

.form-group label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-group input,
.form-group select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  @apply transition-colors duration-200;
}

.form-group input:disabled,
.form-group select:disabled {
  @apply bg-gray-50 text-gray-500 cursor-not-allowed;
}

.form-group input.error,
.form-group select.error {
  @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

.error-message {
  @apply mt-1 text-sm text-red-600;
}

/* Animation for modal */
.modal-enter {
  opacity: 0;
  transform: scale(0.95);
}

.modal-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.modal-leave {
  opacity: 1;
  transform: scale(1);
}

.modal-leave-active {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 300ms, transform 300ms;
}

/* Custom scrollbar */
.max-h-96::-webkit-scrollbar {
  width: 6px;
}

.max-h-96::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .form-section {
    @apply p-3;
  }
  
  .grid {
    @apply grid-cols-1;
  }
}

/* Loading spinner */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Button hover effects */
button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* Focus styles */
button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Disabled state */
button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Success button */
.btn-success {
  @apply bg-green-600 text-white;
}

.btn-success:hover:not(:disabled) {
  @apply bg-green-700;
}

/* Danger button */
.btn-danger {
  @apply bg-red-600 text-white;
}

.btn-danger:hover:not(:disabled) {
  @apply bg-red-700;
}

/* Secondary button */
.btn-secondary {
  @apply bg-gray-600 text-white;
}

.btn-secondary:hover:not(:disabled) {
  @apply bg-gray-700;
}
