import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Custom validators cho lao động
 */
export class LaoDongValidators {
  
  /**
   * Validator cho họ tên lao động
   */
  static hoTen(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const value = control.value.toString().trim();
      
      if (value.length < 2) {
        return { hoTen: { value: control.value, message: 'Họ tên phải có ít nhất 2 ký tự' } };
      }

      if (value.length > 100) {
        return { hoTen: { value: control.value, message: 'Họ tên không được quá 100 ký tự' } };
      }

      // Check for valid Vietnamese name characters
      const nameRegex = /^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵýỷỹ\s]+$/;
      
      if (!nameRegex.test(value)) {
        return { hoTen: { value: control.value, message: 'Họ tên chỉ được chứa chữ cái và khoảng trắng' } };
      }

      return null;
    };
  }

  /**
   * Validator cho mức thu nhập
   */
  static mucThuNhap(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value && control.value !== 0) {
        return null;
      }

      const value = Number(control.value);
      
      if (isNaN(value)) {
        return { mucThuNhap: { value: control.value, message: 'Mức thu nhập phải là số' } };
      }

      if (value < 0) {
        return { mucThuNhap: { value: control.value, message: 'Mức thu nhập phải lớn hơn hoặc bằng 0' } };
      }

      // Kiểm tra mức thu nhập tối đa (có thể điều chỉnh theo quy định)
      const maxIncome = 1000000000; // 1 tỷ VNĐ
      if (value > maxIncome) {
        return { mucThuNhap: { value: control.value, message: 'Mức thu nhập quá lớn' } };
      }

      return null;
    };
  }

  /**
   * Validator cho quốc tịch
   */
  static quocTich(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const value = control.value.toString().trim();
      
      if (value.length < 2) {
        return { quocTich: { value: control.value, message: 'Quốc tịch phải có ít nhất 2 ký tự' } };
      }

      return null;
    };
  }

  /**
   * Validator cho dân tộc
   */
  static danToc(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const value = control.value.toString().trim();
      
      if (value.length < 2) {
        return { danToc: { value: control.value, message: 'Dân tộc phải có ít nhất 2 ký tự' } };
      }

      return null;
    };
  }
}
