import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of } from 'rxjs';

import { KhaiBaoToKhai602Component } from './khai-bao-to-khai-602.component';
import { DaiLyService, ToKhai602Service } from '../../services';

describe('KhaiBaoToKhai602Component', () => {
  let component: KhaiBaoToKhai602Component;
  let fixture: ComponentFixture<KhaiBaoToKhai602Component>;
  let mockDaiLyService: jasmine.SpyObj<DaiLyService>;
  let mockToKhai602Service: jasmine.SpyObj<ToKhai602Service>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    // Create spy objects
    mockDaiLyService = jasmine.createSpyObj('DaiLyService', [
      'layDanhSachDaiLyCuaNguoiDung',
      'chonDaiLy',
      'chonDonVi',
      'resetLuaChon',
      'chuyenDoiDaiLyThanhOptions',
      'chuyenDoiDonViThanhOptions'
    ], {
      danhSachDaiLy$: of([]),
      danhSachDonVi$: of([]),
      daiLyDangChon$: of(null),
      donViDangChon$: of(null),
      dangTai$: of(false)
    });

    mockToKhai602Service = jasmine.createSpyObj('ToKhai602Service', [
      'capNhatThongTinKhac',
      'capNhatFormState',
      'resetForm'
    ]);

    mockRouter = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [
        ReactiveFormsModule,
        KhaiBaoToKhai602Component
      ],
      providers: [
        { provide: DaiLyService, useValue: mockDaiLyService },
        { provide: ToKhai602Service, useValue: mockToKhai602Service },
        { provide: Router, useValue: mockRouter }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(KhaiBaoToKhai602Component);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    fixture.detectChanges();
    
    expect(component.khaiBaoForm).toBeDefined();
    expect(component.khaiBaoForm.get('soSoBHXH')?.value).toBe('1');
    expect(component.khaiBaoForm.get('daiLyId')?.value).toBeNull();
    expect(component.khaiBaoForm.get('donViId')?.value).toBeNull();
  });

  it('should call layDanhSachDaiLyCuaNguoiDung on init', () => {
    mockDaiLyService.layDanhSachDaiLyCuaNguoiDung.and.returnValue(of([]));
    
    fixture.detectChanges();
    
    expect(mockDaiLyService.layDanhSachDaiLyCuaNguoiDung).toHaveBeenCalled();
  });

  it('should validate required fields', () => {
    fixture.detectChanges();
    
    // Form should be invalid initially
    expect(component.khaiBaoForm.valid).toBeFalsy();
    
    // Set required values
    component.khaiBaoForm.patchValue({
      daiLyId: 1,
      donViId: 1,
      soSoBHXH: '1'
    });
    
    expect(component.khaiBaoForm.valid).toBeTruthy();
  });

  it('should show error messages for invalid fields', () => {
    fixture.detectChanges();
    
    const daiLyControl = component.khaiBaoForm.get('daiLyId');
    daiLyControl?.markAsTouched();
    
    expect(component.hasError('daiLyId')).toBeTruthy();
    expect(component.getErrorMessage('daiLyId')).toBe('Vui lòng chọn đại lý');
  });

  it('should reset form when onHuyBo is called', () => {
    fixture.detectChanges();
    
    // Set some values
    component.khaiBaoForm.patchValue({
      daiLyId: 1,
      soSoBHXH: 'test'
    });
    
    component.onHuyBo();
    
    expect(mockDaiLyService.resetLuaChon).toHaveBeenCalled();
    expect(mockToKhai602Service.resetForm).toHaveBeenCalled();
  });

  it('should navigate to nhap-lao-dong when form is valid and onTaoKeKhai is called', () => {
    fixture.detectChanges();
    
    // Mock valid form state
    component.khaiBaoForm.patchValue({
      daiLyId: 1,
      donViId: 1,
      soSoBHXH: '1'
    });
    
    // Mock selected dai ly and don vi
    component.daiLyDangChon = { id: 1, maDaiLy: 'DL001', tenDaiLy: 'Test', trangThaiHoatDong: true, nguoiDungId: 1 };
    component.donViDangChon = { id: 1, maDonVi: 'DV001', tenDonVi: 'Test', daiLyId: 1, trangThaiHoatDong: true };
    
    component.onTaoKeKhai();
    
    expect(mockToKhai602Service.capNhatThongTinKhac).toHaveBeenCalled();
    expect(mockToKhai602Service.capNhatFormState).toHaveBeenCalled();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/ke-khai/to-khai-602/nhap-lao-dong']);
  });

  it('should not navigate when form is invalid', () => {
    fixture.detectChanges();
    
    // Form is invalid by default
    component.onTaoKeKhai();
    
    expect(mockRouter.navigate).not.toHaveBeenCalled();
  });
});
