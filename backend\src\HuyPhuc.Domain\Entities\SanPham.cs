using HuyPhuc.Domain.Entities.Base;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Exceptions;

namespace HuyPhuc.Domain.Entities;

public class SanPham : BaseEntity, IAuditableEntity
{
    public string TenSanPham { get; private set; } = string.Empty;
    public string MoTa { get; private set; } = string.Empty;
    public decimal GiaBan { get; private set; }
    public decimal? GiaGoc { get; private set; }
    public int SoLuongTon { get; private set; }
    public LoaiSanPham LoaiSanPham { get; private set; }
    public string? HinhAnh { get; private set; }
    public bool DangKinhDoanh { get; private set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }

    // Navigation properties
    public virtual ICollection<ChiTietDonHang> DanhSachChiTietDonHang { get; private set; } = new List<ChiTietDonHang>();

    private SanPham() { } // EF Core constructor

    public static SanPham Tao(
        string tenSanPham, 
        string moTa, 
        decimal giaBan, 
        int soLuongTon, 
        LoaiSanPham loaiSanPham,
        decimal? giaGoc = null,
        string? hinhAnh = null)
    {
        if (string.IsNullOrWhiteSpace(tenSanPham))
            throw new DomainException("Tên sản phẩm không được để trống");

        if (giaBan <= 0)
            throw new DomainException("Giá bán phải lớn hơn 0");

        if (soLuongTon < 0)
            throw new DomainException("Số lượng tồn không được âm");

        if (giaGoc.HasValue && giaGoc <= 0)
            throw new DomainException("Giá gốc phải lớn hơn 0");

        return new SanPham
        {
            TenSanPham = tenSanPham.Trim(),
            MoTa = moTa?.Trim() ?? string.Empty,
            GiaBan = giaBan,
            GiaGoc = giaGoc,
            SoLuongTon = soLuongTon,
            LoaiSanPham = loaiSanPham,
            HinhAnh = hinhAnh?.Trim(),
            DangKinhDoanh = true
        };
    }

    public void CapNhatThongTin(string tenSanPham, string moTa, decimal giaBan, LoaiSanPham loaiSanPham, decimal? giaGoc = null)
    {
        if (string.IsNullOrWhiteSpace(tenSanPham))
            throw new DomainException("Tên sản phẩm không được để trống");

        if (giaBan <= 0)
            throw new DomainException("Giá bán phải lớn hơn 0");

        if (giaGoc.HasValue && giaGoc <= 0)
            throw new DomainException("Giá gốc phải lớn hơn 0");

        TenSanPham = tenSanPham.Trim();
        MoTa = moTa?.Trim() ?? string.Empty;
        GiaBan = giaBan;
        GiaGoc = giaGoc;
        LoaiSanPham = loaiSanPham;
    }

    public void CapNhatSoLuongTon(int soLuongMoi)
    {
        if (soLuongMoi < 0)
            throw new DomainException("Số lượng tồn không được âm");

        SoLuongTon = soLuongMoi;
    }

    public void TangSoLuongTon(int soLuong)
    {
        if (soLuong <= 0)
            throw new DomainException("Số lượng tăng phải lớn hơn 0");

        SoLuongTon += soLuong;
    }

    public void GiamSoLuongTon(int soLuong)
    {
        if (soLuong <= 0)
            throw new DomainException("Số lượng giảm phải lớn hơn 0");

        if (SoLuongTon < soLuong)
            throw new BusinessRuleException("KHONG_DU_SO_LUONG_TON", "Không đủ số lượng tồn kho");

        SoLuongTon -= soLuong;
    }

    public void NgungKinhDoanh()
    {
        DangKinhDoanh = false;
    }

    public void TiepTucKinhDoanh()
    {
        DangKinhDoanh = true;
    }

    public void CapNhatHinhAnh(string hinhAnh)
    {
        HinhAnh = hinhAnh?.Trim();
    }

    public bool CoTheBan(int soLuong)
    {
        return DangKinhDoanh && SoLuongTon >= soLuong;
    }

    public decimal TinhTienGiam()
    {
        if (!GiaGoc.HasValue || GiaGoc <= GiaBan)
            return 0;

        return GiaGoc.Value - GiaBan;
    }

    public decimal TinhPhanTramGiam()
    {
        if (!GiaGoc.HasValue || GiaGoc <= GiaBan)
            return 0;

        return Math.Round((GiaGoc.Value - GiaBan) / GiaGoc.Value * 100, 2);
    }
}
