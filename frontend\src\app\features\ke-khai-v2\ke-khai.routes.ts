import { Routes } from '@angular/router';

/**
 * Standalone routing cho feature kê khai v2
 */
export const KE_KHAI_ROUTES: Routes = [
  {
    path: '',
    redirectTo: 'to-khai-602',
    pathMatch: 'full'
  },
  {
    path: 'to-khai-602',
    loadChildren: () => import('./features/to-khai-602/to-khai-602.routes')
      .then(m => m.TO_KHAI_602_ROUTES),
    data: {
      breadcrumb: 'Tờ khai 602',
      description: 'Kê khai đóng BHXH cho lao động tự do'
    }
  },
  {
    path: 'to-khai-603',
    loadComponent: () => import('./shared/components/coming-soon/coming-soon.component')
      .then(m => m.ComingSoonComponent),
    data: {
      breadcrumb: 'Tờ khai 603',
      description: 'Kê khai điều chỉnh BHXH (Sắp ra mắt)'
    }
  }
];
