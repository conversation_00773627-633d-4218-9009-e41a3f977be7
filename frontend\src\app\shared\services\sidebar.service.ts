import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, fromEvent } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';

import { MenuItemModel, SidebarConfig, SidebarState } from '../models/menu-item.model';

/**
 * Service quản lý trạng thái và logic của sidebar
 */
@Injectable({
  providedIn: 'root'
})
export class SidebarService {
  private readonly STORAGE_KEY = 'sidebar_state';
  private readonly MOBILE_BREAKPOINT = 768;

  // Cấu hình mặc định
  private readonly defaultConfig: SidebarConfig = {
    isCollapsed: false,
    isMobileOpen: false,
    width: {
      expanded: 280,
      collapsed: 64
    },
    breakpoint: this.MOBILE_BREAKPOINT
  };

  // State subjects
  private readonly _state$ = new BehaviorSubject<SidebarState>({
    isCollapsed: this.loadStateFromStorage().isCollapsed || false,
    isMobileOpen: false,
    isMobile: window.innerWidth <= this.MOBILE_BREAKPOINT,
    expandedMenuIds: []
  });

  private readonly _menuItems$ = new BehaviorSubject<MenuItemModel[]>([]);

  // Public observables
  public readonly state$ = this._state$.asObservable();
  public readonly menuItems$ = this._menuItems$.asObservable();
  public readonly isMobile$ = fromEvent(window, 'resize').pipe(
    startWith(null),
    map(() => window.innerWidth <= this.MOBILE_BREAKPOINT)
  );

  constructor(private router: Router) {
    this.initializeMenuItems();
    this.setupRouterListener();
    this.setupResizeListener();
  }

  /**
   * Khởi tạo menu items
   */
  private initializeMenuItems(): void {
    const menuItems: MenuItemModel[] = [
      {
        id: 'dashboard',
        nhan: 'Dashboard',
        duongDan: '/dashboard',
        icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v10z',
        moTa: 'Tổng quan hệ thống'
      },
      {
        id: 'ho-so-bhyt',
        nhan: 'Hồ sơ BHYT',
        duongDan: '/ho-so-bhyt',
        icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
        moTa: 'Quản lý hồ sơ BHYT'
      },
      {
        id: 'ho-so-bhxh',
        nhan: 'Hồ sơ BHXH',
        duongDan: '/ho-so-bhxh',
        icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z',
        moTa: 'Quản lý hồ sơ BHXH'
      },
      {
        id: 'danh-muc-thu-tuc',
        nhan: 'Danh mục thủ tục',
        duongDan: '/danh-muc-thu-tuc',
        icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
        moTa: 'Quản lý danh mục thủ tục hành chính'
      },
      {
        id: 'ke-khai',
        nhan: 'Kê khai',
        duongDan: '/ke-khai',
        icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
        moTa: 'Kê khai các loại tờ khai BHXH',
        children: [
          {
            id: 'ke-khai-to-khai-602',
            nhan: 'Tờ khai 602',
            duongDan: '/ke-khai/to-khai-602',
            icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
            moTa: 'Kê khai tờ khai 602 BHXH'
          }
          // TODO: Thêm các tờ khai khác
          // {
          //   id: 'ke-khai-to-khai-603',
          //   nhan: 'Tờ khai 603',
          //   duongDan: '/ke-khai/to-khai-603',
          //   icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
          //   moTa: 'Kê khai tờ khai 603 BHXH'
          // }
        ]
      },
      {
        id: 'bao-cao',
        nhan: 'Báo cáo',
        icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
        moTa: 'Báo cáo và thống kê',
        children: [
          {
            id: 'bao-cao-bhyt',
            nhan: 'Báo cáo BHYT',
            duongDan: '/bao-cao/bhyt',
            icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2z'
          },
          {
            id: 'bao-cao-bhxh',
            nhan: 'Báo cáo BHXH',
            duongDan: '/bao-cao/bhxh',
            icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2z'
          }
        ]
      },
      {
        id: 'cai-dat',
        nhan: 'Cài đặt',
        duongDan: '/cai-dat',
        icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z',
        moTa: 'Cài đặt hệ thống'
      }
    ];

    this._menuItems$.next(menuItems);
  }

  /**
   * Toggle sidebar collapsed state
   */
  toggleCollapsed(): void {
    const currentState = this._state$.value;
    const newState = {
      ...currentState,
      isCollapsed: !currentState.isCollapsed
    };
    this._state$.next(newState);
    this.saveStateToStorage(newState);
  }

  /**
   * Toggle mobile sidebar
   */
  toggleMobile(): void {
    const currentState = this._state$.value;
    this._state$.next({
      ...currentState,
      isMobileOpen: !currentState.isMobileOpen
    });
  }

  /**
   * Đóng mobile sidebar
   */
  closeMobile(): void {
    const currentState = this._state$.value;
    this._state$.next({
      ...currentState,
      isMobileOpen: false
    });
  }

  /**
   * Toggle expanded state của menu item
   */
  toggleMenuExpanded(menuId: string): void {
    const currentState = this._state$.value;
    const expandedIds = [...currentState.expandedMenuIds];
    const index = expandedIds.indexOf(menuId);
    
    if (index > -1) {
      expandedIds.splice(index, 1);
    } else {
      expandedIds.push(menuId);
    }

    this._state$.next({
      ...currentState,
      expandedMenuIds: expandedIds
    });
  }

  /**
   * Lưu state vào localStorage
   */
  private saveStateToStorage(state: SidebarState): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify({
        isCollapsed: state.isCollapsed
      }));
    } catch (error) {
      console.warn('Không thể lưu sidebar state:', error);
    }
  }

  /**
   * Load state từ localStorage
   */
  private loadStateFromStorage(): Partial<SidebarState> {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY);
      return saved ? JSON.parse(saved) : {};
    } catch (error) {
      console.warn('Không thể load sidebar state:', error);
      return {};
    }
  }

  /**
   * Setup router listener để update active menu
   */
  private setupRouterListener(): void {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.updateActiveMenu(event.url);
    });
  }

  /**
   * Setup resize listener
   */
  private setupResizeListener(): void {
    this.isMobile$.subscribe(isMobile => {
      const currentState = this._state$.value;
      this._state$.next({
        ...currentState,
        isMobile,
        isMobileOpen: isMobile ? false : currentState.isMobileOpen
      });
    });
  }

  /**
   * Update active menu dựa trên URL
   */
  private updateActiveMenu(url: string): void {
    const currentState = this._state$.value;
    const menuItems = this._menuItems$.value;
    
    const findActiveMenu = (items: MenuItemModel[], path: string): string | null => {
      for (const item of items) {
        if (item.duongDan && path.startsWith(item.duongDan)) {
          return item.id;
        }
        if (item.children) {
          const childActive = findActiveMenu(item.children, path);
          if (childActive) {
            // Expand parent menu nếu child active
            if (!currentState.expandedMenuIds.includes(item.id)) {
              this.toggleMenuExpanded(item.id);
            }
            return childActive;
          }
        }
      }
      return null;
    };

    const activeMenuId = findActiveMenu(menuItems, url);
    this._state$.next({
      ...currentState,
      activeMenuId: activeMenuId || undefined
    });
  }

  /**
   * Get current state
   */
  getCurrentState(): SidebarState {
    return this._state$.value;
  }

  /**
   * Get config
   */
  getConfig(): SidebarConfig {
    return this.defaultConfig;
  }
}
