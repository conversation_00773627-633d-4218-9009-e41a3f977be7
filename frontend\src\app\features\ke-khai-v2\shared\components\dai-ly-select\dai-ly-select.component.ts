import { Component, Input, Output, EventEmitter, OnInit, inject, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';

import { DaiLyOption } from '../../../core/models';
import { DaiLyStore } from '../../../core/stores';

/**
 * Component select đại lý với ControlValueAccessor
 */
@Component({
  selector: 'app-dai-ly-select',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DaiLySelectComponent),
      multi: true
    }
  ],
  template: `
    <div class="dai-ly-select">
      <label *ngIf="label" [for]="inputId" class="block text-sm font-medium text-gray-700 mb-1">
        {{ label }}
        <span *ngIf="required" class="text-red-500">*</span>
      </label>
      
      <select
        [id]="inputId"
        [value]="value || ''"
        (change)="onSelectionChange($event)"
        (blur)="onTouched()"
        [disabled]="disabled || daiLyStore.dangTai()"
        [class]="selectClasses"
      >
        <option value="">{{ placeholder }}</option>
        <option 
          *ngFor="let option of daiLyStore.daiLyOptions()" 
          [value]="option.id"
        >
          {{ option.tenHienThi }}
        </option>
      </select>
      
      <!-- Loading state -->
      <div *ngIf="daiLyStore.dangTai()" class="absolute right-3 top-1/2 transform -translate-y-1/2">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
      </div>
      
      <!-- Error message -->
      <p *ngIf="daiLyStore.loi()" class="mt-1 text-sm text-red-600">
        {{ daiLyStore.loi() }}
      </p>
      
      <!-- Validation error -->
      <p *ngIf="errorMessage" class="mt-1 text-sm text-red-600">
        {{ errorMessage }}
      </p>
    </div>
  `,
  styles: [`
    .dai-ly-select {
      position: relative;
    }
    
    select {
      @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
    }
    
    select:disabled {
      @apply bg-gray-100 cursor-not-allowed;
    }
    
    select.error {
      @apply border-red-300 focus:ring-red-500 focus:border-red-500;
    }
  `]
})
export class DaiLySelectComponent implements OnInit, ControlValueAccessor {
  @Input() label = '';
  @Input() placeholder = 'Chọn đại lý';
  @Input() required = false;
  @Input() disabled = false;
  @Input() errorMessage = '';
  @Input() inputId = `dai-ly-select-${Math.random().toString(36).substr(2, 9)}`;
  
  @Output() daiLyChange = new EventEmitter<number | null>();

  // Inject store
  readonly daiLyStore = inject(DaiLyStore);

  // ControlValueAccessor
  value: number | null = null;
  onChange = (value: number | null) => {};
  onTouched = () => {};

  ngOnInit() {
    // Load danh sách đại lý khi component khởi tạo
    this.daiLyStore.taiDaiLyOptions();
  }

  get selectClasses(): string {
    const baseClasses = 'w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500';
    const errorClasses = this.errorMessage ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300';
    const disabledClasses = this.disabled || this.daiLyStore.dangTai() ? 'bg-gray-100 cursor-not-allowed' : '';
    
    return `${baseClasses} ${errorClasses} ${disabledClasses}`.trim();
  }

  onSelectionChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const selectedValue = target.value ? Number(target.value) : null;
    
    this.value = selectedValue;
    this.onChange(selectedValue);
    this.daiLyChange.emit(selectedValue);
  }

  // ControlValueAccessor implementation
  writeValue(value: number | null): void {
    this.value = value;
  }

  registerOnChange(fn: (value: number | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
