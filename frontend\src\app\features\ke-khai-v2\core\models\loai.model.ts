/**
 * Model cho thông tin loại
 */
export interface Loai {
  id: number;
  tenLoai: string;
}

/**
 * Response data từ API
 */
export interface LoaiResponseData {
  totalCount: number;
  items: Loai[];
}

/**
 * Response từ API khi lấy danh sách loại
 */
export interface LoaiResponse {
  data: LoaiResponseData;
  success: boolean;
  message?: string;
  errors?: any;
  status: number;
  traceId?: string;
}

/**
 * Option cho dropdown loại
 */
export interface LoaiOption {
  value: number;
  text: string;
}

/**
 * Option cho dropdown loại NSNN
 */
export interface LoaiNsnnOption {
  value: string;
  text: string;
}
