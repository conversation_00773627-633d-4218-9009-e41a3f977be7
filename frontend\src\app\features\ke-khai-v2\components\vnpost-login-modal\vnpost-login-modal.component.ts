import { Component, EventEmitter, Input, OnInit, OnChanges, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { VnPostAuthService, VnPostCaptchaResponse, RefreshTokenRequest } from '../../core/services/vnpost-auth.service';

@Component({
  selector: 'app-vnpost-login-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-gray-900 bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 p-4" *ngIf="isVisible">
      <!-- Modal Container -->
      <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md mx-auto transform transition-all duration-300 ease-out">
        <!-- Header -->
        <div class="relative px-6 py-5 border-b border-gray-100">
          <div class="flex items-center justify-center">
            <!-- VNPost Logo/Icon -->
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Xác thực</h3>
                <p class="text-sm text-gray-500">Vui lòng nhập mã xác thực</p>
              </div>
            </div>
          </div>
          <!-- Close Button -->
          <button
            (click)="closeModal()"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-1 transition-all duration-200"
            type="button">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="px-6 py-6">

          <form id="vnpostLoginForm" (ngSubmit)="onSubmit()" #loginForm="ngForm">
            <!-- Thông tin tài khoản mặc định (ẩn) -->
            <div class="hidden">
              <input
                type="text"
                name="username"
                [(ngModel)]="loginData.userName"
                required>
              <input
                type="password"
                name="password"
                [(ngModel)]="loginData.password"
                required>
            </div>

            <!-- Captcha Section -->
            <div class="space-y-5">
              <!-- Captcha Image Container -->
              <div class="flex flex-col items-center space-y-4">
                <div class="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200">
                  <img
                    *ngIf="captchaData?.image"
                    [src]="'data:image/png;base64,' + (captchaData?.image || '')"
                    alt="Captcha"
                    class="rounded-lg shadow-sm max-w-full h-auto bg-white"
                    style="max-height: 80px; min-width: 160px;">
                  <div *ngIf="!captchaData?.image"
                       class="w-40 h-20 bg-white border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                    <div class="text-center">
                      <svg class="w-6 h-6 text-gray-400 mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                      </svg>
                      <span class="text-gray-500 text-sm">Đang tải...</span>
                    </div>
                  </div>
                </div>

                <!-- Refresh Button -->
                <button
                  type="button"
                  (click)="refreshCaptcha()"
                  [disabled]="isLoadingCaptcha"
                  class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  {{ isLoadingCaptcha ? 'Đang tải...' : 'Làm mới captcha' }}
                </button>
              </div>

              <!-- Input Section -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 text-center">
                  Nhập mã xác thực từ hình ảnh
                </label>
                <div class="relative">
                  <input
                    type="text"
                    name="captchaText"
                    [(ngModel)]="loginData.captchaText"
                    (input)="onCaptchaInput($event)"
                    required
                    class="w-full px-4 py-3 text-center text-xl font-bold uppercase tracking-[0.3em] border-2 border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                    placeholder="_ _ _ _ _ _"
                    maxlength="6"
                    autocomplete="off"
                    style="text-transform: uppercase; letter-spacing: 0.3em;">
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                    </svg>
                  </div>
                </div>
                <p class="text-xs text-gray-500 text-center">Nhập chính xác 6 ký tự từ hình ảnh</p>
              </div>
            </div>

            <!-- Messages -->
            <div class="mt-6 space-y-3">
              <!-- Error Message -->
              <div *ngIf="errorMessage" class="p-4 bg-red-50 border-l-4 border-red-400 rounded-lg">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-red-800">Lỗi xác thực</p>
                    <p class="text-sm text-red-700 mt-1">{{ errorMessage }}</p>
                  </div>
                </div>
              </div>

              <!-- Success Message -->
              <div *ngIf="successMessage" class="p-4 bg-green-50 border-l-4 border-green-400 rounded-lg">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">Thành công</p>
                    <p class="text-sm text-green-700 mt-1">{{ successMessage }}</p>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Footer -->
        <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200 rounded-b-2xl">
          <div class="flex justify-between items-center space-x-4">
            <button
              type="button"
              (click)="closeModal()"
              class="flex-1 px-4 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 shadow-sm">
              <span class="flex items-center justify-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Hủy bỏ
              </span>
            </button>
            <button
              type="submit"
              form="vnpostLoginForm"
              [disabled]="isLoading || !loginData.captchaText || loginData.captchaText.length < 4"
              class="flex-1 px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-xl hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl">
              <span *ngIf="!isLoading" class="flex items-center justify-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                Xác thực
              </span>
              <span *ngIf="isLoading" class="flex items-center justify-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Đang xác thực...
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `
})
export class VnPostLoginModalComponent implements OnInit, OnChanges {
  @Input() isVisible = false;
  @Output() loginSuccess = new EventEmitter<void>();
  @Output() modalClosed = new EventEmitter<void>();

  // Tài khoản mặc định cho VNPost
  private readonly DEFAULT_USERNAME = '884000_xa_tli_phuoclt';
  private readonly DEFAULT_PASSWORD = '123456d@D';

  loginData: RefreshTokenRequest = {
    userName: this.DEFAULT_USERNAME,
    password: this.DEFAULT_PASSWORD,
    captchaText: '',
    captchaCode: ''
  };

  captchaData: { image: string; code: string } | null = null;
  isLoading = false;
  isLoadingCaptcha = false;
  errorMessage = '';
  successMessage = '';

  constructor(private vnPostAuthService: VnPostAuthService) {}

  ngOnInit() {
    console.log('🔵 VNPost Modal ngOnInit - isVisible:', this.isVisible);
    if (this.isVisible) {
      this.loadCaptcha();
    }
  }

  ngOnChanges() {
    console.log('🔵 VNPost Modal ngOnChanges - isVisible:', this.isVisible, 'captchaData:', !!this.captchaData);
    if (this.isVisible && !this.captchaData) {
      this.loadCaptcha();
    }
  }

  loadCaptcha() {
    this.isLoadingCaptcha = true;
    this.errorMessage = '';

    this.vnPostAuthService.getCaptcha().subscribe({
      next: (response: VnPostCaptchaResponse) => {
        if (response.success && response.data) {
          this.captchaData = response.data;
          this.loginData.captchaCode = response.data.code;
        } else {
          this.errorMessage = 'Không thể tải captcha. Vui lòng thử lại.';
        }
        this.isLoadingCaptcha = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Lỗi khi tải captcha';
        this.isLoadingCaptcha = false;
      }
    });
  }

  refreshCaptcha() {
    this.loadCaptcha();
    this.loginData.captchaText = '';
  }

  onCaptchaInput(event: any) {
    const value = event.target.value;
    this.loginData.captchaText = value.toUpperCase();
  }

  onSubmit() {
    if (!this.captchaData) {
      this.errorMessage = 'Vui lòng tải captcha trước khi xác thực';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.vnPostAuthService.refreshToken(this.loginData).subscribe({
      next: (response) => {
        this.successMessage = 'Xác thực thành công!';
        this.isLoading = false;
        
        setTimeout(() => {
          this.loginSuccess.emit();
          this.closeModal();
        }, 1500);
      },
      error: (error) => {
        this.errorMessage = error.message || 'Xác thực thất bại';
        this.isLoading = false;
        this.refreshCaptcha(); // Refresh captcha on error
      }
    });
  }

  closeModal() {
    this.isVisible = false;
    this.modalClosed.emit();
    this.resetForm();
  }

  private resetForm() {
    this.loginData = {
      userName: this.DEFAULT_USERNAME,
      password: this.DEFAULT_PASSWORD,
      captchaText: '',
      captchaCode: ''
    };
    this.captchaData = null;
    this.errorMessage = '';
    this.successMessage = '';
    this.isLoading = false;
    this.isLoadingCaptcha = false;
  }
}
