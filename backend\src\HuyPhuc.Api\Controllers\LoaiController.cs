using HuyPhuc.Api.Controllers.Base;
using HuyPhuc.Application.Features.DiaChi.Queries.GetAllLoai;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HuyPhuc.Api.Controllers;

/// <summary>
/// API Controller cho quản lý loại
/// </summary>
[ApiController]
[Route("api/[controller]")]
// [Authorize] // Temporarily disabled for testing
public class LoaiController : BaseController
{
    /// <summary>
    /// L<PERSON>y danh sách tất cả loại
    /// </summary>
    /// <returns><PERSON>h sách loại</returns>
    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var result = await Mediator.Send(new GetAllLoaiQuery());
        
        if (result.IsSuccess && result.Data != null)
        {
            return Ok(result.Data);
        }

        return BadRequest(new
        {
            data = (object?)null,
            success = false,
            message = string.Join(", ", result.Errors),
            errors = result.Errors,
            status = 400,
            traceId = HttpContext.TraceIdentifier
        });
    }
}
