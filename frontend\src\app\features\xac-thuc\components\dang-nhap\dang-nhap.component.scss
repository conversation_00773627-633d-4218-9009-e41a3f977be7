// Enterprise Login Component Styles
// Sử dụng BEM methodology với tiếng Việt không dấu

.khung-dang-nhap {
  display: flex;
  min-height: 100vh;
  background-color: #f8fafc;

  // Responsive layout
  @media (max-width: 1024px) {
    flex-direction: column;
  }
}

// Left Panel - Branding
.panel-trai {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  position: relative;
  overflow: hidden;

  .noi-dung-brand {
    position: relative;
    z-index: 10;
  }

  .logo-chinh {
    .icon-logo {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
        transition: all 0.3s ease;
      }
    }

    .ten-cong-ty {
      h1 {
        background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }

  .thong-tin-he-thong {
    h2 {
      line-height: 1.2;

      .font-semibold {
        background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }

  .pattern-bg {
    background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  }
}

// Right Panel - Form
.panel-phai {
  background: #ffffff;

  @media (max-width: 1024px) {
    min-height: 100vh;
  }
}

.noi-dung-dang-nhap {
  animation: slideInRight 0.6s ease-out;

  .logo-mobile {
    .icon-logo {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }
  }

  .header-dang-nhap {
    .tieu-de {
      color: #1f2937;
      font-weight: 600;
      letter-spacing: -0.025em;
    }

    .mo-ta {
      color: #6b7280;
      line-height: 1.5;
    }
  }
}

// Form Styles
.form-dang-nhap {
  .thong-bao-loi {
    animation: slideInDown 0.3s ease-out;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border-left-color: #ef4444;
    box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.1);
  }

  .nhom-truong-nhap {
    .truong-email,
    .truong-mat-khau {
      .nhan-truong {
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
      }

      .khung-input {
        position: relative;

        input {
          background: #ffffff;
          border: 1.5px solid #d1d5db;
          font-size: 0.875rem;
          transition: all 0.2s ease-in-out;

          &::placeholder {
            color: #9ca3af;
            font-weight: 400;
          }

          &:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: #ffffff;
          }

          &:hover:not(:focus) {
            border-color: #9ca3af;
          }

          // Error states
          &.border-red-300 {
            border-color: #fca5a5;
            background: #fef2f2;

            &:focus {
              border-color: #ef4444;
              box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }
          }
        }

        .nut-an-hien-mat-khau {
          transition: all 0.2s ease;
          border-radius: 0 0.5rem 0.5rem 0;

          &:hover {
            background-color: #f9fafb;
          }

          svg {
            transition: color 0.2s ease;
          }

          &:hover svg {
            color: #3b82f6;
          }
        }
      }

      .thong-bao-loi-truong {
        animation: slideInDown 0.3s ease-out;
        font-weight: 500;
      }
    }
  }

  .tuy-chon-bo-sung {
    margin: 1.5rem 0;

    .checkbox-ghi-nho {
      input[type="checkbox"] {
        &:checked {
          background-color: #3b82f6;
          border-color: #3b82f6;
        }

        &:focus {
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }

      label {
        font-weight: 500;
        color: #374151;
      }
    }

    .lien-ket-quen-mat-khau {
      button {
        position: relative;
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          width: 0;
          height: 1px;
          bottom: -2px;
          left: 0;
          background-color: #1d4ed8;
          transition: width 0.3s ease;
        }

        &:hover::after {
          width: 100%;
        }
      }
    }
  }

  .nut-dang-nhap {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    position: relative;
    overflow: hidden;
    font-weight: 600;
    letter-spacing: 0.025em;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);

      &::before {
        left: 100%;
      }
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;

      &::before {
        display: none;
      }
    }
  }
}

// Footer Styles
.footer-dang-nhap {
  border-top: 1px solid #e5e7eb;

  p {
    line-height: 1.5;

    a {
      position: relative;
      font-weight: 500;
      transition: color 0.2s ease;

      &::after {
        content: '';
        position: absolute;
        width: 0;
        height: 1px;
        bottom: -1px;
        left: 0;
        background-color: #1d4ed8;
        transition: width 0.3s ease;
      }

      &:hover {
        color: #1d4ed8;

        &::after {
          width: 100%;
        }
      }
    }
  }
}

// Animations
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-3px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(3px);
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .khung-dang-nhap {
    .panel-trai {
      display: none;
    }

    .panel-phai {
      width: 100%;
    }
  }
}

@media (max-width: 640px) {
  .khung-dang-nhap {
    padding: 1rem;
    background: #ffffff;
  }

  .noi-dung-dang-nhap {
    padding: 1.5rem;

    .header-dang-nhap {
      .tieu-de {
        font-size: 1.5rem;
      }

      .mo-ta {
        font-size: 0.875rem;
      }
    }

    .form-dang-nhap {
      .nhom-truong-nhap {
        .truong-email,
        .truong-mat-khau {
          .khung-input input {
            padding: 0.75rem 1rem;
            font-size: 1rem;
          }
        }
      }

      .nut-dang-nhap {
        padding: 0.875rem 1rem;
        font-size: 1rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .noi-dung-dang-nhap {
    padding: 1rem;

    .logo-mobile {
      margin-bottom: 1.5rem;

      .icon-logo {
        width: 2rem;
        height: 2rem;
      }

      .ten-cong-ty h1 {
        font-size: 1.125rem;
      }
    }
  }
}

// Print styles
@media print {
  .khung-dang-nhap {
    display: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .form-dang-nhap {
    .nhom-truong-nhap {
      .truong-email,
      .truong-mat-khau {
        .khung-input input {
          border-width: 2px;
          border-color: #000000;

          &:focus {
            border-color: #0000ff;
            box-shadow: 0 0 0 3px rgba(0, 0, 255, 0.3);
          }
        }
      }
    }

    .nut-dang-nhap {
      background: #000000;
      border: 2px solid #000000;

      &:hover:not(:disabled) {
        background: #333333;
      }
    }
  }
}
