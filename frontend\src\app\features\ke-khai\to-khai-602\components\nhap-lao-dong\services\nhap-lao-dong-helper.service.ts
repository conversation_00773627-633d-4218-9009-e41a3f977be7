import { Injectable } from '@angular/core';

/**
 * Service chứa các utility methods cho nhập lao động
 * Tách từ NhapLaoDongService để tuân thủ quy tắc 400 dòng
 */
@Injectable({
  providedIn: 'root'
})
export class NhapLaoDongHelperService {

  constructor() {}

  /**
   * L<PERSON>y tháng hiện tại theo định dạng YYYY-MM
   */
  layThangHienTai(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }

  /**
   * Tạo ID tạm thời cho lao động mới
   */
  taoIdTamThoi(): string {
    return 'temp_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Format currency theo định dạng Việt Nam
   */
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(value);
  }

  /**
   * Validate mã số BHXH
   */
  validateMaSoBHXH(maSo: string): boolean {
    return /^\d{10}$/.test(maSo);
  }

  /**
   * Validate số CCCD
   */
  validateSoCCCD(soCCCD: string): boolean {
    return /^\d{12}$/.test(soCCCD);
  }

  /**
   * Validate số điện thoại
   */
  validateSoDienThoai(soDienThoai: string): boolean {
    return /^[0-9]{10,11}$/.test(soDienThoai);
  }

  /**
   * Validate email
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Format số với dấu phân cách hàng nghìn
   */
  formatNumber(value: number): string {
    return new Intl.NumberFormat('vi-VN').format(value);
  }

  /**
   * Deep clone object
   */
  deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }

  /**
   * Kiểm tra chuỗi có rỗng không
   */
  isEmpty(value: string | null | undefined): boolean {
    return !value || value.trim().length === 0;
  }

  /**
   * Kiểm tra số có hợp lệ không
   */
  isValidNumber(value: any): boolean {
    return !isNaN(value) && isFinite(value);
  }

  /**
   * Chuyển đổi string thành number an toàn
   */
  safeParseNumber(value: string | number, defaultValue: number = 0): number {
    if (typeof value === 'number') {
      return this.isValidNumber(value) ? value : defaultValue;
    }
    
    const parsed = parseFloat(value);
    return this.isValidNumber(parsed) ? parsed : defaultValue;
  }

  /**
   * Chuyển đổi string thành integer an toàn
   */
  safeParseInt(value: string | number, defaultValue: number = 0): number {
    if (typeof value === 'number') {
      return this.isValidNumber(value) ? Math.floor(value) : defaultValue;
    }
    
    const parsed = parseInt(value, 10);
    return this.isValidNumber(parsed) ? parsed : defaultValue;
  }

  /**
   * Chuẩn hóa chuỗi (trim, lowercase)
   */
  normalizeString(value: string): string {
    return value ? value.trim().toLowerCase() : '';
  }

  /**
   * Chuẩn hóa số điện thoại (loại bỏ ký tự không phải số)
   */
  normalizeSoDienThoai(soDienThoai: string): string {
    return soDienThoai ? soDienThoai.replace(/\D/g, '') : '';
  }

  /**
   * Chuẩn hóa mã số BHXH (loại bỏ ký tự không phải số)
   */
  normalizeMaSoBHXH(maSo: string): string {
    return maSo ? maSo.replace(/\D/g, '') : '';
  }

  /**
   * Tính toán Tiền tự đóng dựa trên mức thu nhập và tỷ lệ
   */
  tinhTienTuDong(mucThuNhap: number, tyLe: number, soThang: number = 1): number {
    if (!this.isValidNumber(mucThuNhap) || !this.isValidNumber(tyLe) || !this.isValidNumber(soThang)) {
      return 0;
    }
    
    return Math.round(mucThuNhap * (tyLe / 100) * soThang);
  }

  /**
   * Tính tổng tiền
   */
  tinhTongTien(tienTuDong: number, tienLai: number = 0, tienThua: number = 0): number {
    const tuDong = this.safeParseNumber(tienTuDong);
    const lai = this.safeParseNumber(tienLai);
    const thua = this.safeParseNumber(tienThua);
    
    return tuDong + lai - thua;
  }

  /**
   * Kiểm tra ngày có hợp lệ không
   */
  isValidDate(dateString: string): boolean {
    if (!dateString) return false;
    
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * Format ngày theo định dạng dd/MM/yyyy
   */
  formatDate(dateString: string): string {
    if (!this.isValidDate(dateString)) return '';
    
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
  }

  /**
   * Chuyển đổi ngày từ dd/MM/yyyy sang yyyy-MM-dd
   */
  convertDateFormat(dateString: string): string {
    if (!dateString) return '';
    
    // Nếu đã là format yyyy-MM-dd
    if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return dateString;
    }
    
    // Nếu là format dd/MM/yyyy
    if (dateString.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
      const [day, month, year] = dateString.split('/');
      return `${year}-${month}-${day}`;
    }
    
    return '';
  }

  /**
   * Lấy danh sách tháng trong năm
   */
  getDanhSachThang(): Array<{value: string, label: string}> {
    const currentYear = new Date().getFullYear();
    const months = [];
    
    for (let i = 1; i <= 12; i++) {
      const month = String(i).padStart(2, '0');
      const value = `${currentYear}-${month}`;
      const label = `Tháng ${i}/${currentYear}`;
      months.push({ value, label });
    }
    
    return months;
  }

  /**
   * Lấy danh sách năm (5 năm trước đến 2 năm sau)
   */
  getDanhSachNam(): Array<{value: number, label: string}> {
    const currentYear = new Date().getFullYear();
    const years = [];
    
    for (let i = currentYear - 5; i <= currentYear + 2; i++) {
      years.push({ value: i, label: `Năm ${i}` });
    }
    
    return years;
  }

  /**
   * Kiểm tra tuổi có hợp lệ không (từ 16 đến 65 tuổi)
   */
  kiemTraTuoi(ngaySinh: string): { valid: boolean, age: number, message: string } {
    if (!this.isValidDate(ngaySinh)) {
      return { valid: false, age: 0, message: 'Ngày sinh không hợp lệ' };
    }
    
    const birthDate = new Date(ngaySinh);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    if (age < 16) {
      return { valid: false, age, message: 'Tuổi phải từ 16 trở lên' };
    }
    
    if (age > 65) {
      return { valid: false, age, message: 'Tuổi không được vượt quá 65' };
    }
    
    return { valid: true, age, message: '' };
  }

  /**
   * Tạo mã tham chiếu ngẫu nhiên
   */
  taoMaThamChieu(prefix: string = 'REF'): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${random}`.toUpperCase();
  }
}
