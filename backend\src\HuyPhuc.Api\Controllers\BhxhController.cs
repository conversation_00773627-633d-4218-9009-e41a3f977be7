using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HuyPhuc.Application.Features.BHXH.DTOs;
using HuyPhuc.Application.Features.BHXH.Interfaces;

namespace HuyPhuc.API.Controllers;

/// <summary>
/// Controller xử lý các API liên quan đến BHXH
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class BhxhController : ControllerBase
{
    private readonly IBhxhLookupService _bhxhLookupService;
    private readonly ITk1TsUpdateService _tk1TsUpdateService;
    private readonly ILogger<BhxhController> _logger;

    public BhxhController(
        IBhxhLookupService bhxhLookupService,
        ITk1TsUpdateService tk1TsUpdateService,
        ILogger<BhxhController> logger)
    {
        _bhxhLookupService = bhxhLookupService;
        _tk1TsUpdateService = tk1TsUpdateService;
        _logger = logger;
    }

    /// <summary>
    /// Tra cứu thông tin BHXH theo mã số BHXH
    /// </summary>
    /// <param name="request">Request chứa mã số BHXH</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Thông tin BHXH</returns>
    [HttpPost("tra-cuu")]
    [ProducesResponseType(typeof(BhxhLookupResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<BhxhLookupResponseDto>> TraCuuThongTinBhxh(
        [FromBody] BhxhLookupRequestDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Nhận request tra cứu BHXH cho mã số: {MaSoBHXH}", request.MaSoBHXH);

            // Validate request
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Request không hợp lệ: {Errors}", 
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                
                return BadRequest(new ProblemDetails
                {
                    Title = "Dữ liệu đầu vào không hợp lệ",
                    Detail = string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)),
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Call service to lookup BHXH info
            var result = await _bhxhLookupService.LookupBhxhInfoAsync(request.MaSoBHXH, cancellationToken);

            if (result.Success)
            {
                _logger.LogInformation("Tra cứu BHXH thành công cho mã số: {MaSoBHXH}", request.MaSoBHXH);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Tra cứu BHXH thất bại cho mã số: {MaSoBHXH}. Lỗi: {Message}", 
                    request.MaSoBHXH, result.Message);

                // Return appropriate status code based on the result
                return result.Status switch
                {
                    400 => BadRequest(new ProblemDetails
                    {
                        Title = "Dữ liệu không hợp lệ",
                        Detail = result.Message,
                        Status = StatusCodes.Status400BadRequest
                    }),
                    401 => StatusCode(StatusCodes.Status401Unauthorized, new ProblemDetails
                    {
                        Title = "Token hết hạn",
                        Detail = result.Message,
                        Status = StatusCodes.Status401Unauthorized
                    }),
                    404 => NotFound(new ProblemDetails
                    {
                        Title = "Không tìm thấy thông tin",
                        Detail = result.Message,
                        Status = StatusCodes.Status404NotFound
                    }),
                    408 => StatusCode(StatusCodes.Status408RequestTimeout, new ProblemDetails
                    {
                        Title = "Timeout",
                        Detail = result.Message,
                        Status = StatusCodes.Status408RequestTimeout
                    }),
                    _ => StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
                    {
                        Title = "Lỗi server",
                        Detail = result.Message,
                        Status = StatusCodes.Status500InternalServerError
                    })
                };
            }
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning("Token VNPost hết hạn cho mã số: {MaSoBHXH}. Lỗi: {Error}", request.MaSoBHXH, ex.Message);

            return StatusCode(StatusCodes.Status401Unauthorized, new ProblemDetails
            {
                Title = "Token hết hạn",
                Detail = ex.Message,
                Status = StatusCodes.Status401Unauthorized
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi không xác định khi tra cứu BHXH cho mã số: {MaSoBHXH}. Exception type: {ExceptionType}", request.MaSoBHXH, ex.GetType().Name);

            // Check if inner exception is UnauthorizedAccessException
            if (ex.InnerException is UnauthorizedAccessException unauthorizedException)
            {
                _logger.LogWarning("Token VNPost hết hạn (inner exception) cho mã số: {MaSoBHXH}. Lỗi: {Error}", request.MaSoBHXH, unauthorizedException.Message);

                return StatusCode(StatusCodes.Status401Unauthorized, new ProblemDetails
                {
                    Title = "Token hết hạn",
                    Detail = unauthorizedException.Message,
                    Status = StatusCodes.Status401Unauthorized
                });
            }

            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Lỗi server",
                Detail = "Có lỗi xảy ra khi xử lý yêu cầu. Vui lòng thử lại sau.",
                Status = StatusCodes.Status500InternalServerError
            });
        }
    }

    /// <summary>
    /// Tra cứu và cập nhật thông tin BHXH vào bảng tk1_ts
    /// </summary>
    /// <param name="request">Request chứa mã số BHXH</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Kết quả tra cứu và cập nhật</returns>
    [HttpPost("tra-cuu-va-cap-nhat")]
    [ProducesResponseType(typeof(BhxhLookupResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult> TraCuuVaCapNhatBhxh([FromBody] BhxhLookupRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Bắt đầu tra cứu và cập nhật BHXH cho mã số: {MaSoBHXH}", request.MaSoBHXH);

            // Validate request
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Request không hợp lệ: {Errors}",
                    string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));

                return BadRequest(new ProblemDetails
                {
                    Title = "Dữ liệu đầu vào không hợp lệ",
                    Detail = string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)),
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Call service to lookup BHXH info
            var result = await _bhxhLookupService.LookupBhxhInfoAsync(request.MaSoBHXH, cancellationToken);

            if (result.Success && result.Data != null)
            {
                // Cập nhật thông tin vào bảng tk1_ts
                var updateSuccess = await _tk1TsUpdateService.CapNhatThongTinBhxhAsync(result.Data, cancellationToken);

                if (updateSuccess)
                {
                    _logger.LogInformation("Tra cứu và cập nhật BHXH thành công cho mã số: {MaSoBHXH}", request.MaSoBHXH);
                }
                else
                {
                    _logger.LogWarning("Tra cứu BHXH thành công nhưng cập nhật tk1_ts thất bại cho mã số: {MaSoBHXH}", request.MaSoBHXH);
                }

                return Ok(result);
            }

            // Handle different error cases
            if (result.Status == 401)
            {
                return Unauthorized(new ProblemDetails
                {
                    Title = "Token hết hạn",
                    Detail = result.Message,
                    Status = StatusCodes.Status401Unauthorized
                });
            }

            if (result.Status == 400)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Dữ liệu không hợp lệ",
                    Detail = result.Message,
                    Status = StatusCodes.Status400BadRequest
                });
            }

            return StatusCode(result.Status, new ProblemDetails
            {
                Title = "Lỗi tra cứu BHXH",
                Detail = result.Message,
                Status = result.Status
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi không xác định khi tra cứu và cập nhật BHXH cho mã số: {MaSoBHXH}", request.MaSoBHXH);

            return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
            {
                Title = "Lỗi server",
                Detail = "Có lỗi xảy ra khi xử lý yêu cầu. Vui lòng thử lại sau.",
                Status = StatusCodes.Status500InternalServerError
            });
        }
    }

    /// <summary>
    /// Kiểm tra tính hợp lệ của mã số BHXH
    /// </summary>
    /// <param name="maSoBHXH">Mã số BHXH cần kiểm tra</param>
    /// <returns>Kết quả kiểm tra</returns>
    [HttpGet("kiem-tra-ma-so/{maSoBHXH}")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public ActionResult KiemTraMaSoBhxh(string maSoBHXH)
    {
        var isValid = _bhxhLookupService.IsValidBhxhCode(maSoBHXH);

        return Ok(new
        {
            maSoBHXH,
            isValid,
            message = isValid ? "Mã số BHXH hợp lệ" : "Mã số BHXH không hợp lệ. Mã số BHXH phải có đúng 10 chữ số."
        });
    }

    /// <summary>
    /// Reset token expired state sau khi đăng nhập thành công
    /// </summary>
    /// <returns>Kết quả reset</returns>
    [HttpPost("reset-token-state")]
    public ActionResult ResetTokenState()
    {
        try
        {
            _logger.LogInformation("Reset token expired state");
            // Logic để reset state sẽ được thực hiện tự động khi có token mới
            return Ok(new { message = "Token state đã được reset", success = true });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi reset token state");
            return StatusCode(500, new { message = "Lỗi server khi reset token state" });
        }
    }
}
