/**
 * Models cho đơn vị
 */

/**
 * Interface cho thông tin đơn vị thuộc đại lý
 */
export interface DonVi {
  id: number;
  ma: string;
  ten: string;
  diaChi?: string;
  soDienThoai?: string;
  email?: string;
  daiLyId: number;
  trangThai: number;
  ngayTao?: Date;
  ngayCapNhat?: Date;

  // Backward compatibility
  maDonVi?: string;
  tenDonVi?: string;
  trangThaiHoatDong?: boolean;
}

/**
 * Interface cho đơn vị trong dropdown/select
 */
export interface DonViOption {
  id: number;
  maDonVi: string;
  tenDonVi: string;
  tenHienThi: string; // Kết hợp mã + tên để hiển thị
  daiLyId: number;
}
