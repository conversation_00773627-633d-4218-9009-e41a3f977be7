using HuyPhuc.Application.Common.Models;
using HuyPhuc.Application.DTOs.DiaChi;
using HuyPhuc.Domain.Repositories;
using MediatR;

namespace HuyPhuc.Application.Features.DiaChi.Queries.GetAllLoai;

/// <summary>
/// Query để lấy tất cả loại
/// </summary>
public record GetAllLoaiQuery : IRequest<Result<LoaiApiResponse>>;

/// <summary>
/// Handler cho GetAllLoaiQuery
/// </summary>
public class GetAllLoaiQueryHandler : IRequestHandler<GetAllLoaiQuery, Result<LoaiApiResponse>>
{
    private readonly IDmLoaiRepository _loaiRepository;

    public GetAllLoaiQueryHandler(IDmLoaiRepository loaiRepository)
    {
        _loaiRepository = loaiRepository;
    }

    public async Task<Result<LoaiApiResponse>> Handle(GetAllLoaiQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var loaiList = await _loaiRepository.GetAllAsync(cancellationToken);

            var items = loaiList.Select(l => new LoaiDto
            {
                Id = l.Id,
                TenLoai = l.TenLoai
            }).ToList();

            var response = new LoaiApiResponse
            {
                Data = new LoaiResponse
                {
                    TotalCount = items.Count,
                    Items = items
                },
                Success = true,
                Message = null,
                Errors = null,
                Status = 200,
                TraceId = null
            };

            return Result<LoaiApiResponse>.Success(response);
        }
        catch (Exception ex)
        {
            var errorResponse = new LoaiApiResponse
            {
                Data = null,
                Success = false,
                Message = $"Lỗi khi lấy danh sách loại: {ex.Message}",
                Errors = new[] { ex.Message },
                Status = 400,
                TraceId = null
            };

            return Result<LoaiApiResponse>.Success(errorResponse);
        }
    }
}
