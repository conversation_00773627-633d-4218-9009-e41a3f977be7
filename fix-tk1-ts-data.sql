-- Script để sửa dữ liệu lỗi trong bảng tk1_ts
-- Vấn đề: ho_ten = "Lao động {ma_so_bhxh}" và cmnd = ma_so_bhxh

-- 1. <PERSON><PERSON><PERSON> tra số lượng bản ghi bị lỗi
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN ho_ten LIKE 'Lao động %' THEN 1 END) as invalid_ho_ten,
    COUNT(CASE WHEN cmnd = ma_so_bhxh THEN 1 END) as invalid_cmnd
FROM tk1_ts;

-- 2. Xem chi tiết các bản ghi bị lỗi
SELECT ma_so_bhxh, ho_ten, cmnd, ngay_sinh, gioi_tinh
FROM tk1_ts 
WHERE ho_ten LIKE 'Lao động %' 
   OR cmnd = ma_so_bhxh
ORDER BY ma_so_bhxh;

-- 3. <PERSON><PERSON><PERSON> nhật dữ liệu (chạy từng lệnh một)

-- C<PERSON><PERSON> nhật họ tên thành placeholder có ý nghĩa
UPDATE tk1_ts 
SET ho_ten = '[Cần cập nhật họ tên]',
    last_updated = CURRENT_TIMESTAMP
WHERE ho_ten LIKE 'Lao động %';

-- Cập nhật CMND thành placeholder (không thể để trống vì có constraint)
UPDATE tk1_ts 
SET cmnd = '000000000000',  -- Placeholder CMND
    last_updated = CURRENT_TIMESTAMP
WHERE cmnd = ma_so_bhxh;

-- 4. Kiểm tra kết quả sau khi cập nhật
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN ho_ten LIKE 'Lao động %' THEN 1 END) as remaining_invalid_ho_ten,
    COUNT(CASE WHEN cmnd = ma_so_bhxh THEN 1 END) as remaining_invalid_cmnd,
    COUNT(CASE WHEN ho_ten = '[Cần cập nhật họ tên]' THEN 1 END) as placeholder_ho_ten,
    COUNT(CASE WHEN cmnd = '000000000000' THEN 1 END) as placeholder_cmnd
FROM tk1_ts;

-- 5. Xem các bản ghi đã được cập nhật
SELECT ma_so_bhxh, ho_ten, cmnd, last_updated
FROM tk1_ts 
WHERE ho_ten = '[Cần cập nhật họ tên]' 
   OR cmnd = '000000000000'
ORDER BY last_updated DESC
LIMIT 10;
