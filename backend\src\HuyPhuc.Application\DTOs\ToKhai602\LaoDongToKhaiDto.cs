using System.ComponentModel.DataAnnotations;

namespace HuyPhuc.Application.DTOs.ToKhai602;

/// <summary>
/// DTO cho thông tin lao động trong tờ khai
/// </summary>
public class LaoDongToKhaiDto
{
    /// <summary>
    /// ID lao động (nếu đã tồn tại)
    /// </summary>
    public int? Id { get; set; }

    /// <summary>
    /// Số thứ tự
    /// </summary>
    public int Stt { get; set; }

    /// <summary>
    /// Mã số BHXH
    /// </summary>
    [Required(ErrorMessage = "Mã số BHXH là bắt buộc")]
    [StringLength(15, ErrorMessage = "Mã số BHXH không được vượt quá 15 ký tự")]
    public string MaSoBHXH { get; set; } = string.Empty;

    /// <summary>
    /// Họ tên
    /// </summary>
    [Required(ErrorMessage = "Họ tên là bắt buộc")]
    [StringLength(100, ErrorMessage = "Họ tên không được vượt quá 100 ký tự")]
    public string HoTen { get; set; } = string.Empty;

    /// <summary>
    /// Số CCCD/CMND
    /// </summary>
    [Required(ErrorMessage = "Số CCCD/CMND là bắt buộc")]
    [StringLength(20, ErrorMessage = "Số CCCD/CMND không được vượt quá 20 ký tự")]
    public string Ccns { get; set; } = string.Empty;

    /// <summary>
    /// Số CCCD/CMND (alias cho Ccns để tương thích với code cũ)
    /// </summary>
    [Required(ErrorMessage = "Số CCCD/CMND là bắt buộc")]
    [StringLength(20, ErrorMessage = "Số CCCD/CMND không được vượt quá 20 ký tự")]
    public string Cmnd { get; set; } = string.Empty;

    /// <summary>
    /// Ngày sinh (dd/MM/yyyy)
    /// </summary>
    [Required(ErrorMessage = "Ngày sinh là bắt buộc")]
    public string NgaySinh { get; set; } = string.Empty;

    /// <summary>
    /// Giới tính (1: Nam, 2: Nữ)
    /// </summary>
    [Range(1, 2, ErrorMessage = "Giới tính phải là 1 (Nam) hoặc 2 (Nữ)")]
    public int GioiTinh { get; set; }

    /// <summary>
    /// Quốc tịch
    /// </summary>
    [StringLength(50, ErrorMessage = "Quốc tịch không được vượt quá 50 ký tự")]
    public string QuocTich { get; set; } = "Việt Nam";

    /// <summary>
    /// Dân tộc
    /// </summary>
    [StringLength(50, ErrorMessage = "Dân tộc không được vượt quá 50 ký tự")]
    public string DanToc { get; set; } = "01"; // Mã dân tộc Kinh

    /// <summary>
    /// Mã tỉnh KS
    /// </summary>
    [StringLength(10, ErrorMessage = "Mã tỉnh KS không được vượt quá 10 ký tự")]
    public string MaTinhKs { get; set; } = string.Empty;

    /// <summary>
    /// Mã huyện KS
    /// </summary>
    [StringLength(10, ErrorMessage = "Mã huyện KS không được vượt quá 10 ký tự")]
    public string MaHuyenKs { get; set; } = string.Empty;

    /// <summary>
    /// Mã xã KS
    /// </summary>
    [StringLength(10, ErrorMessage = "Mã xã KS không được vượt quá 10 ký tự")]
    public string MaXaKs { get; set; } = string.Empty;

    /// <summary>
    /// Điện thoại liên hệ
    /// </summary>
    [StringLength(15, ErrorMessage = "Điện thoại liên hệ không được vượt quá 15 ký tự")]
    public string DienThoaiLh { get; set; } = string.Empty;

    /// <summary>
    /// Mã hộ gia đình
    /// </summary>
    [StringLength(20, ErrorMessage = "Mã hộ gia đình không được vượt quá 20 ký tự")]
    public string MaHoGiaDinh { get; set; } = string.Empty;

    /// <summary>
    /// Phương án đóng BHXH
    /// </summary>
    [Required(ErrorMessage = "Vui lòng chọn phương án đóng")]
    [StringLength(10)]
    public string PhuongAn { get; set; } = string.Empty;

    /// <summary>
    /// Phương thức đóng
    /// </summary>
    [Required(ErrorMessage = "Vui lòng chọn phương thức đóng")]
    [StringLength(10)]
    public string PhuongThuc { get; set; } = string.Empty;

    /// <summary>
    /// Tháng bắt đầu (MM/yyyy)
    /// </summary>
    [Required(ErrorMessage = "Tháng bắt đầu là bắt buộc")]
    public string ThangBatDau { get; set; } = string.Empty;

    /// <summary>
    /// Tiền lãi
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "Tiền lãi phải >= 0")]
    public decimal TienLai { get; set; }

    /// <summary>
    /// Tiền thừa
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "Tiền thừa phải >= 0")]
    public decimal TienThua { get; set; }

    /// <summary>
    /// Tiền tự đóng
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "Tiền tự đóng phải >= 0")]
    public decimal TienTuDong { get; set; }

    /// <summary>
    /// Tổng tiền
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "Tổng tiền phải >= 0")]
    public decimal TongTien { get; set; }

    /// <summary>
    /// NSNN hỗ trợ
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "NSNN hỗ trợ phải >= 0")]
    public decimal TienHoTro { get; set; }

    /// <summary>
    /// Mức thu nhập
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "Mức thu nhập phải >= 0")]
    public decimal MucThuNhap { get; set; }

    /// <summary>
    /// Ghi chú
    /// </summary>
    [StringLength(500, ErrorMessage = "Ghi chú không được vượt quá 500 ký tự")]
    public string? GhiChu { get; set; }

    /// <summary>
    /// Type ID
    /// </summary>
    [StringLength(10)]
    public string TypeId { get; set; } = "TM";

    /// <summary>
    /// Loại NSNN
    /// </summary>
    [StringLength(50)]
    public string? LoaiNsnn { get; set; }

    /// <summary>
    /// Tỷ lệ NSNN hỗ trợ (%)
    /// </summary>
    [Range(0, 100, ErrorMessage = "Tỷ lệ NSNN hỗ trợ phải từ 0 đến 100")]
    public int TyLeNsnnHoTro { get; set; } = 0;

    /// <summary>
    /// Ngày biên lai
    /// </summary>
    [StringLength(20)]
    public string? NgayBienLai { get; set; }

    /// <summary>
    /// Hệ số đóng
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "Hệ số đóng phải >= 0")]
    public int HeSoDong { get; set; } = 0;

    /// <summary>
    /// Tham gia bảo hiểm
    /// </summary>
    public bool IsThamGiaBb { get; set; } = false;

    /// <summary>
    /// Tạm hoãn hợp đồng
    /// </summary>
    public bool IsTamHoanHD { get; set; } = false;

    /// <summary>
    /// Thông báo từ API
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Có lỗi hay không
    /// </summary>
    public bool IsError { get; set; } = false;

    /// <summary>
    /// Mã lỗi
    /// </summary>
    [StringLength(10)]
    public string? MaLoi { get; set; }

    /// <summary>
    /// Mô tả lỗi
    /// </summary>
    public string? MoTaLoi { get; set; }
}
