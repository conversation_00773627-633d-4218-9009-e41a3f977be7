/**
 * Models và interfaces cho feature Tờ khai 602
 * Dựa trên cấu trúc XML D05-TS
 */

/**
 * Interface cho thông tin đại lý
 */
export interface DaiLy {
  id: number;
  maDaiLy: string;
  tenDaiLy: string;
  diaChi?: string;
  soDienThoai?: string;
  email?: string;
  trangThaiHoatDong: boolean;
}

/**
 * Interface cho thông tin đơn vị thuộc đại lý
 */
export interface DonVi {
  id: number;
  maDonVi: string;
  tenDonVi: string;
  diaChi?: string;
  daiLyId: number;
  trangThaiHoatDong: boolean;
}

/**
 * Interface cho thông tin khác trong tờ khai
 */
export interface ThongTinKhac {
  soSoBHXH: string;
  ngayTao?: Date;
  nguoiTao?: string;
  ghiChu?: string;
}

/**
 * Interface cho thông tin lao động
 */
export interface LaoDong {
  id?: number | string; // ID có thể là number (từ backend) hoặc string (temporary ID)
  stt: number;

  // Thông tin cá nhân
  maSoBHXH: string;
  hoTen: string;
  ccns: string;
  ngaySinh: string; // Format: dd/MM/yyyy
  gioiTinh: number; // 1: Nam, 2: Nữ
  quocTich: string;
  danToc: string;
  cmnd: string; // Số CCCD/CMND

  // Địa chỉ thường trú
  maTinhKs: string;
  maHuyenKs: string;
  maXaKs: string;

  // Thông tin liên hệ
  dienThoaiLh: string;
  maHoGiaDinh: string;

  // Thông tin đóng BHXH
  phuongAn: string; // DB, DT, etc.
  phuongThuc: string; // "1", "2", "3"
  thangBatDau: string; // Format: MM/yyyy

  // Thông tin tiền
  tienLai: number;
  tienThua: number;
  tienTuDong: number;
  tongTien: number;
  tienHoTro: number;
  mucThuNhap: number;

  // Thông tin khác
  typeId: string;
  loaiNsnn?: string; // Loại NSNN
  tyLeNsnnHoTro?: number; // Tỷ lệ NSNN hỗ trợ (%)
  heSoDong?: number; // Hệ số đóng
  isThamGiaBb: boolean;
  isTamHoanHD: boolean;

  // Thông tin lỗi/cảnh báo
  message?: string;
  isError: boolean;
  maLoi?: string;
  moTaLoi?: string;

  // Các trường cũ để tương thích (deprecated)
  /** @deprecated Sử dụng maSoBHXH thay thế */
  masoBHXH?: string;
  /** @deprecated Sử dụng cmnd thay thế */
  soCCCD?: string;
  /** @deprecated */
  ngayBienLai?: Date;
  /** @deprecated */
  soBienLai?: string;
  /** @deprecated */
  ghichu?: string;
  /** @deprecated Sử dụng mucThuNhap thay thế */
  mucTien?: number;
  /** @deprecated */
  tyle?: number;
  /** @deprecated */
  soThang?: number;
  /** @deprecated */
  diachiDangSS?: string;
  /** @deprecated */
  matinhDangSS?: string;
  /** @deprecated */
  mahuyenDangSS?: string;
  /** @deprecated */
  maxaDangSS?: string;
  /** @deprecated */
  maNhanvienThu?: string;
  /** @deprecated Sử dụng phuongAn thay thế */
  pa?: string;
  /** @deprecated */
  tyleNSNN?: number;
  /** @deprecated */
  heso?: number;
  /** @deprecated Sử dụng thangBatDau thay thế */
  tuThang?: string;
  /** @deprecated */
  loai?: number;
  /** @deprecated */
  tyleNSDP?: number;
  /** @deprecated */
  tienNSDP?: number;
  /** @deprecated */
  tyleHotroKhac?: number;
  /** @deprecated */
  tienHotroKhac?: number;
}

/**
 * Interface cho thông tin lao động từ API kê khai
 * Sử dụng cho bảng chi_tiet_lao_dong_ke_khai_v2
 */
export interface LaoDongKeKhaiDto {
  id: number;
  hoTen?: string;
  soCCCD?: string;
  soBHXH?: string;
  gioiTinh?: 'Nam' | 'Nữ';
  ngaySinh?: Date;
  diaChi?: string;
  soDienThoai?: string;
  email?: string;
  trangThai?: string;

  // Thông tin BHXH
  mucLuong?: number;
  tyLeDong?: number;
  soTienDong?: number;

  // Thông tin thời gian
  thangBatDau?: string;
  thangKetThuc?: string;

  // Metadata
  ngayTao?: Date;
  ngayCapNhat?: Date;
  nguoiTao?: string;
  nguoiCapNhat?: string;
}

/**
 * Interface chính cho Tờ khai 602
 */
export interface ToKhai602 {
  id?: number;
  daiLyId: number;
  donViId: number;
  thongTinKhac: ThongTinKhac;
  danhSachLaoDong: LaoDong[];
  trangThai: TrangThaiToKhai;
  ngayTao?: Date;
  ngayCapNhat?: Date;
  nguoiTao?: string;
  nguoiCapNhat?: string;
}

/**
 * Enum trạng thái tờ khai
 */
export enum TrangThaiToKhai {
  DangSoan = 'dang_soan',
  DaGui = 'da_gui',
  DaDuyet = 'da_duyet',
  BiTuChoi = 'bi_tu_choi'
}

/**
 * Interface cho request tạo tờ khai mới
 */
export interface TaoToKhaiRequest {
  daiLyId: number;
  donViId: number;
  thongTinKhac: ThongTinKhac;
  danhSachLaoDong: LaoDong[];
}

/**
 * Request tạo draft tờ khai
 */
export interface TaoDraftToKhaiRequest {
  daiLyId: number;
  donViId: number;
  soSoBHXH: string;
  ghiChu?: string;
}

/**
 * Response tạo draft tờ khai
 */
export interface TaoDraftToKhaiResponse {
  draftId: number;
  maToKhai: string;
  daiLy: DaiLyInfo;
  donVi: DonViInfo;
  ngayTao: Date;
  trangThai: string;
}

/**
 * Thông tin đại lý trong response
 */
export interface DaiLyInfo {
  id: number;
  maDaiLy: string;
  tenDaiLy: string;
  diaChi: string;
  soDienThoai: string;
}

/**
 * Thông tin đơn vị trong response
 */
export interface DonViInfo {
  id: number;
  maDonVi: string;
  tenDonVi: string;
  diaChi: string;
}

/**
 * Interface cho response API
 */
export interface ToKhaiApiResponse<T = any> {
  thanhCong: boolean;
  thongBao: string;
  duLieu?: T;
  loi?: string[];
}

/**
 * Interface cho filter/search tờ khai
 */
export interface ToKhaiFilter {
  daiLyId?: number;
  donViId?: number;
  trangThai?: TrangThaiToKhai;
  tuNgay?: Date;
  denNgay?: Date;
  tuKhoa?: string;
}

/**
 * Interface cho pagination
 */
export interface PhanTrang {
  trang: number;
  kichThuoc: number;
  tongSo: number;
  tongTrang: number;
}

/**
 * Interface cho danh sách tờ khai có phân trang
 */
export interface DanhSachToKhaiResponse {
  danhSach: ToKhai602[];
  phanTrang: PhanTrang;
}

/**
 * Interface cho dropdown options
 */
export interface DropdownOption {
  value: number | string;
  label: string;
  disabled?: boolean;
}

/**
 * Interface cho validation errors
 */
export interface ValidationError {
  field: string;
  message: string;
  index?: number; // Cho validation trong array
}

/**
 * Interface cho form state
 */
export interface ToKhaiFormState {
  draftId?: number;
  daiLyDaChon?: DaiLy;
  donViDaChon?: DonVi;
  thongTinKhac: Partial<ThongTinKhac>;
  danhSachLaoDong: Partial<LaoDong>[];
  dangTai: boolean;
  loi: ValidationError[];
}

/**
 * Enum cho giới tính
 */
export enum GioiTinh {
  Nam = 1,
  Nu = 2
}

/**
 * Enum cho các phương thức đóng
 */
export enum PhuongThucDong {
  PhuongThuc1 = "1",
  PhuongThuc2 = "2",
  PhuongThuc3 = "3"
}

/**
 * Enum cho phương án đóng
 */
export enum PhuongAnDong {
  DB = "DB", // Đóng bù
  DT = "DT", // Đóng tiếp
  TN = "TN"  // Tự nguyện
}

/**
 * Enum cho loại lao động
 */
export enum LoaiLaoDong {
  Loai1 = 1,
  Loai2 = 2,
  Loai3 = 3
}

/**
 * Interface cho response API kiểm tra thông tin lao động
 */
export interface KiemTraLaoDongResponse {
  data: LaoDong;
  success: boolean;
  message?: string;
  errors?: string[];
  status: number;
  traceId: string;
}

/**
 * Interface cho request ghi nhận tờ khai
 */
export interface GhiNhanToKhaiRequest {
  draftId: number;
  danhSachLaoDong: LaoDong[];
}

/**
 * Interface cho response ghi nhận tờ khai
 */
export interface GhiNhanToKhaiResponse {
  toKhaiId: number;
  maToKhai: string;
  soLaoDongDaLuu: number;
  soLaoDongLoi: number;
  danhSachLoi: string[];
  danhSachLaoDongDaLuu: LaoDongDaLuuDto[];
}

/**
 * DTO cho thông tin lao động đã được lưu
 */
export interface LaoDongDaLuuDto {
  id?: number;
  stt: number;
  maSoBHXH: string;
  hoTen: string;
  thanhCong: boolean;
  thongBaoLoi?: string;
}

/**
 * Interface cho địa chỉ hành chính
 */
export interface DiaChiHanhChinh {
  maTinh: string;
  tenTinh: string;
  maHuyen: string;
  tenHuyen: string;
  maXa: string;
  tenXa: string;
}

/**
 * Interface cho lookup data
 */
export interface LookupData {
  danhSachTinh: DiaChiHanhChinh[];
  danhSachHuyen: DiaChiHanhChinh[];
  danhSachXa: DiaChiHanhChinh[];
}
