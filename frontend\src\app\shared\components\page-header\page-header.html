<div class="bg-white border-b border-gray-200 px-4 py-6 sm:px-6">
  <div class="flex items-center justify-between">
    <div class="flex items-center space-x-4">
      <button *ngIf="showBackButton"
              class="text-gray-400 hover:text-gray-600 transition-colors"
              (click)="goBack()">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
      <div>
        <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
        <p *ngIf="subtitle" class="mt-1 text-sm text-gray-500">{{ subtitle }}</p>
      </div>
    </div>
    <div class="flex items-center space-x-3">
      <ng-content select="[slot=actions]"></ng-content>
    </div>
  </div>
</div>
