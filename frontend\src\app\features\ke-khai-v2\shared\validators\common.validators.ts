import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * Custom validators chung cho feature kê khai
 */
export class CommonValidators {
  
  /**
   * Validator cho số điện thoại Việt Nam
   */
  static soDienThoai(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null; // Không validate nếu không có giá trị
      }

      const phoneRegex = /^(0[3|5|7|8|9])+([0-9]{8})$/;
      const isValid = phoneRegex.test(control.value);

      return isValid ? null : { soDienThoai: { value: control.value } };
    };
  }

  /**
   * Validator cho CCCD/CMND
   */
  static cccd(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const value = control.value.toString();
      
      // CCCD: 12 số, CMND: 9 hoặc 12 số
      const cccdRegex = /^[0-9]{9}$|^[0-9]{12}$/;
      const isValid = cccdRegex.test(value);

      return isValid ? null : { cccd: { value: control.value } };
    };
  }

  /**
   * Validator cho mã số BHXH
   */
  static maSoBHXH(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const value = control.value.toString();
      
      // Mã số BHXH: 10 số
      const bhxhRegex = /^[0-9]{10}$/;
      const isValid = bhxhRegex.test(value);

      return isValid ? null : { maSoBHXH: { value: control.value } };
    };
  }

  /**
   * Validator cho ngày sinh (định dạng dd/MM/yyyy)
   */
  static ngaySinh(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
      const match = control.value.match(dateRegex);

      if (!match) {
        return { ngaySinh: { value: control.value, message: 'Định dạng ngày không hợp lệ (dd/MM/yyyy)' } };
      }

      const day = parseInt(match[1], 10);
      const month = parseInt(match[2], 10);
      const year = parseInt(match[3], 10);

      // Kiểm tra tính hợp lệ của ngày
      const date = new Date(year, month - 1, day);
      const isValidDate = date.getFullYear() === year && 
                         date.getMonth() === month - 1 && 
                         date.getDate() === day;

      if (!isValidDate) {
        return { ngaySinh: { value: control.value, message: 'Ngày sinh không hợp lệ' } };
      }

      // Kiểm tra tuổi (phải từ 16 đến 100 tuổi)
      const today = new Date();
      const age = today.getFullYear() - year;
      
      if (age < 16 || age > 100) {
        return { ngaySinh: { value: control.value, message: 'Tuổi phải từ 16 đến 100' } };
      }

      return null;
    };
  }

  /**
   * Validator cho tháng năm (định dạng MM/yyyy)
   */
  static thangNam(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const monthYearRegex = /^(\d{2})\/(\d{4})$/;
      const match = control.value.match(monthYearRegex);

      if (!match) {
        return { thangNam: { value: control.value, message: 'Định dạng tháng/năm không hợp lệ (MM/yyyy)' } };
      }

      const month = parseInt(match[1], 10);
      const year = parseInt(match[2], 10);

      if (month < 1 || month > 12) {
        return { thangNam: { value: control.value, message: 'Tháng phải từ 01 đến 12' } };
      }

      if (year < 1900 || year > 2100) {
        return { thangNam: { value: control.value, message: 'Năm không hợp lệ' } };
      }

      return null;
    };
  }

  /**
   * Validator cho số tiền (phải là số dương)
   */
  static soTien(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value && control.value !== 0) {
        return null;
      }

      const value = Number(control.value);
      
      if (isNaN(value)) {
        return { soTien: { value: control.value, message: 'Phải là số' } };
      }

      if (value < 0) {
        return { soTien: { value: control.value, message: 'Số tiền phải lớn hơn hoặc bằng 0' } };
      }

      return null;
    };
  }

  /**
   * Validator cho email
   */
  static email(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      const isValid = emailRegex.test(control.value);

      return isValid ? null : { email: { value: control.value } };
    };
  }
}
