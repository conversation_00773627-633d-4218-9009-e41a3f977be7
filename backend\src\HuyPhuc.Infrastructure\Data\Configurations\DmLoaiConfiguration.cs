using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration cho DmLoai
/// </summary>
public class DmLoaiConfiguration : IEntityTypeConfiguration<DmLoai>
{
    public void Configure(EntityTypeBuilder<DmLoai> builder)
    {
        // Table name
        builder.ToTable("dm_loai");

        // Primary key
        builder.HasKey(l => l.Id);
        builder.Property(l => l.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(l => l.TenLoai)
            .HasColumnName("ten_loai")
            .HasMaxLength(255)
            .IsRequired();

        // Audit fields
        builder.Property(l => l.Created)
            .HasColumnName("created_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(l => l.LastModified)
            .HasColumnName("updated_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        // Ignore properties that don't exist in database
        builder.Ignore(l => l.CreatedBy);
        builder.Ignore(l => l.LastModifiedBy);
        builder.Ignore(l => l.NgayTao);
        builder.Ignore(l => l.NgayCapNhat);
        builder.Ignore(l => l.NguoiTao);
        builder.Ignore(l => l.NguoiCapNhat);

        // Indexes
        builder.HasIndex(l => l.TenLoai)
            .HasDatabaseName("idx_dm_loai_ten_loai");
    }
}
