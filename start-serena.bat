@echo off
echo Starting Serena MCP Server for HuyPhuc Project...
echo.

REM Set environment variables
set PATH=C:\Users\<USER>\.local\bin;%PATH%

REM Change to serena directory
cd /d "C:\Users\<USER>\Documents\huyphuc\huyphuc-ivan\serena"

REM Start Serena MCP Server
echo Starting Serena with project: C:\Users\<USER>\Documents\huyphuc\huyphuc-ivan
echo Dashboard will be available at: http://127.0.0.1:24282/dashboard/index.html
echo.

uv run serena-mcp-server --project "C:\Users\<USER>\Documents\huyphuc\huyphuc-ivan" --context desktop-app --transport stdio

pause
