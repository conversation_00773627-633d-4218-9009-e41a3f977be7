namespace HuyPhuc.Domain.Enums;

public enum TrangThaiNguoiDung
{
    Active = 1,    // 'active' in database
    Inactive = 2   // 'inactive' in database
}

// Extension methods for string conversion
public static class TrangThaiNguoiDungExtensions
{
    public static string ToDbString(this TrangThaiNguoiDung trangThai)
    {
        return trangThai switch
        {
            TrangThaiNguoiDung.Active => "active",
            TrangThaiNguoiDung.Inactive => "inactive",
            _ => "active"
        };
    }

    public static TrangThaiNguoiDung FromDbString(string dbValue)
    {
        return dbValue?.ToLower() switch
        {
            "active" => TrangThaiNguoiDung.Active,
            "inactive" => TrangThaiNguoiDung.Inactive,
            _ => TrangThaiNguoiDung.Active
        };
    }
}
