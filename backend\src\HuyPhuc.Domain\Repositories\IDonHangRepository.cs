using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Repositories.Base;

namespace HuyPhuc.Domain.Repositories;

public interface IDonHangRepository : IRepository<DonHang>
{
    Task<IEnumerable<DonHang>> LayTheoNguoiDungAsync(int nguoiDungId, CancellationToken cancellationToken = default);
    Task<IEnumerable<DonHang>> LayTheoTrangThaiAsync(TrangThaiDonHang trangThai, CancellationToken cancellationToken = default);
    Task<DonHang?> LayTheoMaDonHangAsync(string maDonHang, CancellationToken cancellationToken = default);
    Task<IEnumerable<DonHang>> LayDonHangTrongKhoangThoiGianAsync(
        DateTime tuNgay, 
        DateTime denNgay, 
        CancellationToken cancellationToken = default);
    Task<decimal> TinhTongDoanhThuAsync(
        DateTime? tuNgay = null, 
        DateTime? denNgay = null, 
        CancellationToken cancellationToken = default);
    Task<int> DemDonHangTheoTrangThaiAsync(TrangThaiDonHang trangThai, CancellationToken cancellationToken = default);
}
