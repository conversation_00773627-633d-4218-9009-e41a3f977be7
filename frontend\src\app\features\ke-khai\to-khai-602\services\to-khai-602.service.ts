import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of, throwError } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';

import {
  ToKhai602,
  TaoToKhaiRequest,
  ToKhaiApiResponse,
  ToKhaiFilter,
  DanhSachToKhaiResponse,
  ToKhaiFormState,
  ValidationError,
  LaoDong,
  ThongTinKhac,
  TrangThaiToKhai,
  TaoDraftToKhaiRequest,
  TaoDraftToKhaiResponse,
  GhiNhanToKhaiResponse
} from '../models';
import { environment } from '../../../../../environments/environment';

/**
 * Service quản lý tờ khai 602
 */
@Injectable({
  providedIn: 'root'
})
export class ToKhai602Service {
  private readonly API_URL = environment.apiUrl;
  
  // State management cho form
  private readonly _formState$ = new BehaviorSubject<ToKhaiFormState>({
    thongTinKhac: {},
    danh<PERSON>ach<PERSON>aoDong: [],
    dangTai: false,
    loi: []
  });

  // State management cho danh sách tờ khai
  private readonly _danhSachToKhai$ = new BehaviorSubject<ToKhai602[]>([]);
  private readonly _toKhaiDangChon$ = new BehaviorSubject<ToKhai602 | null>(null);
  private readonly _dangTai$ = new BehaviorSubject<boolean>(false);

  // Public observables
  public readonly formState$ = this._formState$.asObservable();
  public readonly danhSachToKhai$ = this._danhSachToKhai$.asObservable();
  public readonly toKhaiDangChon$ = this._toKhaiDangChon$.asObservable();
  public readonly dangTai$ = this._dangTai$.asObservable();

  constructor(private http: HttpClient) {
    this.khoiTaoFormState();
  }

  /**
   * Khởi tạo form state ban đầu
   */
  private khoiTaoFormState(): void {
    const initialState: ToKhaiFormState = {
      thongTinKhac: {
        soSoBHXH: '1', // Default value
        ngayTao: new Date()
      },
      danhSachLaoDong: [],
      dangTai: false,
      loi: []
    };
    this._formState$.next(initialState);
  }

  /**
   * Cập nhật form state
   */
  capNhatFormState(partialState: Partial<ToKhaiFormState>): void {
    const currentState = this._formState$.value;
    const newState = { ...currentState, ...partialState };
    this._formState$.next(newState);
  }

  /**
   * Thêm lao động mới vào form
   */
  themLaoDongMoi(): void {
    const currentState = this._formState$.value;
    const sttMoi = currentState.danhSachLaoDong.length + 1;
    
    const laoDongMoi: Partial<LaoDong> = {
      id: this.taoIdTamThoi(),
      stt: sttMoi,

      // Thông tin cá nhân
      maSoBHXH: '',
      hoTen: '',
      ccns: '0',
      ngaySinh: '',
      gioiTinh: 1,
      quocTich: 'VN',
      danToc: '01',
      cmnd: '',

      // Địa chỉ thường trú
      maTinhKs: '89',
      maHuyenKs: '890',
      maXaKs: '30538',

      // Thông tin liên hệ
      dienThoaiLh: '',
      maHoGiaDinh: '',

      // Thông tin đóng BHXH
      phuongAn: 'DB',
      phuongThuc: '1',
      thangBatDau: this.layThangHienTai(),

      // Thông tin tiền
      tienLai: 0,
      tienThua: 0,
      tienTuDong: 0,
      tongTien: 0,
      tienHoTro: 0,
      mucThuNhap: 1500000,

      // Thông tin khác
      typeId: 'TM',
      isThamGiaBb: false,
      isTamHoanHD: false,

      // Thông tin lỗi/cảnh báo
      message: '',
      isError: false,
      maLoi: '',
      moTaLoi: ''
    };

    const danhSachMoi = [...currentState.danhSachLaoDong, laoDongMoi];
    this.capNhatFormState({ danhSachLaoDong: danhSachMoi });
  }

  /**
   * Xóa lao động khỏi form
   */
  xoaLaoDong(index: number): void {
    const currentState = this._formState$.value;
    const danhSachMoi = currentState.danhSachLaoDong.filter((_, i) => i !== index);
    
    // Cập nhật lại STT
    danhSachMoi.forEach((laoDong, i) => {
      laoDong.stt = i + 1;
    });

    this.capNhatFormState({ danhSachLaoDong: danhSachMoi });
  }

  /**
   * Cập nhật thông tin lao động
   */
  capNhatLaoDong(index: number, laoDong: Partial<LaoDong>): void {
    const currentState = this._formState$.value;
    const danhSachMoi = [...currentState.danhSachLaoDong];
    danhSachMoi[index] = { ...danhSachMoi[index], ...laoDong };
    
    this.capNhatFormState({ danhSachLaoDong: danhSachMoi });
  }

  /**
   * Cập nhật thông tin khác
   */
  capNhatThongTinKhac(thongTinKhac: Partial<ThongTinKhac>): void {
    const currentState = this._formState$.value;
    const thongTinMoi = { ...currentState.thongTinKhac, ...thongTinKhac };
    this.capNhatFormState({ thongTinKhac: thongTinMoi });
  }

  /**
   * Validate form trước khi submit
   */
  validateForm(): ValidationError[] {
    const state = this._formState$.value;
    const errors: ValidationError[] = [];

    // Validate thông tin khác
    if (!state.thongTinKhac.soSoBHXH) {
      errors.push({ field: 'soSoBHXH', message: 'Số sổ BHXH không được để trống' });
    }

    // Validate danh sách lao động
    if (state.danhSachLaoDong.length === 0) {
      errors.push({ field: 'danhSachLaoDong', message: 'Phải có ít nhất một lao động' });
    }

    state.danhSachLaoDong.forEach((laoDong, index) => {
      if (!laoDong.hoTen) {
        errors.push({ field: 'hoTen', message: 'Họ tên không được để trống', index });
      }
      if (!laoDong.masoBHXH) {
        errors.push({ field: 'masoBHXH', message: 'Mã số BHXH không được để trống', index });
      }
      if (!laoDong.soCCCD) {
        errors.push({ field: 'soCCCD', message: 'Số CCCD không được để trống', index });
      }
      if (!laoDong.soBienLai) {
        errors.push({ field: 'soBienLai', message: 'Số biên lai không được để trống', index });
      }
    });

    this.capNhatFormState({ loi: errors });
    return errors;
  }

  /**
   * Tạo draft tờ khai
   */
  taoDraftToKhai(request: TaoDraftToKhaiRequest): Observable<TaoDraftToKhaiResponse> {
    this._dangTai$.next(true);

    console.log('🔍 Tạo draft tờ khai với request:', request);

    return this.http.post<any>(`${this.API_URL}/ToKhai602/draft`, request)
      .pipe(
        map(response => {
          console.log('🔍 API response:', response);
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi tạo draft');
          }
        }),
        tap((result) => {
          console.log('✅ Tạo draft thành công:', result);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('❌ Lỗi khi tạo draft:', error);
          this._dangTai$.next(false);
          throw error;
        })
      );
  }

  /**
   * Tạo tờ khai mới
   */
  taoToKhai(request: TaoToKhaiRequest): Observable<ToKhaiApiResponse<number>> {
    this._dangTai$.next(true);

    // TODO: Thay thế bằng real API call
    if (!environment.production) {
      return this.mockTaoToKhai(request).pipe(
        tap(() => this._dangTai$.next(false)),
        catchError(error => {
          console.error('Lỗi khi tạo tờ khai:', error);
          this._dangTai$.next(false);
          return of({
            thanhCong: false,
            thongBao: 'Có lỗi xảy ra khi tạo tờ khai'
          });
        })
      );
    }

    return this.http.post<ToKhaiApiResponse<number>>(`${this.API_URL}/to-khai-602`, request)
      .pipe(
        tap(() => this._dangTai$.next(false)),
        catchError(error => {
          console.error('Lỗi khi tạo tờ khai:', error);
          this._dangTai$.next(false);
          return of({
            thanhCong: false,
            thongBao: 'Có lỗi xảy ra khi tạo tờ khai'
          });
        })
      );
  }

  /**
   * Lấy danh sách tờ khai
   */
  layDanhSachToKhai(filter?: ToKhaiFilter): Observable<DanhSachToKhaiResponse> {
    this._dangTai$.next(true);

    // TODO: Thay thế bằng real API call
    if (!environment.production) {
      return this.mockLayDanhSachToKhai(filter).pipe(
        tap(response => {
          this._danhSachToKhai$.next(response.danhSach);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('Lỗi khi lấy danh sách tờ khai:', error);
          this._dangTai$.next(false);
          return of({ danhSach: [], phanTrang: { trang: 1, kichThuoc: 10, tongSo: 0, tongTrang: 0 } });
        })
      );
    }

    const params = this.buildQueryParams(filter);
    return this.http.get<ToKhaiApiResponse<DanhSachToKhaiResponse>>(`${this.API_URL}/to-khai-602${params}`)
      .pipe(
        map(response => response.duLieu || { danhSach: [], phanTrang: { trang: 1, kichThuoc: 10, tongSo: 0, tongTrang: 0 } }),
        tap(response => {
          this._danhSachToKhai$.next(response.danhSach);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('Lỗi khi lấy danh sách tờ khai:', error);
          this._dangTai$.next(false);
          return of({ danhSach: [], phanTrang: { trang: 1, kichThuoc: 10, tongSo: 0, tongTrang: 0 } });
        })
      );
  }

  /**
   * Reset form về trạng thái ban đầu
   */
  resetForm(): void {
    this.khoiTaoFormState();
  }

  /**
   * Cập nhật danh sách lao động
   */
  capNhatDanhSachLaoDong(danhSach: Partial<LaoDong>[]): void {
    const currentState = this._formState$.value;
    if (currentState) {
      currentState.danhSachLaoDong = danhSach;
      this._formState$.next(currentState);
    }
  }

  /**
   * Cập nhật draft ID
   */
  capNhatDraftId(draftId: number): void {
    const currentState = this._formState$.value;
    if (currentState) {
      currentState.draftId = draftId;
      this._formState$.next(currentState);
    }
  }

  /**
   * Ghi nhận tờ khai vào database
   */
  ghiNhanToKhai(draftId: number, danhSachLaoDong: Partial<LaoDong>[]): Observable<GhiNhanToKhaiResponse> {
    const url = `${this.API_URL}/ToKhai602/ghi-nhan`;

    const payload = {
      draftId: draftId,
      danhSachLaoDong: danhSachLaoDong.map((laoDong, index) => ({
        stt: index + 1,
        maSoBHXH: laoDong.maSoBHXH,
        hoTen: laoDong.hoTen,
        ccns: laoDong.ccns,
        ngaySinh: laoDong.ngaySinh,
        gioiTinh: laoDong.gioiTinh,
        quocTich: laoDong.quocTich,
        danToc: laoDong.danToc,
        cmnd: laoDong.cmnd,
        maTinhKs: laoDong.maTinhKs,
        maHuyenKs: laoDong.maHuyenKs,
        maXaKs: laoDong.maXaKs,
        dienThoaiLh: laoDong.dienThoaiLh,
        maHoGiaDinh: laoDong.maHoGiaDinh,
        phuongAn: laoDong.phuongAn,
        phuongThuc: laoDong.phuongThuc,
        thangBatDau: laoDong.thangBatDau,
        mucThuNhap: laoDong.mucThuNhap,
        tienLai: laoDong.tienLai,
        tienThua: laoDong.tienThua,
        tienTuDong: laoDong.tienTuDong,
        tongTien: laoDong.tongTien,
        tienHoTro: laoDong.tienHoTro,
        typeId: laoDong.typeId,
        isThamGiaBb: laoDong.isThamGiaBb,
        isTamHoanHD: laoDong.isTamHoanHD,
        message: laoDong.message,
        isError: laoDong.isError,
        maLoi: laoDong.maLoi,
        moTaLoi: laoDong.moTaLoi
      }))
    };

    console.log('🚀 Gửi request ghi nhận:', payload);

    return this.http.post<any>(url, payload).pipe(
      map((response: any) => {
        // Backend trả về { success: true, data: GhiNhanToKhaiResponse, message: string }
        if (response.success && response.data) {
          return response.data as GhiNhanToKhaiResponse;
        }
        throw new Error(response.message || 'Có lỗi xảy ra khi ghi nhận');
      }),
      tap((response: GhiNhanToKhaiResponse) => console.log('📥 Response ghi nhận:', response)),
      catchError(error => {
        console.error('❌ Lỗi API ghi nhận:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Utility methods
   */
  private taoIdTamThoi(): string {
    return 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private layThangHienTai(): string {
    const now = new Date();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const year = now.getFullYear();
    return `${month}/${year}`;
  }

  private buildQueryParams(filter?: ToKhaiFilter): string {
    if (!filter) return '';
    
    const params = new URLSearchParams();
    if (filter.daiLyId) params.append('daiLyId', filter.daiLyId.toString());
    if (filter.donViId) params.append('donViId', filter.donViId.toString());
    if (filter.trangThai) params.append('trangThai', filter.trangThai);
    if (filter.tuNgay) params.append('tuNgay', filter.tuNgay.toISOString());
    if (filter.denNgay) params.append('denNgay', filter.denNgay.toISOString());
    if (filter.tuKhoa) params.append('tuKhoa', filter.tuKhoa);
    
    return params.toString() ? `?${params.toString()}` : '';
  }

  /**
   * Mock methods cho development
   */
  private mockTaoToKhai(request: TaoToKhaiRequest): Observable<ToKhaiApiResponse<number>> {
    // Simulate API delay
    return of({
      thanhCong: true,
      thongBao: 'Tạo tờ khai thành công',
      duLieu: Math.floor(Math.random() * 1000) + 1
    });
  }

  private mockLayDanhSachToKhai(filter?: ToKhaiFilter): Observable<DanhSachToKhaiResponse> {
    const mockData: ToKhai602[] = [
      {
        id: 1,
        daiLyId: 1,
        donViId: 1,
        thongTinKhac: { soSoBHXH: '1' },
        danhSachLaoDong: [],
        trangThai: TrangThaiToKhai.DangSoan,
        ngayTao: new Date(),
        nguoiTao: 'admin'
      }
    ];

    return of({
      danhSach: mockData,
      phanTrang: {
        trang: 1,
        kichThuoc: 10,
        tongSo: mockData.length,
        tongTrang: 1
      }
    });
  }
}
