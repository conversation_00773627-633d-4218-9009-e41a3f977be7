﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HuyPhuc.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddDiaChiTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Id",
                table: "chi_tiet_to_khai_602",
                newName: "id");

            migrationBuilder.CreateTable(
                name: "danh_sach_ke_khai",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ma_ke_khai = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    thu_tuc_id = table.Column<int>(type: "integer", nullable: false),
                    dai_ly_id = table.Column<int>(type: "integer", nullable: false),
                    don_vi_id = table.Column<int>(type: "integer", nullable: false),
                    so_so_bhxh = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "1"),
                    thong_tin_header = table.Column<string>(type: "jsonb", nullable: true),
                    trang_thai = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    nguoi_tao_id = table.Column<int>(type: "integer", nullable: false),
                    ngay_tao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    nguoi_phe_duyet_id = table.Column<int>(type: "integer", nullable: true),
                    ngay_phe_duyet = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ly_do_tu_choi = table.Column<string>(type: "text", nullable: true),
                    file_xml_path = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    chu_ky_so = table.Column<string>(type: "text", nullable: true),
                    digest_value = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    signature_value = table.Column<string>(type: "text", nullable: true),
                    ghi_chu = table.Column<string>(type: "text", nullable: true),
                    created = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    created_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    last_modified = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    last_modified_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_danh_sach_ke_khai", x => x.id);
                    table.ForeignKey(
                        name: "FK_danh_sach_ke_khai_dai_ly_dai_ly_id",
                        column: x => x.dai_ly_id,
                        principalTable: "dai_ly",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_danh_sach_ke_khai_danh_muc_thu_tuc_thu_tuc_id",
                        column: x => x.thu_tuc_id,
                        principalTable: "danh_muc_thu_tuc",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_danh_sach_ke_khai_don_vi_don_vi_id",
                        column: x => x.don_vi_id,
                        principalTable: "don_vi",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_danh_sach_ke_khai_nguoi_dung_nguoi_phe_duyet_id",
                        column: x => x.nguoi_phe_duyet_id,
                        principalTable: "nguoi_dung",
                        principalColumn: "id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_danh_sach_ke_khai_nguoi_dung_nguoi_tao_id",
                        column: x => x.nguoi_tao_id,
                        principalTable: "nguoi_dung",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "dm_tinh",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ma_tinh = table.Column<string>(type: "character varying(2)", maxLength: 2, nullable: false),
                    ten_tinh = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    text_display = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    NgayTao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    NgayCapNhat = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NguoiTao = table.Column<string>(type: "text", nullable: true),
                    NguoiCapNhat = table.Column<string>(type: "text", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    created_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dm_tinh", x => x.id);
                    table.UniqueConstraint("AK_dm_tinh_ma_tinh", x => x.ma_tinh);
                });

            migrationBuilder.CreateTable(
                name: "tk1_ts",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ma_so_bhxh = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ho_ten = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ccns = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true, defaultValue: "0"),
                    ngay_sinh = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    gioi_tinh = table.Column<int>(type: "integer", nullable: false),
                    quoc_tich = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, defaultValue: "VN"),
                    dan_toc = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, defaultValue: "01"),
                    cmnd = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ma_tinh_ks = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ma_huyen_ks = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ma_xa_ks = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    dien_thoai_lh = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    ma_ho_gia_dinh = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    type_id = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, defaultValue: "TM"),
                    is_tham_gia_bb = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    is_tam_hoan_hd = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    created = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    last_updated = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tk1_ts", x => x.id);
                    table.UniqueConstraint("AK_tk1_ts_ma_so_bhxh", x => x.ma_so_bhxh);
                });

            migrationBuilder.CreateTable(
                name: "dm_huyen",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ma_huyen = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    ten_huyen = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    text_display = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ma_tinh = table.Column<string>(type: "character varying(2)", maxLength: 2, nullable: false),
                    NgayTao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    NgayCapNhat = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NguoiTao = table.Column<string>(type: "text", nullable: true),
                    NguoiCapNhat = table.Column<string>(type: "text", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    created_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dm_huyen", x => x.id);
                    table.UniqueConstraint("AK_dm_huyen_ma_huyen", x => x.ma_huyen);
                    table.ForeignKey(
                        name: "FK_dm_huyen_dm_tinh_ma_tinh",
                        column: x => x.ma_tinh,
                        principalTable: "dm_tinh",
                        principalColumn: "ma_tinh",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "chi_tiet_lao_dong_ke_khai_v2",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ke_khai_id = table.Column<int>(type: "integer", nullable: false),
                    ma_so_bhxh = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    stt = table.Column<int>(type: "integer", nullable: false),
                    phuong_an = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    phuong_thuc = table.Column<string>(type: "character varying(5)", maxLength: 5, nullable: true),
                    thang_bat_dau = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    muc_thu_nhap = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    tien_lai = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    tien_thua = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    tien_tu_dong = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    tong_tien = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    tien_ho_tro = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    ty_le = table.Column<int>(type: "integer", nullable: false, defaultValue: 22),
                    so_thang = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    ngay_bien_lai = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    so_bien_lai = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ghi_chu_bien_lai = table.Column<string>(type: "text", nullable: true),
                    thong_tin_bo_sung = table.Column<string>(type: "jsonb", nullable: true),
                    ty_le_nsnn = table.Column<int>(type: "integer", nullable: false, defaultValue: 10),
                    he_so = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    ty_le_nsdp = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    tien_nsdp = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    ty_le_ho_tro_khac = table.Column<int>(type: "integer", nullable: true),
                    tien_ho_tro_khac = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    dia_chi_dang_ss = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ma_tinh_dang_ss = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ma_huyen_dang_ss = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ma_xa_dang_ss = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    ma_nhan_vien_thu = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    is_error = table.Column<bool>(type: "boolean", nullable: true, defaultValue: false),
                    ma_loi = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    mo_ta_loi = table.Column<string>(type: "text", nullable: true),
                    message = table.Column<string>(type: "text", nullable: true),
                    created = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    created_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    last_modified = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    last_modified_by = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_chi_tiet_lao_dong_ke_khai_v2", x => x.id);
                    table.ForeignKey(
                        name: "FK_chi_tiet_lao_dong_ke_khai_v2_danh_sach_ke_khai_ke_khai_id",
                        column: x => x.ke_khai_id,
                        principalTable: "danh_sach_ke_khai",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_chi_tiet_lao_dong_ke_khai_v2_tk1_ts_ma_so_bhxh",
                        column: x => x.ma_so_bhxh,
                        principalTable: "tk1_ts",
                        principalColumn: "ma_so_bhxh",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "dm_xa",
                columns: table => new
                {
                    id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ma_xa = table.Column<string>(type: "character varying(5)", maxLength: 5, nullable: false),
                    ten_xa = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    text_display = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ma_huyen = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    ma_tinh = table.Column<string>(type: "character varying(2)", maxLength: 2, nullable: false),
                    NgayTao = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    NgayCapNhat = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NguoiTao = table.Column<string>(type: "text", nullable: true),
                    NguoiCapNhat = table.Column<string>(type: "text", nullable: true),
                    created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    created_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    updated_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    updated_by = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_dm_xa", x => x.id);
                    table.ForeignKey(
                        name: "FK_dm_xa_dm_huyen_ma_huyen",
                        column: x => x.ma_huyen,
                        principalTable: "dm_huyen",
                        principalColumn: "ma_huyen",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_dm_xa_dm_tinh_ma_tinh",
                        column: x => x.ma_tinh,
                        principalTable: "dm_tinh",
                        principalColumn: "ma_tinh",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "idx_lao_dong_bo_sung_gin",
                table: "chi_tiet_lao_dong_ke_khai_v2",
                column: "thong_tin_bo_sung")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "idx_lao_dong_ke_khai_ma_so",
                table: "chi_tiet_lao_dong_ke_khai_v2",
                column: "ma_so_bhxh");

            migrationBuilder.CreateIndex(
                name: "idx_lao_dong_ke_khai_ma_so_unique",
                table: "chi_tiet_lao_dong_ke_khai_v2",
                columns: new[] { "ke_khai_id", "ma_so_bhxh" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_lao_dong_ke_khai_stt",
                table: "chi_tiet_lao_dong_ke_khai_v2",
                columns: new[] { "ke_khai_id", "stt" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_ke_khai_dai_ly_don_vi",
                table: "danh_sach_ke_khai",
                columns: new[] { "dai_ly_id", "don_vi_id" });

            migrationBuilder.CreateIndex(
                name: "idx_ke_khai_header_gin",
                table: "danh_sach_ke_khai",
                column: "thong_tin_header")
                .Annotation("Npgsql:IndexMethod", "gin");

            migrationBuilder.CreateIndex(
                name: "idx_ke_khai_ma_ke_khai",
                table: "danh_sach_ke_khai",
                column: "ma_ke_khai",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_ke_khai_ngay_tao",
                table: "danh_sach_ke_khai",
                column: "ngay_tao");

            migrationBuilder.CreateIndex(
                name: "idx_ke_khai_nguoi_tao",
                table: "danh_sach_ke_khai",
                column: "nguoi_tao_id");

            migrationBuilder.CreateIndex(
                name: "idx_ke_khai_thu_tuc_trang_thai",
                table: "danh_sach_ke_khai",
                columns: new[] { "thu_tuc_id", "trang_thai" });

            migrationBuilder.CreateIndex(
                name: "IX_danh_sach_ke_khai_don_vi_id",
                table: "danh_sach_ke_khai",
                column: "don_vi_id");

            migrationBuilder.CreateIndex(
                name: "IX_danh_sach_ke_khai_nguoi_phe_duyet_id",
                table: "danh_sach_ke_khai",
                column: "nguoi_phe_duyet_id");

            migrationBuilder.CreateIndex(
                name: "idx_dm_huyen_ma_huyen",
                table: "dm_huyen",
                column: "ma_huyen",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_dm_huyen_ma_tinh",
                table: "dm_huyen",
                column: "ma_tinh");

            migrationBuilder.CreateIndex(
                name: "idx_dm_huyen_ten_huyen",
                table: "dm_huyen",
                column: "ten_huyen");

            migrationBuilder.CreateIndex(
                name: "idx_dm_tinh_ma_tinh",
                table: "dm_tinh",
                column: "ma_tinh",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_dm_tinh_ten_tinh",
                table: "dm_tinh",
                column: "ten_tinh");

            migrationBuilder.CreateIndex(
                name: "idx_dm_xa_ma_huyen",
                table: "dm_xa",
                column: "ma_huyen");

            migrationBuilder.CreateIndex(
                name: "idx_dm_xa_ma_tinh",
                table: "dm_xa",
                column: "ma_tinh");

            migrationBuilder.CreateIndex(
                name: "idx_dm_xa_ma_xa",
                table: "dm_xa",
                column: "ma_xa",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_dm_xa_ten_xa",
                table: "dm_xa",
                column: "ten_xa");

            migrationBuilder.CreateIndex(
                name: "idx_tk1_ts_cmnd",
                table: "tk1_ts",
                column: "cmnd");

            migrationBuilder.CreateIndex(
                name: "idx_tk1_ts_ho_ten",
                table: "tk1_ts",
                column: "ho_ten");

            migrationBuilder.CreateIndex(
                name: "idx_tk1_ts_ma_so_bhxh",
                table: "tk1_ts",
                column: "ma_so_bhxh",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "fk_chi_tiet_to_khai_602_ma_so_bhxh",
                table: "chi_tiet_to_khai_602",
                column: "ma_so_bhxh",
                principalTable: "tk1_ts",
                principalColumn: "ma_so_bhxh",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_chi_tiet_to_khai_602_ma_so_bhxh",
                table: "chi_tiet_to_khai_602");

            migrationBuilder.DropTable(
                name: "chi_tiet_lao_dong_ke_khai_v2");

            migrationBuilder.DropTable(
                name: "dm_xa");

            migrationBuilder.DropTable(
                name: "danh_sach_ke_khai");

            migrationBuilder.DropTable(
                name: "tk1_ts");

            migrationBuilder.DropTable(
                name: "dm_huyen");

            migrationBuilder.DropTable(
                name: "dm_tinh");

            migrationBuilder.RenameColumn(
                name: "id",
                table: "chi_tiet_to_khai_602",
                newName: "Id");
        }
    }
}
