import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-stats-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './stats-card.html',
  styleUrl: './stats-card.scss'
})
export class StatsCardComponent {
  @Input() title!: string;
  @Input() value!: string;
  @Input() change!: string;
  @Input() trend!: 'up' | 'down' | 'neutral';
}
