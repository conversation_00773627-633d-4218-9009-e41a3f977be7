using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Enums;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.ToKhai602.Commands.XoaToKhai;

/// <summary>
/// Command xóa tờ khai 602
/// </summary>
public record XoaToKhaiCommand(int Id) : IRequest<bool>;

/// <summary>
/// Handler xử lý command xóa tờ khai 602
/// </summary>
public class XoaToKhaiCommandHandler : IRequestHandler<XoaToKhaiCommand, bool>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public XoaToKhaiCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<bool> Handle(XoaToKhaiCommand command, CancellationToken cancellationToken)
    {
        var userIdString = _currentUserService.UserId;
        
        if (userIdString == null || !int.TryParse(userIdString, out var userId))
        {
            throw new UnauthorizedAccessException("Không thể xác định người dùng hiện tại");
        }

        // Tìm tờ khai cần xóa
        var toKhai = await _context.ToKhai602
            .Include(t => t.DanhSachLaoDong)
            .FirstOrDefaultAsync(t => t.Id == command.Id, cancellationToken);

        if (toKhai == null)
        {
            throw new ArgumentException($"Không tìm thấy tờ khai với ID: {command.Id}");
        }

        // Kiểm tra quyền xóa (chỉ cho phép xóa tờ khai đang soạn)
        if (toKhai.TrangThai != TrangThaiToKhai.DangSoan)
        {
            throw new InvalidOperationException("Chỉ có thể xóa tờ khai đang ở trạng thái soạn thảo");
        }

        // Xóa danh sách lao động trước (cascade delete sẽ tự động xử lý)
        _context.LaoDongToKhai602.RemoveRange(toKhai.DanhSachLaoDong);

        // Xóa tờ khai
        _context.ToKhai602.Remove(toKhai);

        await _context.SaveChangesAsync(cancellationToken);

        return true;
    }
}
