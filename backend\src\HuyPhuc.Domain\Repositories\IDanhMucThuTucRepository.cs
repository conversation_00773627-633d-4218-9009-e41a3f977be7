using HuyPhuc.Domain.Repositories.Base;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;

namespace HuyPhuc.Domain.Repositories;

/// <summary>
/// Repository interface cho danh mục thủ tục
/// </summary>
public interface IDanhMucThuTucRepository : IRepository<DanhMucThuTuc>
{
    /// <summary>
    /// Lấy thủ tục theo mã
    /// </summary>
    Task<DanhMucThuTuc?> LayTheoMaAsync(string ma, CancellationToken cancellationToken = default);

    /// <summary>
    /// Kiểm tra mã thủ tục đã tồn tại chưa
    /// </summary>
    Task<bool> KiemTraMaTonTaiAsync(string ma, int? excludeId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Tìm kiếm thủ tục theo từ khóa
    /// </summary>
    Task<IEnumerable<DanhMucThuTuc>> TimKiemAsync(
        string? tuKhoa = null,
        LinhVucThuTuc? linhVuc = null,
        TrangThaiThuTuc? trangThai = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy danh sách thủ tục có phân trang
    /// </summary>
    Task<(IEnumerable<DanhMucThuTuc> Items, int TotalCount)> LayDanhSachPhanTrangAsync(
        int trang = 1,
        int kichThuocTrang = 10,
        string? tuKhoa = null,
        LinhVucThuTuc? linhVuc = null,
        TrangThaiThuTuc? trangThai = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy thủ tục theo lĩnh vực
    /// </summary>
    Task<IEnumerable<DanhMucThuTuc>> LayTheoLinhVucAsync(
        LinhVucThuTuc linhVuc,
        TrangThaiThuTuc? trangThai = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy thủ tục đang hoạt động
    /// </summary>
    Task<IEnumerable<DanhMucThuTuc>> LayThuTucHoatDongAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy thủ tục có hiệu lực (đã đến ngày áp dụng)
    /// </summary>
    Task<IEnumerable<DanhMucThuTuc>> LayThuTucCoHieuLucAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Đếm số lượng thủ tục theo trạng thái
    /// </summary>
    Task<Dictionary<TrangThaiThuTuc, int>> DemTheoTrangThaiAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Đếm số lượng thủ tục theo lĩnh vực
    /// </summary>
    Task<Dictionary<LinhVucThuTuc, int>> DemTheoLinhVucAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy thủ tục được tạo gần đây
    /// </summary>
    Task<IEnumerable<DanhMucThuTuc>> LayThuTucMoiNhatAsync(
        int soLuong = 10,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy thủ tục được cập nhật gần đây
    /// </summary>
    Task<IEnumerable<DanhMucThuTuc>> LayThuTucCapNhatGanDayAsync(
        int soLuong = 10,
        CancellationToken cancellationToken = default);
}
