-- <PERSON><PERSON>t để cập nhật EF Migrations History sau khi xóa cột thong_tin_bo_sung

-- 1. <PERSON><PERSON>m tra migration hiện có
SELECT "MigrationId", "ProductVersion" 
FROM "__EFMigrationsHistory" 
ORDER BY "MigrationId" DESC;

-- 2. Thêm migration entry cho việc xóa cột thong_tin_bo_sung
-- (Ch<PERSON><PERSON> lệnh này trong psql hoặc pgAdmin)
-- INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion") 
-- VALUES ('20250729120000_RemoveThongTinBoSungColumn', '8.0.11');

-- 3. <PERSON><PERSON><PERSON> nhận cột đã được xóa
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'chi_tiet_lao_dong_ke_khai_v2' 
  AND column_name = 'thong_tin_bo_sung';

-- 4. <PERSON><PERSON><PERSON> tra index đã được xóa
SELECT indexname 
FROM pg_indexes 
WHERE tablename = 'chi_tiet_lao_dong_ke_khai_v2' 
  AND indexname LIKE '%bo_sung%';

-- Kết quả mong đợi:
-- - Không có cột thong_tin_bo_sung
-- - Không có index liên quan
-- - Migration entry đã được thêm vào __EFMigrationsHistory
