using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

public class VaiTroQuyenConfiguration : IEntityTypeConfiguration<VaiTroQuyen>
{
    public void Configure(EntityTypeBuilder<VaiTroQuyen> builder)
    {
        builder.ToTable("vai_tro_quyen");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).HasColumnName("id");

        builder.Property(x => x.VaiTroId)
            .IsRequired()
            .HasColumnName("vai_tro_id");

        builder.Property(x => x.QuyenId)
            .IsRequired()
            .HasColumnName("quyen_id");

        builder.Property(x => x.NgayGan)
            .IsRequired()
            .HasColumnName("ngay_gan");

        builder.Property(x => x.TrangThaiHoatDong)
            .IsRequired()
            .HasColumnName("trang_thai_hoat_dong")
            .HasDefaultValue(true);

        builder.Property(x => x.GhiChu)
            .HasColumnName("ghi_chu")
            .HasMaxLength(500);

        // Audit fields
        builder.Property(x => x.NgayTao)
            .IsRequired()
            .HasColumnName("ngay_tao");

        builder.Property(x => x.NgayCapNhat)
            .HasColumnName("ngay_cap_nhat");

        builder.Property(x => x.NguoiTao)
            .HasColumnName("nguoi_tao")
            .HasMaxLength(50);

        builder.Property(x => x.NguoiCapNhat)
            .HasColumnName("nguoi_cap_nhat")
            .HasMaxLength(50);

        // Indexes
        builder.HasIndex(x => new { x.VaiTroId, x.QuyenId })
            .IsUnique()
            .HasDatabaseName("ix_vai_tro_quyen_composite");

        builder.HasIndex(x => x.TrangThaiHoatDong)
            .HasDatabaseName("ix_vai_tro_quyen_trang_thai_hoat_dong");

        // Relationships
        builder.HasOne(x => x.VaiTro)
            .WithMany(x => x.DanhSachQuyen)
            .HasForeignKey(x => x.VaiTroId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(x => x.Quyen)
            .WithMany(x => x.DanhSachVaiTro)
            .HasForeignKey(x => x.QuyenId)
            .OnDelete(DeleteBehavior.Restrict);

        // Ignore domain events
        builder.Ignore(x => x.DomainEvents);
    }
}
