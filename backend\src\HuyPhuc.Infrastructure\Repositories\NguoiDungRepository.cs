using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace HuyPhuc.Infrastructure.Repositories;

public class NguoiDungRepository : INguoiDungRepository
{
    private readonly IApplicationDbContext _context;
    private readonly ISupabaseApiClient _supabaseClient;

    public NguoiDungRepository(IApplicationDbContext context, ISupabaseApiClient supabaseClient)
    {
        _context = context;
        _supabaseClient = supabaseClient;
    }

    public async Task<NguoiDung?> LayTheoIdAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _supabaseClient.LayNguoiDungTheoIdAsync(id);
    }

    public async Task<NguoiDung?> LayTheoIdVoiVaiTroAsync(int id, CancellationToken cancellationToken = default)
    {
        return await _context.NguoiDung
            .Include(x => x.DanhSachVaiTro.Where(nv =>
                nv.TrangThaiHoatDong &&
                (!nv.NgayHetHan.HasValue || nv.NgayHetHan.Value > DateTime.UtcNow)))
                .ThenInclude(nv => nv.VaiTro)
                    .ThenInclude(vt => vt.DanhSachQuyen.Where(vq => vq.TrangThaiHoatDong))
                        .ThenInclude(vq => vq.Quyen)
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);
    }

    public async Task<NguoiDung?> LayTheoEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        return await _context.NguoiDung
            .FirstOrDefaultAsync(x => x.Email.Value.ToLower() == email.ToLower(), cancellationToken);
    }

    public async Task<NguoiDung?> LayTheoEmailVoiVaiTroAsync(string email, CancellationToken cancellationToken = default)
    {
        return await _context.NguoiDung
            .Include(x => x.DanhSachVaiTro.Where(nv =>
                nv.TrangThaiHoatDong &&
                (!nv.NgayHetHan.HasValue || nv.NgayHetHan.Value > DateTime.UtcNow)))
                .ThenInclude(nv => nv.VaiTro)
                    .ThenInclude(vt => vt.DanhSachQuyen.Where(vq => vq.TrangThaiHoatDong))
                        .ThenInclude(vq => vq.Quyen)
            .FirstOrDefaultAsync(x => x.Email.Value.ToLower() == email.ToLower(), cancellationToken);
    }

    public async Task<NguoiDung?> LayTheoUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        return await _context.NguoiDung
            .FirstOrDefaultAsync(x => x.Username.ToLower() == username.ToLower(), cancellationToken);
    }

    public async Task<NguoiDung?> LayTheoUsernameVoiVaiTroAsync(string username, CancellationToken cancellationToken = default)
    {
        return await _context.NguoiDung
            .Include(x => x.DanhSachVaiTro.Where(nv =>
                nv.TrangThaiHoatDong &&
                (!nv.NgayHetHan.HasValue || nv.NgayHetHan.Value > DateTime.UtcNow)))
                .ThenInclude(nv => nv.VaiTro)
                    .ThenInclude(vt => vt.DanhSachQuyen.Where(vq => vq.TrangThaiHoatDong))
                        .ThenInclude(vq => vq.Quyen)
            .FirstOrDefaultAsync(x => x.Username.ToLower() == username.ToLower(), cancellationToken);
    }

    public async Task<bool> EmailDaTonTaiAsync(string email, int? loaiTruId = null, CancellationToken cancellationToken = default)
    {
        var user = await LayTheoEmailAsync(email, cancellationToken);
        if (user == null) return false;

        return loaiTruId.HasValue ? user.Id != loaiTruId.Value : true;
    }

    public async Task<bool> UsernameDaTonTaiAsync(string username, int? loaiTruId = null, CancellationToken cancellationToken = default)
    {
        var user = await LayTheoUsernameAsync(username, cancellationToken);
        if (user == null) return false;

        return loaiTruId.HasValue ? user.Id != loaiTruId.Value : true;
    }

    public async Task<IEnumerable<NguoiDung>> TimKiemTheoTenAsync(string tuKhoa, CancellationToken cancellationToken = default)
    {
        var users = await _supabaseClient.LayDanhSachNguoiDungAsync(0, 1000);
        return users.Where(x => x.HoTen.Contains(tuKhoa, StringComparison.OrdinalIgnoreCase))
                   .OrderBy(x => x.HoTen);
    }

    public async Task<IEnumerable<NguoiDung>> LayTheoTrangThaiAsync(int trangThai, CancellationToken cancellationToken = default)
    {
        var users = await _supabaseClient.LayDanhSachNguoiDungAsync(0, 1000);
        return users.Where(x => (int)x.TrangThai == trangThai)
                   .OrderByDescending(x => x.NgayTao);
    }

    // IRepository<NguoiDung> implementation
    public async Task<IEnumerable<NguoiDung>> TimKiemAsync(Expression<Func<NguoiDung, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var users = await _supabaseClient.LayDanhSachNguoiDungAsync(0, 1000);
        return users.Where(predicate.Compile());
    }

    public async Task<NguoiDung?> TimKiemDauTienAsync(Expression<Func<NguoiDung, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var users = await _supabaseClient.LayDanhSachNguoiDungAsync(0, 1000);
        return users.FirstOrDefault(predicate.Compile());
    }

    public async Task<bool> TonTaiAsync(Expression<Func<NguoiDung, bool>> predicate, CancellationToken cancellationToken = default)
    {
        var users = await _supabaseClient.LayDanhSachNguoiDungAsync(0, 1000);
        return users.Any(predicate.Compile());
    }

    public async Task<int> DemAsync(Expression<Func<NguoiDung, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        var users = await _supabaseClient.LayDanhSachNguoiDungAsync(0, 1000);
        return predicate != null ? users.Count(predicate.Compile()) : users.Count();
    }

    public async Task ThemAsync(NguoiDung entity, CancellationToken cancellationToken = default)
    {
        await _supabaseClient.TaoNguoiDungAsync(entity);
    }

    public async Task ThemNhieuAsync(IEnumerable<NguoiDung> entities, CancellationToken cancellationToken = default)
    {
        foreach (var entity in entities)
        {
            await _supabaseClient.TaoNguoiDungAsync(entity);
        }
    }

    public void CapNhat(NguoiDung entity)
    {
        _context.NguoiDung.Update(entity);
    }

    public void CapNhatNhieu(IEnumerable<NguoiDung> entities)
    {
        foreach (var entity in entities)
        {
            CapNhat(entity);
        }
    }

    public void Xoa(NguoiDung entity)
    {
        _ = Task.Run(async () => await _supabaseClient.XoaNguoiDungAsync(entity.Id));
    }

    public void XoaNhieu(IEnumerable<NguoiDung> entities)
    {
        foreach (var entity in entities)
        {
            Xoa(entity);
        }
    }

    public async Task<IEnumerable<NguoiDung>> LayPhanTrangAsync(
        int trang, int kichThuocTrang,
        Expression<Func<NguoiDung, bool>>? predicate = null,
        Expression<Func<NguoiDung, object>>? orderBy = null,
        bool tangDan = true,
        CancellationToken cancellationToken = default)
    {
        var offset = (trang - 1) * kichThuocTrang;
        var items = await _supabaseClient.LayDanhSachNguoiDungAsync(offset, kichThuocTrang);

        // Apply filtering if predicate is provided
        if (predicate != null)
        {
            items = items.Where(predicate.Compile());
        }

        return items;
    }

    public async Task<IEnumerable<NguoiDung>> LayTatCaAsync(CancellationToken cancellationToken = default)
    {
        return await _supabaseClient.LayDanhSachNguoiDungAsync(0, 1000);
    }
}
