using HuyPhuc.Domain.Entities.Base;

namespace HuyPhuc.Domain.Entities;

/// <summary>
/// Entity lao động trong tờ khai 602 - Chi tiết thông tin có thể thay đổi
/// </summary>
public class LaoDongToKhai602 : BaseEntity, IAuditableEntity
{
    /// <summary>
    /// ID tờ khai 602
    /// </summary>
    public int ToKhai602Id { get; set; }

    /// <summary>
    /// Mã số BHXH (liên kết với bảng tk1_ts)
    /// </summary>
    public string MaSoBHXH { get; set; } = string.Empty;

    /// <summary>
    /// Số thứ tự trong tờ khai
    /// </summary>
    public int Stt { get; set; }

    /// <summary>
    /// Phương án đóng (DB/DT/TN)
    /// </summary>
    public string PhuongAn { get; set; } = string.Empty;

    /// <summary>
    /// Phương thức đóng (1/2/3)
    /// </summary>
    public string PhuongThuc { get; set; } = string.Empty;

    /// <summary>
    /// Tháng bắt đầu (format: MM/yyyy)
    /// </summary>
    public string ThangBatDau { get; set; } = string.Empty;

    /// <summary>
    /// Mức thu nhập
    /// </summary>
    public decimal MucThuNhap { get; set; }

    /// <summary>
    /// Tiền lãi
    /// </summary>
    public decimal TienLai { get; set; } = 0;

    /// <summary>
    /// Tiền thừa
    /// </summary>
    public decimal TienThua { get; set; } = 0;

    /// <summary>
    /// Tiền tự đóng
    /// </summary>
    public decimal TienTuDong { get; set; } = 0;

    /// <summary>
    /// Tổng tiền
    /// </summary>
    public decimal TongTien { get; set; } = 0;

    /// <summary>
    /// NSNN hỗ trợ
    /// </summary>
    public decimal TienHoTro { get; set; } = 0;

    /// <summary>
    /// Loại NSNN
    /// </summary>
    public string? LoaiNsnn { get; set; }

    /// <summary>
    /// Thông báo từ API
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Có lỗi hay không
    /// </summary>
    public bool IsError { get; set; } = false;

    /// <summary>
    /// Mã lỗi
    /// </summary>
    public string? MaLoi { get; set; }

    /// <summary>
    /// Mô tả lỗi
    /// </summary>
    public string? MoTaLoi { get; set; }

    // Navigation properties
    /// <summary>
    /// Tờ khai 602
    /// </summary>
    public virtual ToKhai602 ToKhai602 { get; set; } = null!;

    /// <summary>
    /// Thông tin tĩnh lao động
    /// </summary>
    public virtual Tk1Ts Tk1Ts { get; set; } = null!;

    // IAuditableEntity implementation
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
    public string? NguoiTao { get; set; }
    public string? NguoiCapNhat { get; set; }
}
