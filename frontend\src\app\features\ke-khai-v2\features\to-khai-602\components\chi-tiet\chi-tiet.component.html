<div class="chi-tiet-to-khai-602">
  <!-- Loading state -->
  <div *ngIf="dangTai" class="flex justify-center items-center py-12">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Error state -->
  <div *ngIf="loi && !dangTai" class="p-6">
    <div class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Có lỗi xảy ra</h3>
          <p class="mt-1 text-sm text-red-700">{{ loi }}</p>
        </div>
      </div>
    </div>
    <div class="mt-4">
      <button
        (click)="quayLai()"
        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200"
      >
        Quay lại
      </button>
    </div>
  </div>

  <!-- Content -->
  <div *ngIf="toKhai && !dangTai" class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Chi tiết Tờ khai 602</h1>
        <p class="text-gray-600 mt-1">Mã tờ khai: <span class="font-medium">{{ toKhai.ma || 'Chưa có mã' }}</span></p>
      </div>
      <div class="flex space-x-3">
        <button
          (click)="quayLai()"
          class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200"
        >
          Quay lại
        </button>
      </div>
    </div>

    <div class="bg-white border border-gray-200 rounded-lg p-6">

      <!-- Tab navigation -->
      <div class="border-b border-gray-200 mb-6">
        <nav class="-mb-px flex space-x-8">
          <button
            (click)="chuyenTab('form')"
            [class]="tabHienTai === 'form' 
              ? 'border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'"
          >
            Thêm lao động
          </button>
          <button
            (click)="chuyenTab('danh-sach')"
            [class]="tabHienTai === 'danh-sach' 
              ? 'border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'"
          >
            Danh sách lao động
            <span *ngIf="toKhai?.danhSachLaoDong?.length" 
                  class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium">
              {{ toKhai.danhSachLaoDong.length }}
            </span>
          </button>
        </nav>
      </div>

      <!-- Tab content -->
      <div [ngSwitch]="tabHienTai">
        <!-- Tab Form thêm lao động -->
        <div *ngSwitchCase="'form'">
          <app-lao-dong-form
            [laoDong]="laoDongDangChinhSua"
            [isEdit]="!!laoDongDangChinhSua"
            (luuLaoDong)="onLuuLaoDong($event)"
            (huyForm)="onHuyFormLaoDong()"
          ></app-lao-dong-form>
        </div>

        <!-- Tab Danh sách lao động -->
        <div *ngSwitchCase="'danh-sach'">
          <!-- Empty state -->
          <div *ngIf="!toKhai.danhSachLaoDong || toKhai.danhSachLaoDong.length === 0"
               class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Chưa có lao động nào</h3>
            <p class="mt-1 text-sm text-gray-500">Chuyển sang tab "Thêm lao động" để bắt đầu thêm lao động.</p>
            <div class="mt-6">
              <button
                (click)="chuyenTab('form')"
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200"
              >
                + Thêm lao động
              </button>
            </div>
          </div>

          <!-- Bảng danh sách lao động -->
          <div *ngIf="toKhai.danhSachLaoDong && toKhai.danhSachLaoDong.length > 0">
            <div class="flex justify-between items-center mb-4">
              <p class="text-sm text-gray-600">
                Tổng số lao động: <span class="font-medium">{{ toKhai.danhSachLaoDong.length }}</span>
              </p>
              <button
                (click)="chuyenTab('form')"
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium transition-colors duration-200"
              >
                + Thêm lao động
              </button>
            </div>
            <app-lao-dong-table
              [danhSachLaoDong]="toKhai.danhSachLaoDong"
              (chinhSuaLaoDong)="onChinhSuaLaoDong($event)"
              (xoaLaoDong)="onXoaLaoDong($event)"
            ></app-lao-dong-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
