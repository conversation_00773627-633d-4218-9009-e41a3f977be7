namespace HuyPhuc.Application.Common.Interfaces;

/// <summary>
/// Interface cho Unit of Work pattern
/// </summary>
public interface IUnitOfWork
{
    /// <summary>
    /// Lưu tất cả thay đổi vào database
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Số lượng entities đã được lưu</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lưu tất cả thay đổi vào database (synchronous)
    /// </summary>
    /// <returns>Số lượng entities đã được lưu</returns>
    int SaveChanges();

    /// <summary>
    /// Bắt đầu transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Transaction</returns>
    Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Bắt đầu transaction (synchronous)
    /// </summary>
    /// <returns>Transaction</returns>
    IDbTransaction BeginTransaction();
}

/// <summary>
/// Interface cho database transaction
/// </summary>
public interface IDbTransaction : IDisposable
{
    /// <summary>
    /// Commit transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task CommitAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Commit transaction (synchronous)
    /// </summary>
    void Commit();

    /// <summary>
    /// Rollback transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    Task RollbackAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Rollback transaction (synchronous)
    /// </summary>
    void Rollback();
}
