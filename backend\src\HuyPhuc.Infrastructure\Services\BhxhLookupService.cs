using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using HuyPhuc.Application.Common.Configurations;
using HuyPhuc.Application.Features.BHXH.DTOs;
using HuyPhuc.Application.Features.BHXH.Interfaces;

namespace HuyPhuc.Infrastructure.Services;

/// <summary>
/// Service tra cứu thông tin BHXH từ VNPost API
/// </summary>
public class BhxhLookupService : IBhxhLookupService
{
    private readonly HttpClient _httpClient;
    private readonly VnPostApiConfiguration _config;
    private readonly ILogger<BhxhLookupService> _logger;
    private readonly IVnPostAuthService _authService;

    public BhxhLookupService(
        HttpClient httpClient,
        IOptions<VnPostApiConfiguration> config,
        ILogger<BhxhLookupService> logger,
        IVnPostAuthService authService)
    {
        _httpClient = httpClient;
        _config = config.Value;
        _logger = logger;
        _authService = authService;

        // Configure HttpClient
        _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
    }

    public async Task<BhxhLookupResponseDto> LookupBhxhInfoAsync(string maSoBHXH, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Bắt đầu tra cứu thông tin BHXH cho mã số: {MaSoBHXH}", maSoBHXH);

            // Check if we have a valid token before making the request
            var isTokenExpired = _authService.IsTokenExpired();
            _logger.LogInformation("🔍 Checking VNPost token before BHXH lookup: IsExpired={IsExpired}", isTokenExpired);

            if (isTokenExpired)
            {
                _logger.LogWarning("🔴 VNPost access token đã hết hạn, trả về 401");
                return new BhxhLookupResponseDto
                {
                    Success = false,
                    Message = "Token VNPost đã hết hạn. Vui lòng đăng nhập lại.",
                    Status = 401
                };
            }

            // Validate input
            if (!IsValidBhxhCode(maSoBHXH))
            {
                return new BhxhLookupResponseDto
                {
                    Success = false,
                    Message = "Mã số BHXH không hợp lệ. Mã số BHXH phải có đúng 10 chữ số.",
                    Status = 400
                };
            }

            // Prepare request
            var requestDto = new VnPostBhxhRequestDto
            {
                Code = "",
                Text = "",
                MaSoBHXH = maSoBHXH
            };

            var jsonContent = JsonSerializer.Serialize(requestDto, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            // Generate authentication headers like VNPost frontend
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var authToken = GenerateAuthToken(timestamp);

            // Prepare headers
            var request = new HttpRequestMessage(HttpMethod.Post, _config.FullBhxhLookupUrl)
            {
                Content = content
            };

            request.Headers.Add("Timestamp", timestamp.ToString());
            request.Headers.Add("Authorization", $"Bearer {authToken}");
            request.Headers.Add("Accept", "application/json, text/plain, */*");

            // Send request
            _logger.LogDebug("Gửi request đến VNPost API: {Url}", _config.FullBhxhLookupUrl);
            
            var response = await _httpClient.SendAsync(request, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            _logger.LogDebug("Nhận response từ VNPost API. Status: {StatusCode}, Content: {Content}",
                response.StatusCode, responseContent);

            if (response.IsSuccessStatusCode)
            {
                // Debug: Parse raw JSON để xem structure thực tế
                try
                {
                    using var document = JsonDocument.Parse(responseContent);
                    var root = document.RootElement;

                    if (root.TryGetProperty("data", out var dataElement))
                    {
                        _logger.LogInformation("🔍 Raw JSON data properties: {Properties}",
                            string.Join(", ", dataElement.EnumerateObject().Select(p => $"{p.Name}={p.Value}")));

                        if (dataElement.TryGetProperty("gioiTinh", out var gioiTinhElement))
                        {
                            _logger.LogInformation("🔍 Raw gioiTinh value: {Value} (Kind: {Kind})",
                                gioiTinhElement.ToString(), gioiTinhElement.ValueKind);
                        }
                        else
                        {
                            _logger.LogWarning("⚠️ Không tìm thấy field 'gioiTinh' trong response");
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi parse raw JSON để debug");
                }

                var result = JsonSerializer.Deserialize<BhxhLookupResponseDto>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    PropertyNameCaseInsensitive = true
                });

                if (result != null)
                {
                    _logger.LogInformation("Tra cứu BHXH thành công cho mã số: {MaSoBHXH}", maSoBHXH);
                    _logger.LogInformation("🔍 Debug giới tính từ VNPost API: {GioiTinh} (Type: {Type})",
                        result.Data.GioiTinh, result.Data.GioiTinh.GetType().Name);
                    return result;
                }
            }

            // Handle error response
            _logger.LogWarning("VNPost API trả về lỗi. Status: {StatusCode}, Content: {Content}",
                response.StatusCode, responseContent);

            // 406 Not Acceptable từ VNPost có nghĩa là token hết hạn
            if (response.StatusCode == HttpStatusCode.NotAcceptable ||
                response.StatusCode == HttpStatusCode.Unauthorized)
            {
                return new BhxhLookupResponseDto
                {
                    Success = false,
                    Message = "Token VNPost đã hết hạn. Vui lòng đăng nhập lại.",
                    Status = 401 // Return 401 to indicate token expired
                };
            }

            return new BhxhLookupResponseDto
            {
                Success = false,
                Message = $"Không thể tra cứu thông tin BHXH. HTTP Status: {response.StatusCode}",
                Status = (int)response.StatusCode
            };
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _logger.LogError(ex, "Timeout khi tra cứu BHXH cho mã số: {MaSoBHXH}", maSoBHXH);
            return new BhxhLookupResponseDto
            {
                Success = false,
                Message = "Timeout khi tra cứu thông tin BHXH. Vui lòng thử lại sau.",
                Status = 408
            };
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Lỗi network khi tra cứu BHXH cho mã số: {MaSoBHXH}", maSoBHXH);
            return new BhxhLookupResponseDto
            {
                Success = false,
                Message = "Lỗi kết nối mạng. Vui lòng kiểm tra kết nối và thử lại.",
                Status = 500
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi không xác định khi tra cứu BHXH cho mã số: {MaSoBHXH}", maSoBHXH);
            return new BhxhLookupResponseDto
            {
                Success = false,
                Message = "Có lỗi xảy ra khi tra cứu thông tin BHXH. Vui lòng thử lại sau.",
                Status = 500
            };
        }
    }

    public bool IsValidBhxhCode(string maSoBHXH)
    {
        if (string.IsNullOrWhiteSpace(maSoBHXH))
            return false;

        // Mã BHXH phải có đúng 10 chữ số
        return Regex.IsMatch(maSoBHXH, @"^\d{10}$");
    }

    /// <summary>
    /// Generate authentication token like VNPost frontend
    /// Based on: const x = I.SHA256(p.c.secretId + T + p.c.secretKey).toString(I.enc.Hex)
    /// const v = btoa(`${p.c.secretId}:${x}:${this.authService.accessToken}`)
    /// </summary>
    private string GenerateAuthToken(long timestamp)
    {
        try
        {
            // Create hash: SHA256(secretId + timestamp + secretKey)
            var hashInput = $"{_config.SecretId}{timestamp}{_config.SecretKey}";
            var hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(hashInput));
            var hashHex = Convert.ToHexString(hashBytes).ToLowerInvariant();

            // Create token: base64(secretId:hash:accessToken)
            var currentAccessToken = _authService.GetCurrentAccessToken();
            var tokenInput = $"{_config.SecretId}:{hashHex}:{currentAccessToken}";
            var tokenBytes = Encoding.UTF8.GetBytes(tokenInput);
            var base64Token = Convert.ToBase64String(tokenBytes);

            _logger.LogDebug("Generated auth token for timestamp: {Timestamp}", timestamp);
            return base64Token;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating auth token");
            throw;
        }
    }
}
