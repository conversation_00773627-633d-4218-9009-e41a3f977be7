{"ConnectionStrings": {"DefaultConnection": "Host=************;Port=5454;Database=ctyhuyphuc;Username=postgres;Password=************;SSL Mode=Prefer;Trust Server Certificate=true"}, "JwtSettings": {"SecretKey": "HuyPhuc-JWT-Secret-Key-2024-Very-Long-And-Secure-Key-For-Production", "Issuer": "HuyPhuc.Api", "Audience": "HuyPhuc.Client", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 7}, "Supabase": {"ApiUrl": "https://iflhpowkcbptcplankaz.supabase.co", "ApiKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlmbGhwb3drY2JwdGNwbGFua2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0NzAyMzIsImV4cCI6MjA2MzA0NjIzMn0.kBgZ8fESy0vx0ZgX1WRszlXCMcCHYF4Jh4TzcJh1OK4"}, "EmailSettings": {"SmtpHost": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "your-app-password", "FromEmail": "<EMAIL>", "FromName": "HuyPhuc System"}, "VnPostApi": {"BaseUrl": "https://ssm.vnpost.vn", "BhxhLookupEndpoint": "/tracuu/connect/tracuu/thongtinbhxhtnforkekhai", "SecretId": "SSM_OAUTH1", "SecretKey": "s6Q-Vnyt!hF&%h]y_r", "AccessToken": "eyJhyGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc8NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiODg0MDAwX3hhX3RsaV9waHVvY2x0IiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoidXNlciIsInN1YiI6IjEwMDkxNyIsInNpZCI6ImY2bEdGbUZXc1kxWVRadDF0djhQeGZ5cWFFMXhWMGstSi1jRDQ1anVxcnMiLCJuYW1lIjoiTMOqIFRo4buLIFBoxrDhu5tjIiwibmlja25hbWUiOiI4ODQwMDBfeGFfdGxpX3BodW9jbHQiLCJjbGllbnRfaWQiOiJZamcyTldVd01XRXRORFZtWlMwME1UZGhMVGc1TTJNdE56ZGtabUUzTmpVNE56VXoiLCJtYW5nTHVvaSI6Ijc2MjU1IiwiZG9uVmlDb25nVGFjIjoixJBp4buDbSB0aHUgeMOjIFTDom4gTOG7o2kiLCJjaHVjRGFuaCI6IkPhu5luZyB0w6FjIHZpw6puIHRodSIsImVtYWlsIjoibmd1eWVudGFuZHVuZzI3MTE4OUBnbWFpbC5jb20iLCJzb0RpZW5UaG9haSI6IiIsImlzU3VwZXJBZG1pbiI6IkZhbHNlIiwiaXNDYXMiOiJGYWxzZSIsIm5iZiI6MTc1MzY3NjY4MCwiZXhwIjoxNzUzNjk0NjgwLCJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjUwMDAiLCJhdWQiOiJodHRwOi8vbG9jYWxob3N0OjQyMDAifQ.JWNDrF_18W_kt291cKh_K1QYkMyAivtxaUM1nyPkBrY", "AuthToken": "Bearer U1NNX09BVVRIMTpiMjk2OTliNGM0OTk1N2FjODExMTIyMmQxM2E0MTkwZDJmZWMyMTNkZGMyYTBkODM0MGVmNzNmMDVmYzY3NjBhOmV5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpvZEhSd09pOHZjMk5vWlcxaGN5NTRiV3h6YjJGd0xtOXlaeTkzY3k4eU1EQTFMekExTDJsa1pXNTBhWFI1TDJOc1lXbHRjeTl1WVcxbElqb2lPRGcwTURBd1gzaGhYM1JzYVY5d2FIVnZZMngwSWl3aWFIUjBjRG92TDNOamFHVnRZWE11YldsamNtOXpiMlowTG1OdmJTOTNjeTh5TURBNEx6QTJMMmxrWlc1MGFYUjVMMk5zWVdsdGN5OXliMnhsSWpvaWRYTmxjaUlzSW5OMVlpSTZJakV3TURreE55SXNJbk5wWkNJNkltNWlSMGd4WVdaTWFWTlhNRlJhVWxkRFIyY3pSbU5STmpOYVoyOXdka2RJTldwSmJFTk1SalpOYjNjaUxDSnVZVzFsSWpvaVRNT3FJRlJvNGJ1TElGQm94ckRodTV0aklpd2libWxqYTI1aGJXVWlPaUk0T0RRd01EQmZlR0ZmZEd4cFgzQm9kVzlqYkhRaUxDSmpiR2xsYm5SZmFXUWlPaUpaYW1jeVRsZFZkMDFYUlhST1JGWnRXbE13TUUxVVpHaE1WR2MxVFRKTmRFNTZaR3RhYlVVelRtcFZORTU2VlhvaUxDSnRZVzVuVEhWdmFTSTZJamMyTWpVMUlpd2laRzl1Vm1sRGIyNW5WR0ZqSWpvaXhKQnA0YnVEYlNCMGFIVWdlTU9qSUZURG9tNGdUT0c3bzJraUxDSmphSFZqUkdGdWFDSTZJa1BodTVsdVp5QjB3NkZqSUhacHc2cHVJSFJvZFNJc0ltVnRZV2xzSWpvaWJtZDFlV1Z1ZEdGdVpIVnVaekkzTVRFNE9VQm5iV0ZwYkM1amIyMGlMQ0p6YjBScFpXNVVhRzloYVNJNklpSXNJbWx6VTNWd1pYSkJaRzFwYmlJNklrWmhiSE5sSWl3aWFYTkRZWE1pT2lKR1lXeHpaU0lzSW01aVppSTZNVGMxTXpZM016RXhPQ3dpWlhod0lqb3hOelV6TmpreE1URTRMQ0pwYzNNaU9pSm9kSFJ3T2k4dmJHOWpZV3hvYjNOME9qVXdNREFpTENKaGRXUWlPaUpvZEhSd09pOHZiRzlqWVd4b2IzTjBPalF5TURBaWZRLmY5eWtpY2JWVFNhdEZOSGM0eV85QUo2S2l2VElkR3dTczBnNHYweU1EWVU=", "TimeoutSeconds": 30, "Referer": "https://ssm.vnpost.vn/qldv/ke-khai/buu-cuc-ke-khai/to-khai-bhxh", "UserAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*"}