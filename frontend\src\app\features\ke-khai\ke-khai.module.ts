import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { KeKhaiRoutingModule } from './ke-khai-routing.module';

/**
 * Feature module ch<PERSON>h cho tất cả các loại kê khai
 * <PERSON><PERSON> gồm: T<PERSON> khai 602, Tờ khai 603, v.v.
 */
@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    KeKhaiRoutingModule
  ],
  providers: []
})
export class KeKhaiModule { }
