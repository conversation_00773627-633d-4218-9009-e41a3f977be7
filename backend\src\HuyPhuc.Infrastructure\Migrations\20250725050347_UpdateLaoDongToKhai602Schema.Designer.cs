﻿// <auto-generated />
using System;
using HuyPhuc.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace HuyPhuc.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250725050347_UpdateLaoDongToKhai602Schema")]
    partial class UpdateLaoDongToKhai602Schema
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("HuyPhuc.Domain.Entities.ChiTietDonHang", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("DonHangId")
                        .HasColumnType("integer");

                    b.Property<decimal>("GiaBan")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SanPhamId")
                        .HasColumnType("integer");

                    b.Property<int>("SoLuong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<decimal>("ThanhTien")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.HasKey("Id");

                    b.HasIndex("DonHangId")
                        .HasDatabaseName("IX_ChiTietDonHang_DonHangId");

                    b.HasIndex("SanPhamId")
                        .HasDatabaseName("IX_ChiTietDonHang_SanPhamId");

                    b.HasIndex("DonHangId", "SanPhamId")
                        .IsUnique()
                        .HasDatabaseName("IX_ChiTietDonHang_DonHangId_SanPhamId");

                    b.ToTable("ChiTietDonHang", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DaiLy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("DiaChi")
                        .HasColumnType("text")
                        .HasColumnName("dia_chi");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("email");

                    b.Property<string>("GhiChu")
                        .HasColumnType("text")
                        .HasColumnName("ghi_chu");

                    b.Property<string>("MaDaiLy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("ma_dai_ly");

                    b.Property<string>("MaSoThue")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("ma_so_thue");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<DateTime>("NgayTao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("updated_by");

                    b.Property<string>("NguoiDaiDien")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("nguoi_dai_dien");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("created_by");

                    b.Property<string>("SoDienThoai")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("so_dien_thoai");

                    b.Property<string>("TenDaiLy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("ten_dai_ly");

                    b.Property<bool>("TrangThaiHoatDong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("trang_thai_hoat_dong");

                    b.HasKey("Id");

                    b.HasIndex("MaDaiLy")
                        .IsUnique()
                        .HasDatabaseName("idx_dai_ly_ma_dai_ly");

                    b.HasIndex("TrangThaiHoatDong")
                        .HasDatabaseName("idx_dai_ly_trang_thai");

                    b.ToTable("dai_ly", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DanhMucThuTuc", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CanCuPhapLy")
                        .HasColumnType("TEXT")
                        .HasColumnName("can_cu_phap_ly");

                    b.Property<string>("CoQuanThucHien")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("co_quan_thuc_hien");

                    b.Property<int>("LinhVuc")
                        .HasColumnType("integer")
                        .HasColumnName("linh_vuc");

                    b.Property<string>("Ma")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("ma");

                    b.Property<string>("MoTa")
                        .HasColumnType("TEXT")
                        .HasColumnName("mo_ta");

                    b.Property<DateTime?>("NgayApDung")
                        .HasColumnType("DATE")
                        .HasColumnName("ngay_ap_dung");

                    b.Property<DateTime?>("NgayCapNhat")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime>("NgayTao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("updated_by");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("created_by");

                    b.Property<decimal?>("PhiThucHien")
                        .HasColumnType("DECIMAL(18,2)")
                        .HasColumnName("phi_thuc_hien");

                    b.Property<string>("Ten")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("ten");

                    b.Property<string>("TenLinhVuc")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("ten_linh_vuc");

                    b.Property<int?>("ThoiGianXuLy")
                        .HasColumnType("integer")
                        .HasColumnName("thoi_gian_xu_ly");

                    b.Property<int>("TrangThai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1)
                        .HasColumnName("trang_thai");

                    b.HasKey("Id");

                    b.HasIndex("LinhVuc")
                        .HasDatabaseName("idx_danh_muc_thu_tuc_linh_vuc");

                    b.HasIndex("Ma")
                        .IsUnique()
                        .HasDatabaseName("idx_danh_muc_thu_tuc_ma");

                    b.HasIndex("NgayCapNhat")
                        .HasDatabaseName("idx_danh_muc_thu_tuc_updated");

                    b.HasIndex("NgayTao")
                        .HasDatabaseName("idx_danh_muc_thu_tuc_created");

                    b.HasIndex("TrangThai")
                        .HasDatabaseName("idx_danh_muc_thu_tuc_trang_thai");

                    b.HasIndex("LinhVuc", "TrangThai")
                        .HasDatabaseName("idx_danh_muc_thu_tuc_linh_vuc_trang_thai");

                    b.HasIndex("TrangThai", "NgayApDung")
                        .HasDatabaseName("idx_danh_muc_thu_tuc_trang_thai_ngay_ap_dung");

                    b.HasIndex("Ma", "Ten", "TenLinhVuc")
                        .HasDatabaseName("idx_danh_muc_thu_tuc_search");

                    b.ToTable("danh_muc_thu_tuc", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DonHang", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("MaDonHang")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("NgayDatHang")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("NgayGiaoHang")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("NguoiDungId")
                        .HasColumnType("integer");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("PhiVanChuyen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("TongGiaTri")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("TongThanhToan")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<int>("TrangThai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.HasKey("Id");

                    b.HasIndex("MaDonHang")
                        .IsUnique()
                        .HasDatabaseName("IX_DonHang_MaDonHang");

                    b.HasIndex("NgayDatHang")
                        .HasDatabaseName("IX_DonHang_NgayDatHang");

                    b.HasIndex("NguoiDungId")
                        .HasDatabaseName("IX_DonHang_NguoiDungId");

                    b.HasIndex("TrangThai")
                        .HasDatabaseName("IX_DonHang_TrangThai");

                    b.ToTable("DonHang", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DonVi", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("DaiLyId")
                        .HasColumnType("integer")
                        .HasColumnName("dai_ly_id");

                    b.Property<string>("DiaChi")
                        .HasColumnType("text")
                        .HasColumnName("dia_chi");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("email");

                    b.Property<string>("GhiChu")
                        .HasColumnType("text")
                        .HasColumnName("ghi_chu");

                    b.Property<string>("MaDonVi")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("ma_don_vi");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<DateTime>("NgayTao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("updated_by");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("created_by");

                    b.Property<string>("SoDienThoai")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("so_dien_thoai");

                    b.Property<string>("TenDonVi")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("ten_don_vi");

                    b.Property<bool>("TrangThaiHoatDong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("trang_thai_hoat_dong");

                    b.HasKey("Id");

                    b.HasIndex("DaiLyId")
                        .HasDatabaseName("idx_don_vi_dai_ly_id");

                    b.HasIndex("MaDonVi")
                        .HasDatabaseName("idx_don_vi_ma_don_vi");

                    b.HasIndex("TrangThaiHoatDong")
                        .HasDatabaseName("idx_don_vi_trang_thai");

                    b.HasIndex("MaDonVi", "DaiLyId")
                        .IsUnique()
                        .HasDatabaseName("uk_don_vi_ma_dai_ly");

                    b.ToTable("don_vi", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.LaoDongToKhai602", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsError")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_error");

                    b.Property<string>("MaLoi")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("ma_loi");

                    b.Property<string>("MaSoBHXH")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasColumnName("ma_so_bhxh");

                    b.Property<string>("Message")
                        .HasColumnType("text")
                        .HasColumnName("message");

                    b.Property<string>("MoTaLoi")
                        .HasColumnType("text")
                        .HasColumnName("mo_ta_loi");

                    b.Property<decimal>("MucThuNhap")
                        .HasColumnType("decimal(15,2)")
                        .HasColumnName("muc_thu_nhap");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("last_modified_by");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("created_by");

                    b.Property<string>("PhuongAn")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("phuong_an");

                    b.Property<string>("PhuongThuc")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("phuong_thuc");

                    b.Property<int>("Stt")
                        .HasColumnType("integer")
                        .HasColumnName("stt");

                    b.Property<string>("ThangBatDau")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("thang_bat_dau");

                    b.Property<decimal>("TienHoTro")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(15,2)")
                        .HasDefaultValue(0m)
                        .HasColumnName("tien_ho_tro");

                    b.Property<decimal>("TienLai")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(15,2)")
                        .HasDefaultValue(0m)
                        .HasColumnName("tien_lai");

                    b.Property<decimal>("TienThua")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(15,2)")
                        .HasDefaultValue(0m)
                        .HasColumnName("tien_thua");

                    b.Property<decimal>("TienTuDong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(15,2)")
                        .HasDefaultValue(0m)
                        .HasColumnName("tien_tu_dong");

                    b.Property<int>("ToKhai602Id")
                        .HasColumnType("integer")
                        .HasColumnName("to_khai_602_id");

                    b.Property<decimal>("TongTien")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(15,2)")
                        .HasDefaultValue(0m)
                        .HasColumnName("tong_tien");

                    b.HasKey("Id");

                    b.HasIndex("MaSoBHXH")
                        .HasDatabaseName("idx_chi_tiet_to_khai_602_ma_so_bhxh");

                    b.HasIndex("ToKhai602Id")
                        .HasDatabaseName("idx_chi_tiet_to_khai_602_to_khai_id");

                    b.HasIndex("ToKhai602Id", "MaSoBHXH")
                        .IsUnique()
                        .HasDatabaseName("ix_chi_tiet_to_khai_602_unique");

                    b.ToTable("chi_tiet_to_khai_602", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.NguoiDung", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AvatarUrl")
                        .HasColumnType("text")
                        .HasColumnName("avatar_url");

                    b.Property<int?>("DaiLyId")
                        .HasColumnType("integer")
                        .HasColumnName("dai_ly_id");

                    b.Property<string>("DiaChi")
                        .HasColumnType("text")
                        .HasColumnName("dia_chi");

                    b.Property<string>("GioiTinh")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasColumnName("gioi_tinh");

                    b.Property<string>("HoTen")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("ho_ten");

                    b.Property<DateTime?>("LastLogin")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_login");

                    b.Property<string>("MaNhanVien")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("ma_nhan_vien");

                    b.Property<string>("MatKhau")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("mat_khau");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<DateTime?>("NgaySinh")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_sinh");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("updated_by");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("created_by");

                    b.Property<string>("TrangThai")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text")
                        .HasDefaultValue("active")
                        .HasColumnName("trang_thai");

                    b.Property<string>("Username")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("username");

                    b.HasKey("Id");

                    b.HasIndex("DaiLyId");

                    b.ToTable("nguoi_dung", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.NguoiDungVaiTro", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("ghi_chu");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_cap_nhat");

                    b.Property<DateTime>("NgayGan")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_gan");

                    b.Property<DateTime?>("NgayHetHan")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_het_han");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_tao");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nguoi_cap_nhat");

                    b.Property<int>("NguoiDungId")
                        .HasColumnType("integer")
                        .HasColumnName("nguoi_dung_id");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nguoi_tao");

                    b.Property<bool>("TrangThaiHoatDong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("trang_thai_hoat_dong");

                    b.Property<int>("VaiTroId")
                        .HasColumnType("integer")
                        .HasColumnName("vai_tro_id");

                    b.HasKey("Id");

                    b.HasIndex("NgayHetHan")
                        .HasDatabaseName("ix_nguoi_dung_vai_tro_ngay_het_han");

                    b.HasIndex("TrangThaiHoatDong")
                        .HasDatabaseName("ix_nguoi_dung_vai_tro_trang_thai_hoat_dong");

                    b.HasIndex("VaiTroId");

                    b.HasIndex("NguoiDungId", "VaiTroId")
                        .HasDatabaseName("ix_nguoi_dung_vai_tro_composite");

                    b.ToTable("nguoi_dung_vai_tro", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.Quyen", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("LaQuyenHeThong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("la_quyen_he_thong");

                    b.Property<string>("MaQuyen")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("ma_quyen");

                    b.Property<string>("MoTa")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("mo_ta");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_cap_nhat");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_tao");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nguoi_cap_nhat");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nguoi_tao");

                    b.Property<string>("NhomQuyen")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nhom_quyen");

                    b.Property<string>("TenQuyen")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("ten_quyen");

                    b.Property<bool>("TrangThaiHoatDong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("trang_thai_hoat_dong");

                    b.HasKey("Id");

                    b.HasIndex("MaQuyen")
                        .IsUnique()
                        .HasDatabaseName("ix_quyen_ma_quyen");

                    b.HasIndex("NhomQuyen")
                        .HasDatabaseName("ix_quyen_nhom_quyen");

                    b.HasIndex("TenQuyen")
                        .HasDatabaseName("ix_quyen_ten_quyen");

                    b.HasIndex("TrangThaiHoatDong")
                        .HasDatabaseName("ix_quyen_trang_thai_hoat_dong");

                    b.ToTable("quyen", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("DaThuHoi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("da_thu_hoi");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)")
                        .HasColumnName("ip_address");

                    b.Property<string>("LyDoThuHoi")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("ly_do_thu_hoi");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("updated_by");

                    b.Property<int>("NguoiDungId")
                        .HasColumnType("integer")
                        .HasColumnName("nguoi_dung_id");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("created_by");

                    b.Property<DateTime>("ThoiGianHetHan")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("thoi_gian_het_han");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("token");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("user_agent");

                    b.HasKey("Id");

                    b.HasIndex("DaThuHoi")
                        .HasDatabaseName("ix_refresh_token_da_thu_hoi");

                    b.HasIndex("NguoiDungId")
                        .HasDatabaseName("ix_refresh_token_nguoi_dung_id");

                    b.HasIndex("ThoiGianHetHan")
                        .HasDatabaseName("ix_refresh_token_thoi_gian_het_han");

                    b.HasIndex("Token")
                        .IsUnique()
                        .HasDatabaseName("ix_refresh_token_token");

                    b.HasIndex("NguoiDungId", "DaThuHoi", "ThoiGianHetHan")
                        .HasDatabaseName("ix_refresh_token_composite");

                    b.ToTable("refresh_token", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.SanPham", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("DangKinhDoanh")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<decimal>("GiaBan")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("GiaGoc")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("HinhAnh")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("LoaiSanPham")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(99);

                    b.Property<string>("MoTa")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("SoLuongTon")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("TenSanPham")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("DangKinhDoanh")
                        .HasDatabaseName("IX_SanPham_DangKinhDoanh");

                    b.HasIndex("LoaiSanPham")
                        .HasDatabaseName("IX_SanPham_LoaiSanPham");

                    b.HasIndex("TenSanPham")
                        .HasDatabaseName("IX_SanPham_TenSanPham");

                    b.ToTable("SanPham", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.ToKhai602", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("DaiLyId")
                        .HasColumnType("integer")
                        .HasColumnName("dai_ly_id");

                    b.Property<int>("DonViId")
                        .HasColumnType("integer")
                        .HasColumnName("don_vi_id");

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("ghi_chu");

                    b.Property<string>("IAuditableEntity.NguoiCapNhat")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("last_modified_by");

                    b.Property<string>("IAuditableEntity.NguoiTao")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("created_by");

                    b.Property<string>("LyDoTuChoi")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("ly_do_tu_choi");

                    b.Property<string>("MaToKhai")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("ma_to_khai");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_modified");

                    b.Property<DateTime?>("NgayPheDuyet")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_phe_duyet");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_tao");

                    b.Property<int?>("NguoiPheDuyetId")
                        .HasColumnType("integer")
                        .HasColumnName("nguoi_phe_duyet_id");

                    b.Property<int>("NguoiTaoId")
                        .HasColumnType("integer")
                        .HasColumnName("nguoi_tao_id");

                    b.Property<string>("SoSoBHXH")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("so_so_bhxh");

                    b.Property<int>("TrangThai")
                        .HasColumnType("integer")
                        .HasColumnName("trang_thai");

                    b.HasKey("Id");

                    b.HasIndex("DaiLyId");

                    b.HasIndex("DonViId");

                    b.HasIndex("MaToKhai")
                        .IsUnique();

                    b.HasIndex("NgayTao");

                    b.HasIndex("NguoiPheDuyetId");

                    b.HasIndex("NguoiTaoId");

                    b.HasIndex("TrangThai");

                    b.ToTable("to_khai_602", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.VaiTro", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("LaVaiTroHeThong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("la_vai_tro_he_thong");

                    b.Property<string>("MoTa")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("mo_ta");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_cap_nhat");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_tao");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nguoi_cap_nhat");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nguoi_tao");

                    b.Property<string>("TenVaiTro")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("ten_vai_tro");

                    b.Property<bool>("TrangThaiHoatDong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("trang_thai_hoat_dong");

                    b.HasKey("Id");

                    b.HasIndex("TenVaiTro")
                        .IsUnique()
                        .HasDatabaseName("ix_vai_tro_ten_vai_tro");

                    b.HasIndex("TrangThaiHoatDong")
                        .HasDatabaseName("ix_vai_tro_trang_thai_hoat_dong");

                    b.ToTable("vai_tro", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.VaiTroQuyen", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("GhiChu")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("ghi_chu");

                    b.Property<DateTime?>("NgayCapNhat")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_cap_nhat");

                    b.Property<DateTime>("NgayGan")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_gan");

                    b.Property<DateTime>("NgayTao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("ngay_tao");

                    b.Property<string>("NguoiCapNhat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nguoi_cap_nhat");

                    b.Property<string>("NguoiTao")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("nguoi_tao");

                    b.Property<int>("QuyenId")
                        .HasColumnType("integer")
                        .HasColumnName("quyen_id");

                    b.Property<bool>("TrangThaiHoatDong")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true)
                        .HasColumnName("trang_thai_hoat_dong");

                    b.Property<int>("VaiTroId")
                        .HasColumnType("integer")
                        .HasColumnName("vai_tro_id");

                    b.HasKey("Id");

                    b.HasIndex("QuyenId");

                    b.HasIndex("TrangThaiHoatDong")
                        .HasDatabaseName("ix_vai_tro_quyen_trang_thai_hoat_dong");

                    b.HasIndex("VaiTroId", "QuyenId")
                        .IsUnique()
                        .HasDatabaseName("ix_vai_tro_quyen_composite");

                    b.ToTable("vai_tro_quyen", (string)null);
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.ChiTietDonHang", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.DonHang", "DonHang")
                        .WithMany("DanhSachChiTiet")
                        .HasForeignKey("DonHangId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HuyPhuc.Domain.Entities.SanPham", "SanPham")
                        .WithMany("DanhSachChiTietDonHang")
                        .HasForeignKey("SanPhamId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DonHang");

                    b.Navigation("SanPham");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DonHang", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.NguoiDung", "NguoiDung")
                        .WithMany("DanhSachDonHang")
                        .HasForeignKey("NguoiDungId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("NguoiDung");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DonVi", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.DaiLy", "DaiLy")
                        .WithMany("DanhSachDonVi")
                        .HasForeignKey("DaiLyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DaiLy");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.LaoDongToKhai602", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.ToKhai602", "ToKhai602")
                        .WithMany("DanhSachLaoDong")
                        .HasForeignKey("ToKhai602Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_chi_tiet_to_khai_602");

                    b.Navigation("ToKhai602");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.NguoiDung", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.DaiLy", "DaiLy")
                        .WithMany("DanhSachNguoiDung")
                        .HasForeignKey("DaiLyId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("HuyPhuc.Domain.ValueObjects.Email", "Email", b1 =>
                        {
                            b1.Property<int>("NguoiDungId")
                                .HasColumnType("integer");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)")
                                .HasColumnName("email");

                            b1.HasKey("NguoiDungId");

                            b1.ToTable("nguoi_dung");

                            b1.WithOwner()
                                .HasForeignKey("NguoiDungId");
                        });

                    b.OwnsOne("HuyPhuc.Domain.ValueObjects.SoDienThoai", "SoDienThoai", b1 =>
                        {
                            b1.Property<int>("NguoiDungId")
                                .HasColumnType("integer");

                            b1.Property<string>("Value")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("so_dien_thoai");

                            b1.HasKey("NguoiDungId");

                            b1.ToTable("nguoi_dung");

                            b1.WithOwner()
                                .HasForeignKey("NguoiDungId");
                        });

                    b.Navigation("DaiLy");

                    b.Navigation("Email")
                        .IsRequired();

                    b.Navigation("SoDienThoai");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.NguoiDungVaiTro", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.NguoiDung", "NguoiDung")
                        .WithMany("DanhSachVaiTro")
                        .HasForeignKey("NguoiDungId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HuyPhuc.Domain.Entities.VaiTro", "VaiTro")
                        .WithMany("DanhSachNguoiDung")
                        .HasForeignKey("VaiTroId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("NguoiDung");

                    b.Navigation("VaiTro");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.RefreshToken", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.NguoiDung", "NguoiDung")
                        .WithMany()
                        .HasForeignKey("NguoiDungId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_refresh_token_nguoi_dung");

                    b.Navigation("NguoiDung");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.ToKhai602", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.DaiLy", "DaiLy")
                        .WithMany()
                        .HasForeignKey("DaiLyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_to_khai_602_dai_ly");

                    b.HasOne("HuyPhuc.Domain.Entities.DonVi", "DonVi")
                        .WithMany()
                        .HasForeignKey("DonViId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_to_khai_602_don_vi");

                    b.HasOne("HuyPhuc.Domain.Entities.NguoiDung", "NguoiPheDuyet")
                        .WithMany()
                        .HasForeignKey("NguoiPheDuyetId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("fk_to_khai_602_nguoi_phe_duyet");

                    b.HasOne("HuyPhuc.Domain.Entities.NguoiDung", "NguoiTao")
                        .WithMany()
                        .HasForeignKey("NguoiTaoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_to_khai_602_nguoi_tao");

                    b.Navigation("DaiLy");

                    b.Navigation("DonVi");

                    b.Navigation("NguoiPheDuyet");

                    b.Navigation("NguoiTao");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.VaiTroQuyen", b =>
                {
                    b.HasOne("HuyPhuc.Domain.Entities.Quyen", "Quyen")
                        .WithMany("DanhSachVaiTro")
                        .HasForeignKey("QuyenId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HuyPhuc.Domain.Entities.VaiTro", "VaiTro")
                        .WithMany("DanhSachQuyen")
                        .HasForeignKey("VaiTroId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Quyen");

                    b.Navigation("VaiTro");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DaiLy", b =>
                {
                    b.Navigation("DanhSachDonVi");

                    b.Navigation("DanhSachNguoiDung");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.DonHang", b =>
                {
                    b.Navigation("DanhSachChiTiet");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.NguoiDung", b =>
                {
                    b.Navigation("DanhSachDonHang");

                    b.Navigation("DanhSachVaiTro");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.Quyen", b =>
                {
                    b.Navigation("DanhSachVaiTro");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.SanPham", b =>
                {
                    b.Navigation("DanhSachChiTietDonHang");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.ToKhai602", b =>
                {
                    b.Navigation("DanhSachLaoDong");
                });

            modelBuilder.Entity("HuyPhuc.Domain.Entities.VaiTro", b =>
                {
                    b.Navigation("DanhSachNguoiDung");

                    b.Navigation("DanhSachQuyen");
                });
#pragma warning restore 612, 618
        }
    }
}
