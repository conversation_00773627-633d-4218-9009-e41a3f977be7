import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

// Components
import { LogoComponent } from './components/logo/logo.component';
import { HeaderComponent } from './components/layout/header.component';
import { MainLayoutComponent } from './components/layout/main-layout.component';
import { SidebarComponent } from './components/layout/sidebar.component';
import { TabsComponent } from './components/tabs/tabs.component';
import { ModalXacNhanComponent } from './components/modal/modal-xac-nhan/modal-xac-nhan.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,

    // Standalone components
    LogoComponent,
    HeaderComponent,
    MainLayoutComponent,
    SidebarComponent,
    TabsComponent,
    ModalXacNhanComponent
  ],
  exports: [
    // Angular modules
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,

    // Components
    LogoComponent,
    HeaderComponent,
    MainLayoutComponent,
    SidebarComponent,
    TabsComponent,
    ModalXacNhanComponent
  ]
})
export class SharedModule { }
