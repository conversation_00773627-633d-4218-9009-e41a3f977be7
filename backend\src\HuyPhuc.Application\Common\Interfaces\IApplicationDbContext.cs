using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;

namespace HuyPhuc.Application.Common.Interfaces;

public interface IApplicationDbContext
{
    DbSet<NguoiDung> NguoiDung { get; }
    DbSet<SanPham> San<PERSON><PERSON> { get; }
    DbSet<DonHang> DonHang { get; }
    DbSet<ChiTietDonHang> ChiTietDonHang { get; }
    DbSet<RefreshToken> RefreshToken { get; }
    DbSet<DanhMucThuTuc> DanhMucThuTuc { get; }

    // Đại lý và đơn vị entities
    DbSet<DaiLy> DaiLy { get; }
    DbSet<DonVi> DonVi { get; }

    // Role-based Authorization entities
    DbSet<VaiTro> VaiTro { get; }
    DbSet<Quyen> Quyen { get; }
    DbSet<NguoiDungVaiTro> NguoiDungVaiTro { get; }
    DbSet<VaiTroQuyen> VaiTroQuyen { get; }

    // Tờ khai 602 entities
    DbSet<ToKhai602> ToKhai602 { get; }
    DbSet<LaoDongToKhai602> LaoDongToKhai602 { get; }
    DbSet<Tk1Ts> Tk1Ts { get; }

    // Danh mục địa chỉ entities
    DbSet<DmTinh> DmTinh { get; }
    DbSet<DmHuyen> DmHuyen { get; }
    DbSet<DmXa> DmXa { get; }
    DbSet<DmDanToc> DmDanToc { get; }
    DbSet<DmLoai> DmLoai { get; }

    // Hệ thống kê khai tổng quát entities
    DbSet<KeKhai> DanhSachKeKhai { get; }
    DbSet<ChiTietLaoDongKeKhaiV2> ChiTietLaoDongKeKhaiV2 { get; }

    // Database operations
    DatabaseFacade Database { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
