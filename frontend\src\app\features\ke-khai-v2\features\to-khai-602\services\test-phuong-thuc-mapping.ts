/**
 * Test file để kiểm tra mapping phương thức đóng
 * Chạy file này để đảm bảo mapping hoạt động đúng
 */

export class TestPhuongThucMapping {
  /**
   * Chuyển đổi phương thức từ frontend format sang API format
   */
  private static mapPhuongThucToApi(frontendPhuongThuc: string): string {
    const mapping: { [key: string]: string } = {
      '1-thang': '1',
      '3-thang': '3',
      '6-thang': '6',
      '12-thang': '12',
      'nam-sau': 'nam-sau',
      'nam-thieu': 'nam-thieu'
    };
    
    return mapping[frontendPhuongThuc] || frontendPhuongThuc;
  }

  /**
   * Test các trường hợp mapping
   */
  static runTests(): void {
    console.log('🧪 === TEST PHƯƠNG THỨC MAPPING ===');
    
    const testCases = [
      { input: '1-thang', expected: '1' },
      { input: '3-thang', expected: '3' },
      { input: '6-thang', expected: '6' },
      { input: '12-thang', expected: '12' },
      { input: 'nam-sau', expected: 'nam-sau' },
      { input: 'nam-thieu', expected: 'nam-thieu' },
      { input: 'unknown', expected: 'unknown' },
      { input: '', expected: '' }
    ];

    let passedTests = 0;
    let totalTests = testCases.length;

    testCases.forEach((testCase, index) => {
      const result = this.mapPhuongThucToApi(testCase.input);
      const passed = result === testCase.expected;
      
      console.log(`Test ${index + 1}: ${passed ? '✅' : '❌'}`);
      console.log(`  Input: "${testCase.input}"`);
      console.log(`  Expected: "${testCase.expected}"`);
      console.log(`  Got: "${result}"`);
      
      if (passed) {
        passedTests++;
      } else {
        console.log(`  ❌ FAILED!`);
      }
      console.log('');
    });

    console.log(`📊 RESULTS: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! Mapping is working correctly.');
    } else {
      console.log('💥 Some tests failed! Please check the mapping logic.');
    }

    // Test database constraint
    console.log('\n🔍 === DATABASE CONSTRAINT TEST ===');
    const longValues = [
      '1-thang',      // 7 chars - should fail
      '3-thang',      // 7 chars - should fail  
      '6-thang',      // 7 chars - should fail
      '12-thang',     // 8 chars - should fail
      'nam-sau',      // 7 chars - should fail
      'nam-thieu'     // 9 chars - should fail
    ];

    longValues.forEach(value => {
      const mapped = this.mapPhuongThucToApi(value);
      const isValid = mapped.length <= 5;
      console.log(`"${value}" -> "${mapped}" (${mapped.length} chars) ${isValid ? '✅' : '❌'}`);
    });
  }
}

// Uncomment to run tests
// TestPhuongThucMapping.runTests();
