using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Common.Models;
using HuyPhuc.Application.Features.XacThuc.Models;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Repositories;
using HuyPhuc.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace HuyPhuc.Application.Features.XacThuc.Commands.DangNhap;

/// <summary>
/// Handler xử lý đăng nhập người dùng
/// </summary>
public class DangNhapCommandHandler : IRequestHandler<DangNhapCommand, Result<DangNhapResponse>>
{
    private readonly INguoiDungRepository _nguoiDungRepository;
    private readonly IRefreshTokenRepository _refreshTokenRepository;
    private readonly IPasswordHashingService _passwordHashingService;
    private readonly IJwtService _jwtService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<DangNhapCommandHandler> _logger;

    public DangNhapCommandHandler(
        INguoiDungRepository nguoiDungRepository,
        IRefreshTokenRepository refreshTokenRepository,
        IPasswordHashingService passwordHashingService,
        IJwtService jwtService,
        IUnitOfWork unitOfWork,
        ILogger<DangNhapCommandHandler> logger)
    {
        _nguoiDungRepository = nguoiDungRepository;
        _refreshTokenRepository = refreshTokenRepository;
        _passwordHashingService = passwordHashingService;
        _jwtService = jwtService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Result<DangNhapResponse>> Handle(DangNhapCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Bắt đầu xử lý đăng nhập cho username: {Username}", request.Username);

            // Tìm người dùng theo username và load thông tin vai trò
            var nguoiDung = await _nguoiDungRepository.LayTheoUsernameVoiVaiTroAsync(request.Username, cancellationToken);
            if (nguoiDung == null)
            {
                _logger.LogWarning("Không tìm thấy người dùng với username: {Username}", request.Username);
                return Result<DangNhapResponse>.Failure("Username hoặc mật khẩu không đúng");
            }

            // Kiểm tra trạng thái người dùng
            if (!nguoiDung.CoTheThucHienGiaoDich())
            {
                _logger.LogWarning("Người dùng {UserId} đã bị vô hiệu hóa", nguoiDung.Id);
                return Result<DangNhapResponse>.Failure("Tài khoản đã bị vô hiệu hóa");
            }

            // Xác minh mật khẩu
            if (!_passwordHashingService.XacMinhMatKhau(request.MatKhau, nguoiDung.MatKhau))
            {
                _logger.LogWarning("Mật khẩu không đúng cho người dùng: {Username}", request.Username);
                return Result<DangNhapResponse>.Failure("Username hoặc mật khẩu không đúng");
            }

            // Cập nhật thời gian đăng nhập cuối
            nguoiDung.CapNhatLastLogin();
            _nguoiDungRepository.CapNhat(nguoiDung);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Tạo JWT tokens
            var accessToken = _jwtService.TaoAccessToken(nguoiDung);
            var refreshTokenString = _jwtService.TaoRefreshToken();
            var thoiGianHetHan = _jwtService.LayThoiGianHetHan(accessToken) ?? DateTime.UtcNow.AddHours(1);

            // Lưu refresh token vào database
            var refreshTokenEntity = RefreshToken.TaoMoi(
                token: refreshTokenString,
                nguoiDungId: nguoiDung.Id,
                thoiGianHetHan: DateTime.UtcNow.AddDays(7), // Refresh token hết hạn sau 7 ngày
                ipAddress: null, // TODO: Lấy IP từ HttpContext
                userAgent: null  // TODO: Lấy User-Agent từ HttpContext
            );

            await _refreshTokenRepository.ThemAsync(refreshTokenEntity, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            var response = new DangNhapResponse
            {
                NguoiDung = AuthNguoiDungDto.TuEntity(nguoiDung),
                AccessToken = accessToken,
                RefreshToken = refreshTokenString,
                ThoiGianHetHan = thoiGianHetHan
            };

            _logger.LogInformation("Đăng nhập thành công cho người dùng: {UserId}", nguoiDung.Id);
            return Result<DangNhapResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Lỗi khi xử lý đăng nhập cho username: {Username}", request.Username);
            return Result<DangNhapResponse>.Failure("Có lỗi xảy ra khi đăng nhập. Vui lòng thử lại.");
        }
    }
}
