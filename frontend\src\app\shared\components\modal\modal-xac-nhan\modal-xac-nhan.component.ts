import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface ModalXacNhanData {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'info' | 'warning' | 'danger' | 'success';
  icon?: string;
}

/**
 * Component modal xác nhận chung cho toàn bộ ứng dụng
 * Thay thế browser confirm mặc định
 */
@Component({
  selector: 'app-modal-xac-nhan',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './modal-xac-nhan.component.html',
  styleUrls: ['./modal-xac-nhan.component.scss']
})
export class ModalXacNhanComponent implements OnInit {
  @Input() isOpen = false;
  @Input() data: ModalXacNhanData | null = null;
  
  @Output() confirm = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<void>();
  @Output() close = new EventEmitter<void>();

  ngOnInit(): void {
    // Set default values if not provided
    if (this.data) {
      this.data.confirmText = this.data.confirmText || 'Xác nhận';
      this.data.cancelText = this.data.cancelText || 'Hủy bỏ';
      this.data.type = this.data.type || 'info';
    }
  }

  /**
   * Xử lý khi người dùng xác nhận
   */
  onConfirm(): void {
    this.confirm.emit(true);
    this.close.emit();
  }

  /**
   * Xử lý khi người dùng hủy bỏ
   */
  onCancel(): void {
    this.cancel.emit();
    this.confirm.emit(false);
    this.close.emit();
  }

  /**
   * Xử lý khi đóng modal (click overlay)
   */
  onCloseModal(): void {
    this.onCancel();
  }

  /**
   * Lấy icon dựa trên type
   */
  get modalIcon(): string {
    if (this.data?.icon) {
      return this.data.icon;
    }

    switch (this.data?.type) {
      case 'warning':
        return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z';
      case 'danger':
        return 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z';
      case 'success':
        return 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z';
      case 'info':
      default:
        return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
    }
  }

  /**
   * Lấy màu sắc dựa trên type
   */
  get modalColorClass(): string {
    switch (this.data?.type) {
      case 'warning':
        return 'text-yellow-600';
      case 'danger':
        return 'text-red-600';
      case 'success':
        return 'text-green-600';
      case 'info':
      default:
        return 'text-blue-600';
    }
  }

  /**
   * Lấy class cho nút xác nhận
   */
  get confirmButtonClass(): string {
    switch (this.data?.type) {
      case 'warning':
        return 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500';
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 focus:ring-red-500';
      case 'success':
        return 'bg-green-600 hover:bg-green-700 focus:ring-green-500';
      case 'info':
      default:
        return 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500';
    }
  }

  /**
   * Kiểm tra modal có đang mở không
   */
  get isModalOpen(): boolean {
    return this.isOpen && !!this.data;
  }
}
