using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Enums;
using HuyPhuc.Domain.Repositories.Base;

namespace HuyPhuc.Domain.Repositories;

public interface ISanPhamRepository : IRepository<SanPham>
{
    Task<IEnumerable<SanPham>> TimKiemTheoTenAsync(string tuKhoa, CancellationToken cancellationToken = default);
    Task<IEnumerable<SanPham>> LayTheoLoaiAsync(LoaiSanPham loaiSanPham, CancellationToken cancellationToken = default);
    Task<IEnumerable<SanPham>> LaySanPhamDangKinhDoanhAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<SanPham>> LaySanPhamSapHetHangAsync(int soLuongToiThieu = 10, CancellationToken cancellationToken = default);
    Task<IEnumerable<SanPham>> LaySanPhamBanChayAsync(int soLuong = 10, CancellationToken cancellationToken = default);
}
