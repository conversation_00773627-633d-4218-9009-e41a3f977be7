using System.ComponentModel.DataAnnotations;

namespace HuyPhuc.Application.DTOs.ToKhai602;

/// <summary>
/// Request DTO cho việc tạo draft tờ khai 602
/// </summary>
public class TaoDraftToKhaiRequest
{
    /// <summary>
    /// ID đại lý
    /// </summary>
    [Required(ErrorMessage = "Vui lòng chọn đại lý")]
    public int DaiLyId { get; set; }

    /// <summary>
    /// ID đơn vị
    /// </summary>
    [Required(ErrorMessage = "Vui lòng chọn đơn vị")]
    public int DonViId { get; set; }

    /// <summary>
    /// Số sổ BHXH
    /// </summary>
    [Required(ErrorMessage = "Vui lòng nhập số sổ BHXH")]
    [StringLength(50, ErrorMessage = "Số sổ BHXH không được vượt quá 50 ký tự")]
    public string SoSoBHXH { get; set; } = string.Empty;

    /// <summary>
    /// Ghi chú
    /// </summary>
    [StringLength(500, ErrorMessage = "<PERSON>hi chú không được vượt quá 500 ký tự")]
    public string? GhiChu { get; set; }
}
