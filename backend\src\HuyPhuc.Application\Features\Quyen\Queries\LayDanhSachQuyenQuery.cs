using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Common.Models;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.Quyen.Queries;

public record LayDanhSachQuyenQuery : IRequest<Result<List<QuyenDto>>>
{
    public bool? TrangThaiHoatDong { get; init; }
    public string? NhomQuyen { get; init; }
    public string? TimKiem { get; init; }
}

public class LayDanhSachQuyenQueryHandler : IRequestHandler<LayDanhSachQuyenQuery, Result<List<QuyenDto>>>
{
    private readonly IApplicationDbContext _context;

    public LayDanhSachQuyenQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<QuyenDto>>> Handle(LayDanhSachQuyenQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Quyen.AsQueryable();

        // Lọc theo trạng thái hoạt động
        if (request.TrangThaiHoatDong.HasValue)
        {
            query = query.Where(q => q.TrangThaiHoatDong == request.TrangThaiHoatDong.Value);
        }

        // Lọc theo nhóm quyền
        if (!string.IsNullOrWhiteSpace(request.NhomQuyen))
        {
            query = query.Where(q => q.NhomQuyen == request.NhomQuyen);
        }

        // Tìm kiếm theo tên hoặc mã quyền
        if (!string.IsNullOrWhiteSpace(request.TimKiem))
        {
            query = query.Where(q => q.TenQuyen.Contains(request.TimKiem) || 
                                    q.MaQuyen.Contains(request.TimKiem) ||
                                    (q.MoTa != null && q.MoTa.Contains(request.TimKiem)));
        }

        var quyenList = await query
            .OrderBy(q => q.NhomQuyen)
            .ThenBy(q => q.TenQuyen)
            .ToListAsync(cancellationToken);

        var result = quyenList.Select(q => new QuyenDto
        {
            Id = q.Id,
            TenQuyen = q.TenQuyen,
            MaQuyen = q.MaQuyen,
            MoTa = q.MoTa,
            NhomQuyen = q.NhomQuyen,
            LaQuyenHeThong = q.LaQuyenHeThong,
            TrangThaiHoatDong = q.TrangThaiHoatDong,
            NgayTao = q.NgayTao,
            NgayCapNhat = q.NgayCapNhat
        }).ToList();

        return Result<List<QuyenDto>>.Success(result);
    }
}

public class QuyenDto
{
    public int Id { get; set; }
    public string TenQuyen { get; set; } = string.Empty;
    public string MaQuyen { get; set; } = string.Empty;
    public string? MoTa { get; set; }
    public string? NhomQuyen { get; set; }
    public bool LaQuyenHeThong { get; set; }
    public bool TrangThaiHoatDong { get; set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
}
