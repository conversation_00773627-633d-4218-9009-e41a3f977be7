using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Application.Common.Models;
using HuyPhuc.Application.DTOs.DanToc;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.DanToc.Queries.GetAllDanToc;

/// <summary>
/// Query để lấy danh sách tất cả dân tộc
/// </summary>
public record GetAllDanTocQuery : IRequest<Result<List<DanTocDto>>>;

/// <summary>
/// Handler xử lý GetAllDanTocQuery
/// </summary>
public class GetAllDanTocQueryHandler : IRequestHandler<GetAllDanTocQuery, Result<List<DanTocDto>>>
{
    private readonly IApplicationDbContext _context;

    public GetAllDanTocQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<DanTocDto>>> Handle(GetAllDanTocQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var danTocList = await _context.DmDanToc
                .OrderBy(dt => dt.Rownum)
                .ThenBy(dt => dt.Ma)
                .Select(dt => new DanTocDto
                {
                    Id = dt.Id,
                    Ma = dt.Ma,
                    Ten = dt.Ten,
                    MaVaTen = dt.MaVaTen,
                    CanCu = dt.CanCu,
                    Rownum = dt.Rownum,
                    CreatedAt = dt.Created,
                    UpdatedAt = dt.LastModified
                })
                .ToListAsync(cancellationToken);

            return Result<List<DanTocDto>>.Success(danTocList);
        }
        catch (Exception ex)
        {
            return Result<List<DanTocDto>>.Failure(new[] { $"Lỗi khi lấy danh sách dân tộc: {ex.Message}" });
        }
    }
}
