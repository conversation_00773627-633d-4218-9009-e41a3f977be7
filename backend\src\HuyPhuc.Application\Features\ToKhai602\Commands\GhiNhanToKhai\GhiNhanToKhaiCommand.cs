using HuyPhuc.Application.DTOs.ToKhai602;
using HuyPhuc.Application.Common.Interfaces;
using HuyPhuc.Domain.Entities;
using HuyPhuc.Domain.Entities.Base;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace HuyPhuc.Application.Features.ToKhai602.Commands.GhiNhanToKhai;

/// <summary>
/// Command ghi nhận tờ khai 602
/// </summary>
public record GhiNhanToKhaiCommand(GhiNhanToKhaiRequest Request) : IRequest<GhiNhanToKhaiResponse>;

/// <summary>
/// Handler xử lý ghi nhận tờ khai 602
/// </summary>
public class GhiNhanToKhaiCommandHandler : IRequestHandler<GhiNhanToKhaiCommand, GhiNhanToKhaiResponse>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GhiNhanToKhaiCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<GhiNhanToKhaiResponse> Handle(GhiNhanToKhaiCommand command, CancellationToken cancellationToken)
    {
        var request = command.Request;
        var userIdString = _currentUserService.UserId;
        
        if (userIdString == null || !int.TryParse(userIdString, out var userId))
        {
            throw new UnauthorizedAccessException("Không thể xác định người dùng hiện tại");
        }

        // Kiểm tra tờ khai có tồn tại không
        var toKhai = await _context.ToKhai602
            .Include(x => x.DanhSachLaoDong)
            .FirstOrDefaultAsync(x => x.Id == request.DraftId, cancellationToken);
        
        if (toKhai == null)
        {
            throw new ArgumentException($"Không tìm thấy tờ khai với ID: {request.DraftId}");
        }

        var response = new GhiNhanToKhaiResponse
        {
            ToKhaiId = toKhai.Id,
            MaToKhai = toKhai.MaToKhai
        };

        var danhSachLoi = new List<string>();
        var danhSachLaoDongDaLuu = new List<LaoDongDaLuuDto>();

        // Xóa dữ liệu lao động cũ (nếu có)
        if (toKhai.DanhSachLaoDong.Any())
        {
            _context.LaoDongToKhai602.RemoveRange(toKhai.DanhSachLaoDong);
        }

        // Xử lý từng lao động
        foreach (var laoDongDto in request.DanhSachLaoDong)
        {
            try
            {
                // Validate dữ liệu lao động
                var validationResult = ValidateLaoDong(laoDongDto);
                if (!validationResult.IsValid)
                {
                    danhSachLoi.Add($"Lao động {laoDongDto.Stt} - {laoDongDto.HoTen}: {validationResult.ErrorMessage}");
                    
                    danhSachLaoDongDaLuu.Add(new LaoDongDaLuuDto
                    {
                        Stt = laoDongDto.Stt,
                        MaSoBHXH = laoDongDto.MaSoBHXH,
                        HoTen = laoDongDto.HoTen,
                        ThanhCong = false,
                        ThongBaoLoi = validationResult.ErrorMessage
                    });
                    continue;
                }

                // Lưu thông tin static vào bảng tk1_ts (nếu chưa có)
                await LuuThongTinStaticNeuChua(laoDongDto, cancellationToken);

                // Tạo entity LaoDongToKhai602
                var laoDongEntity = new LaoDongToKhai602
                {
                    ToKhai602Id = toKhai.Id,
                    MaSoBHXH = laoDongDto.MaSoBHXH,
                    Stt = laoDongDto.Stt,
                    PhuongAn = laoDongDto.PhuongAn,
                    PhuongThuc = laoDongDto.PhuongThuc,
                    ThangBatDau = laoDongDto.ThangBatDau,
                    MucThuNhap = laoDongDto.MucThuNhap,
                    TienLai = laoDongDto.TienLai,
                    TienThua = laoDongDto.TienThua,
                    TienTuDong = laoDongDto.TienTuDong,
                    TongTien = laoDongDto.TongTien,
                    TienHoTro = laoDongDto.TienHoTro,
                    Message = laoDongDto.Message,
                    IsError = laoDongDto.IsError,
                    MaLoi = laoDongDto.MaLoi,
                    MoTaLoi = laoDongDto.MoTaLoi,
                    NgayTao = DateTime.UtcNow,
                    NguoiTao = userId.ToString()
                };

                _context.LaoDongToKhai602.Add(laoDongEntity);

                danhSachLaoDongDaLuu.Add(new LaoDongDaLuuDto
                {
                    Stt = laoDongDto.Stt,
                    MaSoBHXH = laoDongDto.MaSoBHXH,
                    HoTen = laoDongDto.HoTen,
                    ThanhCong = true
                });
            }
            catch (Exception ex)
            {
                danhSachLoi.Add($"Lao động {laoDongDto.Stt} - {laoDongDto.HoTen}: {ex.Message}");
                
                danhSachLaoDongDaLuu.Add(new LaoDongDaLuuDto
                {
                    Stt = laoDongDto.Stt,
                    MaSoBHXH = laoDongDto.MaSoBHXH,
                    HoTen = laoDongDto.HoTen,
                    ThanhCong = false,
                    ThongBaoLoi = ex.Message
                });
            }
        }

        // Cập nhật thông tin tờ khai
        toKhai.NgayCapNhat = DateTime.UtcNow;
        ((IAuditableEntity)toKhai).NguoiCapNhat = userId.ToString();

        // Lưu thay đổi
        await _context.SaveChangesAsync(cancellationToken);

        // Cập nhật response
        response.SoLaoDongDaLuu = danhSachLaoDongDaLuu.Count(x => x.ThanhCong);
        response.SoLaoDongLoi = danhSachLaoDongDaLuu.Count(x => !x.ThanhCong);
        response.DanhSachLoi = danhSachLoi;
        response.DanhSachLaoDongDaLuu = danhSachLaoDongDaLuu;

        return response;
    }

    /// <summary>
    /// Validate thông tin lao động
    /// </summary>
    private (bool IsValid, string ErrorMessage) ValidateLaoDong(LaoDongToKhaiDto laoDong)
    {
        if (string.IsNullOrWhiteSpace(laoDong.MaSoBHXH))
            return (false, "Mã số BHXH không được để trống");

        if (string.IsNullOrWhiteSpace(laoDong.HoTen))
            return (false, "Họ tên không được để trống");

        if (string.IsNullOrWhiteSpace(laoDong.Cmnd))
            return (false, "Số CCCD/CMND không được để trống");

        if (laoDong.MucThuNhap < 1500000)
            return (false, "Mức thu nhập tối thiểu là 1,500,000 VNĐ");

        if (string.IsNullOrWhiteSpace(laoDong.PhuongAn))
            return (false, "Phương án đóng không được để trống");

        if (string.IsNullOrWhiteSpace(laoDong.PhuongThuc))
            return (false, "Phương thức đóng không được để trống");

        return (true, string.Empty);
    }

    /// <summary>
    /// Lưu thông tin static vào bảng tk1_ts nếu chưa có
    /// </summary>
    private async Task LuuThongTinStaticNeuChua(LaoDongToKhaiDto laoDong, CancellationToken cancellationToken)
    {
        // Kiểm tra xem đã có thông tin trong tk1_ts chưa
        var existingTk1Ts = await _context.Tk1Ts
            .FirstOrDefaultAsync(x => x.MaSoBHXH == laoDong.MaSoBHXH, cancellationToken);

        if (existingTk1Ts == null)
        {
            // Tạo mới bản ghi tk1_ts
            var tk1TsEntity = Tk1Ts.Tao(
                maSoBHXH: laoDong.MaSoBHXH,
                hoTen: laoDong.HoTen,
                ngaySinh: laoDong.NgaySinh,
                gioiTinh: laoDong.GioiTinh,
                cmnd: laoDong.Cmnd,
                ccns: string.IsNullOrWhiteSpace(laoDong.Ccns) ? null : laoDong.Ccns,
                dienThoaiLh: string.IsNullOrWhiteSpace(laoDong.DienThoaiLh) ? null : laoDong.DienThoaiLh,
                maTinhKs: string.IsNullOrWhiteSpace(laoDong.MaTinhKs) ? null : laoDong.MaTinhKs,
                maHuyenKs: string.IsNullOrWhiteSpace(laoDong.MaHuyenKs) ? null : laoDong.MaHuyenKs,
                maXaKs: string.IsNullOrWhiteSpace(laoDong.MaXaKs) ? null : laoDong.MaXaKs,
                maHoGiaDinh: string.IsNullOrWhiteSpace(laoDong.MaHoGiaDinh) ? null : laoDong.MaHoGiaDinh
            );

            // Set additional properties from DTO
            tk1TsEntity.QuocTich = laoDong.QuocTich;
            tk1TsEntity.DanToc = laoDong.DanToc;
            tk1TsEntity.TypeId = laoDong.TypeId;
            tk1TsEntity.IsThamGiaBb = laoDong.IsThamGiaBb;
            tk1TsEntity.IsTamHoanHd = laoDong.IsTamHoanHD;

            _context.Tk1Ts.Add(tk1TsEntity);
            Console.WriteLine($"✅ Đã tạo mới tk1_ts: {laoDong.MaSoBHXH} - {laoDong.HoTen}");
        }
        else
        {
            // Cập nhật thông tin nếu cần
            existingTk1Ts.CapNhatThongTin(
                hoTen: laoDong.HoTen,
                ngaySinh: laoDong.NgaySinh,
                gioiTinh: laoDong.GioiTinh,
                cmnd: laoDong.Cmnd,
                ccns: string.IsNullOrWhiteSpace(laoDong.Ccns) ? null : laoDong.Ccns,
                dienThoaiLh: string.IsNullOrWhiteSpace(laoDong.DienThoaiLh) ? null : laoDong.DienThoaiLh,
                maTinhKs: string.IsNullOrWhiteSpace(laoDong.MaTinhKs) ? null : laoDong.MaTinhKs,
                maHuyenKs: string.IsNullOrWhiteSpace(laoDong.MaHuyenKs) ? null : laoDong.MaHuyenKs,
                maXaKs: string.IsNullOrWhiteSpace(laoDong.MaXaKs) ? null : laoDong.MaXaKs,
                maHoGiaDinh: string.IsNullOrWhiteSpace(laoDong.MaHoGiaDinh) ? null : laoDong.MaHoGiaDinh
            );

            // Update additional properties
            existingTk1Ts.QuocTich = laoDong.QuocTich;
            existingTk1Ts.DanToc = laoDong.DanToc;
            existingTk1Ts.TypeId = laoDong.TypeId;
            existingTk1Ts.IsThamGiaBb = laoDong.IsThamGiaBb;
            existingTk1Ts.IsTamHoanHd = laoDong.IsTamHoanHD;

            Console.WriteLine($"✅ Đã cập nhật tk1_ts: {laoDong.MaSoBHXH} - {laoDong.HoTen}");
        }
    }
}
