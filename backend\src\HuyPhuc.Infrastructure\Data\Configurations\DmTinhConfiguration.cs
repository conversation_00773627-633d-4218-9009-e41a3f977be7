using HuyPhuc.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HuyPhuc.Infrastructure.Data.Configurations;

/// <summary>
/// Entity configuration cho DmTinh
/// </summary>
public class DmTinhConfiguration : IEntityTypeConfiguration<DmTinh>
{
    public void Configure(EntityTypeBuilder<DmTinh> builder)
    {
        // Table name
        builder.ToTable("dm_tinh");

        // Primary key
        builder.HasKey(t => t.Id);
        builder.Property(t => t.Id)
            .HasColumnName("id")
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(t => t.MaTinh)
            .HasColumnName("ma_tinh")
            .HasMaxLength(2)
            .IsRequired();

        builder.Property(t => t.TenTinh)
            .HasColumnName("ten_tinh")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(t => t.TextDisplay)
            .HasColumnName("text_display")
            .HasMaxLength(255)
            .IsRequired();

        // Audit fields
        builder.Property(t => t.Created)
            .HasColumnName("created_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(t => t.LastModified)
            .HasColumnName("updated_at")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        // Ignore properties that don't exist in database
        builder.Ignore(t => t.CreatedBy);
        builder.Ignore(t => t.LastModifiedBy);
        builder.Ignore(t => t.NgayTao);
        builder.Ignore(t => t.NgayCapNhat);
        builder.Ignore(t => t.NguoiTao);
        builder.Ignore(t => t.NguoiCapNhat);

        // Indexes
        builder.HasIndex(t => t.MaTinh)
            .IsUnique()
            .HasDatabaseName("idx_dm_tinh_ma_tinh");

        builder.HasIndex(t => t.TenTinh)
            .HasDatabaseName("idx_dm_tinh_ten_tinh");

        // Relationships
        builder.HasMany(t => t.DanhSachHuyen)
            .WithOne(h => h.Tinh)
            .HasForeignKey(h => h.MaTinh)
            .HasPrincipalKey(t => t.MaTinh)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(t => t.DanhSachXa)
            .WithOne(x => x.Tinh)
            .HasForeignKey(x => x.MaTinh)
            .HasPrincipalKey(t => t.MaTinh)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
