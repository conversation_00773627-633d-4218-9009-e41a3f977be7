using System.ComponentModel.DataAnnotations;

namespace HuyPhuc.Application.DTOs.ToKhai602;

/// <summary>
/// Request DTO cho việc cập nhật tờ khai 602
/// </summary>
public class CapNhatToKhaiRequest
{
    /// <summary>
    /// ID đại lý
    /// </summary>
    public int? DaiLyId { get; set; }

    /// <summary>
    /// ID đơn vị
    /// </summary>
    public int? DonViId { get; set; }

    /// <summary>
    /// Số sổ BHXH
    /// </summary>
    [StringLength(50, ErrorMessage = "Số sổ BHXH không được vượt quá 50 ký tự")]
    public string? SoSoBHXH { get; set; }

    /// <summary>
    /// Kỳ kê khai
    /// </summary>
    [StringLength(20, ErrorMessage = "Kỳ kê khai không được vượt quá 20 ký tự")]
    public string? KyKeKhai { get; set; }

    /// <summary>
    /// Ghi chú
    /// </summary>
    [StringLength(500, ErrorMessage = "<PERSON><PERSON> chú không được vượt quá 500 ký tự")]
    public string? GhiChu { get; set; }

    /// <summary>
    /// Danh sách lao động (nếu cần cập nhật)
    /// </summary>
    public List<LaoDongToKhaiDto>? DanhSachLaoDong { get; set; }
}


