namespace HuyPhuc.Application.DTOs.ToKhai602;

/// <summary>
/// Response DTO cho việc lấy danh sách tờ khai 602
/// </summary>
public class LayDanhSachToKhaiResponse
{
    /// <summary>
    /// Danh sách tờ khai
    /// </summary>
    public List<ToKhai602Dto> DanhSach { get; set; } = new();

    /// <summary>
    /// Thông tin phân trang
    /// </summary>
    public PaginationInfo PhanTrang { get; set; } = new();
}

/// <summary>
/// DTO cho thông tin tờ khai 602
/// </summary>
public class ToKhai602Dto
{
    /// <summary>
    /// ID tờ khai
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Mã tờ khai
    /// </summary>
    public string MaToKhai { get; set; } = string.Empty;

    /// <summary>
    /// ID đại lý
    /// </summary>
    public int DaiLyId { get; set; }

    /// <summary>
    /// Tên đại lý
    /// </summary>
    public string TenDaiLy { get; set; } = string.Empty;

    /// <summary>
    /// ID đơn vị
    /// </summary>
    public int DonViId { get; set; }

    /// <summary>
    /// Tên đơn vị
    /// </summary>
    public string TenDonVi { get; set; } = string.Empty;

    /// <summary>
    /// Số sổ BHXH
    /// </summary>
    public string SoSoBHXH { get; set; } = string.Empty;

    /// <summary>
    /// Kỳ kê khai
    /// </summary>
    public string? KyKeKhai { get; set; }

    /// <summary>
    /// Trạng thái
    /// </summary>
    public string TrangThai { get; set; } = string.Empty;

    /// <summary>
    /// Tổng số lao động
    /// </summary>
    public int TongSoLaoDong { get; set; }

    /// <summary>
    /// Tổng tiền đóng
    /// </summary>
    public decimal TongTienDong { get; set; }

    /// <summary>
    /// Ghi chú
    /// </summary>
    public string? GhiChu { get; set; }

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime NgayTao { get; set; }

    /// <summary>
    /// Ngày cập nhật
    /// </summary>
    public DateTime? NgayCapNhat { get; set; }

    /// <summary>
    /// Người tạo
    /// </summary>
    public string? NguoiTao { get; set; }

    /// <summary>
    /// Người cập nhật
    /// </summary>
    public string? NguoiCapNhat { get; set; }
}

/// <summary>
/// DTO cho chi tiết tờ khai 602
/// </summary>
public class ToKhai602ChiTietDto : ToKhai602Dto
{
    /// <summary>
    /// Danh sách lao động
    /// </summary>
    public List<LaoDongToKhaiDto> DanhSachLaoDong { get; set; } = new();
}

/// <summary>
/// Thông tin phân trang
/// </summary>
public class PaginationInfo
{
    /// <summary>
    /// Trang hiện tại
    /// </summary>
    public int Trang { get; set; }

    /// <summary>
    /// Kích thước trang
    /// </summary>
    public int KichThuoc { get; set; }

    /// <summary>
    /// Tổng số bản ghi
    /// </summary>
    public int TongSo { get; set; }

    /// <summary>
    /// Tổng số trang
    /// </summary>
    public int TongTrang { get; set; }
}
