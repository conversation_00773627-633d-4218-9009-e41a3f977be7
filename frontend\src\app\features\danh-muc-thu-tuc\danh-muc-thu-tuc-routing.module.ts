import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { DanhSachThuTucComponent } from './components/danh-sach-thu-tuc/danh-sach-thu-tuc.component';

/**
 * Routing module cho feature danh mục thủ tục
 */
const routes: Routes = [
  {
    path: '',
    redirectTo: 'danh-sach',
    pathMatch: 'full'
  },
  {
    path: 'danh-sach',
    component: DanhSachThuTucComponent,
    data: {
      title: 'Danh sách thủ tục',
      breadcrumb: 'Danh sách'
    }
  }
  // TODO: Thêm các routes khác
  // {
  //   path: 'chi-tiet/:id',
  //   component: ChiTietThuTucComponent,
  //   data: {
  //     title: 'Chi tiết thủ tục',
  //     breadcrumb: 'Chi tiết'
  //   }
  // },
  // {
  //   path: 'them-moi',
  //   component: ThemSuaThuTucComponent,
  //   data: {
  //     title: 'Thêm mới thủ tục',
  //     breadcrumb: 'Thêm mới'
  //   }
  // },
  // {
  //   path: 'chinh-sua/:id',
  //   component: ThemSuaThuTucComponent,
  //   data: {
  //     title: 'Chỉnh sửa thủ tục',
  //     breadcrumb: 'Chỉnh sửa'
  //   }
  // }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DanhMucThuTucRoutingModule { }
