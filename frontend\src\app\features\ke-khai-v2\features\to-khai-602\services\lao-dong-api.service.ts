import { Injectable, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import { SimpleHttpClientService } from '../../../core/services/simple-http-client.service';
import { LaoDong } from '../../../core/models';

/**
 * Interface cho request thêm lao động
 */
export interface ThemLaoDongRequest {
  keKhaiId: number;
  maSoBHXH: string;
  stt: number;

  // Thông tin cá nhân (sẽ được lưu vào tk1_ts)
  hoTen?: string;
  cmnd?: string;
  ccns?: string;
  ngaySinh?: string;
  gioiTinh?: number;
  dienThoai?: string;
  maTinhKs?: string;
  maHuyenKs?: string;
  maXaKs?: string;
  maHoGiaDinh?: string;

  // Thông tin đặc thù cho 602
  phuongAn?: string;
  phuongThuc?: string;
  thangBatDau?: string;
  mucThuNhap?: number;
  tienLai?: number;
  tienThua?: number;
  tienTuDong?: number;
  tongTien?: number;
  tienHoTro?: number;
  tyLe?: number;
  soThang?: number;

}

/**
 * Interface cho request cập nhật lao động
 */
export interface CapNhatLaoDongRequest extends ThemLaoDongRequest {
  id: number;
}

/**
 * Interface cho response API
 */
export interface LaoDongApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

/**
 * Service quản lý API calls cho lao động trong tờ khai 602
 */
@Injectable({
  providedIn: 'root'
})
export class LaoDongApiService {
  private readonly httpClient = inject(SimpleHttpClientService);
  private readonly baseEndpoint = 'ke-khai';

  /**
   * Thêm lao động vào tờ khai
   */
  themLaoDong(request: ThemLaoDongRequest): Observable<LaoDongApiResponse> {
    const endpoint = `${this.baseEndpoint}/${request.keKhaiId}/lao-dong`;

    return this.httpClient.post<any>(endpoint, request).pipe(
      map((response: any) => ({
        success: response.success || true,
        data: response.data,
        message: response.message || 'Thêm lao động thành công'
      })),
      catchError((error: any) => {
        console.error('Lỗi khi thêm lao động:', error);
        return of({
          success: false,
          message: error.error?.message || 'Có lỗi xảy ra khi thêm lao động'
        });
      })
    );
  }

  /**
   * Cập nhật thông tin lao động
   */
  capNhatLaoDong(request: CapNhatLaoDongRequest): Observable<LaoDongApiResponse> {
    const endpoint = `${this.baseEndpoint}/${request.keKhaiId}/lao-dong/${request.id}`;

    return this.httpClient.put<any>(endpoint, request).pipe(
      map((response: any) => ({
        success: response.success || true,
        data: response.data,
        message: response.message || 'Cập nhật lao động thành công'
      })),
      catchError((error: any) => {
        console.error('Lỗi khi cập nhật lao động:', error);
        return of({
          success: false,
          message: error.error?.message || 'Có lỗi xảy ra khi cập nhật lao động'
        });
      })
    );
  }

  /**
   * Xóa lao động khỏi tờ khai
   */
  xoaLaoDong(keKhaiId: number, laoDongId: number): Observable<LaoDongApiResponse> {
    const endpoint = `${this.baseEndpoint}/${keKhaiId}/lao-dong/${laoDongId}`;

    return this.httpClient.delete<any>(endpoint).pipe(
      map((response: any) => ({
        success: response.success || true,
        data: response.data,
        message: response.message || 'Xóa lao động thành công'
      })),
      catchError((error: any) => {
        console.error('Lỗi khi xóa lao động:', error);
        return of({
          success: false,
          message: error.error?.message || 'Có lỗi xảy ra khi xóa lao động'
        });
      })
    );
  }

  /**
   * Lấy danh sách lao động của tờ khai
   */
  layDanhSachLaoDong(keKhaiId: number): Observable<LaoDongApiResponse<LaoDong[]>> {
    const endpoint = `${this.baseEndpoint}/${keKhaiId}/lao-dong`;

    return this.httpClient.get<any>(endpoint).pipe(
      map((response: any) => ({
        success: response.success || true,
        data: response.data || [],
        message: response.message || 'Lấy danh sách lao động thành công'
      })),
      catchError((error: any) => {
        console.error('Lỗi khi lấy danh sách lao động:', error);
        return of({
          success: false,
          data: [],
          message: error.error?.message || 'Có lỗi xảy ra khi lấy danh sách lao động'
        });
      })
    );
  }

  /**
   * Chuyển đổi phương thức từ frontend format sang API format
   */
  private mapPhuongThucToApi(frontendPhuongThuc: string): string {
    const mapping: { [key: string]: string } = {
      '1-thang': '1',
      '3-thang': '3',
      '6-thang': '6',
      '12-thang': '12',
      'nam-sau': 'nam-sau',
      'nam-thieu': 'nam-thieu'
    };

    return mapping[frontendPhuongThuc] || frontendPhuongThuc;
  }

  /**
   * Chuyển đổi giới tính từ string sang number cho API
   */
  private mapGioiTinhToNumber(gioiTinh: any): number | undefined {
    if (typeof gioiTinh === 'number') {
      return gioiTinh;
    }

    if (typeof gioiTinh === 'string') {
      switch (gioiTinh.toLowerCase()) {
        case 'nam':
        case 'male':
        case '1':
          return 1;
        case 'nữ':
        case 'nu':
        case 'female':
        case '2':
        case '0':
          return 2; // Map cả 0 và 2 thành 2 (Nữ)
        default:
          return undefined;
      }
    }

    return undefined;
  }

  /**
   * Chuyển đổi từ LaoDong model sang ThemLaoDongRequest
   */
  mapLaoDongToRequest(laoDong: LaoDong, keKhaiId: number): ThemLaoDongRequest {
    return {
      keKhaiId,
      maSoBHXH: laoDong.maSoBHXH,
      stt: laoDong.stt,

      // Thông tin cá nhân
      hoTen: laoDong.hoTen,
      cmnd: laoDong.cmnd,
      ccns: laoDong.ccns,
      ngaySinh: laoDong.ngaySinh,
      gioiTinh: this.mapGioiTinhToNumber(laoDong.gioiTinh),
      dienThoai: laoDong.dienThoaiLh,
      maTinhKs: laoDong.maTinhKs,
      maHuyenKs: laoDong.maHuyenKs,
      maXaKs: laoDong.maXaKs,
      maHoGiaDinh: laoDong.maHoGiaDinh,

      // Thông tin đặc thù cho 602
      phuongAn: laoDong.phuongAn,
      phuongThuc: this.mapPhuongThucToApi(laoDong.phuongThuc || ''),
      thangBatDau: laoDong.thangBatDau,
      mucThuNhap: laoDong.mucThuNhap,
      tienLai: laoDong.tienLai,
      tienThua: laoDong.tienThua,
      tienTuDong: laoDong.tienTuDong,
      tongTien: laoDong.tongTien,
      tienHoTro: laoDong.tienHoTro,
      tyLe: 22, // Default value
      soThang: 1 // Default value
    };
  }

  /**
   * Chuyển đổi từ LaoDong model sang CapNhatLaoDongRequest
   */
  mapLaoDongToUpdateRequest(laoDong: LaoDong, keKhaiId: number, laoDongId: number): CapNhatLaoDongRequest {
    const baseRequest = this.mapLaoDongToRequest(laoDong, keKhaiId);
    return {
      ...baseRequest,
      id: laoDongId
    };
  }
}
