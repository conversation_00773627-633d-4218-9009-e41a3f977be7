# HuyPhuc Backend API

Backend API cho hệ thống quản lý HuyPhuc được xây dựng với .NET 8, theo kiến trúc Clean Architecture và Domain-Driven Design.

## 🏗️ Kiến trúc

Dự án được tổ chức theo Clean Architecture với 4 layers chính:

### 1. **Domain Layer** (`HuyPhuc.Domain`)
- **Entities**: <PERSON><PERSON><PERSON> thực thể nghiệp vụ (NguoiDung, SanPham, DonHang, ChiTietDonHang)
- **Value Objects**: Email, SoDienThoai, DiaChi
- **Enums**: TrangThaiNguoiDung, TrangThaiDonHang, LoaiSanPham
- **Domain Events**: NguoiDungDaTaoEvent, DonHangDaTaoEvent
- **Repository Interfaces**: Định nghĩa contracts cho data access
- **Domain Services**: Business logic phức tạp

### 2. **Application Layer** (`HuyPhuc.Application`)
- **Features**: <PERSON><PERSON> chức theo tính năng (QuanLyNguoiDung, QuanLy<PERSON>an<PERSON>ham, QuanLyDonHang, BaoCaoThongKe, XacThuc)
- **CQRS Pattern**: Commands và Queries riêng biệt
- **MediatR**: Xử lý requests/responses
- **FluentValidation**: Validation rules
- **AutoMapper**: Object mapping
- **DTOs**: Data Transfer Objects

### 3. **Infrastructure Layer** (`HuyPhuc.Infrastructure`)
- **Data Access**: Entity Framework Core với SQL Server
- **Repository Implementations**: Concrete implementations
- **External Services**: Email, File storage
- **Interceptors**: Audit và Domain Events
- **Configurations**: Entity configurations

### 4. **API Layer** (`HuyPhuc.Api`)
- **Controllers**: RESTful API endpoints theo features
- **Middleware**: Exception handling, logging
- **Extensions**: Service configurations
- **Swagger**: API documentation

## 🚀 Cài đặt và Chạy

### Yêu cầu hệ thống
- .NET 8 SDK
- SQL Server hoặc SQL Server LocalDB
- Visual Studio 2022 hoặc VS Code

### Bước 1: Clone repository
```bash
git clone <repository-url>
cd huyphuc-ivan/backend
```

### Bước 2: Restore packages
```bash
dotnet restore
```

### Bước 3: Cập nhật Connection String
Chỉnh sửa `appsettings.json` trong project `HuyPhuc.Api`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HuyPhucDb;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

### Bước 4: Tạo và chạy migrations
```bash
cd src/HuyPhuc.Api
dotnet ef migrations add InitialCreate --project ../HuyPhuc.Infrastructure
dotnet ef database update --project ../HuyPhuc.Infrastructure
```

### Bước 5: Chạy ứng dụng
```bash
dotnet run --project src/HuyPhuc.Api
```

API sẽ chạy tại: `https://localhost:7000` hoặc `http://localhost:5000`

Swagger UI: `https://localhost:7000` (trang chủ)

## 📚 API Endpoints

### Quản lý Người dùng
- `GET /api/nguoi-dung` - Lấy danh sách người dùng (có phân trang)
- `GET /api/nguoi-dung/{id}` - Lấy chi tiết người dùng
- `POST /api/nguoi-dung` - Tạo người dùng mới
- `PUT /api/nguoi-dung/{id}` - Cập nhật người dùng
- `DELETE /api/nguoi-dung/{id}` - Xóa người dùng

### Ví dụ Request/Response

#### Tạo người dùng mới
```http
POST /api/nguoi-dung
Content-Type: application/json

{
  "hoTen": "Nguyễn Văn A",
  "email": "<EMAIL>",
  "soDienThoai": "0901234567"
}
```

#### Response thành công
```json
{
  "isSuccess": true,
  "data": 1,
  "errors": []
}
```

## 🛠️ Công nghệ sử dụng

- **.NET 8**: Framework chính
- **Entity Framework Core**: ORM
- **SQL Server**: Database
- **MediatR**: CQRS và Mediator pattern
- **FluentValidation**: Validation
- **AutoMapper**: Object mapping
- **Swagger/OpenAPI**: API documentation
- **Serilog**: Logging (có thể thêm)

## 📁 Cấu trúc Project

```
backend/
├── src/
│   ├── HuyPhuc.Domain/           # Domain layer
│   ├── HuyPhuc.Application/      # Application layer  
│   ├── HuyPhuc.Infrastructure/   # Infrastructure layer
│   └── HuyPhuc.Api/             # API layer
├── tests/                       # Test projects (sẽ thêm)
├── docs/                        # Documentation
├── scripts/                     # Build/deployment scripts
└── README.md
```

## 🔧 Cấu hình

### Email Settings
Cập nhật cấu hình email trong `appsettings.json`:
```json
{
  "EmailSettings": {
    "SmtpHost": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "your-app-password",
    "FromEmail": "<EMAIL>",
    "FromName": "HuyPhuc System"
  }
}
```

## 🧪 Testing

Để chạy tests (sẽ implement sau):
```bash
dotnet test
```

## 📝 Quy tắc đặt tên

Dự án tuân thủ quy tắc đặt tên tiếng Việt không dấu:
- **Classes**: PascalCase (VD: `NguoiDungService`)
- **Methods**: camelCase (VD: `layDanhSachNguoiDung`)
- **Properties**: camelCase (VD: `hoTen`, `soDienThoai`)
- **Constants**: UPPER_SNAKE_CASE (VD: `SO_LUONG_TOI_DA`)
- **Database**: snake_case (VD: `nguoi_dung`, `don_hang`)

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/TenTinhNang`)
3. Commit changes (`git commit -am 'Thêm tính năng mới'`)
4. Push to branch (`git push origin feature/TenTinhNang`)
5. Tạo Pull Request

## 📄 License

Dự án này thuộc về HuyPhuc Team.
