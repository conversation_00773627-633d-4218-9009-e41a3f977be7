namespace HuyPhuc.Application.DTOs.DiaChi;

/// <summary>
/// DTO cho thông tin huyện/quận
/// </summary>
public class HuyenDto
{
    /// <summary>
    /// ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Mã huyện
    /// </summary>
    public string MaHuyen { get; set; } = string.Empty;

    /// <summary>
    /// Tên huyện/quận
    /// </summary>
    public string TenHuyen { get; set; } = string.Empty;

    /// <summary>
    /// Text hiển thị (mã - tên)
    /// </summary>
    public string TextDisplay { get; set; } = string.Empty;

    /// <summary>
    /// Mã tỉnh
    /// </summary>
    public string MaTinh { get; set; } = string.Empty;

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Ng<PERSON>y cập nhật
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// DTO cho option dropdown huyện
/// </summary>
public class HuyenOptionDto
{
    /// <summary>
    /// Giá trị (mã huyện)
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Text hiển thị
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Tên huyện
    /// </summary>
    public string Ten { get; set; } = string.Empty;

    /// <summary>
    /// Mã (null cho tương thích với frontend)
    /// </summary>
    public string? Ma { get; set; } = null;
}
