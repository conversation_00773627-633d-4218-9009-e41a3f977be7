import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';

import { ThuTucDto } from '../models/ke-khai.model';
import { environment } from '../../../environments/environment';

/**
 * Service quản lý danh mục thủ tục
 */
@Injectable({
  providedIn: 'root'
})
export class ThuTucService {
  private readonly API_URL = `${environment.apiUrl}/danh-muc-thu-tuc`;

  // State management
  private readonly _dangTai$ = new BehaviorSubject<boolean>(false);
  private readonly _danhSachThuTuc$ = new BehaviorSubject<ThuTucDto[]>([]);

  // Public observables
  readonly dangTai$ = this._dangTai$.asObservable();
  readonly danhSachThuTuc$ = this._danhSachThuTuc$.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Lấy danh sách tất cả thủ tục
   */
  layDanhSachThuTuc(): Observable<ThuTucDto[]> {
    this._dangTai$.next(true);

    return this.http.get<any>(this.API_URL)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi lấy danh sách thủ tục');
          }
        }),
        tap((result) => {
          this._danhSachThuTuc$.next(result);
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('❌ Lỗi khi lấy danh sách thủ tục:', error);
          this._dangTai$.next(false);
          throw error;
        })
      );
  }

  /**
   * Lấy danh sách thủ tục có thể tạo kê khai (chỉ lấy 602, 603...)
   */
  layDanhSachThuTucKeKhai(): Observable<ThuTucDto[]> {
    this._dangTai$.next(true);

    return this.http.get<any>(`${this.API_URL}/ke-khai`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi lấy danh sách thủ tục kê khai');
          }
        }),
        tap((result) => {
          this._dangTai$.next(false);
        }),
        catchError(error => {
          console.error('❌ Lỗi khi lấy danh sách thủ tục kê khai:', error);
          this._dangTai$.next(false);
          throw error;
        })
      );
  }

  /**
   * Lấy chi tiết thủ tục theo ID
   */
  layChiTietThuTuc(id: number): Observable<ThuTucDto> {
    return this.http.get<any>(`${this.API_URL}/${id}`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi lấy chi tiết thủ tục');
          }
        })
      );
  }

  /**
   * Lấy chi tiết thủ tục theo mã
   */
  layChiTietThuTucTheoMa(ma: string): Observable<ThuTucDto> {
    return this.http.get<any>(`${this.API_URL}/ma/${ma}`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi lấy chi tiết thủ tục');
          }
        })
      );
  }

  /**
   * Lấy schema validation cho thủ tục
   */
  laySchemaValidation(thuTucId: number): Observable<any> {
    return this.http.get<any>(`${this.API_URL}/${thuTucId}/schema`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi lấy schema validation');
          }
        })
      );
  }

  /**
   * Validate dữ liệu theo schema của thủ tục
   */
  validateDuLieu(thuTucId: number, duLieu: any): Observable<{ isValid: boolean; errors: string[] }> {
    return this.http.post<any>(`${this.API_URL}/${thuTucId}/validate`, duLieu)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Có lỗi xảy ra khi validate dữ liệu');
          }
        })
      );
  }

  /**
   * Reset state
   */
  resetState(): void {
    this._danhSachThuTuc$.next([]);
    this._dangTai$.next(false);
  }

  /**
   * Lấy thủ tục từ cache theo ID
   */
  getThuTucFromCache(id: number): ThuTucDto | undefined {
    const danhSach = this._danhSachThuTuc$.value;
    return danhSach.find(t => t.id === id);
  }

  /**
   * Lấy thủ tục từ cache theo mã
   */
  getThuTucFromCacheByMa(ma: string): ThuTucDto | undefined {
    const danhSach = this._danhSachThuTuc$.value;
    return danhSach.find(t => t.ma === ma);
  }

  /**
   * Kiểm tra thủ tục có hỗ trợ kê khai không
   */
  isThuTucKeKhai(ma: string): boolean {
    const thuTucKeKhai = ['602', '603', '604', '605']; // Danh sách mã thủ tục hỗ trợ kê khai
    return thuTucKeKhai.includes(ma);
  }

  /**
   * Lấy tên hiển thị của thủ tục
   */
  getTenHienThi(thuTuc: ThuTucDto): string {
    return `${thuTuc.ma} - ${thuTuc.ten}`;
  }

  /**
   * Lấy mô tả ngắn của thủ tục
   */
  getMoTaNgan(thuTuc: ThuTucDto): string {
    if (!thuTuc.moTa) return thuTuc.ten;
    
    const maxLength = 100;
    if (thuTuc.moTa.length <= maxLength) {
      return thuTuc.moTa;
    }
    
    return thuTuc.moTa.substring(0, maxLength) + '...';
  }

  /**
   * Lấy màu sắc cho thủ tục (để hiển thị UI)
   */
  getMauSac(ma: string): string {
    const mauSacMap: { [key: string]: string } = {
      '602': 'bg-blue-100 text-blue-800',
      '603': 'bg-green-100 text-green-800',
      '604': 'bg-yellow-100 text-yellow-800',
      '605': 'bg-purple-100 text-purple-800'
    };
    
    return mauSacMap[ma] || 'bg-gray-100 text-gray-800';
  }

  /**
   * Lấy icon cho thủ tục
   */
  getIcon(ma: string): string {
    const iconMap: { [key: string]: string } = {
      '602': 'fas fa-user-plus',
      '603': 'fas fa-heart',
      '604': 'fas fa-file-alt',
      '605': 'fas fa-clipboard-list'
    };
    
    return iconMap[ma] || 'fas fa-file';
  }
}
