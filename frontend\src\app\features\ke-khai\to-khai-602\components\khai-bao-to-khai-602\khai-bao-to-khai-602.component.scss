/* Simplified styles without @apply */

.khai-bao-to-khai-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 1rem;
}

.header-section h1 {
  font-size: 1.875rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 0.5rem;
}

.header-section h1::after {
  content: '';
  display: block;
  width: 4rem;
  height: 0.25rem;
  background-color: #2563eb;
  margin-top: 0.5rem;
}

.header-section p {
  color: #6b7280;
}

.form-container {
  background-color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.section-title svg {
  flex-shrink: 0;
  margin-right: 0.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-group label .text-red-500 {
  margin-left: 0.25rem;
  color: #ef4444;
}

.form-select,
.form-input,
.form-textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}

.form-select:focus,
.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select:disabled,
.form-input:disabled,
.form-textarea:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.border-red-500 {
  border-color: #ef4444;
}

.border-red-500:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.selected-info {
  border-radius: 0.5rem;
  border: 1px solid;
  padding: 1rem;
  margin-top: 1rem;
}

.selected-info.bg-blue-50 {
  background-color: #eff6ff;
  border-color: #bfdbfe;
}

.selected-info.bg-blue-50 h3 {
  color: #1e3a8a;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.selected-info.bg-green-50 {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
}

.selected-info.bg-green-50 h3 {
  color: #14532d;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.selected-info .grid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1rem;
}

@media (min-width: 768px) {
  .selected-info .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.selected-info .grid div {
  display: flex;
  flex-direction: column;
}

@media (min-width: 640px) {
  .selected-info .grid div {
    flex-direction: row;
    align-items: center;
  }
}

.selected-info .grid div span:first-child {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

@media (min-width: 640px) {
  .selected-info .grid div span:first-child {
    margin-bottom: 0;
    margin-right: 0.5rem;
  }
}

.selected-info .grid div span:last-child {
  color: #111827;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
  margin-top: 2rem;
}

.btn-secondary,
.btn-primary {
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.btn-secondary {
  background-color: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-secondary:hover {
  background-color: #f9fafb;
}

.btn-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 2px #6b7280;
}

.btn-primary {
  background-color: #2563eb;
  border: 1px solid #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #9ca3af;
}

.btn-primary:disabled:hover {
  background-color: #9ca3af;
}

.btn-secondary svg,
.btn-primary svg {
  flex-shrink: 0;
  margin-right: 0.5rem;
}

.grid {
  display: grid;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .khai-bao-to-khai-container {
    padding: 1rem;
  }

  .form-container {
    padding: 1rem;
  }

  .selected-info .grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons button {
    width: 100%;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.text-red-600 {
  color: #dc2626;
}

.mt-1 {
  margin-top: 0.25rem;
}

.text-sm {
  font-size: 0.875rem;
}
