import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

/**
 * Component logo công ty có thể tái sử dụng
 * Hỗ trợ nhiều kích thước và theme khác nhau
 */
@Component({
  selector: 'app-logo',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="logo-container flex items-center" [class]="containerClass">
      <div class="icon-logo flex items-center justify-center rounded-lg" 
           [class]="iconClass">
        <svg class="text-white" [class]="svgClass" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
          </path>
        </svg>
      </div>
      
      <div class="ten-cong-ty ml-3" *ngIf="showText">
        <h1 class="font-bold leading-tight" [class]="titleClass">
          {{ tenCongTy }}
        </h1>
        <p class="text-sm leading-tight" [class]="subtitleClass" *ngIf="moTa">
          {{ moTa }}
        </p>
      </div>
    </div>
  `,
  styles: [`
    .logo-container {
      .icon-logo {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
        }
      }
      
      .ten-cong-ty {
        h1 {
          color: inherit;
        }
        
        p {
          color: inherit;
          opacity: 0.8;
        }
      }
    }
    
    // Theme variations
    .theme-light {
      .ten-cong-ty {
        h1 {
          color: #1f2937;
        }
        
        p {
          color: #6b7280;
        }
      }
    }
    
    .theme-dark {
      .ten-cong-ty {
        h1 {
          background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
        
        p {
          color: #94a3b8;
        }
      }
    }
  `]
})
export class LogoComponent {
  @Input() kichThuoc: 'small' | 'medium' | 'large' = 'medium';
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() showText = true;
  @Input() tenCongTy = 'Enterprise Portal';
  @Input() moTa = 'Hệ thống quản lý doanh nghiệp';

  get containerClass(): string {
    const baseClass = `theme-${this.theme}`;
    return baseClass;
  }

  get iconClass(): string {
    const sizeClasses = {
      small: 'w-8 h-8',
      medium: 'w-10 h-10',
      large: 'w-12 h-12'
    };
    return sizeClasses[this.kichThuoc];
  }

  get svgClass(): string {
    const sizeClasses = {
      small: 'w-4 h-4',
      medium: 'w-5 h-5',
      large: 'w-7 h-7'
    };
    return sizeClasses[this.kichThuoc];
  }

  get titleClass(): string {
    const sizeClasses = {
      small: 'text-lg',
      medium: 'text-xl',
      large: 'text-2xl'
    };
    return sizeClasses[this.kichThuoc];
  }

  get subtitleClass(): string {
    const sizeClasses = {
      small: 'text-xs',
      medium: 'text-sm',
      large: 'text-sm'
    };
    return sizeClasses[this.kichThuoc];
  }
}
